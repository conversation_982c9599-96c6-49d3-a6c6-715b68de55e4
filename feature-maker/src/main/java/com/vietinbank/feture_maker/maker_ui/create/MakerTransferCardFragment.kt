package com.vietinbank.feture_maker.maker_ui.create

import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.viewbinding.ViewBinding
import com.google.gson.Gson
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.getNextDay
import com.vietinbank.core_common.extensions.onSearchTextChanged
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.Utils.asciiOnlyFilter
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_maker.databinding.FragmentMakerCreateTransferCardBinding
import com.vietinbank.feture_maker.maker_ui.bottomSheet.DataFillBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.bottomSheet.NextApproverBottomSheetFragment
import com.vietinbank.feture_maker.maker_ui.viewmodel.MakeTransferViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MakerTransferCardFragment : BaseFragment<MakeTransferViewModel>() {
    override val viewModel: MakeTransferViewModel by activityViewModels()
    override val useCompose: Boolean = false
    private val chooseTimeScheduleTypeDialog = DataFillBottomSheetFragment()
    var nextApproverBottomSheetCardFragment = NextApproverBottomSheetFragment()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun inflateViewBinding(inflater: LayoutInflater, container: ViewGroup?): ViewBinding {
        return FragmentMakerCreateTransferCardBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setDataFeePaymentCardMethod()
        viewModel.getAccountCardList()
        viewModel.contactList()
        viewModel.setDataTypeScheduleTime(chooseTimeScheduleTypeDialog)
        initData()
        initListener()
    }

    private fun initData() {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            titleTimeSchedule.isVisible = true
            tvTimeSchedule.isVisible = true
            tvTypeFee.text = viewModel.currentTypeTransferCard?.name ?: ""
            tvTimeSchedule.text = viewModel.currentTypeCardSchedule?.name ?: ""
            rcvContact.adapter = viewModel.contactCardAdapter
            viewModel.accListCardState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        clFromAccount.visibility = View.VISIBLE
                        viewModel.chooseAccountNoCardDialog.setData(
                            state.data.accountDefault.filter { it.status == "0" }
                                .toMutableList(),
                        )
                        setTextAccountInfo(viewModel.currentCardDefaultDomain)
                        if (!TextUtils.isEmpty(edtToCardNo.text)) {
                            viewModel.validateNapasCard(cardNumber = edtToCardNo.text.toString())
                        }
                    }

                    is Resource.Error -> {
                        clFromAccount.visibility = View.GONE
                    }
                }
            }
            viewModel.validateNapasCardState.observe(viewLifecycleOwner) { state ->
                when (state) {
                    is Resource.Success -> {
                        setTextCardInfo(state.data)
                        edtCustomerCode.text?.clear()
                        if (viewModel.isEnableSaveAccount(edtToCardNo.text.toString())) {
                            swCreateContact.visibility = View.VISIBLE
                            tvSaveAccount.visibility = View.VISIBLE
                        } else {
                            swCreateContact.visibility = View.GONE
                            tvSaveAccount.visibility = View.GONE
                        }
                    }

                    is Resource.Error -> {
                        clearAllText()
                    }
                }
            }
            viewModel.validateNapasCardTransferState.observe(viewLifecycleOwner) { state ->
                val listConfirmTransferObjects = viewModel.confirmTransferCardObjects(
                    state,
                    viewModel.currentCardDefaultDomain?.currentBalance,
                )
                val listConfirmJson = Gson().toJson(listConfirmTransferObjects)
                val bundle = Bundle().apply {
                    putString(
                        Tags.TransferType.TYPE_TRANSFER,
                        Tags.TransferType.TYPE_CARD,
                    )
                    putBoolean(
                        Tags.TransferType.IS_CONTACT_CREATE,
                        viewModel.isSaveCard,
                    )
                    state.customercode = (edtCustomerCode.text.toString())
                    putString(
                        Tags.TransferType.CONFIRM_OBJECT,
                        Utils.g().provideGson().toJson(state),
                    )
                    putString(
                        Tags.TransferType.LIST_CONFIRM_OBJECT,
                        listConfirmJson,
                    )
                }
//                appNavigator.goToMakerConfirmTransferFragment(bundle)
            }

            viewModel.clickItemCardFromContactFragmentState.observe(viewLifecycleOwner) { contactDomains ->
                contactDomains.let {
                    validateNapasCard(contactDomains.cardnumber ?: "")
                }
            }
            viewModel.clickCardShampleFromContactFragmentState.observe(viewLifecycleOwner) { it ->
                validateNapasCard(cardNumber = it.toAccountNo ?: "")
                val formattedText = Utils.g().getDotMoney(it.amount ?: "")
                edtAmount.setText(formattedText)
                edtRemark.setText(it.content ?: "")
            }
            viewModel.handleDatePickerCardState.observe(viewLifecycleOwner) {
                tvDaySchedule.text = it
            }
            viewModel.onSelectTabCardState.observe(viewLifecycleOwner) {
                clearAll()
            }
            viewModel.nextStatusCardTransactionByRuleState.observe(viewLifecycleOwner) { data ->
                if (data.nextApprovers?.size == 0) {
                    showNoticeDialog("Hiện không có người phê duyệt tiếp theo")
                } else {
                    data.nextApprovers?.let { nextApproverBottomSheetCardFragment.setData(it) }
                    nextApproverBottomSheetCardFragment.show(
                        childFragmentManager,
                        "chooseTimeScheduleTypeDialog",
                    )
                }
            }
        }
    }

    private fun setTextCardInfo(data: ValidateNapasCardDomains) {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            viewModel.validateNapasCardDomains = data
            tvToCardName.text = data.cardOwner
            tvToCardInfo.text = data.bankName
            tvToCardName.visibility = View.VISIBLE
            tvToCardInfo.visibility = View.VISIBLE
        }
    }

    private fun initListener() {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            clDaySchedule.setThrottleClickListener {
                edtRemark.filters = arrayOf(asciiOnlyFilter(), InputFilter.LengthFilter(210))
                edtRemark.onSearchTextChanged { s ->
                    tvRemarkCount.text = s.length.toString() + "/210"
                }
//                context?.let {
//                    it.showDatePickerDialog { selectedDate ->
//                        viewModel.handleDatePickerCardState.postValue(selectedDate)
//                    }
//                }
            }

//            titleTimeSchedule.setThrottleClickListener {
//                chooseTimeScheduleTypeDialog.show(
//                    childFragmentManager,
//                    "chooseTimeScheduleTypeDialog",
//                )
//            }
//            tvTimeSchedule.setThrottleClickListener {
//                titleTimeSchedule.callOnClick()
//            }
            chooseTimeScheduleTypeDialog.setOnClickItemListener {
                tvTimeSchedule.text = it.name
                viewModel.currentTypeCardSchedule = it
                if ("0" == it.id) {
                    clDaySchedule.visibility = View.GONE
                    tvDaySchedule.text = ""
                } else {
                    clDaySchedule.visibility = View.VISIBLE
                    tvDaySchedule.text = getNextDay()
                }
            }
            btnOpenContact.setThrottleClickListener {
                viewModel.viewPagerContactAndSampleBottomSheet.show(
                    childFragmentManager,
                    "chooseContactAndSample",
                )
            }
            viewModel.contactCardAdapter.onClickItem = {
                validateNapasCard(it.cardnumber ?: "")
            }
            tvAccountNumber.setThrottleClickListener {
                viewModel.chooseAccountNoCardDialog.show(parentFragmentManager, "chooseAccountNo")
            }

            viewModel.chooseAccountNoCardDialog.setOnClickItemListener {
                viewModel.currentCardDefaultDomain = it
                setTextAccountInfo(viewModel.currentCardDefaultDomain)
            }
            edtAmount.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                }

                override fun afterTextChanged(p0: Editable?) {
                    if (!TextUtils.isEmpty(p0)) {
                        val formattedText = Utils.g().getDotMoney(p0.toString())
                        edtAmount.removeTextChangedListener(this)
                        if (edtAmount.text.toString().replace(",", "").length >= 16) {
                            edtAmount.setText(
                                Utils.g()
                                    .getDotMoney(formattedText.replace(",", "").substring(0, 16)),
                            )
                            edtAmount.setSelection(edtAmount.text.toString().length)
                        } else {
                            edtAmount.setText(formattedText)
                            tvAmountToWord.text =
                                viewModel.readAmountInWord(p0.toString().replace(",", ""))
                            edtAmount.setSelection(formattedText.length)
                        }

                        edtAmount.addTextChangedListener(this)
                    } else {
                        tvAmountToWord.text = ""
                    }
                }
            })
            edtToCardNo.setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
                    hideKeyboard()
                    if (!TextUtils.isEmpty(edtToCardNo.text)) {
                        viewModel.validateNapasCard(cardNumber = edtToCardNo.text.toString())
                    }
                    true
                } else {
                    false
                }
            }
            edtToCardNo.setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !TextUtils.isEmpty(edtToCardNo.text)) {
                    viewModel.validateNapasCard(cardNumber = edtToCardNo.text.toString())
                }
            }
            edtToCardNo.onSearchTextChanged { s ->
                clearAllText()
                viewModel.setDefaultCcurrentTypeAccountSchedule()
                tvTimeSchedule.text = viewModel.currentTypeAccountSchedule?.name ?: ""
                clDaySchedule.visibility = View.GONE
                viewModel.chooseTypeFeeBEN()
                tvTypeFee.text = viewModel.currentTypeTransfer?.name ?: ""
                clearListApprover()
                val newList = if (s.isEmpty()) {
                    viewModel.listContactFilter
                } else {
                    viewModel.listContactFilter.filter {
                        (it.cardnumber ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.revBankName ?: "").lowercase().contains(
                            s.lowercase(),
                        ) || (it.payeename ?: "").lowercase().contains(
                            s.lowercase(),
                        )
                    }.toMutableList()
                }
                viewModel.contactCardAdapter.setData(newList.take(5).toMutableList())
            }
//            titleTypeFee.setThrottleClickListener {
//                viewModel.chooseTransferCardTypeDialog.show(
//                    parentFragmentManager,
//                    "chooseTransferTypeDialog",
//                )
//            }
            viewModel.chooseTransferCardTypeDialog.setOnClickItemListener {
                tvTypeFee.text = it.name
                viewModel.currentTypeTransferCard = it
            }
            btnCreateMaker.setThrottleClickListener {
                val errorMessage = viewModel.validateTransferCardFields(
                    amount = edtAmount.text.toString(),
                    toCardNo = edtToCardNo.text.toString(),
                    remark = edtRemark.text.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }

                viewModel.validNapasCardTransfer(
                    amount = edtAmount.text.toString().replace(",", "").toString(),
                    toCardNo = edtToCardNo.text.toString(),
                    remark = edtRemark.text.toString(),
                    processDate = tvDaySchedule.text.toString(),
                )
            }
            swCreateContact.setOnCheckedChangeListener { _, isChecked ->
                viewModel.isSaveCard = isChecked
                edtCustomerCode.setText("")
                if (isChecked) {
                    edtCustomerCode.visibility = View.VISIBLE
                } else {
                    edtCustomerCode.visibility = View.GONE
                }
            }
            clNextApprover.setThrottleClickListener {
                val errorMessage = viewModel.validateNextStatusCardTransactionByRuleFields(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtToCardNo.text.toString(),
                )

                if (errorMessage != null) {
                    showNoticeDialog(errorMessage)
                    return@setThrottleClickListener
                }
                viewModel.nextStatusCardTransactionByRule(
                    amount = edtAmount.text.toString().replace(",", ""),
                    toAccountNo = edtToCardNo.text.toString(),
                )
            }
            nextApproverBottomSheetCardFragment.setOnClickItemListener { it ->
                viewModel.listAccountApprovers = it
                contentNextApprover.text = viewModel.getNextApproversListString()
            }
        }
    }

    private fun validateNapasCard(
        cardNumber: String,
    ) {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            edtToCardNo.setText(cardNumber)
            viewModel.validateNapasCard(cardNumber = cardNumber)
        }
    }

    private fun setTextAccountInfo(it: AccountDefaultDomain?) {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            edtAmount.setText("")
            tvAccountNumber.text = it?.accountNo ?: ""
            tvCurrentBalnce.text = Utils.g()
                .getDotMoneyHasCcy(it?.currentBalance ?: "0", (it?.currency ?: "").toString())
            tvCurrentcyAmount.text = it?.currency ?: ""
            viewModel.setCcy(it?.currency ?: "")
        }
    }

    private fun clearAllText() {
        (binding as FragmentMakerCreateTransferCardBinding).apply {
            tvToCardName.text = ""
            tvToCardInfo.text = ""
            edtAmount.text?.clear()
            edtRemark.text?.clear()
            tvToCardName.visibility = View.GONE
            tvToCardInfo.visibility = View.GONE
        }
    }

    private fun FragmentMakerCreateTransferCardBinding.clearListApprover() {
        viewModel.clearListApprover()
        nextApproverBottomSheetCardFragment.setData(mutableListOf())
        contentNextApprover.text = "Theo đăng ký với VietinBank"
    }

    private fun FragmentMakerCreateTransferCardBinding.clearAll() {
        clearAllText()
        edtToCardNo.text.clear()
        viewModel.setDefaultCcurrentTypeAccountSchedule()
        tvTimeSchedule.text = viewModel.currentTypeAccountSchedule?.name ?: ""
        clDaySchedule.visibility = View.GONE
        viewModel.chooseTypeFeeBEN()
        tvTypeFee.text = viewModel.currentTypeTransfer?.name ?: ""
        viewModel.currentAccountDefaultDomain = null
        clearListApprover()
        viewModel.setDataContactCardAdapter(viewModel.listContact.filter { it.trantype == Tags.TransferType.TYPE_NAPAS_CARD }.toMutableList())
    }
}