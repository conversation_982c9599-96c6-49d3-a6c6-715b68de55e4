package com.vietinbank.feture_maker.transfer_new.screen.confirm

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.CreateTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CLICK_TYPE_URI_FILE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class MakerTransferConfirmViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    val transferCacheManager: ITransferCacheManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private val _state = MutableStateFlow(MakerTransferConfirmState())
    val state: StateFlow<MakerTransferConfirmState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferConfirmEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    init {
        updateCorpName(userProf.getCorpName() ?: "")
    }

    private fun updateValidateNapasAccountTransferDomain(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        validateNapasAccountTransferDomain.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasAccountTransferDomain = item,
                )
            }
            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateTranType(tranType = item?.trxType ?: "")
            updateTypeTransfer(typeTransfer = item?.typeTransfer ?: "")
            updateIsTypeSplitTransfer(Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == item?.isSplit)
        }
    }

    private fun updateValidateNapasCardTransferDomains(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        validateNapasCardTransferDomains.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasCardTransferDomains = item,
                )
            }
            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateTranType(tranType = Tags.TransferType.TYPE_NAPAS_CARD)
            updateTypeTransfer(typeTransfer = item?.typeTransfer ?: "")
        }
    }

    private fun updateValidatePaymentOrderDomains(validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains) {
        validatePaymentOrderTransferDomains.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validatePaymentOrderTransferDomains = item,
                )
            }
            updateAmountString(amount = item.amount ?: "", currency = item.currency ?: "")
            updateMoneyReader(amount = item.amount ?: "", currency = item.currency ?: "")
            updateTranType(tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER)
            updateTypeTransfer(typeTransfer = item.typeTransfer ?: "")
        }
    }

    private fun updateAmountString(amount: String = "", currency: String = "") {
        val newValue = Utils.g().getDotMoneyHasCcy(amount, currency)
        _state.update { currentState ->
            currentState.copy(
                amountString = "${getTitleForTransferType()}\n$newValue",
            )
        }
    }

    private fun getTitleForTransferType(): String {
        val resId = if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            when (state.value.validateNapasAccountTransferDomain?.bankDomain?.type) {
                Tags.TransferType.TYPE_IN -> R.string.maker_transfer_dashboard_title_in
                Tags.TransferType.TYPE_OUT -> R.string.maker_transfer_dashboard_normal
                Tags.TransferType.TYPE_NAPAS -> R.string.maker_transfer_dashboard_title_np247
                Tags.TransferType.TYPE_NORMAL -> R.string.maker_transfer_dashboard_title_normal
                else -> R.string.maker_transfer_dashboard_title_normal
            }
        } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
            R.string.maker_transfer_dashboard_card
        } else if (KEY_TRANSFER_TYPE_PAYMENT_ORDER == state.value.isTransferTypeKey) {
            R.string.maker_payment_order_dashboard_title
        } else {
            R.string.maker_transfer_dashboard_title_normal
        }

        return resourceProvider.getString(resId)
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: MoneyCurrency.VND.name)

    private fun updateMoneyReader(amount: String, currency: String) {
        _state.update { currentState ->
            currentState.copy(
                amountReader = readAmountInWord(amount, currency),
            )
        }
    }

    private fun updateTranType(tranType: String) {
        _state.update { currentState ->
            currentState.copy(
                tranType = tranType,
            )
        }
    }

    private fun updateTypeTransfer(typeTransfer: String) {
        _state.update { currentState ->
            currentState.copy(
                typeTransfer = typeTransfer,
            )
        }
    }

    private fun updateIsTransferTypeKey(value: String) {
        _state.update { currentState ->
            currentState.copy(
                isTransferTypeKey = value,
            )
        }
    }

    private fun updateIsTypeSplitTransfer(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isTypeSplitTransfer = value,
            )
        }
        if (value) {
            val totalVAT = (
                state.value.validateNapasAccountTransferDomain?.feeSplitNonVat
                    ?: ""
                ).toBigDecimal().add(
                (
                    state.value.validateNapasAccountTransferDomain?.feeSplitVat
                        ?: ""
                    ).toBigDecimal(),
            )
            val totalVatString = Utils.g().getDotMoneyHasCcy(
                totalVAT.toString(),
                state.value.validateNapasAccountTransferDomain?.currency ?: "",
            )
            updateTotalVatSplitTransfer(totalVatString)
        }
    }

    private fun updateTotalVatSplitTransfer(value: String) {
        _state.update { currentState ->
            currentState.copy(
                totalVatSplitTransfer = value,
            )
        }
    }

    private fun updateCorpName(value: String) {
        _state.update { currentState ->
            currentState.copy(
                corpName = value,
            )
        }
    }

    private fun createTransfer() {
        val params = CreateTransferParams(
            username = userProf.getUserName() ?: "",
            tranType = state.value.tranType,
            transferType = state.value.typeTransfer,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createNapasAccountTransfer(params)
            handleResource(res) { data ->
                val stringDomains = when (state.value.isTransferTypeKey) {
                    KEY_TRANSFER_TYPE_ACCOUNT -> {
                        Utils.g().provideGson()
                            .toJson(state.value.validateNapasAccountTransferDomain)
                    }

                    KEY_TRANSFER_TYPE_CARD -> {
                        Utils.g().provideGson().toJson(state.value.validateNapasCardTransferDomains)
                    }

                    KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                        Utils.g().provideGson()
                            .toJson(state.value.validatePaymentOrderTransferDomains)
                    }

                    else -> ""
                }
                _events.send(
                    MakerTransferConfirmEvent.OnConfirmPressed(
                        createTransferDomain = data,
                        stringDomain = stringDomains,
                        keyTransferType = state.value.isTransferTypeKey,
                    ),
                )
            }
        }
    }

    private fun getNextApproversListString() {
        if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        } else if (KEY_TRANSFER_TYPE_PAYMENT_ORDER == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validatePaymentOrderTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validatePaymentOrderTransferDomains?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        }
    }

    fun onAction(action: MakerTransferConfirmAction) {
        when (action) {
            is MakerTransferConfirmAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(MakerTransferConfirmEvent.NavigateBack)
                }
            }

            is MakerTransferConfirmAction.OnConfirmPressed -> {
                createTransfer()
            }

            is MakerTransferConfirmAction.OnGetNextApproversListString -> {
                getNextApproversListString()
            }

            is MakerTransferConfirmAction.OnClickedItem -> {
                when (action.clickType) {
                    CLICK_TYPE_URI_FILE -> {
                        viewModelScope.launch {
                            _events.send(
                                MakerTransferConfirmEvent.OnClickedItem(
                                    state.value.validatePaymentOrderTransferDomains?.uriFile ?: "",
                                ),
                            )
                        }
                    }
                }
            }
        }
    }

    sealed class MakerTransferConfirmEvent {
        data object NavigateBack : MakerTransferConfirmEvent()
        data class OnConfirmPressed(
            val keyTransferType: String = "",
            val createTransferDomain: CreateTransferDomain,
            val stringDomain: String,
        ) : MakerTransferConfirmEvent()

        data class OnClickedItem(
            val uriFile: String,
        ) : MakerTransferConfirmEvent()
    }

    sealed class MakerTransferConfirmAction {
        data object OnBackPressed : MakerTransferConfirmAction()
        data object OnConfirmPressed : MakerTransferConfirmAction()
        data object OnGetNextApproversListString : MakerTransferConfirmAction()
        data class OnClickedItem(val clickType: String) : MakerTransferConfirmAction()
    }

    data class MakerTransferConfirmState(
        var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
        var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null,
        var validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains? = null,
        var amountString: String = "",
        var amountReader: String = "",
        var tranType: String = "",
        var typeTransfer: String = "",
        var listApproverComfirmString: String = "",
        val isTransferTypeKey: String = "",
        var isTypeSplitTransfer: Boolean = false,
        var totalVatSplitTransfer: String = "",
        var corpName: String = "",

    )

    inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        val raw = arguments.getString(key, "") ?: return null
        return try {
            val decoded = URLDecoder.decode(raw, UTF8)
            Utils.g().provideGson().fromJson(decoded, T::class.java)
        } catch (_: Exception) {
            null
        }
    }

    fun handleArguments(arguments: Bundle) {
        when (arguments.getString(TransferConstants.Bundle.KEY_TRANSFER_TYPE, "")) {
            KEY_TRANSFER_TYPE_ACCOUNT -> {
                updateIsTransferTypeKey(value = KEY_TRANSFER_TYPE_ACCOUNT)
                decodeArgumentJson<ValidateNapasAccountTransferDomain>(
                    arguments,
                    TransferConstants.Bundle.CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasAccountTransferDomain(it) }
            }

            KEY_TRANSFER_TYPE_CARD -> {
                updateIsTransferTypeKey(value = KEY_TRANSFER_TYPE_CARD)
                decodeArgumentJson<ValidateNapasCardTransferDomains>(
                    arguments,
                    TransferConstants.Bundle.CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasCardTransferDomains(it) }
            }

            KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                updateIsTransferTypeKey(value = KEY_TRANSFER_TYPE_PAYMENT_ORDER)
                decodeArgumentJson<ValidatePaymentOrderTransferDomains>(
                    arguments,
                    TransferConstants.Bundle.CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidatePaymentOrderDomains(it) }
            }
        }
    }
}