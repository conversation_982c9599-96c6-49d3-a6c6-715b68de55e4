package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed

import android.os.Bundle
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_domain.models.login.AccountListParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.BranchDomains
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.UserApprovalItem
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountParams
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardParams
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferParams
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.BottomSheetItem
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.HourEntity
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SPLIT_TRANSFER_YES
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RECENT
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseEvent.ShowErrorMess
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseEvent.ShowErrorValidateFieldTransferAccount
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseEvent.ValidateNapasAccountTransferState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseEvent.ValidateNapasCardTransferState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class MakerTransferBankChoosedViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val loginUseCase: LoginUseCase,
    private val moneyHelper: MoneyHelper,

) : BaseViewModel() {
    private var transferTimeNow = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TIME_NOW,
        icon = R.drawable.ic_feature_maker_transfer_now,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
        isSelected = true,
    )

    private var transferTimeSchedule = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TIME_SCHEDULE,
        icon = R.drawable.ic_feature_maker_transfer_schedule,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
        isSelected = false,
    )

    private var transferTypeOUR = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TYPE_OUR,
        icon = R.drawable.ic_feature_maker_transfer_our,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_OUR),
        isSelected = true,
        content = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_OUR_content),
    )

    private var transferTypeBEN = BottomSheetItem(
        id = TransferConstants.Transfer.ID_TRANSFER_TYPE_BEN,
        icon = R.drawable.ic_feature_maker_transfer_ben,
        text = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_BEN),
        isSelected = false,
        content = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_BEN_content),
    )

    private val _state = MutableStateFlow(MakerTransferBankChooseState())
    val state: StateFlow<MakerTransferBankChooseState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferBankChooseEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val originalAccountList: MutableList<AccountDefaultDomain> = mutableListOf()
    private val originalBankList: MutableList<DataBankDomain> = mutableListOf()
    private val originalContactList: MutableList<ContactDomains> = mutableListOf()
    private val originalTempTransactionList: MutableList<TempTransactionDomains> = mutableListOf()
    private val originalTransactionInfoList: MutableList<TransactionInfo> = mutableListOf()
    private var originalNextApproversList: MutableList<ApproverDomains> = mutableListOf()
    private var hasValidatedAccountAndCard = false
    private var argumentsHandled = false
    var isCopyFormTransactionManager = false

    fun getAccList() {
        val cachedAccounts = getCachedAccountsForTransferType()

        if (cachedAccounts?.isNotEmpty() == true) {
            processAccounts(cachedAccounts)
        } else {
            fetchAccountsFromApi()
        }
    }

    private fun getCachedAccountsForTransferType(): MutableList<AccountDefaultDomain>? {
        return when {
            isTransferIn() -> transferCacheManager.getAccountsIN()
            isTransferOut() || isTransferNomal() -> transferCacheManager.getAccountsOUT()
            isTransferNapas() -> transferCacheManager.getAccountsNP247()
            else -> null
        }
    }

    private fun processAccounts(accounts: MutableList<AccountDefaultDomain>) {
        updateCurrentAccountDefault(null)
        updateAccountListDomain(accounts)
        updateOriginalAccountList(accounts)

        getDefaultAccount(accounts)?.let { defaultAccount ->
            updateCurrentAccountDefault(defaultAccount)
        }
    }

    private fun fetchAccountsFromApi() {
        launchJob(showLoading = true) {
            val params = AccountListParams(
                username = userProf.getUserName().orEmpty(),
                serviceType = getServiceTypeForCurrentTransfer(),
            )
            val result = loginUseCase.accList(params)

            handleResource(result) { data ->
                if (data.accountDefault.isNotEmpty()) {
                    val activeAccounts = filterActiveAccounts(data.accountDefault)
                    cacheAccountsForTransferType(activeAccounts)
                    processAccounts(data.accountDefault)
                } else {
                    sendErrorMessage(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_list_account_null))
                }
            }
        }
    }

    private fun filterActiveAccounts(accounts: List<AccountDefaultDomain>): MutableList<AccountDefaultDomain> {
        return accounts.filter { it.status == "0" }.toMutableList()
    }

    private fun cacheAccountsForTransferType(activeAccounts: MutableList<AccountDefaultDomain>) {
        when {
            isTransferIn() -> transferCacheManager.saveAccountsIN(activeAccounts)
            isTransferOut() || isTransferNomal() -> transferCacheManager.saveAccountsOUT(
                activeAccounts,
            )

            isTransferNapas() -> transferCacheManager.saveAccountsNP247(activeAccounts)
            else -> transferCacheManager.saveAccountsCARD(activeAccounts)
        }
    }

    private fun getDefaultAccount(accountDefault: MutableList<AccountDefaultDomain>): AccountDefaultDomain? {
        return accountDefault.maxByOrNull {
            it.currentBalance?.toDoubleOrNull() ?: Double.MIN_VALUE
        }
    }

    private fun getPaymentTemplateList() {
        updateIsLoadingListTemp(true)
        if (transferCacheManager.getTempList()?.isNotEmpty() == true) {
            val tempList = transferCacheManager.getTempList()?.filter {
                Tags.TransferType.TYPE_IN == it.tranType ||
                    (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.tranType && it.toBankCode?.isNotEmpty() == true) ||
                    (Tags.TransferType.TYPE_OUT == it.tranType && it.toBankCode?.isNotEmpty() == true)
            }?.toMutableList() ?: mutableListOf()
            updateListTempDomains(tempList)
            originalTempTransactionList.clear()
            originalTempTransactionList.addAll(tempList)
            updateIsLoadingListTemp(false)
            return
        }
        launchJob(showLoading = true) {
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(tranType = "all"),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                transferCacheManager.saveTempList(data.tempTransactionList)
                val tempList = data.tempTransactionList.filter {
                    Tags.TransferType.TYPE_IN == it.tranType ||
                        (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.tranType && it.toBankCode?.isNotEmpty() == true) ||
                        (Tags.TransferType.TYPE_OUT == it.tranType && it.toBankCode?.isNotEmpty() == true)
                }.toMutableList()
                updateListTempDomains(tempList)
                originalTempTransactionList.clear()
                originalTempTransactionList.addAll(tempList)
                updateIsLoadingListTemp(false)
            }
        }
    }

    private fun getContactList() {
        updateIsLoadingListContact(true)
        updateIsTransferScreen()
        if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            val contactList = transferCacheManager.getContactList()?.filter {
                Tags.TransferType.TYPE_IN == it.trantype ||
                    (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trantype && it.bank?.isNotEmpty() == true) ||
                    (Tags.TransferType.TYPE_OUT == it.trantype && it.bank?.isNotEmpty() == true)
            }?.toMutableList() ?: mutableListOf()
            updateListContactDomains(contactList)
            originalContactList.clear()
            originalContactList.addAll(contactList)
            if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                updateSelectedTabContactIndex(0)
            } else {
                updateSelectedTabContactIndex(1)
            }
            canShowBottomSheetContactSaved(true)
            updateIsTransferTypeKeyContactSaveBottomSheet()
            updateIsLoadingListContact(false)
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                updateIsTransferTypeKeyContactSaveBottomSheet()
                transferCacheManager.saveContactist(data.contacts)
                val contactList = data.contacts.filter {
                    Tags.TransferType.TYPE_IN == it.trantype ||
                        (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trantype && it.bank?.isNotEmpty() == true) ||
                        (Tags.TransferType.TYPE_OUT == it.trantype && it.bank?.isNotEmpty() == true)
                }.toMutableList()
                updateListContactDomains(contactList)
                originalContactList.clear()
                originalContactList.addAll(contactList)
                if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                    updateSelectedTabContactIndex(0)
                } else {
                    updateSelectedTabContactIndex(1)
                }
                canShowBottomSheetContactSaved(true)
                updateIsLoadingListContact(false)
            }
        }
    }

    private fun updateCurrentDataBankDomain(currentDataBankDomain: DataBankDomain?) {
        _state.update { currentState ->
            currentState.copy(currentDataBankDomain = currentDataBankDomain)
        }
        if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            getAccList()
            updateListTransferType()
            updateListTransferTime()
            visibilityBottomSheetTransferTime()
        }
        visibilityShowBottomSheetTransferType()
    }

    fun changeUiToNormalTransfer() {
        updateValidateNapasAccountDomain(null)
        setNormalTransferType(true)
        updateTitleAppbar(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_normal))
        changeType247ToNormalListBank()
    }

    private fun updateTitleAppbar(value: String) {
        _state.update { currentState ->
            currentState.copy(titleAppbar = value)
        }
    }

    private fun getTitleForTransferType(): String {
        val res = when {
            isTransferIn() -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_in
            isTransferOut() -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal
            isTransferNapas() -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_np247
            isTransferCard() -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_card
            else -> com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title_normal
        }
        return resourceProvider.getString(res)
    }

    private fun validateNapasAccount(receiveAccount: String) {
        when {
            state.value.currentDataBankDomain == null -> {
                return
            }

            isTransferNomal() || isTransferOut() -> {
                changeUiToNormalTransfer()
                return
            }

            (isTransferIn() || isTransferNapas()) && state.value.currentAccountDefaultDomain != null -> {
                launchJob(showLoading = true) {
                    val params = ValidateNapasAccountParams(
                        receiveAccount = receiveAccount,
                        receiveBin = state.value.currentDataBankDomain?.binCode ?: "",
                        debitAccount = state.value.currentAccountDefaultDomain?.accountNo ?: "",
                        debitFullname = userProf.getFullName() ?: "",
                        cifno = userProf.getCifNo() ?: "",
                        currency = state.value.currentAccountDefaultDomain?.currency ?: "",
                        username = userProf.getUserName() ?: "",
                        tranType = state.value.currentDataBankDomain?.type ?: "",
                        receiveBankName = state.value.currentDataBankDomain?.bankName ?: "",
                    )

                    val res = transferUseCase.validateNapasAccount(params)
                    handleResource(res) { data ->
                        updateValidateNapasAccountDomain(data)
                        updateBeneficiaryAccountName(data.accountOwner ?: "")
                        if (state.value.amountHolder.isNotEmpty()) {
                            return@handleResource
                        }
                        updateIsFocusedAmount(true)
                    }
                }
            }

            else -> getAccList()
        }
    }

    private fun updateCurrentAccountDefault(value: AccountDefaultDomain?) {
        val updatedList = state.value.chooseAccountBottomSheetUiState.accountList?.map { account ->
            when (account.accountNo) {
                state.value.currentAccountDefaultDomain?.accountNo -> account.copy(isSelected = false)
                value?.accountNo -> account.copy(isSelected = true)
                else -> account
            }
        }
        updateAccountListDomain(updatedList?.toMutableList() ?: mutableListOf())
        updateOriginalAccountList(updatedList?.toMutableList() ?: mutableListOf())
        _state.update { currentState ->
            currentState.copy(
                currentAccountDefaultDomain = value,
            )
        }
        viewModelScope.launch {
            _state.collect { currentState ->
                if (currentState.currentAccountDefaultDomain != null && !hasValidatedAccountAndCard) {
                    hasValidatedAccountAndCard = true
                    when (currentState.isTransferTypeKey) {
                        TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT -> {
                            if (currentState.beneficiaryAccount.text.isNotEmpty()) {
                                validateNapasAccount(currentState.beneficiaryAccount.text)
                            }
                        }

                        else -> {
                            if (currentState.cardNumber.text.isNotEmpty()) {
                                validateNapasCard(currentState.cardNumber.text)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun updateCardNumber(value: String) {
        _state.update { currentState ->
            currentState.copy(
                cardNumber = TextFieldValue(
                    text = value,
                    selection = TextRange(value.length),
                ),
            )
        }
    }

    private fun updateAccountText(value: String, isValidate: Boolean = true) {
//        _state.update { currentState ->
//            currentState.copy(
//                beneficiaryAccount = value,
//            )
//        }
        _state.update { currentState ->
            currentState.copy(
                beneficiaryAccount = TextFieldValue(
                    text = value,
                    selection = TextRange(value.length),
                ),
            )
        }
        if (isValidate) {
            _state.update { currentState ->
                currentState.copy(
                    isErrorBeneficiaryAccount = value.isEmpty(),
                )
            }
        }

        if (value.isEmpty() && isValidate) {
            _state.update { currentState ->
                currentState.copy(
                    isErrorBeneficiaryAccountMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account_input),
                )
            }
        }
    }

    private fun updateAccountOwnerHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                accountOwnerHolder = value,
            )
        }
    }

    private fun updateOnTransferTimeText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                transferTimeText = value,
            )
        }
    }

    private fun updateBeneficiaryAccountName(value: String) {
        _state.update { currentState ->
            currentState.copy(
                beneficiaryAccountName = value,
            )
        }
    }

    private fun updateAmountHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                amountHolder = value,
            )
        }
        if (value.isNotEmpty()) {
            updateAmountHolderReader(
                readAmountInWord(
                    amount = value.replace(",", ""),
                    ccy = state.value.currentAccountDefaultDomain?.currency
                        ?: MoneyCurrency.VND.name,
                ),
            )
        }
    }

    private fun updateAmountHolderReader(value: String) {
        _state.update { currentState ->
            currentState.copy(
                amountHolderReader = value,
            )
        }
    }

    private fun updateContentHolder(value: String) {
        _state.update { currentState ->
            currentState.copy(
                contentHolder = value,
            )
        }
    }

    private fun updateSearchTextBankText(value: String) {
        _state.update { currentState ->
            val searchBankText =
                currentState.chooseBankBottomSheetUiState.copy(searchTextBank = value)
            currentState.copy(
                chooseBankBottomSheetUiState = searchBankText,
            )
        }
    }

    private fun updateTransferTime(value: BottomSheetItem) {
        _state.update { currentState ->
            currentState.copy(
                transferTime = value,
            )
        }
    }

    private fun updateTransferType(value: BottomSheetItem) {
        _state.update { currentState ->
            currentState.copy(
                transferTypeFee = value,
            )
        }
    }

    private fun updateListTransferTime() {
        val selectedItems = mutableListOf(transferTimeNow, transferTimeSchedule)
        updateTransferTime(transferTimeNow)
        isShowTransferTimeSchedule(false)
        _state.update { currentState ->
            val items =
                currentState.radioBottomSheetTransferTimeUiState.copy(items = selectedItems)
            currentState.copy(
                radioBottomSheetTransferTimeUiState = items,
            )
        }
    }

    private fun updateListTransferType() {
        val selectedItems = mutableListOf(transferTypeOUR, transferTypeBEN)
        updateTransferType(transferTypeOUR)
        _state.update { currentState ->
            val items =
                currentState.radioBottomSheetTransferTypeFeeUiState.copy(items = selectedItems)
            currentState.copy(
                radioBottomSheetTransferTypeFeeUiState = items,
            )
        }
    }

    private fun dismissBottomSheetTransferType() {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferType = false,
            )
        }
    }

    private fun dismissBottomSheetTransferTime() {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferTime = false,
            )
        }
    }

    private fun validateAndFocusTransferAccountField(): TransferValidationResult? {
        val s = state.value

        return when {
            s.currentDataBankDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_bank_validate),
            )

            isTransferNomal() && s.accountOwnerHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountOwner(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_validate),
            )

            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.beneficiaryAccount.text.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountNumber(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_name_validate),
            )

            s.beneficiaryAccountName.isBlank() && !isTransferNomal() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountNumber(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_validate),
            ).also { updateAccountText(s.beneficiaryAccount.text) }

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            (
                s.amountHolder.replace(",", "").toBigDecimalOrNull() ?: BigDecimal.ZERO
                ) < BigDecimal(2000) &&
                MoneyCurrency.VND.name == (s.currentAccountDefaultDomain?.currency ?: "") &&
                !isTransferIn() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_min_validate),
            )

            s.contentHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedContent(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_content_holder_validate),
            )

            else -> null
        }
    }

    fun validateAndFocusTransferCardFields(): TransferValidationResult? {
        val s = state.value
        return when {
            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.cardNumber.text.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountNumber(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_card_input),
            )

            s.cardOwner.isBlank() ->
                TransferValidationResult(
                    focusAction = { updateIsFocusedAccountNumber(true) },
                    errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_validate),
                )

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            (
                s.amountHolder.replace(",", "").toBigDecimalOrNull() ?: BigDecimal.ZERO
                ) < BigDecimal(2000) &&
                MoneyCurrency.VND.name == (s.currentAccountDefaultDomain?.currency ?: "") &&
                !isTransferIn() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_min_validate),
            )

            s.contentHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedContent(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_content_holder_validate),
            )

            else -> null
        }
    }

    private fun validateNextStatusTransactionByRuleFields(): TransferValidationResult? {
        val s = state.value
        return when {
            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.beneficiaryAccount.text.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountNumber(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_beneficiary_account_name_validate),
            )

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            else -> null
        }
    }

    fun validateNextStatusCardTransactionByRuleFields(): TransferValidationResult? {
        val s = state.value
        return when {
            s.currentAccountDefaultDomain == null -> TransferValidationResult(
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_current_account_default_validate),
            )

            s.cardNumber.text.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAccountNumber(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_card_input),
            )

            s.amountHolder.isBlank() -> TransferValidationResult(
                focusAction = { updateIsFocusedAmount(true) },
                errorMessage = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_account_amount_holder_validate),
            )

            else -> null
        }
    }

    private fun isShowBottomSheetTransferType(): Boolean =
        (state.value.currentDataBankDomain != null && !isTransferNapas()) && (transferTimeSchedule.id != state.value.transferTime?.id)

    private fun canShowBottomSheetTransferType() {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferType = isShowBottomSheetTransferType(),
            )
        }
    }

    private fun isShowBottomSheetTransferTime(): Boolean =
        state.value.currentDataBankDomain != null && !isTransferNapas()

    private fun canShowBottomSheetTransferTime() {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTransferTime = isShowBottomSheetTransferTime(),
            )
        }
    }

    private fun canShowBottomSheetChooseBank(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetChooseBank = show,
            )
        }
    }

    private fun visibilityBottomSheetTransferTime() {
        _state.update { currentState ->
            currentState.copy(
                visibilityBottomSheetTransferTime = isShowBottomSheetTransferTime(),
            )
        }
    }

    private fun isShowTransferTimeSchedule(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                showTransferTimeSchedule = value,
            )
        }
    }

    private fun visibilityShowBottomSheetTransferType() {
        _state.update { currentState ->
            currentState.copy(
                visibilityShowBottomSheetTransferType = isShowBottomSheetTransferType(),
            )
        }
    }

    private fun canShowBottomSheetChooseAccount(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetChooseAccount = show,
            )
        }
    }

    private fun canShowBottomSheetContactSaved(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetContactSaved = show,
            )
        }
    }

    private fun canShowBottomSheetSplitTransfer(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSplitTransfer = show,
            )
        }
    }

    private fun canShowBottomSheetTypeLoan(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetTypeLoan = show,
            )
        }
    }

    private fun updateValidateNapasAccountDomain(validateNapasAccountDomain: ValidateNapasAccountDomain?) {
        _state.update { currentState ->
            currentState.copy(
                validateNapasAccountDomain = validateNapasAccountDomain,
            )
        }
    }

    private fun updateSwitchNextApproversChange(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                switchNextApprovers = value,
            )
        }
    }

    private fun updateCanShowBottomSheetNextApprovers(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetNextApprovers = value,
            )
        }
    }

    private fun updateNextApproversList(value: MutableList<ApproverDomains>) {
        _state.update { currentState ->
            val items =
                currentState.nextApproverBottomSheetUiState.copy(nextApproversList = value)
            currentState.copy(
                nextApproverBottomSheetUiState = items,
            )
        }
    }

    private fun setNormalTransferType(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isNormalTransferType = value,
            )
        }
    }

    private fun getNextApproversListString(): String {
        return if (state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected }) {
            resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_register)
        } else if (state.value.nextApproverBottomSheetUiState.nextApproversList.any { it.isSelected }) {
            resourceProvider.getString(
                com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_people,
                state.value.nextApproverBottomSheetUiState.nextApproversList.filter { it.isSelected }.size.toString(),
            )
        } else {
            ""
        }
    }

    private fun updateNextApproversListString() {
        _state.update { currentState ->
            currentState.copy(
                nextApproversListString = getNextApproversListString(),
            )
        }
    }

    private fun updateIsFocusedAccountNumber(value: Boolean) {
        if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    isFocusedAccountNumber = if (currentState.beneficiaryAccount.text.isNotEmpty()) {
                        false
                    } else {
                        value
                    },
                )
            }
        } else {
            _state.update { currentState ->
                currentState.copy(
                    isFocusedAccountNumber = if (currentState.cardNumber.text.isNotEmpty()) {
                        false
                    } else {
                        value
                    },
                )
            }
        }
    }

    private fun clearAllFocus() {
        updateIsFocusedAccountNumber(false)
        updateIsFocusedAmount(false)
        updateIsFocusedContent(false)
        updateIsFocusedAccountOwner(false)
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: MoneyCurrency.VND.name)

    private fun getNapasBankList() {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            updateListDataBanks(
                transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf(),
            )
            originalBankList.clear()
            originalBankList.addAll(
                transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf(),
            )
            return
        }
        launchJob(showLoading = true) {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                originalBankList.clear()
                originalBankList.addAll(data.dataBanks)
                updateListDataBanks(data.dataBanks)
                transferCacheManager.saveBankList(data.dataBanks)
            }
        }
    }

    private fun updateListDataBanks(listDataBanks: MutableList<DataBankDomain>) {
        _state.update { currentState ->
            currentState.copy(
                listDataBanks = listDataBanks,
            )
        }
        updateBankListBottomSheet(listDataBanks)
    }

    private fun updateAccountListDomain(value: MutableList<AccountDefaultDomain>) {
        _state.update { currentState ->
            val accountList =
                currentState.chooseAccountBottomSheetUiState.copy(accountList = value)
            currentState.copy(
                chooseAccountBottomSheetUiState = accountList,
            )
        }
    }

    private fun updateBankListBottomSheet(value: MutableList<DataBankDomain>) {
        _state.update { currentState ->
            val bankList =
                currentState.chooseBankBottomSheetUiState.copy(bankList = value)
            currentState.copy(
                chooseBankBottomSheetUiState = bankList,
            )
        }
    }

    private fun updateOriginalAccountList(listDataAccount: MutableList<AccountDefaultDomain>) {
        originalAccountList.clear()
        originalAccountList.addAll(listDataAccount)
    }

    private fun updateOnAllItemApproverSelected(value: Boolean) {
        _state.update { currentState ->
            val onAllItemSelected =
                currentState.nextApproverBottomSheetUiState.copy(onAllItemApproverSelected = value)
            currentState.copy(
                nextApproverBottomSheetUiState = onAllItemSelected,
            )
        }
    }

    private fun updateIsTransferTypeKeyContactSaveBottomSheet() {
        _state.update { currentState ->
            val value =
                currentState.contactSavedBottomSheetUiState.copy(
                    isTransferTypeKey = KEY_TRANSFER_TYPE_ACCOUNT,
                    contentEmptyState = com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null_content,
                )

            currentState.copy(
                contactSavedBottomSheetUiState = value,
            )
        }
    }

    private fun updateListContactDomains(value: MutableList<ContactDomains>) {
        _state.update { currentState ->
            val listContactDomains =
                currentState.contactSavedBottomSheetUiState.copy(listContactDomains = value)
            currentState.copy(
                contactSavedBottomSheetUiState = listContactDomains,
            )
        }
    }

    private fun updateListTempDomains(value: MutableList<TempTransactionDomains>) {
        _state.update { currentState ->
            val listTempDomains =
                currentState.contactSavedBottomSheetUiState.copy(listTempDomains = value)
            currentState.copy(
                contactSavedBottomSheetUiState = listTempDomains,
            )
        }
    }

    private fun updateTransactionInfoList(value: MutableList<TransactionInfo>) {
        _state.update { currentState ->
            currentState.copy(
                transactionInfoList = value,
            )
        }
    }

    private fun updateSearchContactText(value: String) {
        _state.update { currentState ->
            val searchContactText =
                currentState.contactSavedBottomSheetUiState.copy(searchContactText = value)
            currentState.copy(
                contactSavedBottomSheetUiState = searchContactText,
            )
        }
    }

    private fun updateSearchRecentText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchRecentText = value,
            )
        }
    }

    private fun updateSelectedTabContactIndex(value: Int) {
        _state.update { currentState ->
            val selectedTabContactIndex =
                currentState.contactSavedBottomSheetUiState.copy(selectedTabIndex = value)
            currentState.copy(
                contactSavedBottomSheetUiState = selectedTabContactIndex,
            )
        }
    }

    private fun updateIsLoadingListContact(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListContact =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListContact = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListContact,
            )
        }
    }

    private fun updateIsLoadingListTemp(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListTemp =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListTemp = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListTemp,
            )
        }
    }

    private fun updateIsTransferScreen() {
        _state.update { currentState ->
            val isTransferScreen =
                currentState.contactSavedBottomSheetUiState.copy(isEdited = false)
            currentState.copy(
                contactSavedBottomSheetUiState = isTransferScreen,
            )
        }
    }

    private fun updateIsFocusedAmount(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusedAmount = if (currentState.amountHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun updateIsFocusedContent(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusedContent = if (currentState.contentHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun updateIsFocusedAccountOwner(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isFocusedAccountOwner = if (currentState.accountOwnerHolder.isNotEmpty()) {
                    false
                } else {
                    value
                },
            )
        }
    }

    private fun clearApprovers() {
        updateNextApproversList(mutableListOf())
        updateNextApproversListString()
        updateOnAllItemApproverSelected(false)
        updateSwitchNextApproversChange(false)
    }

    private fun findMatchingDataBankDomain(
        bank: String?,
        trantype: String?,
    ): DataBankDomain? = when (trantype) {
        Tags.TransferType.TYPE_IN -> state.value.listDataBanks.find { it.type == trantype }
        else -> state.value.listDataBanks.find { it.binCode == bank || it.ebankCode == bank }
    }

    private fun validNapasCardTransfer() {
        launchJob(showLoading = true) {
            val currentAccount = state.value.currentAccountDefaultDomain ?: return@launchJob
            val currentTransferType = state.value.transferTypeFee ?: return@launchJob

            val params = ValidateNapasCardTransferParams(
                amount = state.value.amountHolder,
                currency = currentAccount.currency ?: "",
                feePayMethod = currentTransferType.id,
                fromAcctNo = currentAccount.accountNo ?: "",
                isQRTransfer = "",
                processDate = "",
                remark = state.value.contentHolder,
                sendBank = "12345",
                toBankName = state.value.validateNapasCardDomains?.bankName ?: "",
                toCardName = state.value.validateNapasCardDomains?.cardOwner ?: "",
                toCardNo = state.value.cardNumber.text,
                username = userProf.getUserName() ?: "",
                nextApprovers = if (state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected }) emptyList() else state.value.nextApproverBottomSheetUiState.nextApproversList.filter { it.isSelected },
            )
            val res = transferUseCase.validateNapasCardTransfer(params)
            handleResource(res) { data ->
                data.toBankIconURL = state.value.currentDataBankDomain?.icon ?: ""
                data.typeTransfer = Tags.TransferType.TYPE_CARD
                data.fromAccName = state.value.currentAccountDefaultDomain?.accountName
                data.fromAccBalance = Utils.g().getDotMoneyHasCcy(
                    state.value.currentAccountDefaultDomain?.availableBalance ?: "",
                    state.value.currentAccountDefaultDomain?.currency ?: "",
                )
                data.bankDomain = state.value.currentDataBankDomain
                _events.send(ValidateNapasCardTransferState(data))
            }
        }
    }

    private fun validateNapasAccountTransfer(
        confirm: String? = null,
    ) {
        launchJob(showLoading = true) {
            val currentBankDomain = state.value.currentDataBankDomain ?: return@launchJob
            val currentAccount = state.value.currentAccountDefaultDomain ?: return@launchJob
            val currentTransferType = state.value.transferTypeFee ?: return@launchJob
            val isNapasOrInTransfer = isTransferIn() || isTransferNapas()
            val toAcctName =
                if (isNapasOrInTransfer && state.value.validateNapasAccountDomain != null) {
                    state.value.validateNapasAccountDomain?.accountOwner ?: ""
                } else {
                    state.value.accountOwnerHolder
                }
            val tranType = if (isNapasOrInTransfer) {
                currentBankDomain.type ?: ""
            } else {
                Tags.TransferType.TYPE_OUT
            }
            val receiveBin = when (currentBankDomain.type) {
                Tags.TransferType.TYPE_IN ->
                    state.value.validateNapasAccountDomain?.receiveBin
                        ?: ""

                Tags.TransferType.TYPE_NAPAS -> currentBankDomain.binCode ?: ""
                else -> currentBankDomain.ebankCode ?: ""
            }
            val params = ValidateNapasAccountTransferParams(
                amount = state.value.amountHolder.replace(",", ""),
                currency = currentAccount.currency ?: "",
                feePayMethod = currentTransferType.id,
                fromAcctNo = currentAccount.accountNo ?: "",
                isQRTransfer = "",
                processDate = if (TransferConstants.Transfer.ID_TRANSFER_TIME_NOW == state.value.transferTime?.id) "" else state.value.transferTimeText,
                receiveBin = receiveBin,
                remark = state.value.contentHolder,
                sendBank = "10698",
                toAcctName = toAcctName,
                toAcctNo = state.value.beneficiaryAccount.text,
                toBankName = currentBankDomain.bankName ?: "",
                username = userProf.getUserName() ?: "",
                tranType = tranType,
                nextApprovers = if (state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected }) emptyList() else state.value.nextApproverBottomSheetUiState.nextApproversList.filter { it.isSelected },
                confirm = confirm ?: "",
            )
            val res = transferUseCase.validateNapasAccountTransfer(params)
            handleResource(res) { data ->
                data.toBankIconURL = state.value.currentDataBankDomain?.icon ?: ""
                data.typeTransfer = Tags.TransferType.TYPE_ACCOUNT
                data.fromAccName = state.value.currentAccountDefaultDomain?.accountName
                data.fromAccBalance = Utils.g().getDotMoneyHasCcy(
                    state.value.currentAccountDefaultDomain?.availableBalance ?: "",
                    state.value.currentAccountDefaultDomain?.currency ?: "",
                )
                data.bankDomain = state.value.currentDataBankDomain
                updateValidateNapasAccountTransferDomain(data)
                if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == confirm) {
                    _events.send(ValidateNapasAccountTransferState(data))
                } else if (Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == data.isSplit && !isTransferNomal()) {
                    canShowBottomSheetSplitTransfer(true)
                } else {
                    _events.send(ValidateNapasAccountTransferState(data))
                }
            }
        }
    }

    private fun updateValidateNapasAccountTransferDomain(value: ValidateNapasAccountTransferDomain) {
        _state.update { currentState ->
            currentState.copy(
                validateNapasAccountTransferDomain = value,
            )
        }
        _state.update { currentState ->
            val itemValidate =
                currentState.splitTransferBottomSheetUiState.copy(itemValidate = value)
            currentState.copy(
                splitTransferBottomSheetUiState = itemValidate,
            )
        }
    }

    private fun updateSearchAccountText(value: String) {
        _state.update { currentState ->
            val searchAccountText =
                currentState.chooseAccountBottomSheetUiState.copy(searchAccountText = value)
            currentState.copy(
                chooseAccountBottomSheetUiState = searchAccountText,
            )
        }
    }

    private fun nextStatusTransactionByRule() {
        launchJob(showLoading = true) {
            val params = buildNextStatusParams()
            val res = transferUseCase.nextStatusTransactionByRule(params)

            handleResource(res) { data ->
                val nextApprovers = data.nextApprovers.orEmpty().toMutableList()
                nextApprovers.forEach { it.isSelected = true }
                updateOnAllItemApproverSelected(true)
                updateNextApproversList(nextApprovers)
                originalNextApproversList.clear()
                originalNextApproversList.addAll(nextApprovers)

                updateCanShowBottomSheetNextApprovers(true)
            }
        }
    }

    private fun buildNextStatusParams(): NextStatusTransactionByRuleParams {
        val stateValue = state.value
        val username = userProf.getUserName().orEmpty()
        val customerNumber = userProf.getCifNo().orEmpty()
        val fromAccountNo = stateValue.currentAccountDefaultDomain?.accountNo.orEmpty()

        val toAccountNo =
            if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                stateValue.beneficiaryAccount.text
            } else {
                stateValue.cardNumber.text
            }

        val serviceCode =
            if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                getServiceTypeForCurrentTransfer()
            } else {
                Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
            }

        return NextStatusTransactionByRuleParams(
            amount = stateValue.amountHolder,
            creator = username,
            currentStatus = "",
            currentUserGroup = "",
            currentUserLevel = "0",
            customerNumber = customerNumber,
            fromAccountNo = fromAccountNo,
            serviceCode = serviceCode,
            toAccountNo = toAccountNo,
            username = username,
            mtId = "",
        )
    }

    private fun changeType247ToNormalListBank() {
        val currentState = state.value
        if (currentState.listDataBanks.isEmpty()) return

        val updatedBanks = currentState.listDataBanks.map { bank ->
            if (bank.type == Tags.TransferType.TYPE_NAPAS) {
                bank.convertToNormalType()
            } else {
                bank
            }
        }.toMutableList()

        updateListDataBanks(updatedBanks)
        updateCurrentDataBankIfExists(updatedBanks, currentState.currentDataBankDomain)
    }

    private fun DataBankDomain.convertToNormalType(): DataBankDomain {
        return copy(type = Tags.TransferType.TYPE_NORMAL)
    }

    private fun updateCurrentDataBankIfExists(
        updatedBanks: List<DataBankDomain>,
        currentBank: DataBankDomain?,
    ) {
        currentBank?.let { current ->
            val matchingBank = updatedBanks.find { bank ->
                bank.shortName == current.shortName && bank.bankName == current.bankName
            }
            updateCurrentDataBankDomain(matchingBank)
        }
    }

    private fun validateNapasCard(cardNumber: String) {
        launchJob(showLoading = true) {
            if (state.value.currentAccountDefaultDomain != null) {
                val params = ValidateNapasCardParams(
                    branch = "99998",
                    cardNumber = cardNumber,
                    currency = state.value.currentAccountDefaultDomain?.currency ?: "",
                    debitAccount = state.value.currentAccountDefaultDomain?.accountNo ?: "",
                    debitFullname = userProf.getFullName() ?: "",
                    username = userProf.getUserName() ?: "",
                )
                val res = transferUseCase.validateNapasCard(params)
                handleResource(res) { data ->
                    val result: DataBankDomain? =
                        state.value.listDataBanks.find { it.binCode == data.bankCode }
                    updateCurrentDataBankDomain(result)
                    updateValidateNapasCardDomains(data)
                    updateCardOwner(data.cardOwner ?: "")
                    if (state.value.amountHolder.isNotEmpty()) {
                        return@handleResource
                    }
                    updateIsFocusedAmount(true)
                }
            } else {
                getAccountCardList()
            }
        }
    }

    private fun updateValidateNapasCardDomains(value: ValidateNapasCardDomains) {
        _state.update { currentState ->
            currentState.copy(
                validateNapasCardDomains = value,
            )
        }
    }

    private fun updateCardOwner(value: String) {
        _state.update { currentState ->
            currentState.copy(
                cardOwner = value,
            )
        }
    }

    private fun getAccountCardList() {
        val cachedAccounts = transferCacheManager.getAccountsCARD()
        if (cachedAccounts?.isNotEmpty() == true) {
            processAccounts(cachedAccounts)
            return
        }
        val accountListParams = AccountListParams(
            accountType = "",
            currencySort = "",
            username = userProf.getUserName() ?: "",
            serviceType = Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD,
        )
        launchJob(showLoading = true) {
            val res = loginUseCase.accList(accountListParams)
            handleResource(res) { data ->
                if (data.accountDefault.isNotEmpty()) {
                    val activeAccounts = filterActiveAccounts(data.accountDefault)
                    cacheAccountsForTransferType(activeAccounts)
                    processAccounts(data.accountDefault)
                } else {
                    sendErrorMessage(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_list_account_null))
                }
            }
        }
    }

    private fun clearFocusValidate(includeOwner: Boolean = false) {
        updateIsFocusedAccountNumber(false)
        updateIsFocusedAmount(false)
        updateIsFocusedContent(false)
        if (includeOwner) updateIsFocusedAccountOwner(false)
    }

    private fun handleErrorValidate(error: TransferValidationResult) {
        viewModelScope.launch {
            _events.send(ShowErrorValidateFieldTransferAccount(error))
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
                Constants.MB_VALIDATE_ACCOUNT_TRANSFER, Constants.MB_VALIDATE_ACCOUNT -> {
                    if (exception.code == Tags.TransferType.ERROR_CODE_NORMAL_TRANSFER) {
                        sendErrorMessage(exception.message, resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal))
                        return
                    }
                }
                Constants.MB_ACCOUNT_LIST, Constants.MB_VALIDATE_NAPAS_CARD -> { sendErrorMessage(exception.message) }
                Constants.MB_CONTACT_LIST,
                -> { updateIsLoadingListContact(false) }
                Constants.MB_GET_PAYMENT_TEMPLATE_LIST,
                -> { updateIsLoadingListTemp(false) }
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    private fun sendErrorMessage(message: String?, textButton: String = "") {
        viewModelScope.launch {
            _events.send(ShowErrorMess(message.orEmpty(), textButton = textButton))
        }
    }

    fun onAction(action: MakerTransferBankChooseAction) {
        when (action) {
            is MakerTransferBankChooseAction.OnBeneficiaryAccountTextChange -> {
                updateAccountText(action.beneficiaryAccount.text)
            }

            is MakerTransferBankChooseAction.OnCardNumberTextChange -> {
                updateCardNumber(value = action.cardNumber.text.trim())
                updateCardOwner("")
            }

            is MakerTransferBankChooseAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(MakerTransferBankChooseEvent.NavigateBack)
                }
            }

            is MakerTransferBankChooseAction.OnBeneficiaryAccountFocusChange -> {
                if (!action.beneficiaryAccountFocus && state.value.beneficiaryAccount.text.isNotEmpty()) {
                    validateNapasAccount(state.value.beneficiaryAccount.text)
                    updateIsFocusedAccountNumber(false)
                }
            }

            is MakerTransferBankChooseAction.OnCardNumberChangeFocusChange -> {
                if (!action.onCardNumberChangeFocusChange && state.value.cardNumber.text.isNotEmpty()) {
                    validateNapasCard(cardNumber = state.value.cardNumber.text)
                    updateIsFocusedAccountNumber(false)
                }
            }

            is MakerTransferBankChooseAction.OnChooseTransferTime -> {
                updateTransferTime(action.transferTime)
                if (action.transferTime.id == transferTimeSchedule.id) {
                    updateTransferType(transferTypeOUR)
                }
            }

            is MakerTransferBankChooseAction.OnChooseTransferType -> {
                updateTransferType(action.transferType)
            }

            is MakerTransferBankChooseAction.OnTransferTimeTextChange -> {
                updateOnTransferTimeText(action.transferTimeText)
            }

            is MakerTransferBankChooseAction.OnAmountHolderChange -> {
                updateAmountHolder(action.amountHolder)
            }

            is MakerTransferBankChooseAction.OnContentHolderChange -> {
                updateContentHolder(action.contentHolder)
            }

            is MakerTransferBankChooseAction.CanShowBottomSheetTransferTime -> {
                canShowBottomSheetTransferTime()
                clearAllFocus()
            }

            is MakerTransferBankChooseAction.DismissShowBottomSheetTransferTime -> {
                dismissBottomSheetTransferTime()
            }

            is MakerTransferBankChooseAction.CanShowBottomSheetTransferType -> {
                canShowBottomSheetTransferType()
                clearAllFocus()
            }

            is MakerTransferBankChooseAction.DismissShowBottomSheetTransferType -> {
                dismissBottomSheetTransferType()
            }

            is MakerTransferBankChooseAction.OnClickButtonContinueSPLIT -> {
                validateNapasAccountTransfer(confirm = CONFIRM_SPLIT_TRANSFER_YES)
                canShowBottomSheetSplitTransfer(false)
            }

            is MakerTransferBankChooseAction.OnClickButtonContinue -> {
                clearAllFocus()
                if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                    if (state.value.isFocusedAccountNumber && state.value.beneficiaryAccount.text.isNotEmpty()) {
                        updateIsFocusedAccountNumber(false)
                        return
                    }
                    clearFocusValidate(includeOwner = true)

                    val errorObj = validateAndFocusTransferAccountField()
                    if (errorObj != null) {
                        handleErrorValidate(errorObj)
                        return
                    }
                    validateNapasAccountTransfer()
                } else {
                    if (state.value.isFocusedAccountNumber && state.value.cardNumber.text.isNotEmpty()) {
                        updateIsFocusedAccountNumber(false)
                        return
                    }
                    clearFocusValidate()

                    val errorObj = validateAndFocusTransferCardFields()
                    if (errorObj != null) {
                        handleErrorValidate(errorObj)
                        return
                    }
                    validNapasCardTransfer()
                }
            }

            is MakerTransferBankChooseAction.OnAccountOwnerHolderChange -> {
                updateAccountOwnerHolder(action.accountOwnerHolder)
            }

            is MakerTransferBankChooseAction.OnSwitchNextApproversChange -> {
                clearAllFocus()
                val errorObject =
                    if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                        if (state.value.isFocusedAccountNumber && state.value.beneficiaryAccount.text.isNotEmpty()) {
                            updateIsFocusedAccountNumber(false)
                            return
                        }
                        clearFocusValidate(includeOwner = true)
                        validateNextStatusTransactionByRuleFields()
                    } else {
                        if (state.value.isFocusedAccountNumber && state.value.cardNumber.text.isNotEmpty()) {
                            updateIsFocusedAccountNumber(false)
                            return
                        }
                        clearFocusValidate()
                        validateNextStatusCardTransactionByRuleFields()
                    }

                if (errorObject != null) {
                    handleErrorValidate(errorObject)
                    return
                }

                updateSwitchNextApproversChange(action.switchNextApprovers)

                if (state.value.switchNextApprovers) {
                    nextStatusTransactionByRule()
                    updateOnAllItemApproverSelected(false)
                } else {
                    updateNextApproversList(mutableListOf())
                    updateNextApproversListString()
                }
            }

            is MakerTransferBankChooseAction.DismissShowBottomSheetNextApprovers -> {
                updateCanShowBottomSheetNextApprovers(false)

                val hasSelectedItem =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.any { it.isSelected }
                val hadSelectedBefore = originalNextApproversList.any { it.isSelected }

                if (action.isConfirm) {
                    if (!hasSelectedItem) {
                        updateOnAllItemApproverSelected(false)
                        updateSwitchNextApproversChange(false)
                    }
                    originalNextApproversList.apply {
                        clear()
                        addAll(state.value.nextApproverBottomSheetUiState.nextApproversList)
                    }
                    updateNextApproversList(originalNextApproversList)
                    updateNextApproversListString()
                } else {
                    when {
                        !hasSelectedItem && hadSelectedBefore -> {
                            clearApprovers()
                        }

                        hadSelectedBefore -> {
                            return
                        }

                        else -> {
                            clearApprovers()
                        }
                    }
                }
            }

            is MakerTransferBankChooseAction.OnClickItemApprove -> {
                val newList =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.map { approver ->
                        if (approver == action.approverDomains) {
                            approver.copy(isSelected = !approver.isSelected)
                        } else {
                            approver
                        }
                    }
                updateNextApproversList(newList.toMutableList())
                updateOnAllItemApproverSelected(state.value.nextApproverBottomSheetUiState.nextApproversList.all { it.isSelected })
            }

            is MakerTransferBankChooseAction.OnClickAllItemApprove -> {
                val newList =
                    state.value.nextApproverBottomSheetUiState.nextApproversList.map { approver ->
                        approver.copy(isSelected = action.isAllItemSelected)
                    }
                updateNextApproversList(newList.toMutableList())
                updateOnAllItemApproverSelected(!state.value.nextApproverBottomSheetUiState.onAllItemApproverSelected)
            }

            is MakerTransferBankChooseAction.ShowBottomSheetNextApprovers -> {
                clearAllFocus()
                updateNextApproversList(originalNextApproversList)
                updateCanShowBottomSheetNextApprovers(value = true)
            }

            is MakerTransferBankChooseAction.CanShowBottomSheetChooseAccount -> {
                clearAllFocus()
                canShowBottomSheetChooseAccount(action.isShow)
            }

            is MakerTransferBankChooseAction.CanShowBottomSheetChooseBank -> {
                clearAllFocus()
                canShowBottomSheetChooseBank(action.isShow)
            }

            is MakerTransferBankChooseAction.OnCurrentBankDataChange -> {
                updateCurrentDataBankDomain(action.dataBankDomain)
                setAsAccountTransfer()
                updateBeneficiaryAccountName("")
                updateAccountOwnerHolder("")
                canShowBottomSheetChooseBank(show = false)
            }

            is MakerTransferBankChooseAction.OnCurrentAccountDataChange -> {
                updateCurrentAccountDefault(action.accountDefaultDomain)
                if (MoneyCurrency.VND.name != action.accountDefaultDomain.currency) {
                    updateAmountHolder(state.value.amountHolder)
                }
//                updateAccountText("", isValidate = false)
//                updateBeneficiaryAccountName("")
//                updateAccountOwnerHolder("")
//                updateCardNumber("")
//                updateCardOwner("")
//                updateAmountHolder("")
//                updateContentHolder("")
                canShowBottomSheetChooseAccount(show = false)
            }

            is MakerTransferBankChooseAction.OnClearBeneficiaryAccount -> {
                updateAccountText("", isValidate = false)
                updateBeneficiaryAccountName("")
                updateAccountOwnerHolder("")
                updateAmountHolder("")
                updateContentHolder("")
            }

            is MakerTransferBankChooseAction.OnSearchAccountText -> {
                updateSearchAccountText(action.searchAccountText)
                val searchText = action.searchAccountText.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalAccountList
                } else {
                    originalAccountList.filter { item ->
                        item.accountName?.contains(searchText, ignoreCase = true) == true ||
                            item.aliasName?.contains(searchText, ignoreCase = true) == true ||
                            item.accountNo?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateAccountListDomain(filteredBanks.toMutableList())
            }

            is MakerTransferBankChooseAction.OnSearchTextBankText -> {
                updateSearchTextBankText(action.searchTextBank)
                val searchText = action.searchTextBank.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalBankList
                } else {
                    originalBankList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.shortName?.contains(searchText, ignoreCase = true) == true ||
                            bank.name?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListDataBanks(filteredBanks.toMutableList())
            }

            is MakerTransferBankChooseAction.OnClickContactBottomSheet -> {
                clearAllFocus()
                if (!action.canShowBottomSheetContactSaved) {
                    canShowBottomSheetContactSaved(false)
                    return
                }
                getContactList()
            }

            is MakerTransferBankChooseAction.OnSearchContactText -> {
                updateSearchContactText(action.searchContactText)
                val searchText = action.searchContactText.trim()
                val filteredContacts = if (searchText.isBlank()) {
                    originalContactList
                } else {
                    originalContactList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.account?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeename?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeenickname?.contains(
                                searchText,
                                ignoreCase = true,
                            ) == true ||
                            bank.cardnumber?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListContactDomains(filteredContacts.toMutableList())

                val filteredTemps = if (searchText.isBlank()) {
                    originalTempTransactionList
                } else {
                    originalTempTransactionList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.content?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListTempDomains(filteredTemps.toMutableList())
            }

            is MakerTransferBankChooseAction.OnSelectedTabContactIndex -> {
                updateSelectedTabContactIndex(action.selectedTabIndex)
                if (TAB_RECENT == action.selectedTabIndex) {
                    getPaymentTemplateList()
                }
            }

            is MakerTransferBankChooseAction.OnSearchRecentText -> {
                updateSearchRecentText(action.searchRecentText)
                val searchText = action.searchRecentText.trim()
                val filteredLatest = if (searchText.isBlank()) {
                    originalTransactionInfoList
                } else {
                    originalTransactionInfoList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.toAccount?.contains(searchText, ignoreCase = true) == true ||
                            bank.bankName?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateTransactionInfoList(filteredLatest.toMutableList())
            }

            MakerTransferBankChooseAction.OnClearCardNumber -> {
                updateCardNumber("")
            }

            MakerTransferBankChooseAction.GetAccountList -> {
                if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                    getAccList()
                } else {
                    getAccountCardList()
                }
            }

            is MakerTransferBankChooseAction.ClickContactItem -> {
                updateInfoFromContact(action.contactItem)
                canShowBottomSheetContactSaved(false)
                updateBeneficiaryAccountName("")
                updateAccountOwnerHolder("")
                updateCardOwner("")
            }

            is MakerTransferBankChooseAction.ClickTempItem -> {
                updateInfoFromTemp(action.tempTransactionItem)
                canShowBottomSheetContactSaved(false)
            }

            is MakerTransferBankChooseAction.OnShowSplitTransferBottomSheet -> {
                canShowBottomSheetSplitTransfer(action.canShowBottomSheetSplitTransfer)
            }

            is MakerTransferBankChooseAction.MakeTransferNormalType -> {
                updateAccountOwnerHolder(
                    state.value.validateNapasAccountDomain?.accountOwner ?: "",
                )
                changeUiToNormalTransfer()
                canShowBottomSheetSplitTransfer(false)
                validateNapasAccountTransfer()
            }

            is MakerTransferBankChooseAction.ShowTransferTimeSchedule -> {
                clearAllFocus()
                isShowTransferTimeSchedule(action.show)
            }

            is MakerTransferBankChooseAction.OnShowTypeLoanBottomSheet -> {
                canShowBottomSheetTypeLoan(action.canShowBottomSheetTypeLoan)
            }

            is MakerTransferBankChooseAction.OnFocusAmountChange -> {
                updateIsFocusedAmount(action.isFocusedAmount)
                if (action.isFocusedAmount) {
                    updateIsFocusedAccountNumber(false)
                }
            }

            is MakerTransferBankChooseAction.OnFocusContentChange -> {
                updateIsFocusedContent(action.isFocusedContent)
                if (action.isFocusedContent) {
                    updateIsFocusedAccountNumber(false)
                }
            }

            is MakerTransferBankChooseAction.OnFocusAccountOwnerChange -> {
                updateIsFocusedAccountOwner(action.isFocusedAccountOwner)
            }
        }
    }

    sealed class MakerTransferBankChooseEvent {
        data object NavigateBack : MakerTransferBankChooseEvent()
        data class ValidateNapasAccountTransferState(val validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain) :
            MakerTransferBankChooseEvent()

        data class ValidateNapasCardTransferState(val validateNapasCardTransferDomains: ValidateNapasCardTransferDomains) :
            MakerTransferBankChooseEvent()

        data class ShowErrorMess(val errorMess: String, val textButton: String = "") :
            MakerTransferBankChooseEvent()

        data class ShowErrorValidateFieldTransferAccount(val errorObject: TransferValidationResult) :
            MakerTransferBankChooseEvent()
    }

    sealed class MakerTransferBankChooseAction {
        data object OnBackPressed : MakerTransferBankChooseAction()
        data class OnBeneficiaryAccountTextChange(val beneficiaryAccount: TextFieldValue) :
            MakerTransferBankChooseAction()

        data class OnCardNumberTextChange(val cardNumber: TextFieldValue) :
            MakerTransferBankChooseAction()

        data class OnAccountOwnerHolderChange(val accountOwnerHolder: String) :
            MakerTransferBankChooseAction()

        data class OnTransferTimeTextChange(val transferTimeText: String) :
            MakerTransferBankChooseAction()

        data class OnBeneficiaryAccountFocusChange(val beneficiaryAccountFocus: Boolean) :
            MakerTransferBankChooseAction()

        data class OnCardNumberChangeFocusChange(val onCardNumberChangeFocusChange: Boolean) :
            MakerTransferBankChooseAction()

        data class OnChooseTransferTime(val transferTime: BottomSheetItem) :
            MakerTransferBankChooseAction()

        data class OnChooseTransferType(val transferType: BottomSheetItem) :
            MakerTransferBankChooseAction()

        data class OnAmountHolderChange(val amountHolder: String) : MakerTransferBankChooseAction()
        data class OnFocusAmountChange(val isFocusedAmount: Boolean) :
            MakerTransferBankChooseAction()

        data class OnContentHolderChange(val contentHolder: String) :
            MakerTransferBankChooseAction()

        data class OnFocusContentChange(val isFocusedContent: Boolean) :
            MakerTransferBankChooseAction()

        data class OnFocusAccountOwnerChange(val isFocusedAccountOwner: Boolean) :
            MakerTransferBankChooseAction()

        data class OnSwitchNextApproversChange(val switchNextApprovers: Boolean) :
            MakerTransferBankChooseAction()

        data object CanShowBottomSheetTransferTime : MakerTransferBankChooseAction()

        data object DismissShowBottomSheetTransferTime : MakerTransferBankChooseAction()

        data object CanShowBottomSheetTransferType : MakerTransferBankChooseAction()
        data class CanShowBottomSheetChooseAccount(val isShow: Boolean) :
            MakerTransferBankChooseAction()

        data class CanShowBottomSheetChooseBank(val isShow: Boolean) :
            MakerTransferBankChooseAction()

        data class OnCurrentBankDataChange(val dataBankDomain: DataBankDomain) :
            MakerTransferBankChooseAction()

        data class OnCurrentAccountDataChange(val accountDefaultDomain: AccountDefaultDomain) :
            MakerTransferBankChooseAction()

        data object DismissShowBottomSheetTransferType : MakerTransferBankChooseAction()
        data object ShowBottomSheetNextApprovers : MakerTransferBankChooseAction()

        data class DismissShowBottomSheetNextApprovers(val isConfirm: Boolean) :
            MakerTransferBankChooseAction()

        data object OnClickButtonContinue : MakerTransferBankChooseAction()
        data object OnClickButtonContinueSPLIT : MakerTransferBankChooseAction()
        data class OnClickItemApprove(val approverDomains: ApproverDomains) :
            MakerTransferBankChooseAction()

        data class OnClickAllItemApprove(val isAllItemSelected: Boolean) :
            MakerTransferBankChooseAction()

        data object OnClearBeneficiaryAccount : MakerTransferBankChooseAction()
        data object OnClearCardNumber : MakerTransferBankChooseAction()
        data class OnSearchAccountText(val searchAccountText: String) :
            MakerTransferBankChooseAction()

        data class OnSearchTextBankText(val searchTextBank: String) :
            MakerTransferBankChooseAction()

        data class OnClickContactBottomSheet(val canShowBottomSheetContactSaved: Boolean) :
            MakerTransferBankChooseAction()

        data class OnShowSplitTransferBottomSheet(val canShowBottomSheetSplitTransfer: Boolean) :
            MakerTransferBankChooseAction()

        data class OnShowTypeLoanBottomSheet(val canShowBottomSheetTypeLoan: Boolean) :
            MakerTransferBankChooseAction()

        data class OnSearchContactText(val searchContactText: String) :
            MakerTransferBankChooseAction()

        data class OnSearchRecentText(val searchRecentText: String) :
            MakerTransferBankChooseAction()

        data class OnSelectedTabContactIndex(val selectedTabIndex: Int) :
            MakerTransferBankChooseAction()

        data object GetAccountList : MakerTransferBankChooseAction()
        data class ClickContactItem(val contactItem: ContactDomains) :
            MakerTransferBankChooseAction()

        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) :
            MakerTransferBankChooseAction()

        data object MakeTransferNormalType : MakerTransferBankChooseAction()
        data class ShowTransferTimeSchedule(val show: Boolean) : MakerTransferBankChooseAction()
    }

    data class MakerTransferBankChooseState(
        var titleAppbar: String = "",
        var currentDataBankDomain: DataBankDomain? = null,
        var beneficiaryAccount: TextFieldValue = TextFieldValue(""),
        var cardNumber: TextFieldValue = TextFieldValue(""),
        var cardOwner: String = "",
        var validateNapasCardDomains: ValidateNapasCardDomains? = null,
        var beneficiaryAccountName: String = "",
        var currentAccountDefaultDomain: AccountDefaultDomain? = null,
        var canShowBottomSheetChooseBank: Boolean = false,
        var canShowBottomSheetChooseAccount: Boolean = false,
        var canShowBottomSheetContactSaved: Boolean = false,
        var canShowBottomSheetSplitTransfer: Boolean = false,
        var canShowBottomSheetTypeLoan: Boolean = false,
        val amountHolder: String = "",
        val amountHolderReader: String = "",
        val contentHolder: String = "",
        val accountOwnerHolder: String = "",
        var beneficiaryAccountFocus: Boolean = false,
        var radioBottomSheetTransferTimeUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        var radioBottomSheetTransferTypeFeeUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        var transferTime: BottomSheetItem? = null,
        var transferTimeText: String = "",
        var transferTypeFee: BottomSheetItem? = null,
        var canShowBottomSheetTransferTime: Boolean = false,
        var visibilityBottomSheetTransferTime: Boolean = false,
        var showTransferTimeSchedule: Boolean = false,
        var switchNextApprovers: Boolean = false,
        var canShowBottomSheetTransferType: Boolean = false,
        var visibilityShowBottomSheetTransferType: Boolean = false,
        var validateNapasAccountDomain: ValidateNapasAccountDomain? = null,
        var isNormalTransferType: Boolean = false,
        var canShowBottomSheetNextApprovers: Boolean = false,
        val nextApproversListString: String = "",
        val listDataBanks: MutableList<DataBankDomain> = mutableListOf(),
        val isErrorBeneficiaryAccount: Boolean = false,
        val isErrorBeneficiaryAccountMessage: String = "",
        val searchTextBank: String = "",
        val transactionInfoList: MutableList<TransactionInfo> = mutableListOf(),
        val searchRecentText: String = "",
        val isLoadingListRecent: Boolean = true,
        val isTransferTypeKey: String = "",
        val validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
        var chooseAccountBottomSheetUiState: ChooseAccountBottomSheetUiState = ChooseAccountBottomSheetUiState(),
        var chooseBankBottomSheetUiState: ChooseBankBottomSheetUiState = ChooseBankBottomSheetUiState(),
        var contactSavedBottomSheetUiState: ContactSavedBottomSheetUiState = ContactSavedBottomSheetUiState(),
        var nextApproverBottomSheetUiState: NextApproverBottomSheetUiState = NextApproverBottomSheetUiState(),
        var splitTransferBottomSheetUiState: SplitTransferBottomSheetUiState = SplitTransferBottomSheetUiState(),
        val focusRequesterAccountNumber: FocusRequester = FocusRequester(),
        val focusRequesterAmount: FocusRequester = FocusRequester(),
        val focusRequesterContent: FocusRequester = FocusRequester(),
        val focusRequesterAccountOwner: FocusRequester = FocusRequester(),

        val isFocusedAccountNumber: Boolean = true,
        val isFocusedAmount: Boolean = false,
        val isFocusedContent: Boolean = false,
        val isFocusedAccountOwner: Boolean = false,

    )

    private fun getServiceTypeForCurrentTransfer(): String {
        return when {
            isTransferIn() -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
            isTransferOut() -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
            else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
        }
    }

    private fun isTransferIn() =
        Tags.TransferType.TYPE_IN == state.value.currentDataBankDomain?.type

    private fun isTransferOut() =
        Tags.TransferType.TYPE_OUT == state.value.currentDataBankDomain?.type

    private fun isTransferNapas() =
        Tags.TransferType.TYPE_NAPAS == state.value.currentDataBankDomain?.type

    private fun isTransferCard() =
        Tags.TransferType.TYPE_NAPAS_CARD == state.value.currentDataBankDomain?.type

    private fun isTransferNomal() =
        (Tags.TransferType.TYPE_NORMAL) == state.value.currentDataBankDomain?.type || Tags.TransferType.TYPE_OUT == state.value.currentDataBankDomain?.type

    private fun updateIsTransferTypeKey(value: String) {
        _state.update { currentState ->
            currentState.copy(
                isTransferTypeKey = value,
            )
        }
    }

    private inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        val raw = arguments.getString(key, "") ?: return null
        return try {
            val decoded = URLDecoder.decode(raw, TransferConstants.Bundle.UTF8)
            Utils.g().provideGson().fromJson(decoded, T::class.java)
        } catch (_: Exception) {
            null
        }
    }

    private fun updateInfoFromTemp(domain: TempTransactionDomains) {
        val bankItem = findMatchingDataBankDomain(domain.toBankCode, domain.tranType)
        updateCurrentDataBankDomain(bankItem)
        if (isAccountTransfer(domain.tranType)) {
            setAsAccountTransfer()
            updateAccountText(domain.toAccountNo ?: "", isValidate = false)
            validateNapasAccount(state.value.beneficiaryAccount.text)
        } else {
            setAsCardTransfer()
            updateCardNumber(domain.toAccountNo ?: "")
            validateNapasCard(state.value.cardNumber.text)
        }
        updateAmountHolder(domain.amount ?: "")
        updateContentHolder(domain.content ?: "")
        updateIsFocusedAccountNumber(false)
    }

    private fun updateInfoFromContact(domain: ContactDomains) {
        val bankItem = findMatchingDataBankDomain(domain.bank, domain.trantype)
        updateCurrentDataBankDomain(bankItem)

        if (isAccountTransfer(domain.trantype)) {
            setAsAccountTransfer()
            getAccList()
            updateAccountText(domain.account ?: "", isValidate = true)
            validateNapasAccount(state.value.beneficiaryAccount.text)
        } else {
            setAsCardTransfer()
            updateCardNumber(domain.cardnumber ?: "")
            validateNapasCard(state.value.cardNumber.text)
        }
        updateIsFocusedAccountNumber(false)
    }

    private fun setAsAccountTransfer() {
        updateIsTransferTypeKey(TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT)
        updateTitleAppbar(getTitleForTransferType())
    }

    private fun setAsCardTransfer() {
        updateIsTransferTypeKey(TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD)
        getAccountCardList()
        updateTitleAppbar(resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_card))
    }

    private fun isAccountTransfer(tranType: String?): Boolean {
        return tranType == Tags.TransferType.TYPE_NAPAS_ACCOUNT ||
            tranType == Tags.TransferType.TYPE_NAPAS ||
            tranType == Tags.TransferType.TYPE_IN ||
            tranType == Tags.TransferType.TYPE_OUT
    }

    fun handleArguments(arguments: Bundle) {
        if (argumentsHandled) return
        argumentsHandled = true

        updateListTransferTime()
        updateListTransferType()
        getNapasBankList()

        when (arguments.getString(TransferConstants.Bundle.KEY_TRANSFER_TYPE, "")) {
            TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT -> {
                decodeArgumentJson<DataBankDomain>(
                    arguments,
                    TransferConstants.Bundle.KEY_BANK_ITEM,
                )?.let { domain ->
                    updateCurrentDataBankDomain(domain)
                    setAsAccountTransfer()
                    if (isTransferNomal()) {
                        changeUiToNormalTransfer()
                    }
                    getAccList()
                }
            }

            TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD -> {
                setAsCardTransfer()
            }

            TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_BANK -> {
                decodeArgumentJson<ContactDomains>(
                    arguments,
                    TransferConstants.Bundle.KEY_BANK_ITEM,
                )?.let { domain ->
                    updateInfoFromContact(domain)
                }
            }

            TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_TEMP -> {
                decodeArgumentJson<TempTransactionDomains>(
                    arguments,
                    TransferConstants.Bundle.KEY_BANK_ITEM,
                )?.let { domain ->
                    isCopyFormTransactionManager = domain.isCopy
                    updateInfoFromTemp(domain)
                }
            }

            TransferConstants.Bundle.KEY_TRANSFER_TYPE_SAVED_RECENT -> {
                decodeArgumentJson<TransactionInfo>(
                    arguments,
                    TransferConstants.Bundle.KEY_BANK_ITEM,
                )?.let { domain ->
                    val bankItem = findMatchingDataBankDomain(domain.ebankCode, domain.trxType)
                    updateCurrentDataBankDomain(bankItem)
                    if (isAccountTransfer(domain.trxType)) {
                        setAsAccountTransfer()
                        getAccList()
                        updateAccountText(domain.toAccount ?: "", isValidate = false)
                        validateNapasAccount(state.value.beneficiaryAccount.text)
                    } else {
                        setAsCardTransfer()
                        updateCardNumber(domain.toAccount ?: "")
                        validateNapasCard(state.value.cardNumber.text)
                    }
                    updateAmountHolder(domain.amount ?: "")
                    updateContentHolder(domain.content ?: "")
                    updateIsFocusedAccountNumber(false)
                }
            }
        }
        if (TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            visibilityBottomSheetTransferTime()
        }
        visibilityShowBottomSheetTransferType()
    }
}

data class ChooseAccountBottomSheetUiState(
    val accountList: List<AccountDefaultDomain>? = emptyList(),
    val searchAccountText: String? = "",
)

data class ChooseBankBottomSheetUiState(
    val bankList: List<DataBankDomain>? = emptyList(),
    val searchTextBank: String? = "",
)

data class ContactSavedBottomSheetUiState(
    val searchContactText: String = "",
    val listContactDomains: MutableList<ContactDomains> = mutableListOf(),
    val listTempDomains: MutableList<TempTransactionDomains> = mutableListOf(),
    val selectedTabIndex: Int = 0,
    val isLoadingListContact: Boolean = true,
    val isLoadingListTemp: Boolean = true,
    val isEdited: Boolean = false,
    val isTransferTypeKey: String = "",
    val contentEmptyState: Int = 0,
)

data class NextApproverBottomSheetUiState(
    val confirmTextRes: Int = com.vietinbank.core_ui.R.string.common_apply,
    val cancelTextRes: Int = com.vietinbank.core_ui.R.string.dialog_button_back,
    val onAllItemApproverSelected: Boolean = false,
    val nextApproversList: List<ApproverDomains> = emptyList(),
)

data class RadioBottomSheetUiState(
    var items: List<BottomSheetItem> = emptyList(),
)

data class HourPickerBottomSheetUiState(
    var itemsHour: List<HourEntity> = emptyList(),
)

data class SaveContactBottomSheetUiState(
    var bankItem: DataBankDomain? = null,
    var accountNo: String = "",
    var accountName: String = "",
    var isEdit: Boolean = false,
)

data class SaveTemplateBottomSheetUiState(
    var isTransferTypeKey: String = "",
    var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
    var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null,
    var validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains? = null,
    var createTransferDomain: CreateTransferDomain? = null,
)

data class SplitTransferBottomSheetUiState(
    var itemValidate: ValidateNapasAccountTransferDomain? = null,
)

data class CheckLimitedApprovalBottomSheetUiState(
    val userApprovalList: MutableList<UserApprovalItem>? = mutableListOf(),
    val searchText: String? = "",
    val isShowSeartext: Boolean = false,
)

data class BranchBottomSheetUiState(
    val listBranch: MutableList<BranchDomains> = mutableListOf(),
    val searchBranchText: String? = "",
)

data class TransferValidationResult(
    val focusAction: (() -> Unit)? = null,
    val errorMessage: String? = "",
)