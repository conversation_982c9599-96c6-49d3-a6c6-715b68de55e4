package com.vietinbank.feture_maker.transfer_new.screen.result

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_1
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.maker.ContactCreateParams
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.CreateTemplateParams
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.models.maker.UserApprovalItem
import com.vietinbank.core_domain.models.maker.ValidateNapasAccountTransferDomain
import com.vietinbank.core_domain.models.maker.ValidateNapasCardTransferDomains
import com.vietinbank.core_domain.models.maker.ValidatePaymentOrderTransferDomains
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.CONFIRM_TRANSFER_ITEM
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.RESULT_TRANSFER_ITEM
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CLICK_TYPE_URI_FILE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_CONTACT_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_YES
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.getServiceId
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.CheckLimitedApprovalBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.SaveContactBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.SaveTemplateBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultEvent.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class MakerTransferResultViewModel @Inject constructor(
    val transferCacheManager: ITransferCacheManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val transferUseCase: TransferUseCase,
) : BaseViewModel() {
    private val _state = MutableStateFlow(MakerTransferResultState())
    val state: StateFlow<MakerTransferResultState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferResultEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val originaluserApprovalList: MutableList<UserApprovalItem> = mutableListOf()

    init {
        updateDebitFullName()
    }

    fun getContactList() {
        updateIsLoadingListContact(true)
        val cachedContacts = transferCacheManager.getContactList().orEmpty()
        if (cachedContacts.isNotEmpty()) {
            updateListContactDomains(cachedContacts.toMutableList())
            checkEnableSaveAccount()
            updateIsLoadingListContact(false)
            return
        }

        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().orEmpty(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                updateListContactDomains(data.contacts)
                transferCacheManager.saveContactist(data.contacts)
                checkEnableSaveAccount()
                updateIsLoadingListContact(false)
            }
        }
    }

    private fun checkEnableSaveAccount() {
        val account = when (state.value.isTransferTypeKey) {
            KEY_TRANSFER_TYPE_ACCOUNT -> {
                state.value.validateNapasAccountTransferDomain?.toAcctNo
            }

            KEY_TRANSFER_TYPE_CARD -> {
                state.value.validateNapasCardTransferDomains?.toCardNo
            }

            KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                state.value.validatePaymentOrderTransferDomains?.toAcctNo
            }

            else -> {
                ""
            }
        }
        updateIsEnableSaveAccount(isEnableSaveAccount(account.orEmpty()))
    }

    private fun updateIsEnableSaveAccount(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isEnableSaveAccount = value,
            )
        }
    }

    private fun updateListContactDomains(value: MutableList<ContactDomains>) {
        _state.update { currentState ->
            currentState.copy(
                listContactDomains = value,
            )
        }
    }

    private fun updateDebitFullName() {
        _state.update { currentState ->
            currentState.copy(
                debitFullName = userProf.getFullName() ?: "",
            )
        }
    }

    private fun updateIsLoadingListContact(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListContact = value,
            )
        }
    }

    fun isEnableSaveAccount(receiveAccount: String): Boolean {
        return when (state.value.isTransferTypeKey) {
            KEY_TRANSFER_TYPE_ACCOUNT -> {
                !state.value.listContactDomains.any { it.account == receiveAccount }
            }

            KEY_TRANSFER_TYPE_CARD -> {
                !state.value.listContactDomains.any { it.cardnumber == receiveAccount }
            }

            KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                !state.value.listContactDomains.any { it.account == receiveAccount }
            }

            else -> {
                false
            }
        }
    }

    fun updateValidateNapasAccountTransferDomain(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        validateNapasAccountTransferDomain.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasAccountTransferDomain = item,
                )
            }
            _state.update { currentState ->
                val bankItem =
                    currentState.saveContactBottomSheetUiState.copy(
                        bankItem = item?.bankDomain,
                        accountNo = item?.fromAcctNo ?: "",
                    )
                currentState.copy(
                    saveContactBottomSheetUiState = bankItem,
                )
            }

            _state.update { currentState ->
                val tempItem =
                    currentState.saveTemplateBottomSheetUiState.copy(
                        isTransferTypeKey = KEY_TRANSFER_TYPE_ACCOUNT,
                        validateNapasAccountTransferDomain = item,
                    )
                currentState.copy(
                    saveTemplateBottomSheetUiState = tempItem,
                )
            }
            updateAccountNameBottomSheetSaveContact()
            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateIsTypeSplitTransfer(Tags.TransferType.TYPE_SPLIT_TRANSFER_YES == item?.isSplit)
        }
    }

    private fun updateAccountNameBottomSheetSaveContact() {
        _state.update { currentState ->

            val accountName = when (state.value.isTransferTypeKey) {
                KEY_TRANSFER_TYPE_ACCOUNT -> {
                    state.value.validateNapasAccountTransferDomain?.toAcctName ?: ""
                }

                KEY_TRANSFER_TYPE_CARD -> {
                    state.value.validateNapasCardTransferDomains?.toCardName ?: ""
                }

                KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                    state.value.validatePaymentOrderTransferDomains?.receiveName ?: ""
                }

                else -> {
                    ""
                }
            }
            val bankItem =
                currentState.saveContactBottomSheetUiState.copy(
                    accountName = accountName,
                )
            currentState.copy(
                saveContactBottomSheetUiState = bankItem,
            )
        }
    }

    fun updateValidateNapasCardTransferDomains(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        validateNapasCardTransferDomains.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validateNapasCardTransferDomains = item,
                )
            }
            _state.update { currentState ->
                val bankItem =
                    currentState.saveContactBottomSheetUiState.copy(
                        bankItem = item?.bankDomain,
                        accountNo = item?.toCardNo ?: "",
                        isEdit = false,
                    )
                currentState.copy(
                    saveContactBottomSheetUiState = bankItem,
                )
            }
            _state.update { currentState ->
                val tempItem =
                    currentState.saveTemplateBottomSheetUiState.copy(
                        isTransferTypeKey = KEY_TRANSFER_TYPE_CARD,
                        validateNapasCardTransferDomains = item,
                    )
                currentState.copy(
                    saveTemplateBottomSheetUiState = tempItem,
                )
            }
            updateAccountNameBottomSheetSaveContact()

            updateAmountString(amount = item?.amount ?: "", currency = item?.currency ?: "")
            updateMoneyReader(amount = item?.amount ?: "", currency = item?.currency ?: "")
        }
    }

    private fun updateValidatePaymentOrderDomains(validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains) {
        validatePaymentOrderTransferDomains.let { item ->
            _state.update { currentState ->
                currentState.copy(
                    validatePaymentOrderTransferDomains = item,
                )
            }
            _state.update { currentState ->
                val bankItem =
                    currentState.saveContactBottomSheetUiState.copy(
                        bankItem = item.bankDomain,
                        accountNo = item.toAcctNo ?: "",
                        isEdit = false,
                    )
                currentState.copy(
                    saveContactBottomSheetUiState = bankItem,
                )
            }
            _state.update { currentState ->
                val tempItem =
                    currentState.saveTemplateBottomSheetUiState.copy(
                        isTransferTypeKey = KEY_TRANSFER_TYPE_PAYMENT_ORDER,
                        validatePaymentOrderTransferDomains = item,
                    )
                currentState.copy(
                    saveTemplateBottomSheetUiState = tempItem,
                )
            }
            updateAccountNameBottomSheetSaveContact()
            updateAmountString(amount = item.amount ?: "", currency = item.currency ?: "")
            updateMoneyReader(amount = item.amount ?: "", currency = item.currency ?: "")
        }
    }

    fun updateCreateTransferDomain(createTransferDomain: CreateTransferDomain) {
        createTransferDomain.let { item ->
            val fullName = when (state.value.isTransferTypeKey) {
                KEY_TRANSFER_TYPE_ACCOUNT, KEY_TRANSFER_TYPE_CARD -> {
                    item.fullNameCreate ?: ""
                }

                KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                    state.value.validatePaymentOrderTransferDomains?.fromAccountName ?: ""
                }

                else -> {
                    ""
                }
            }
            val userApprovalItemMaker = UserApprovalItem(
                username = userProf.getUserName() ?: "",
                fullname = fullName,
                approverlevel = resourceProvider.getString(
                    R.string.maker_transfer_account_result_create_at,
                    item.createdDate.getDateToFormat(
                        formatFrom = dd_MM_yyyy_HH_mm_ss_1,
                        formatTo = dd_MM_yyyy_HH_mm_ss,
                    ) ?: "",
                ),
                dailyLimit = "",
                transLimit = "",
            )
            originaluserApprovalList.clear()
            originaluserApprovalList.addAll(item.userApprovalList ?: mutableListOf())
            updateCheckLimitedApprovalBottomSheetList(list = item.userApprovalList ?: mutableListOf())
            updateIsShowSearchTextLimitedApprovalBottomSheetList((item.userApprovalList?.size ?: 0) >= 7)
            _state.update { currentState ->
                val tempItem =
                    currentState.saveTemplateBottomSheetUiState.copy(createTransferDomain = item)
                currentState.copy(
                    saveTemplateBottomSheetUiState = tempItem,
                )
            }
            item.userApprovalList?.add(index = 0, element = userApprovalItemMaker)
            _state.update { currentState ->
                currentState.copy(
                    createTransferDomain = item,
                )
            }
        }
    }

    private fun updateCheckLimitedApprovalBottomSheetList(list: MutableList<UserApprovalItem>) {
        _state.update { currentState ->
            val userApprovalList =
                currentState.checkLimitedApprovalBottomSheetUiState.copy(
                    userApprovalList = list,
                )
            currentState.copy(
                checkLimitedApprovalBottomSheetUiState = userApprovalList,
            )
        }
    }
    private fun updateIsShowSearchTextLimitedApprovalBottomSheetList(value: Boolean) {
        _state.update { currentState ->
            val userApprovalList =
                currentState.checkLimitedApprovalBottomSheetUiState.copy(
                    isShowSeartext = value,
                )
            currentState.copy(
                checkLimitedApprovalBottomSheetUiState = userApprovalList,
            )
        }
    }

    private fun updateIsTransferTypeKey(value: String) {
        _state.update { currentState ->
            currentState.copy(
                isTransferTypeKey = value,
            )
        }
    }

    private fun updateAmountString(amount: String = "", currency: String = "") {
        val newValue = Utils.g().getDotMoneyHasCcy(amount, currency)
        _state.update { currentState ->
            currentState.copy(
                amountString = "${getTitleForTransferType()}\n$newValue",
            )
        }
    }

    private fun getTitleForTransferType(): String {
        val resId = if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            when (state.value.validateNapasAccountTransferDomain?.bankDomain?.type) {
                Tags.TransferType.TYPE_IN -> R.string.maker_transfer_dashboard_title_in
                Tags.TransferType.TYPE_OUT -> R.string.maker_transfer_dashboard_normal
                Tags.TransferType.TYPE_NAPAS -> R.string.maker_transfer_dashboard_title_np247
                Tags.TransferType.TYPE_NORMAL -> R.string.maker_transfer_dashboard_title_normal
                else -> R.string.maker_transfer_dashboard_title_normal
            }
        } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
            R.string.maker_transfer_dashboard_card
        } else if (KEY_TRANSFER_TYPE_PAYMENT_ORDER == state.value.isTransferTypeKey) {
            R.string.maker_payment_order_dashboard_title
        } else {
            R.string.maker_transfer_dashboard_title_normal
        }

        return resourceProvider.getString(resId)
    }

    private fun updateIsTypeSplitTransfer(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isTypeSplitTransfer = value,
            )
        }
        if (value) {
            val totalVAT = (
                state.value.validateNapasAccountTransferDomain?.feeSplitNonVat
                    ?: ""
                ).toBigDecimal()
                .add(
                    (
                        state.value.validateNapasAccountTransferDomain?.feeSplitVat
                            ?: ""
                        ).toBigDecimal(),
                )
            val totalVatString = Utils.g()
                .getDotMoneyHasCcy(
                    totalVAT.toString(),
                    state.value.validateNapasAccountTransferDomain?.currency ?: "",
                )
            updateTotalVatSplitTransfer(totalVatString)
        }
    }

    private fun updateTotalVatSplitTransfer(value: String) {
        _state.update { currentState ->
            currentState.copy(
                totalVatSplitTransfer = value,
            )
        }
    }

    private fun updateSelectedTabIndex(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedTabIndex = value,
            )
        }
    }

    private fun canShowBottomSheetSaveContact(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveContact = show,
            )
        }
    }

    private fun isSaveContactSuccess(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isSaveContactSuccess = value,
            )
        }
    }

    private fun isSaveTempSuccess(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isSaveTempSuccess = value,
            )
        }
    }

    private fun updateCanShowBottomSheetSaveTemp(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveTemp = value,
            )
        }
    }

    private fun updateCustomerCodeContact(value: String) {
        _state.update { currentState ->
            currentState.copy(
                customerCodeContact = value,
            )
        }
    }

    private fun updateCustomerCodeTemp(value: String) {
        _state.update { currentState ->
            currentState.copy(
                customerCodeTemp = value,
            )
        }
    }

    private fun sendErrorMessage(message: String?) {
        viewModelScope.launch {
            _events.send(ShowErrorMess(message.orEmpty()))
        }
    }

    fun showToast(message: String) {
        viewModelScope.launch {
            _events.send(ShowToastMess(message))
        }
    }

    private fun updateCanShowBottomSheetCheckLimitedApproval(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetCheckLimitedApproval = value,
            )
        }
    }

    fun createTemplateAccount(
        confirm: String = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
        validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toAccountName = state.value.customerCodeTemp,
                toBankName = validateNapasAccountTransferDomain?.toBankName ?: "",
                fromAccountNo = validateNapasAccountTransferDomain?.fromAcctNo ?: "",
                toAccountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
                amount = validateNapasAccountTransferDomain?.amount ?: "",
                toBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasAccountTransferDomain?.currency ?: "",
                content = validateNapasAccountTransferDomain?.remark ?: "",
                tranType = validateNapasAccountTransferDomain?.trxType ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                isSaveTempSuccess(true)
                sendErrorMessage(resourceProvider.getString(R.string.maker_transfer_saved_temp_success))
                transferCacheManager.clearTempList()
            }
        }
    }

    fun createTemplateCard(
        confirm: String = CONFIRM_SAVE_TEMP_TRANSFER_NO,
        validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toBankName = validateNapasCardTransferDomains?.toBankName ?: "",
                fromAccountNo = validateNapasCardTransferDomains?.toCardNo ?: "",
                toBankCode = validateNapasCardTransferDomains?.bankDomain?.binCode
                    ?: validateNapasCardTransferDomains?.bankDomain?.ebankCode ?: "",
                bankIcon = validateNapasCardTransferDomains?.bankDomain?.icon ?: "",
                toAccountName = state.value.customerCodeTemp,
                amount = validateNapasCardTransferDomains?.amount ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validateNapasCardTransferDomains?.currency ?: "",
                content = validateNapasCardTransferDomains?.remark ?: "",
                tranType = Tags.TransferType.TYPE_NAPAS_CARD,
                toCardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                isSaveTempSuccess(true)
                sendErrorMessage(resourceProvider.getString(R.string.maker_transfer_saved_temp_success))
                transferCacheManager.clearTempList()
            }
        }
    }

    fun createTemplateOrderPayment(
        confirm: String = CONFIRM_SAVE_TEMP_TRANSFER_NO,
        validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains?,
    ) {
        val params = CreateTemplateParams(
            username = userProf.getUserName() ?: "",
            tempTransaction = TempTransactionParams(
                toBankName = validatePaymentOrderTransferDomains?.receiveBankName ?: "",
                toBankCode = validatePaymentOrderTransferDomains?.bankDomain?.binCode
                    ?: validatePaymentOrderTransferDomains?.bankDomain?.ebankCode ?: "",
                fromAccountNo = validatePaymentOrderTransferDomains?.fromAcctNo ?: "",
                toAccountName = state.value.customerCodeTemp,
                amount = validatePaymentOrderTransferDomains?.amount ?: "",
                userId = userProf.getUserName() ?: "",
                currency = validatePaymentOrderTransferDomains?.currency ?: "",
                content = validatePaymentOrderTransferDomains?.remark ?: "",
                tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER,
                toAccountNo = validatePaymentOrderTransferDomains?.toAcctNo ?: "",
                confirm = confirm,
            ),
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.createTemplate(params)
            handleResource(res) { data ->
                isSaveTempSuccess(true)
                showSuccessMessage(resourceProvider.getString(R.string.maker_transfer_saved_temp_success))
                transferCacheManager.clearTempList()
            }
        }
    }

    private fun showSuccessMessage(success: String) {
        viewModelScope.launch {
            _events.send(HandleSuccessCreateSaveState(success))
        }
    }

    fun contactAccountCreate(validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain?) {
        val params = ContactCreateParams(
            accountNo = validateNapasAccountTransferDomain?.toAcctNo ?: "",
            confirm = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
            currency = validateNapasAccountTransferDomain?.currency ?: "",
            customercode = state.value.customerCodeContact,
            payeeName = validateNapasAccountTransferDomain?.toAcctName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasAccountTransferDomain?.toBankName ?: ""),
            revBankCode = validateNapasAccountTransferDomain?.binCode ?: "",
            serviceId = getServiceId(validateNapasAccountTransferDomain?.trxType ?: ""),
            username = userProf.getUserName() ?: "",
            tranType = validateNapasAccountTransferDomain?.trxType ?: "",
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                isSaveContactSuccess(true)
                transferCacheManager.clearContactList()
            }
        }
    }

    fun contactCardCreate(validateNapasCardTransferDomains: ValidateNapasCardTransferDomains?) {
        val params = ContactCreateParams(
            cardNo = validateNapasCardTransferDomains?.toCardNo ?: "",
            confirm = CONFIRM_SAVE_CONTACT_TRANSFER_NO,
            currency = validateNapasCardTransferDomains?.currency ?: "",
            customercode = state.value.customerCodeContact,
            payeeName = validateNapasCardTransferDomains?.toCardName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validateNapasCardTransferDomains?.toBankName ?: ""),
            revBankCode = "99998",
            serviceId = Tags.TransferType.TYPE_NAPAS_CARD,
            username = userProf.getUserName() ?: "",
            tranType = Tags.TransferType.TYPE_NAPAS_CARD,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                isSaveContactSuccess(true)
                transferCacheManager.clearContactList()
            }
        }
    }

    fun contactPaymentOrderCreate(validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains?) {
        val params = ContactCreateParams(
            accountNo = validatePaymentOrderTransferDomains?.toAcctNo ?: "",
            confirm = "N",
            currency = validatePaymentOrderTransferDomains?.currency ?: "",
            customercode = state.value.customerCodeContact,
            payeeName = validatePaymentOrderTransferDomains?.receiveName ?: "",
            receiveBankName = Utils.g()
                .removeAccent(validatePaymentOrderTransferDomains?.receiveBankName ?: ""),
            revBankCode = "99998",
            serviceId = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER,
            username = userProf.getUserName() ?: "",
            tranType = Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER,
        )
        launchJob(showLoading = true) {
            val res = transferUseCase.contactCreate(params)
            handleResource(res) { data ->
                isSaveContactSuccess(true)
                showSuccessMessage(resourceProvider.getString(R.string.maker_transfer_saved_contact_success))
                transferCacheManager.clearContactList()
            }
        }
    }

    private fun getNextApproversListString() {
        if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validateNapasAccountTransferDomain?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        } else if (KEY_TRANSFER_TYPE_PAYMENT_ORDER == state.value.isTransferTypeKey) {
            _state.update { currentState ->
                currentState.copy(
                    listApproverComfirmString = if (state.value.validatePaymentOrderTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                        state.value.validatePaymentOrderTransferDomains?.nextApprovers?.joinToString(
                            "\n",
                        ) { it.username ?: "" } ?: ""
                    } else {
                        ""
                    },
                )
            }
        }
    }

    fun readAmountInWord(amount: String? = null, ccy: String? = null) =
        moneyHelper.convertAmountToWords(amount ?: "", ccy ?: MoneyCurrency.VND.name)

    private fun updateMoneyReader(amount: String, currency: String) {
        _state.update { currentState ->
            currentState.copy(
                amountReader = readAmountInWord(amount, currency),
            )
        }
    }
    private fun updateSearchApprovalLimitedText(value: String) {
        _state.update { currentState ->
            val item =
                currentState.checkLimitedApprovalBottomSheetUiState.copy(searchText = value)
            currentState.copy(
                checkLimitedApprovalBottomSheetUiState = item,
            )
        }
    }
    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_CREATE_TEMPLATE) {
            if ("2" == exception.code) {
                viewModelScope.launch {
                    _events.send(HandleErrorCreateTemplateState(exception.message ?: ""))
                }
                return
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: MakerTransferResultAction) {
        when (action) {
            is MakerTransferResultAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(NavigateBack)
                }
            }

            is MakerTransferResultAction.MakerNewTransfer -> {
                viewModelScope.launch {
                    if (KEY_TRANSFER_TYPE_ACCOUNT == state.value.isTransferTypeKey) {
                        state.value.validateNapasAccountTransferDomain?.bankDomain?.let {
                            _events.send(
                                MakerNewTransferAccount(it),
                            )
                        }
                    } else if (KEY_TRANSFER_TYPE_CARD == state.value.isTransferTypeKey) {
                        _events.send(MakerNewTransferCard)
                    } else if (KEY_TRANSFER_TYPE_PAYMENT_ORDER == state.value.isTransferTypeKey) {
                        _events.send(MakerNewPaymentOrderTransfer)
                    }
                }
            }

            is MakerTransferResultAction.OnGetNextApproversListString -> {
                getNextApproversListString()
            }

            is MakerTransferResultAction.SelectedTabIndex -> {
                updateSelectedTabIndex(action.selectedTabIndex)
            }

            is MakerTransferResultAction.OnShowSaveContactBottomSheet -> {
                canShowBottomSheetSaveContact(action.canShowBottomSheetSaveContact)
            }

            is MakerTransferResultAction.OnShowCheckLimitedApproval -> {
                updateCanShowBottomSheetCheckLimitedApproval(action.canShowBottomSheetCheckLimitedApproval)
            }

            is MakerTransferResultAction.OnClickSaveContactBottomSheet -> {
                if (action.customercode.isNotEmpty()) {
                    updateCustomerCodeContact(action.customercode)

                    when (state.value.isTransferTypeKey) {
                        KEY_TRANSFER_TYPE_ACCOUNT -> {
                            contactAccountCreate(state.value.validateNapasAccountTransferDomain)
                        }

                        KEY_TRANSFER_TYPE_CARD -> {
                            contactCardCreate(state.value.validateNapasCardTransferDomains)
                        }

                        KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                            contactPaymentOrderCreate(state.value.validatePaymentOrderTransferDomains)
                        }
                    }
                    canShowBottomSheetSaveContact(false)
                } else {
                    sendErrorMessage(resourceProvider.getString(R.string.maker_transfer_saved_contact_input))
                }
            }

            is MakerTransferResultAction.OnShowSaveTempBottomSheet -> {
                updateCanShowBottomSheetSaveTemp(action.canShowBottomSheetSaveTemp)
            }

            is MakerTransferResultAction.ShowToastMessage -> {
                showToast(resourceProvider.getString(action.toastMessageRes))
            }

            is MakerTransferResultAction.OnClickSaveTempBottomSheet -> {
                val createTemplate: () -> Unit = {
                    when (state.value.isTransferTypeKey) {
                        KEY_TRANSFER_TYPE_ACCOUNT -> {
                            createTemplateAccount(
                                confirm = action.confirm,
                                state.value.validateNapasAccountTransferDomain,
                            )
                        }

                        KEY_TRANSFER_TYPE_CARD -> {
                            createTemplateCard(
                                confirm = action.confirm,
                                state.value.validateNapasCardTransferDomains,
                            )
                        }

                        KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                            createTemplateOrderPayment(
                                confirm = action.confirm,
                                state.value.validatePaymentOrderTransferDomains,
                            )
                        }
                    }
                }

                when {
                    action.customercode.isNotEmpty() -> {
                        updateCustomerCodeTemp(action.customercode)
                        createTemplate()
                        updateCanShowBottomSheetSaveTemp(false)
                    }

                    CONFIRM_SAVE_TEMP_TRANSFER_YES == action.confirm -> {
                        createTemplate()
                    }

                    else -> {
                        sendErrorMessage(
                            resourceProvider.getString(
                                R.string.maker_transfer_saved_contact_input,
                            ),
                        )
                    }
                }
            }

            is MakerTransferResultAction.OnClickedItem -> {
                when (action.clickType) {
                    CLICK_TYPE_URI_FILE -> {
                        viewModelScope.launch {
                            _events.send(
                                OnClickedItem(
                                    state.value.validatePaymentOrderTransferDomains?.uriFile ?: "",
                                ),
                            )
                        }
                    }
                }
            }

            is MakerTransferResultAction.OnSearchLimitedApproval -> {
                updateSearchApprovalLimitedText(action.searchText)
                val searchText = action.searchText.trim()
                val filteredList = if (searchText.isBlank()) {
                    originaluserApprovalList
                } else {
                    originaluserApprovalList.filter { item ->
                        item.username?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateCheckLimitedApprovalBottomSheetList(filteredList.toMutableList())
            }
            is MakerTransferResultAction.GotoTransactionManager -> {
                viewModelScope.launch {
                    _events.send(GotoTransactionManager)
                }
            }
        }
    }

    sealed class MakerTransferResultEvent {
        data object NavigateBack : MakerTransferResultEvent()
        data class MakerNewTransferAccount(val bankDomain: DataBankDomain) :
            MakerTransferResultEvent()

        data object MakerNewPaymentOrderTransfer : MakerTransferResultEvent()

        data object MakerNewTransferCard : MakerTransferResultEvent()
        data class ShowErrorMess(val errorMess: String) : MakerTransferResultEvent()
        data class ShowToastMess(val toastMess: String) : MakerTransferResultEvent()
        data class HandleErrorCreateTemplateState(val errorMess: String) :
            MakerTransferResultEvent()
        data class HandleSuccessCreateSaveState(val successMess: String) :
            MakerTransferResultEvent()
        data class OnClickedItem(val uriFile: String) : MakerTransferResultEvent()
        data object GotoTransactionManager : MakerTransferResultEvent()
    }

    sealed class MakerTransferResultAction {
        data object OnBackPressed : MakerTransferResultAction()
        data object MakerNewTransfer : MakerTransferResultAction()
        data object GotoTransactionManager : MakerTransferResultAction()
        data object OnGetNextApproversListString : MakerTransferResultAction()
        data class SelectedTabIndex(val selectedTabIndex: Int) : MakerTransferResultAction()
        data class OnShowSaveContactBottomSheet(val canShowBottomSheetSaveContact: Boolean) :
            MakerTransferResultAction()

        data class OnShowCheckLimitedApproval(val canShowBottomSheetCheckLimitedApproval: Boolean) :
            MakerTransferResultAction()

        data class OnShowSaveTempBottomSheet(val canShowBottomSheetSaveTemp: Boolean) :
            MakerTransferResultAction()

        data class OnClickSaveContactBottomSheet(val customercode: String) :
            MakerTransferResultAction()

        data class OnClickSaveTempBottomSheet(val customercode: String, val confirm: String) :
            MakerTransferResultAction()

        data class ShowToastMessage(val toastMessageRes: Int) :
            MakerTransferResultAction()

        data class OnClickedItem(val clickType: String) : MakerTransferResultAction()
        data class OnSearchLimitedApproval(val searchText: String) :
            MakerTransferResultAction()
    }

    data class MakerTransferResultState(
        var validateNapasAccountTransferDomain: ValidateNapasAccountTransferDomain? = null,
        var validateNapasCardTransferDomains: ValidateNapasCardTransferDomains? = null,
        var validatePaymentOrderTransferDomains: ValidatePaymentOrderTransferDomains? = null,
        var listApproverComfirmString: String = "",
        var createTransferDomain: CreateTransferDomain? = null,
        var amountString: String = "",
        var amountReader: String = "",
        var isTransferTypeKey: String = "",
        var isTypeSplitTransfer: Boolean = false,
        var totalVatSplitTransfer: String = "",
        var selectedTabIndex: Int = 0,
        var canShowBottomSheetSaveContact: Boolean = false,
        var saveContactBottomSheetUiState: SaveContactBottomSheetUiState = SaveContactBottomSheetUiState(),
        var saveTemplateBottomSheetUiState: SaveTemplateBottomSheetUiState = SaveTemplateBottomSheetUiState(),
        var isSaveContactSuccess: Boolean = false,
        var isSaveTempSuccess: Boolean = false,
        var customerCodeContact: String = "",
        var debitFullName: String = "",
        var customerCodeTemp: String = "",
        var canShowBottomSheetSaveTemp: Boolean = false,
        val isLoadingListContact: Boolean = true,
        val isEnableSaveAccount: Boolean = false,
        val listContactDomains: MutableList<ContactDomains> = mutableListOf(),
        val canShowBottomSheetCheckLimitedApproval: Boolean = false,
        val checkLimitedApprovalBottomSheetUiState: CheckLimitedApprovalBottomSheetUiState = CheckLimitedApprovalBottomSheetUiState(),
    )

    inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        return try {
            val json = arguments.getString(key, "") ?: return null
            Utils.g().provideGson().fromJson(
                URLDecoder.decode(json, UTF8),
                T::class.java,
            )
        } catch (_: Exception) {
            null
        }
    }

    fun handleArguments(arguments: Bundle) {
        when (arguments.getString(KEY_TRANSFER_TYPE, "")) {
            KEY_TRANSFER_TYPE_ACCOUNT -> {
                updateIsTransferTypeKey(KEY_TRANSFER_TYPE_ACCOUNT)
                decodeArgumentJson<ValidateNapasAccountTransferDomain>(
                    arguments,
                    CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasAccountTransferDomain(it) }
            }

            KEY_TRANSFER_TYPE_CARD -> {
                updateIsTransferTypeKey(KEY_TRANSFER_TYPE_CARD)
                decodeArgumentJson<ValidateNapasCardTransferDomains>(
                    arguments,
                    CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidateNapasCardTransferDomains(it) }
            }

            KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                updateIsTransferTypeKey(value = KEY_TRANSFER_TYPE_PAYMENT_ORDER)
                decodeArgumentJson<ValidatePaymentOrderTransferDomains>(
                    arguments,
                    CONFIRM_TRANSFER_ITEM,
                )?.let { updateValidatePaymentOrderDomains(it) }
            }
        }
        decodeArgumentJson<CreateTransferDomain>(
            arguments,
            RESULT_TRANSFER_ITEM,
        )?.let { updateCreateTransferDomain(it) }
    }
}