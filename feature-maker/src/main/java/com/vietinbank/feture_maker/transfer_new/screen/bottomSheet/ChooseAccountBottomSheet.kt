package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.R
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.AccountDefaultDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feture_maker.maker_ui.adapter.AccountAdapter.Companion.TYPE_GROUP_CONTENT
import com.vietinbank.feture_maker.maker_ui.adapter.AccountAdapter.Companion.TYPE_GROUP_TITLE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TYPE_D
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TYPE_T
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ChooseAccountBottomSheetUiState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChooseAccountBottomSheet(
    isVisible: Boolean,
    title: String,
    onItemSelected: (AccountDefaultDomain) -> Unit,
    onDismiss: () -> Unit,
    onSearchAccountText: (String) -> Unit,
    chooseAccountBottomSheetUiState: ChooseAccountBottomSheetUiState?,
) {
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
            onDismissRequest = {
                onDismiss()
                coroutineScope.launch {
                    delay(50)
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
            },
            containerColor = FDS.Colors.backgroundBgContainer,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title
                    FoundationText(
                        text = title,
                        style = FDS.Typography.headingH4,
                        color = FDS.Colors.characterPrimary,
                        modifier = Modifier
                            .padding(
                                vertical = FDS.Sizer.Gap.gap24,
                            ),
                    )

                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                        modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                    )
                    FoundationFieldType(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.gray50),
                        value = chooseAccountBottomSheetUiState?.searchAccountText ?: "",
                        onValueChange = {
                            onSearchAccountText.invoke(it)
                        },
                        placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_account),
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                                contentDescription = "Search",
                                tint = Color.Unspecified,
                            )
                        },
                    )
                    chooseAccountBottomSheetUiState?.accountList?.let { listDataAccounts ->
                        LazyColumn(
                            modifier = Modifier
                                .padding(top = FDS.Sizer.Gap.gap12)
                                .padding(
                                    horizontal = FDS.Sizer.Gap.gap24,
                                ),
                        ) {
                            items(listDataAccounts) { accountItem ->
                                when (accountItem.group) {
                                    TYPE_GROUP_TITLE -> {
                                        val title = when (accountItem.accountType) {
                                            TYPE_D -> stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_type_account_D)
                                            TYPE_T -> stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_type_account_T)
                                            else ->
                                                accountItem.accountType
                                                    ?: stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_type_account_other)
                                        }
                                        GroupTitleItem(title = title)
                                    }

                                    TYPE_GROUP_CONTENT -> {
                                        AccountItem(
                                            accountItem = accountItem,
                                            onClick = { onItemSelected(accountItem) },
                                        )
                                    }

                                    else -> {
                                        AccountItem(
                                            accountItem = accountItem,
                                            onClick = { onItemSelected(accountItem) },
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun GroupTitleItem(title: String) {
    FoundationText(
        text = title,
        style = FDS.Typography.captionCaptionL,
        color = FDS.Colors.characterSecondary,
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = FDS.Sizer.Gap.gap16,
            ),
    )
}

@Composable
fun AccountItem(
    accountItem: AccountDefaultDomain,
    onClick: () -> Unit,
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .safeClickable { onClick.invoke() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_maker.R.drawable.ic_feature_maker_transfer_wallet),
                contentDescription = "item.text",
                tint = Color.Unspecified,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
            Column(
                modifier = Modifier
                    .weight(1f)
                    .safeClickable { onClick.invoke() },
            ) {
                if (accountItem.aliasName?.isEmpty() == false || accountItem.accountName?.isEmpty() == false) {
                    FoundationText(
                        text = accountItem.aliasName?.ifEmpty { accountItem.accountName.orEmpty() }
                            ?: accountItem.accountName.orEmpty(),
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterHighlighted,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                }

                FoundationText(
                    text = listOfNotNull(
                        accountItem.accountNo?.takeIf { it.isNotBlank() },
                        accountItem.aliasName?.takeIf { it.isNotBlank() },
                    ).joinToString(" - "),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = Utils.g().getDotMoneyHasCcy(
                        accountItem.availableBalance ?: "",
                        accountItem.currency ?: "",
                    ),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                )
            }
            FoundationSelector(
                boxType = SelectorType.Radio,
                isSelected = accountItem.isSelected,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                onClick = { onClick.invoke() },
            )
        }
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        HorizontalDivider(
            color = FDS.Colors.divider,
            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap16),
        )
    }
}
