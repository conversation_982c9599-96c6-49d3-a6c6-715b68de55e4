package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.NextApproverBottomSheetUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NextApproverBottomSheet(
    isVisible: Boolean,
    title: String,
    onItemSelected: (ApproverDomains) -> Unit,
    onClickAllItemSelected: () -> Unit,
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
    nextApproverBottomSheetUiState: NextApproverBottomSheetUiState,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = onCancel,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f).clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth().background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,

                                ),
                            )
                            .weight(1f),
                    ) {
                        FoundationText(
                            text = title,
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier
                                .padding(
                                    vertical = FDS.Sizer.Gap.gap24,
                                    horizontal = FDS.Sizer.Gap.gap24,
                                )
                                .align(Alignment.CenterHorizontally),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        )
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { onClickAllItemSelected.invoke() }
                                .padding(horizontal = FDS.Sizer.Gap.gap24, vertical = FDS.Sizer.Gap.gap24),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_pick_all),
                                style = FDS.Typography.bodyB1Emphasized,
                                color = FDS.Colors.characterPrimary,
                                modifier = Modifier.weight(1f),
                            )

                            FoundationSelector(
                                boxType = SelectorType.Checkbox,
                                isSelected = nextApproverBottomSheetUiState.onAllItemApproverSelected,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                isClickable = false,
                            )
                        }
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                        ) {
                            itemsIndexed(nextApproverBottomSheetUiState.nextApproversList) { index, item ->
                                NextApproverBottomItem(
                                    item = item,
                                    onClick = { onItemSelected(item) },
                                    modifier = Modifier,
                                )

                                if (index < nextApproverBottomSheetUiState.nextApproversList.size - 1) {
                                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                                }
                            }
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap16),
                    ) {
                        FoundationButton(
                            isLightButton = false,
                            text = stringResource(nextApproverBottomSheetUiState.cancelTextRes),
                            onClick = {
                                onCancel.invoke()
                            },
                            enabled = true,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                                .padding(
                                    horizontal = FDS.Sizer.Gap.gap4,

                                ),
                        )
                        FoundationButton(
                            isLightButton = true,
                            text = stringResource(nextApproverBottomSheetUiState.confirmTextRes),
                            onClick = { onConfirm.invoke() },
                            enabled = true,
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                                .padding(
                                    horizontal = FDS.Sizer.Gap.gap4,
                                ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun NextApproverBottomItem(
    item: ApproverDomains,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = FDS.Sizer.Gap.gap24, vertical = FDS.Sizer.Gap.gap12),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
        ) {
            FoundationText(
                text = item.fullname ?: "",
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterPrimary,
            )

            FoundationText(
                text = item.username ?: "",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterSecondary,
            )
        }
        FoundationSelector(
            boxType = SelectorType.Checkbox,
            isSelected = item.isSelected,
            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            isClickable = false,
        )
    }
}