package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CalendarLocale
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DatePickerFormatter
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.Surface
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.toDateString
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import java.util.Calendar
import java.util.TimeZone
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerPaymentOrderDialog(
    showDatePicker: Boolean,
    onDateSelected: (String) -> Unit,
    onDismiss: () -> Unit,
    isAvailableToday: Boolean = false,
) {
    val defaultFormatter = DatePickerDefaults.dateFormatter()
    val context = LocalContext.current

    val customFormatter = remember {
        object : DatePickerFormatter {
            override fun formatDate(
                dateMillis: Long?,
                locale: CalendarLocale,
                forContentDescription: Boolean,
            ): String? {
                return defaultFormatter.formatDate(dateMillis, locale, forContentDescription)
            }

            override fun formatMonthYear(
                monthMillis: Long?,
                locale: CalendarLocale,
            ): String? {
                return monthMillis?.let {
                    val calendar = Calendar.getInstance().apply { timeInMillis = monthMillis }
                    val month = calendar.get(Calendar.MONTH) + 1
                    val year = calendar.get(Calendar.YEAR)
                    context.getString(R.string.common_format_date, month, year)
                }
            }
        }
    }
    if (showDatePicker) {
        val tomorrowMillis = remember {
            Calendar.getInstance().apply {
                timeZone = TimeZone.getTimeZone("UTC")
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
                if (isAvailableToday) {
                    add(Calendar.DAY_OF_MONTH, 0)
                } else {
                    add(Calendar.DAY_OF_MONTH, 1)
                }
            }.timeInMillis
        }

        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = tomorrowMillis,
            selectableDates = object : SelectableDates {
                override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                    return utcTimeMillis >= tomorrowMillis
                }
            },
        )

        val confirmEnabled by remember {
            derivedStateOf { datePickerState.selectedDateMillis != null }
        }

        DatePickerDialog(
            colors = DatePickerDefaults.colors(
                containerColor = Color.Transparent,
                headlineContentColor = Color.Transparent,
                weekdayContentColor = Color.Transparent,
            ),
            onDismissRequest = onDismiss,
            confirmButton = {},
            dismissButton = {},
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = FoundationDesignSystem.Colors.backgroundBgContainer,
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        ),
                ) {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_date),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .align(Alignment.CenterHorizontally),
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Surface(
                        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        color = FDS.Colors.backgroundBgContainer,
                    ) {
                        DatePicker(
                            dateFormatter = customFormatter,
                            state = datePickerState,
                            title = null,
                            headline = null,
                            showModeToggle = false,
                            colors = DatePickerDefaults.colors(
                                containerColor = FDS.Colors.backgroundBgContainer,
                                titleContentColor = FDS.Colors.characterPrimary,
                                headlineContentColor = Color.Transparent, // Make it transparent as backup
                                weekdayContentColor = FDS.Colors.characterPrimary,
                                subheadContentColor = FDS.Colors.characterPrimary,
                                navigationContentColor = FDS.Colors.characterPrimary,
                                yearContentColor = FDS.Colors.characterPrimary,
                                disabledYearContentColor = FDS.Colors.gray600,
                                currentYearContentColor = FDS.Colors.characterPrimary,
                                selectedYearContentColor = Color.White,
                                disabledDayContentColor = FDS.Colors.gray600,
                                todayContentColor = FDS.Colors.characterPrimary,
                                dayContentColor = FDS.Colors.characterPrimary,
                                selectedDayContentColor = FDS.Colors.white,
                                selectedDayContainerColor = FDS.Colors.stateActive,
                                todayDateBorderColor = FDS.Colors.stateActive,
                            ),
                        )
                    }
                }
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    FoundationButton(
                        isLightButton = false,
                        text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_back),
                        onClick = { onDismiss.invoke() },
                        enabled = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap4,
                            ),
                    )
                    FoundationButton(
                        isLightButton = true,
                        text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                        onClick = {
                            datePickerState.selectedDateMillis?.let { millis ->
                                onDateSelected(millis.toDateString(pattern = dd_MM_yyyy_1))
                            }
                        },
                        enabled = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(horizontal = FDS.Sizer.Gap.gap4),
                    )
                }
            }
        }
    }
}