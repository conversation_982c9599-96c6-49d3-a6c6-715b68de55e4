package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.RadioBottomSheetUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class BottomSheetItem(
    val id: String,
    val icon: Int,
    val text: String,
    val content: String = "",
    val isSelected: Boolean = false,
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RadioBottomSheet(
    isVisible: Boolean,
    title: String,
    onItemSelected: (BottomSheetItem) -> Unit,
    onDismiss: () -> Unit,
    radioBottomSheetUiState: RadioBottomSheetUiState,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FoundationDesignSystem.Colors.backgroundBgContainer,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth().clip(RoundedCornerShape(AppSizer.Radius.radius32)),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Title
                FoundationText(
                    text = title,
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                )

                HorizontalDivider(
                    color = FDS.Colors.divider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                    modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
                )

                // Items list
                radioBottomSheetUiState.items.forEachIndexed { index, item ->
                    RadioButtonItem(
                        item = item,
                        onClick = { onItemSelected(item) },
                        modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap4),
                    )

                    if (index < radioBottomSheetUiState.items.size - 1) {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                    }
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            }
        }
    }
}

@Composable
private fun RadioButtonItem(
    item: BottomSheetItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = FDS.Sizer.Gap.gap24),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(id = item.icon),
            contentDescription = item.text,
            tint = Color.Unspecified,
            modifier = Modifier.size(FDS.Sizer.Icon.icon40),
        )

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

        Column(
            modifier = Modifier.weight(1f),
        ) {
            FoundationText(
                text = item.text,
                style = FDS.Typography.bodyB1,
                color = FDS.Colors.characterPrimary,
            )
            if (item.content.isNotEmpty()) {
                FoundationText(
                    text = item.content,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                )
            }
        }
        FoundationSelector(
            boxType = SelectorType.Radio,
            isSelected = item.isSelected,
            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            isClickable = false,
        )
    }
}