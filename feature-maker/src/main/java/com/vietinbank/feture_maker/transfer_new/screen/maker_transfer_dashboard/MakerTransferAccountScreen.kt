package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SaveContactBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_NEW
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_SAVED
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.ContactSavedScreen
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.RecentTransactionScreen
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferViewModel.MakerTransferAccountAction
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferViewModel.MakerTransferAccountState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferAccountScreen(
    state: MakerTransferAccountState,
    onAction: ((MakerTransferAccountAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(top = FDS.Sizer.Gap.gap16), // IME padding at root level for proper keyboard handling
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            // App Bar
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_title),
                onNavigationClick = { onAction.invoke(MakerTransferAccountAction.OnBackPressed) },
                navigationIcon = painterResource(com.vietinbank.core_ui.R.drawable.ic_core_ui_home),
                actions = listOf(
                    AppBarAction(
                        icon = R.drawable.ic_feature_maker_transfer_sccan_qr,
                        contentDescription = "ic_feature_maker_transfer_sccan_qr",
                        onClick = { },
                    ),
                ),
            )

            FoundationTabs(
                tabs = listOf(
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_new),
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_saved),
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_recent),
                ),
                selectedIndex = state.selectedIndex,
                onTabSelected = { onAction.invoke(MakerTransferAccountAction.OnSelectedIndex(it)) },
                type = TabType.Pill,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap16, vertical = FDS.Sizer.Gap.gap16),
                horizontalItemPadding = FDS.Sizer.Padding.padding0,
            )
            if (TAB_NEW == state.selectedIndex) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .safeClickable { onAction.invoke(MakerTransferAccountAction.ClickTransferCard) }
                            .padding(vertical = FDS.Sizer.Gap.gap24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.ic_feature_maker_transfer_card_40dp),
                            contentDescription = "ic_feature_maker_transfer_card",
                            tint = Color.Unspecified,
                        )

                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

                        Column(
                            modifier = Modifier
                                .weight(1f),
                            verticalArrangement = Arrangement.SpaceBetween,
                        ) {
                            FoundationText(
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_card),
                                color = FDS.Colors.characterHighlighted,
                                style = FDS.Typography.bodyB2Emphasized,
                            )
                        }
                    }

                    // Bank List Section
                    FoundationText(
                        modifier = Modifier.padding(
                            bottom = FDS.Sizer.Gap.gap8,
                        ),
                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_list_bank),
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.headingH4,
                    )
                    FoundationFieldType(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.gray50),
                        value = state.searchTextBank,
                        onValueChange = {
                            onAction.invoke(
                                MakerTransferAccountAction.OnSearchTextBankChange(
                                    it,
                                ),
                            )
                        },
                        placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_bank),
                        leadingIcon = {
                            Icon(
                                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                                contentDescription = "Search",
                                tint = Color.Unspecified,
                            )
                        },
                    )
                    if (state.listDataBanks.isNotEmpty()) {
                        LazyColumn(
                            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            items(state.listDataBanks) { bank ->
                                BankItem(
                                    bank = bank,
                                    imageLoader = imageLoader,
                                    onAction = {
                                        onAction.invoke(
                                            MakerTransferAccountAction.ClickBankItem(
                                                it,
                                            ),
                                        )
                                    },
                                )
                            }
                        }
                    } else {
                        // todo xử lý khi null list
                    }
                }
            } else if (TAB_SAVED == state.selectedIndex) {
                ContactSavedScreen(
                    imageLoader = imageLoader,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer),

                    onSearchContactText = {
                        onAction.invoke(MakerTransferAccountAction.OnSearchContactText(it))
                    },
                    onSelectedTabIndex = {
                        onAction.invoke(MakerTransferAccountAction.OnSelectedTabContactIndex(it))
                    },
                    onClickItemContact = {
                        onAction.invoke(MakerTransferAccountAction.ClickContactItem(it))
                    },
                    onClickItemTemp = {
                        onAction.invoke(MakerTransferAccountAction.ClickTempItem(it))
                    },
                    onClickEditItemContact = {
                        // todo hỏi api để update
//                        onAction.invoke(MakerTransferAccountAction.MapFieldContactToBank(it))
                    },
                    contactSavedBottomSheetUiState = state.contactSavedBottomSheetUiState,
                )
            } else {
                RecentTransactionScreen(
                    imageLoader = imageLoader,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer),
                    searchRecentText = state.searchRecentText,
                    onSearchRecentText = {
                        onAction.invoke(
                            MakerTransferAccountAction.OnSearchRecentText(
                                it,
                            ),
                        )
                    },
                    listLatest = state.transactionInfoList.toMutableList(),
                    onRecentItemClick = {
                        onAction.invoke(MakerTransferAccountAction.ClickRecentItem(it))
                    },
                    isLoadingListRecent = state.isLoadingListRecent,
                )
            }
        }
    }
    SaveContactBottomSheet(
        isVisible = state.canShowBottomSheetSaveContact == true,
        onCancel = {
            onAction.invoke(MakerTransferAccountAction.OnShowSaveContactBottomSheet(false))
        },
        onConfirm = { },
        imageLoader = imageLoader,
        saveContactBottomSheetUiState = state.saveContactBottomSheetUiState,
    )
}

@Composable
fun BankItem(
    bank: DataBankDomain,
    imageLoader: CoilImageLoader,
    onAction: (item: DataBankDomain) -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = FDS.Sizer.Gap.gap12)
            .safeClickable { onAction.invoke(bank) },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40)
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            imageLoader.LoadUrl(
                url = bank.icon ?: "",
                isCache = true,
                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

        // Bank Info
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = bank.shortName ?: "",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterPrimary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                when (bank.type) {
                    Tags.TransferType.TYPE_IN -> {
                        InOutTypeLabel(label = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_in))
                    }

                    Tags.TransferType.TYPE_OUT -> {
                        InOutTypeLabel(label = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal))
                    }

                    Tags.TransferType.TYPE_NAPAS -> {
                        InOutTypeLabel(label = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_np247))
                    }

                    Tags.TransferType.TYPE_NORMAL -> {
                        InOutTypeLabel(label = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_normal))
                    }

                    else -> ""
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
            FoundationText(
                text = bank.bankName ?: "",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )
        }
    }
}

@Composable
fun InOutTypeLabel(label: String) {
    Box(
        modifier = Modifier
            .background(
                color = FDS.Colors.blue50,
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            )
            .border(
                width = FDS.Sizer.Stroke.stroke1,
                color = FDS.Colors.blue200,
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            )
            .padding(horizontal = FDS.Sizer.Gap.gap8, vertical = FDS.Sizer.Gap.gap4),
    ) {
        FoundationText(
            text = label,
            style = FDS.Typography.captionCaptionMBold,
            color = FDS.Colors.characterHighlighted,
        )
    }
}