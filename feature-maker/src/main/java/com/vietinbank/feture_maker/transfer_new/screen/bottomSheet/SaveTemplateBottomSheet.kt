package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.R
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.SaveTemplateBottomSheetUiState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SaveTemplateBottomSheet(
    isVisible: Boolean,
    onCancel: () -> Unit,
    onConfirm: (String) -> Unit,
    imageLoader: CoilImageLoader? = null,
    saveTemplateBottomSheetUiState: SaveTemplateBottomSheetUiState,
) {
    var customerCode by remember { mutableStateOf("") }
    val scrollState = rememberScrollState()
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = {
                onCancel()
                coroutineScope.launch {
                    delay(50)
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
            },
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(scrollState),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,

                                ),
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Title
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_temp),
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap16)
                                .border(
                                    width = FDS.Sizer.Stroke.stroke1,
                                    color = FDS.Colors.strokeDivider,
                                    shape = RoundedCornerShape(FDS.Sizer.Gap.gap16),
                                ),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            when (saveTemplateBottomSheetUiState.isTransferTypeKey) {
                                KEY_TRANSFER_TYPE_ACCOUNT -> {
                                    FoundationTransfer(
                                        isShowImageWaterMark = false,
                                        verticalPadding = FDS.Sizer.Gap.gap16,
                                        horizontalPadding = FDS.Sizer.Gap.gap16,
                                        imageLoader = imageLoader,

                                        accountFrom = TransferResult(
                                            bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                            accountName = saveTemplateBottomSheetUiState.createTransferDomain?.fullNameCreate
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.fromAcctNo
                                                ?: "",
                                            fromAccBalance = "",
                                        ),
                                        accountTo = TransferResult(
                                            bankIconURL = saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.toBankIconURL
                                                ?: "",
                                            bankName = saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.bankDomain?.shortName
                                                ?: "",
                                            accountName = saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.toAcctName
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.toAcctNo
                                                ?: "",
                                        ),
                                        lstContent = buildList<ItemResult> {
                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.amount
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                                    saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.remark
                                                        ?: "",
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.feeVat
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                                    TransferConstants.Transfer.getFeeMethodName(
                                                        saveTemplateBottomSheetUiState.validateNapasAccountTransferDomain?.feePayMethod
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        },
                                    )
                                }

                                KEY_TRANSFER_TYPE_CARD -> {
                                    FoundationTransfer(
                                        isShowImageWaterMark = false,
                                        imageLoader = imageLoader,
                                        accountFrom = TransferResult(
                                            bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                            accountName = saveTemplateBottomSheetUiState.createTransferDomain?.fullNameCreate
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.fromAcctNo
                                                ?: "",
                                            fromAccBalance = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.fromAccBalance
                                                ?: "",
                                        ),
                                        accountTo = TransferResult(
                                            bankIconURL = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.toBankIconURL
                                                ?: "",
                                            bankName = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.toBankName
                                                ?: "",
                                            accountName = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.toCardName
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.toCardNo
                                                ?: "",
                                        ),
                                        lstContent = buildList<ItemResult> {
                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.amount
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                                    saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.remark
                                                        ?: "",
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.feeVat
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                                    TransferConstants.Transfer.getFeeMethodName(
                                                        saveTemplateBottomSheetUiState.validateNapasCardTransferDomains?.feePayMethod
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        },
                                    )
                                }

                                KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                                    FoundationTransfer(
                                        isShowImageWaterMark = false,
                                        imageLoader = imageLoader,
                                        accountFrom = TransferResult(
                                            bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                            accountName = saveTemplateBottomSheetUiState.createTransferDomain?.fullNameCreate
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.fromAcctNo
                                                ?: "",
                                            fromAccBalance = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.fromAccBalance
                                                ?: "",
                                        ),
                                        accountTo = TransferResult(
                                            bankIconURL = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.toBankIconURL
                                                ?: "",
                                            bankName = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.receiveBankName
                                                ?: "",
                                            accountName = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.receiveName
                                                ?: "",
                                            accountNo = saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.toAcctNo
                                                ?: "",
                                        ),
                                        lstContent = buildList<ItemResult> {
                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.amount
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                                    saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.remark
                                                        ?: "",
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                                    Utils.g().getDotMoneyHasCcy(
                                                        saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.feeVat
                                                            ?: "",
                                                        saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.currency
                                                            ?: "",
                                                    ),
                                                ),
                                            )

                                            add(
                                                ItemResult(
                                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                                    TransferConstants.Transfer.getFeeMethodName(
                                                        saveTemplateBottomSheetUiState.validatePaymentOrderTransferDomains?.feePayMethod
                                                            ?: "",
                                                    ),
                                                ),
                                            )
                                        },
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        FoundationEditText(
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                            value = customerCode,
                            onValueChange = { newValue ->
                                customerCode = newValue
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            maxLength = 146,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    }
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16),
            ) {
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_back),
                    onClick = {
                        onCancel.invoke()
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,

                        ),
                )
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                    onClick = { onConfirm.invoke(customerCode) },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
            }
        }
    }
}