package com.vietinbank.feture_maker.transfer_new.screen.confirm

import android.text.TextUtils
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.confirm.MakerTransferConfirmViewModel.MakerTransferConfirmAction
import com.vietinbank.feture_maker.transfer_new.screen.confirm.MakerTransferConfirmViewModel.MakerTransferConfirmState
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CLICK_TYPE_URI_FILE
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferConfirmScreen(
    state: MakerTransferConfirmState,
    onAction: ((MakerTransferConfirmAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(vertical = FDS.Sizer.Gap.gap16)
                // QUAN TRỌNG: Thêm bottom padding để tránh bị button che
                .padding(bottom = FDS.Sizer.Gap.gap80), // Chiều cao của button + padding
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = "",
                onNavigationClick = { onAction.invoke(MakerTransferConfirmAction.OnBackPressed) },
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            FoundationText(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_request),
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.blue300,
            )
            FoundationText(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Gap.gap8,
                    start = FDS.Sizer.Gap.gap24,
                    end = FDS.Sizer.Gap.gap24,
                ),
                text = state.amountString,
                style = FDS.Typography.headingH2,
                color = FDS.Colors.white,
            )
            FoundationText(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Gap.gap8,
                    start = FDS.Sizer.Gap.gap24,
                    end = FDS.Sizer.Gap.gap24,
                ),
                text = state.amountReader,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterSecondary,
            )
            when (state.isTransferTypeKey) {
                KEY_TRANSFER_TYPE_ACCOUNT -> {
                    FoundationTransfer(
                        isShowImageWaterMark = false,
                        imageLoader = imageLoader,
                        modifier = Modifier.padding(
                            top = FDS.Sizer.Gap.gap16,
                            start = FDS.Sizer.Gap.gap8,
                        ),
                        accountFrom = TransferResult(
                            bankIconResource = R.drawable.ic_vietinbank,
                            accountName = state.validateNapasAccountTransferDomain?.fromAccName ?: "",
                            accountNo = state.validateNapasAccountTransferDomain?.fromAcctNo ?: "",
                            fromAccBalance = state.validateNapasAccountTransferDomain?.fromAccBalance
                                ?: "",
                        ),
                        accountTo = TransferResult(
                            bankIconURL = state.validateNapasAccountTransferDomain?.toBankIconURL
                                ?: "",
                            bankName = state.validateNapasAccountTransferDomain?.toBankName ?: "",
                            accountName = state.validateNapasAccountTransferDomain?.toAcctName
                                ?: "",
                            accountNo = state.validateNapasAccountTransferDomain?.toAcctNo ?: "",
                        ),
                        lstContent = buildList<ItemResult> {
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    state.validateNapasAccountTransferDomain?.remark ?: "",
                                ),
                            )

                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                    Utils.g().getDotMoneyHasCcy(
                                        state.validateNapasAccountTransferDomain?.totalFee ?: "",
                                        state.validateNapasAccountTransferDomain?.currency ?: "",
                                    ),
                                ),
                            )

                            if (state.isTypeSplitTransfer) {
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee_split),
                                        state.totalVatSplitTransfer,
                                    ),
                                )
                            }

                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    TransferConstants.Transfer.getFeeMethodName(
                                        state.validateNapasAccountTransferDomain?.feePayMethod
                                            ?: "",
                                    ),
                                ),
                            )

                            if (state.isTypeSplitTransfer) {
                                addAll(
                                    listOf(
                                        ItemResult(
                                            stringResource(
                                                com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                                state.validateNapasAccountTransferDomain?.subTransactionCount
                                                    ?: "",
                                            ),
                                            Utils.g().getDotMoneyHasCcy(
                                                state.validateNapasAccountTransferDomain?.subSplitTransAmount
                                                    ?: "",
                                                state.validateNapasAccountTransferDomain?.currency
                                                    ?: "",
                                            ),
                                        ),
                                        ItemResult(
                                            stringResource(
                                                com.vietinbank.core_ui.R.string.maker_transfer_split_number_content_transaction,
                                                state.validateNapasAccountTransferDomain?.subTransactionsRemainderCount
                                                    ?: "",
                                            ),
                                            Utils.g().getDotMoneyHasCcy(
                                                state.validateNapasAccountTransferDomain?.subTransactionsRemainderAmount
                                                    ?: "",
                                                state.validateNapasAccountTransferDomain?.currency
                                                    ?: "",
                                            ),
                                        ),
                                    ),
                                )
                            }

                            if (TextUtils.isEmpty(state.validateNapasAccountTransferDomain?.processDate)) {
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                    ),
                                )
                            } else {
                                addAll(
                                    listOf(
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                        ),
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                            state.validateNapasAccountTransferDomain?.processDate
                                                ?: "",
                                        ),
                                    ),
                                )
                            }
                        },
                        contentBottom = {
                            if (state.validateNapasAccountTransferDomain?.nextApprovers?.isNotEmpty() == true) {
                                ApproverRow(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver),
                                    state.validateNapasAccountTransferDomain?.nextApprovers,
                                )
                            }
                        },
                    )
                }

                KEY_TRANSFER_TYPE_CARD -> {
                    FoundationTransfer(
                        isShowImageWaterMark = false,
                        imageLoader = imageLoader,
                        modifier = Modifier.padding(
                            top = FDS.Sizer.Gap.gap16,
                            start = FDS.Sizer.Gap.gap8,
                        ),
                        accountFrom = TransferResult(
                            bankIconResource = R.drawable.ic_vietinbank,
                            accountName = state.validateNapasCardTransferDomains?.fromAccName.orEmpty(),
                            accountNo = state.validateNapasCardTransferDomains?.fromAcctNo ?: "",
                            fromAccBalance = state.validateNapasCardTransferDomains?.fromAccBalance
                                ?: "",
                        ),
                        accountTo = TransferResult(
                            bankIconURL = state.validateNapasCardTransferDomains?.toBankIconURL
                                ?: "",
                            bankName = state.validateNapasCardTransferDomains?.toBankName ?: "",
                            accountName = state.validateNapasCardTransferDomains?.toCardName ?: "",
                            accountNo = state.validateNapasCardTransferDomains?.toCardNo ?: "",
                        ),
                        lstContent = buildList<ItemResult> {
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    state.validateNapasCardTransferDomains?.remark ?: "",
                                ),
                            )

                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                    Utils.g().getDotMoneyHasCcy(
                                        state.validateNapasCardTransferDomains?.totalFee ?: "",
                                        state.validateNapasCardTransferDomains?.currency ?: "",
                                    ),
                                ),
                            )

                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    TransferConstants.Transfer.getFeeMethodName(
                                        state.validateNapasCardTransferDomains?.feePayMethod ?: "",
                                    ),
                                ),
                            )

                            if (TextUtils.isEmpty(state.validateNapasCardTransferDomains?.processDate)) {
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                    ),
                                )
                            } else {
                                addAll(
                                    listOf(
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                        ),
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                            state.validateNapasCardTransferDomains?.processDate
                                                ?: "",
                                        ),
                                    ),
                                )
                            }
                        },
                        contentBottom = {
                            if (state.validateNapasCardTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                                ApproverRow(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver),
                                    state.validateNapasCardTransferDomains?.nextApprovers,
                                )
                            }
                        },
                    )
                }
                KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                    FoundationTransfer(
                        isShowImageWaterMark = false,
                        imageLoader = imageLoader,
                        modifier = Modifier.padding(
                            top = FDS.Sizer.Gap.gap16,
                            start = FDS.Sizer.Gap.gap8,
                        ),
                        accountFrom = TransferResult(
                            bankIconResource = R.drawable.ic_vietinbank,
                            accountName = state.validatePaymentOrderTransferDomains?.fromAccountName.orEmpty(),
                            accountNo = state.validatePaymentOrderTransferDomains?.fromAcctNo ?: "",
                            fromAccBalance = state.validatePaymentOrderTransferDomains?.fromAccBalance
                                ?: "",
                        ),
                        accountTo = TransferResult(
                            bankIconURL = state.validatePaymentOrderTransferDomains?.toBankIconURL
                                ?: "",
                            bankName = state.validatePaymentOrderTransferDomains?.receiveBankName?.replace(" ", "").orEmpty(),
                            accountName = state.validatePaymentOrderTransferDomains?.receiveName?.replace(" ", "").orEmpty(),
                            accountNo = state.validatePaymentOrderTransferDomains?.toAcctNo?.replace(" ", "").orEmpty(),
                        ),
                        lstContent = buildList<ItemResult> {
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account_type_D),
                                    state.validatePaymentOrderTransferDomains?.fromAccountFeeNo ?: "",
                                    valueExtra = state.validatePaymentOrderTransferDomains?.fromAccountName
                                        ?: "",
                                ),
                            )
                            val brandNameFull =
                                (
                                    state.validatePaymentOrderTransferDomains?.branch?.branchId
                                        ?: ""
                                    ) + " - " + (
                                    state.validatePaymentOrderTransferDomains?.branch?.branchName
                                        ?: ""
                                    )
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_processing_branch),
                                    brandNameFull,
                                ),
                            )
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    state.validatePaymentOrderTransferDomains?.remark ?: "",
                                ),
                            )
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                    stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_fee_content_confirm),
                                ),
                            )
                            add(
                                ItemResult(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    TransferConstants.Transfer.getFeeMethodName(
                                        state.validatePaymentOrderTransferDomains?.feePayMethod ?: "",
                                    ),
                                ),
                            )
                            if (TextUtils.isEmpty(state.validatePaymentOrderTransferDomains?.processDate)) {
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_now),
                                    ),
                                )
                            } else {
                                addAll(
                                    listOf(
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                        ),
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                            state.validatePaymentOrderTransferDomains?.processDate
                                                ?: "",
                                        ),
                                    ),
                                )
                            }
                            if (state.validatePaymentOrderTransferDomains?.uriFileName?.isNotEmpty() == true) {
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                                        state.validatePaymentOrderTransferDomains?.uriFileName ?: "",
                                        isTitleUnderline = true,
                                        valueColor = FDS.Colors.characterHighlighted,
                                        clickType = CLICK_TYPE_URI_FILE,
                                    ),
                                )
                            }
                        },
                        contentBottom = {
                            if (state.validatePaymentOrderTransferDomains?.nextApprovers?.isNotEmpty() == true) {
                                ApproverRow(
                                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver),
                                    state.validatePaymentOrderTransferDomains?.nextApprovers,
                                )
                            }
                        },
                        onClick = {
                            onAction.invoke(MakerTransferConfirmAction.OnClickedItem(it))
                        },
                    )
                }
            }
        }
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            FDS.Colors.dialogBackground.copy(alpha = 0f),
                            FDS.Colors.dialogBackground.copy(alpha = 0.5f),
                            FDS.Colors.dialogBackground.copy(alpha = 1f),
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY,
                    ),
                )
                .padding(
                    horizontal = FDS.Sizer.Gap.gap24,
                    vertical = FDS.Sizer.Gap.gap8,
                ),
        ) {
            FoundationButton(
                isLightButton = true,
                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_confirm_approver_btn),
                onClick = {
                    onAction.invoke(MakerTransferConfirmAction.OnConfirmPressed)
                },
                enabled = true,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

@Composable
fun ApproverRow(
    label: String,
    nextApprovers: List<ApproverDomains>?,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = FDS.Sizer.Gap.gap24,
                end = FDS.Sizer.Gap.gap24,
                top = FDS.Sizer.Gap.gap16,
                bottom = FDS.Sizer.Gap.gap24,
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        FoundationText(
            modifier = Modifier.weight(1f),
            text = label,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.textSecondary,
        )

        Column(
            modifier = Modifier.weight(1f),
        ) {
            nextApprovers?.forEach { approverItem ->
                Column {
                    FoundationText(
                        text = approverItem.username ?: "",
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterPrimary,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.End,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                    FoundationText(
                        text = approverItem.fullname ?: "",
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterSecondary,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.End,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                }
            }
        }
    }
}
