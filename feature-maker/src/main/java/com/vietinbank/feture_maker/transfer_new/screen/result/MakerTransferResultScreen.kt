package com.vietinbank.feture_maker.transfer_new.screen.result

import android.text.TextUtils
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_1
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.UserApprovalItem
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem.Colors.colorResource
import com.vietinbank.core_ui.utils.ScreenshotUtil
import com.vietinbank.core_ui.utils.ScreenshotUtil.trackBounds
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.CheckLimitedApprovalBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SaveContactBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SaveTemplateBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_PAYMENT_ORDER
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CLICK_TYPE_URI_FILE
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_NO
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RESULT_REQUEST
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultAction
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultAction.ShowToastMessage
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultEvent.GotoTransactionManager
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferResultScreen(
    state: MakerTransferResultState,
    onAction: ((MakerTransferResultAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    val lineColor = colorResource(id = R.color.foundation_state_warning_lighter)
    val lineSuccessColor = colorResource(id = R.color.foundation_state_success)
    var isCapturingScreenshot by remember { mutableStateOf(false) }

    // share clone from SuccessScreen
    val coroutineScope = rememberCoroutineScope()
    val rootView = LocalView.current
    val context = LocalContext.current
    // State to track the bounds of the content area for screenshot
    var contentBounds by remember { mutableStateOf<Rect?>(null) }

    val shareAction: () -> Unit = {
        coroutineScope.launch {
            isCapturingScreenshot = true
            delay(100)
            contentBounds?.let { bounds ->
                val bitmap = ScreenshotUtil.captureScreenshot(rootView, bounds, context)
                bitmap?.let { capturedBitmap ->
                    ScreenshotUtil.shareBitmap(context, capturedBitmap)
                }
                    ?: onAction.invoke(ShowToastMessage(R.string.maker_transfer_account_result_error_share_picture))
            }
            isCapturingScreenshot = false
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(top = FDS.Sizer.Gap.gap16),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .imePadding()
                .verticalScroll(scrollState)
                .padding(bottom = FDS.Sizer.Gap.gap140)
                .trackBounds { coordinates ->
                    contentBounds = coordinates.boundsInRoot()
                },
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = "",
                navigationIcon = painterResource(R.drawable.ic_core_ui_home),
                onNavigationClick = { onAction.invoke(MakerTransferResultAction.OnBackPressed) },
                actions = listOf(
                    AppBarAction(
                        icon = R.drawable.ic_common_share_24,
                        contentDescription = "share",
                        onClick = { shareAction() },
                    ),
                ),
            )

            FoundationText(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Gap.gap8,
                    start = FDS.Sizer.Gap.gap24,
                    end = FDS.Sizer.Gap.gap24,
                ),
                text = state.amountString,
                style = FDS.Typography.headingH2,
                color = FDS.Colors.white,
            )
            FoundationText(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Gap.gap8,
                    start = FDS.Sizer.Gap.gap24,
                    end = FDS.Sizer.Gap.gap24,
                ),
                text = state.amountReader,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterSecondary,
            )
            FoundationTabs(
                tabs = listOf(
                    stringResource(R.string.maker_transfer_account_result_request),
                    stringResource(R.string.maker_transfer_account_result_trans_his),
                ),
                selectedIndex = state.selectedTabIndex,
                onTabSelected = { onAction.invoke(MakerTransferResultAction.SelectedTabIndex(it)) },
                type = TabType.Pill,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24, vertical = FDS.Sizer.Gap.gap24),
            )
            if (TAB_RESULT_REQUEST == state.selectedTabIndex) {
                when (state.isTransferTypeKey) {
                    KEY_TRANSFER_TYPE_ACCOUNT -> {
                        FoundationTransfer(
                            imageLoader = imageLoader,
                            contentTop = {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationStatus(
                                        modifier = Modifier,
                                        statusCode = Status.MakerTransferSuccess,
                                    )
                                }
                            },
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                            ),
                            accountFrom = TransferResult(
                                bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                accountName = state.createTransferDomain?.fullNameCreate.orEmpty(),
                                accountNo = state.validateNapasAccountTransferDomain?.fromAcctNo
                                    ?: "",
                                fromAccBalance = "",
                            ),
                            accountTo = TransferResult(
                                bankIconURL = state.validateNapasAccountTransferDomain?.toBankIconURL
                                    ?: "",
                                bankName = state.validateNapasAccountTransferDomain?.bankDomain?.shortName
                                    ?: "",
                                accountName = state.validateNapasAccountTransferDomain?.toAcctName
                                    ?: "",
                                accountNo = state.validateNapasAccountTransferDomain?.toAcctNo
                                    ?: "",
                            ),
                            lstContent = buildList<ItemResult> {
                                add(
                                    ItemResult(
                                        stringResource(R.string.common_transaction_no),
                                        state.createTransferDomain?.mtId ?: "",
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_content),
                                        state.validateNapasAccountTransferDomain?.remark ?: "",
                                    ),
                                )

                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_fee),
                                        Utils.g().getDotMoneyHasCcy(
                                            state.validateNapasAccountTransferDomain?.totalFee
                                                ?: "",
                                            state.validateNapasAccountTransferDomain?.currency
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (state.isTypeSplitTransfer) {
                                    add(
                                        ItemResult(
                                            stringResource(R.string.maker_transfer_dashboard_fee_split),
                                            state.totalVatSplitTransfer,
                                        ),
                                    )
                                }

                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_type_fee),
                                        TransferConstants.Transfer.getFeeMethodName(
                                            state.validateNapasAccountTransferDomain?.feePayMethod
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (state.isTypeSplitTransfer) {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(
                                                    R.string.maker_transfer_split_number_content_transaction,
                                                    state.validateNapasAccountTransferDomain?.subTransactionCount
                                                        ?: "",
                                                ),
                                                Utils.g().getDotMoneyHasCcy(
                                                    state.validateNapasAccountTransferDomain?.subSplitTransAmount
                                                        ?: "",
                                                    state.validateNapasAccountTransferDomain?.currency
                                                        ?: "",
                                                ),
                                            ),
                                            ItemResult(
                                                stringResource(
                                                    R.string.maker_transfer_split_number_content_transaction,
                                                    state.validateNapasAccountTransferDomain?.subTransactionsRemainderCount
                                                        ?: "",
                                                ),
                                                Utils.g().getDotMoneyHasCcy(
                                                    state.validateNapasAccountTransferDomain?.subTransactionsRemainderAmount
                                                        ?: "",
                                                    state.validateNapasAccountTransferDomain?.currency
                                                        ?: "",
                                                ),
                                            ),
                                        ),
                                    )
                                }

                                if (TextUtils.isEmpty(state.validateNapasAccountTransferDomain?.processDate)) {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_now),
                                            ),
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                state.createTransferDomain?.createdDate.getDateToFormat(
                                                    formatFrom = dd_MM_yyyy_HH_mm_ss_1,
                                                    formatTo = dd_MM_yyyy_HH_mm_ss,
                                                ) ?: "",
                                            ),
                                        ),
                                    )
                                } else {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_schedule),
                                            ),
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                state.validateNapasAccountTransferDomain?.processDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                }
                            },

                        )
                    }

                    KEY_TRANSFER_TYPE_CARD -> {
                        FoundationTransfer(
                            imageLoader = imageLoader,
                            contentTop = {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationStatus(
                                        modifier = Modifier,
                                        statusCode = Status.MakerTransferSuccess,
                                    )
                                }
                            },
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                            ),
                            accountFrom = TransferResult(
                                bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                accountName = state.createTransferDomain?.fullNameCreate.orEmpty(),
                                accountNo = state.validateNapasCardTransferDomains?.fromAcctNo
                                    ?: "",
                                fromAccBalance = state.validateNapasCardTransferDomains?.fromAccBalance
                                    ?: "",
                            ),
                            accountTo = TransferResult(
                                bankIconURL = state.validateNapasCardTransferDomains?.toBankIconURL
                                    ?: "",
                                bankName = state.validateNapasCardTransferDomains?.toBankName ?: "",
                                accountName = state.validateNapasCardTransferDomains?.toCardName
                                    ?: "",
                                accountNo = state.validateNapasCardTransferDomains?.toCardNo ?: "",
                            ),
                            lstContent = buildList<ItemResult> {
                                add(
                                    ItemResult(
                                        stringResource(R.string.common_transaction_no),
                                        state.createTransferDomain?.mtId ?: "",
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_content),
                                        state.validateNapasCardTransferDomains?.remark ?: "",
                                    ),
                                )

                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_fee),
                                        Utils.g().getDotMoneyHasCcy(
                                            state.validateNapasCardTransferDomains?.totalFee ?: "",
                                            state.validateNapasCardTransferDomains?.currency ?: "",
                                        ),
                                    ),
                                )

                                add(
                                    ItemResult(
                                        stringResource(R.string.maker_transfer_dashboard_type_fee),
                                        TransferConstants.Transfer.getFeeMethodName(
                                            state.validateNapasCardTransferDomains?.feePayMethod
                                                ?: "",
                                        ),
                                    ),
                                )

                                if (TextUtils.isEmpty(state.validateNapasCardTransferDomains?.processDate)) {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_now),
                                            ),
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                state.createTransferDomain?.createdDate.getDateToFormat(
                                                    formatFrom = dd_MM_yyyy_HH_mm_ss_1,
                                                    formatTo = dd_MM_yyyy_HH_mm_ss,
                                                ) ?: "",
                                            ),
                                        ),
                                    )
                                } else {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_schedule),
                                            ),
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                state.validateNapasCardTransferDomains?.processDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                }
                            },
                        )
                    }

                    KEY_TRANSFER_TYPE_PAYMENT_ORDER -> {
                        FoundationTransfer(
                            imageLoader = imageLoader,
                            contentTop = {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationStatus(
                                        modifier = Modifier,
                                        statusCode = Status.MakerTransferSuccess,
                                    )
                                }
                            },
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                            ),
                            accountFrom = TransferResult(
                                bankIconResource = com.vietinbank.feature_maker.R.drawable.ic_vietinbank,
                                accountName = state.validatePaymentOrderTransferDomains?.fromAccountName.orEmpty(),
                                accountNo = state.validatePaymentOrderTransferDomains?.fromAcctNo
                                    ?: "",
                                fromAccBalance = state.validatePaymentOrderTransferDomains?.fromAccBalance
                                    ?: "",
                            ),
                            accountTo = TransferResult(
                                bankIconURL = state.validatePaymentOrderTransferDomains?.toBankIconURL
                                    ?: "",
                                bankName = state.validatePaymentOrderTransferDomains?.receiveBankName?.replace(" ", "").orEmpty(),
                                accountName = state.validatePaymentOrderTransferDomains?.receiveName?.replace(" ", "").orEmpty(),
                                accountNo = state.validatePaymentOrderTransferDomains?.toAcctNo?.replace(" ", "").orEmpty(),
                            ),
                            lstContent = buildList<ItemResult> {
                                add(
                                    ItemResult(
                                        stringResource(R.string.common_transaction_no),
                                        state.createTransferDomain?.mtId ?: "",
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account_type_D),
                                        state.validatePaymentOrderTransferDomains?.fromAcctNo ?: "",
                                        valueExtra = state.validatePaymentOrderTransferDomains?.fromAccountName
                                            ?: "",
                                    ),
                                )
                                val brandNameFull =
                                    (
                                        state.validatePaymentOrderTransferDomains?.branch?.branchId
                                            ?: ""
                                        ) + " - " + (
                                        state.validatePaymentOrderTransferDomains?.branch?.branchName
                                            ?: ""
                                        )
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_processing_branch),
                                        brandNameFull,
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                        state.validatePaymentOrderTransferDomains?.remark ?: "",
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_fee),
                                        stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_fee_content_confirm),
                                    ),
                                )
                                add(
                                    ItemResult(
                                        stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                        TransferConstants.Transfer.getFeeMethodName(
                                            state.validatePaymentOrderTransferDomains?.feePayMethod
                                                ?: "",
                                        ),
                                    ),
                                )
                                if (TextUtils.isEmpty(state.validatePaymentOrderTransferDomains?.processDate)) {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(R.string.maker_transfer_dashboard_now),
                                            ),
                                            ItemResult(
                                                stringResource(R.string.maker_transfer_dashboard_transfer_date_time_transfer_maker),
                                                state.createTransferDomain?.createdDate.getDateToFormat(
                                                    formatFrom = dd_MM_yyyy_HH_mm_ss_1,
                                                    formatTo = dd_MM_yyyy_HH_mm_ss,
                                                ) ?: "",
                                            ),
                                        ),
                                    )
                                } else {
                                    addAll(
                                        listOf(
                                            ItemResult(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_schedule),
                                            ),
                                            ItemResult(
                                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                                                state.validatePaymentOrderTransferDomains?.processDate
                                                    ?: "",
                                            ),
                                        ),
                                    )
                                }
                                if (state.validatePaymentOrderTransferDomains?.uriFileName?.isNotEmpty() == true) {
                                    add(
                                        ItemResult(
                                            stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                                            state.validatePaymentOrderTransferDomains?.uriFileName
                                                ?: "",
                                            isTitleUnderline = true,
                                            valueColor = FDS.Colors.characterHighlighted,
                                            clickType = CLICK_TYPE_URI_FILE,
                                        ),
                                    )
                                }
                            },
                            onClick = {
                                onAction.invoke(MakerTransferResultAction.OnClickedItem(it))
                            },
                        )
                    }
                }
                if (!isCapturingScreenshot) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                                vertical = FDS.Sizer.Gap.gap4,
                            )
                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                            .background(FDS.Colors.backgroundBgContainer)
                            .height(FDS.Sizer.Gap.gap64),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        if (!state.isSaveTempSuccess) {
                            Row(
                                modifier = Modifier
                                    .weight(1f)
                                    .safeClickable {
                                        onAction.invoke(
                                            MakerTransferResultAction.OnShowSaveTempBottomSheet(
                                                true,
                                            ),
                                        )
                                    }
                                    .fillMaxSize(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_transfer_copy),
                                    contentDescription = "ic_feature_transfer_copy",
                                    tint = Color.Unspecified,
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_template),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.interactionSmallButton,
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(start = FDS.Sizer.Gap.gap24)
                                    .fillMaxSize()
                                    .safeClickable {},
                                verticalArrangement = Arrangement.Center,
                            ) {
                                FoundationText(
                                    modifier = Modifier,
                                    text = stringResource(R.string.maker_transfer_account_result_save_template),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    modifier = Modifier,
                                    text = state.customerCodeTemp,
                                    maxLines = 1,
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                )
                            }
                        }

                        VerticalDivider(
                            modifier = Modifier.height(FDS.Sizer.Gap.gap60),
                            color = FDS.Colors.gray100,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        if (!state.isSaveContactSuccess) {
                            val isEnabled = state.isEnableSaveAccount
                            val tint = if (isEnabled) Color.Unspecified else FDS.Colors.gray500
                            val textColor =
                                if (isEnabled) FDS.Colors.characterHighlighted else FDS.Colors.gray500
                            val onClick: () -> Unit = if (isEnabled) {
                                {
                                    onAction(
                                        MakerTransferResultAction.OnShowSaveContactBottomSheet(
                                            true,
                                        ),
                                    )
                                }
                            } else {
                                {}
                            }

                            Row(
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxSize()
                                    .safeClickable { onClick.invoke() },
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_contact),
                                    contentDescription = "ic_feature_maker_contact",
                                    tint = tint,
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    text = stringResource(R.string.maker_transfer_account_result_save_contact),
                                    color = textColor,
                                    style = FDS.Typography.interactionSmallButton,
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .padding(start = FDS.Sizer.Gap.gap24)
                                    .weight(1f)
                                    .fillMaxSize()
                                    .safeClickable {},
                                verticalArrangement = Arrangement.Center,
                            ) {
                                FoundationText(
                                    text = stringResource(R.string.maker_transfer_account_result_save_contact),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    text = state.customerCodeContact,
                                    maxLines = 1,
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB2Emphasized,
                                )
                            }
                        }
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                ) {
                    if (state.createTransferDomain?.userApprovalList?.isNotEmpty() != false) {
                        LazyColumn(
                            modifier = Modifier.fillMaxWidth(),

                        ) {
                            state.createTransferDomain?.userApprovalList?.let {
                                itemsIndexed(it) { index, item ->
                                    ApprovalItem(
                                        item = item,
                                        isFirst = index == 0,
                                        isLast = index == it.lastIndex,
                                        linePendingColor = lineColor,
                                        lineSuccessColor = lineSuccessColor,
                                        onLastItemClick = {
                                            onAction.invoke(
                                                MakerTransferResultAction.OnShowCheckLimitedApproval(
                                                    true,
                                                ),
                                            )
                                        },
                                    )
                                }
                            }
                        }
                    } else {
                        // todo xử lý khi null list
                    }
                }
            }
        }
        if (!isCapturingScreenshot) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                FDS.Colors.dialogBackground.copy(alpha = 0f),
                                FDS.Colors.dialogBackground.copy(alpha = 0.5f),
                                FDS.Colors.dialogBackground.copy(alpha = 1f),
                            ),
                            startY = 0f,
                            endY = Float.POSITIVE_INFINITY,
                        ),
                    )
                    .padding(
                        horizontal = FDS.Sizer.Gap.gap24,
                        vertical = FDS.Sizer.Gap.gap8,
                    ),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    FoundationButton(
                        isLightButton = true,
                        leadingIcon = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_transfer_maker_square_rounded_plus),
                        text = stringResource(R.string.maker_transfer_account_result_create_new_trans),
                        onClick = { onAction.invoke(MakerTransferResultAction.MakerNewTransfer) },
                        enabled = true,
                        modifier = Modifier.fillMaxWidth(),
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                    FoundationButton(
                        isLightButton = false,
                        text = stringResource(R.string.maker_transfer_account_result_trans_manager),
                        onClick = { onAction.invoke(MakerTransferResultAction.GotoTransactionManager) },
                        enabled = true,
                        modifier = Modifier.fillMaxWidth(),
                    )
                }
            }
        }
    }
    SaveContactBottomSheet(
        isVisible = state.canShowBottomSheetSaveContact == true,
        onCancel = {
            onAction.invoke(MakerTransferResultAction.OnShowSaveContactBottomSheet(false))
        },
        onConfirm = {
            onAction.invoke(MakerTransferResultAction.OnClickSaveContactBottomSheet(it))
        },
        imageLoader = imageLoader,
        saveContactBottomSheetUiState = state.saveContactBottomSheetUiState,
    )
    SaveTemplateBottomSheet(
        isVisible = state.canShowBottomSheetSaveTemp == true,
        onCancel = {
            onAction.invoke(
                MakerTransferResultAction.OnShowSaveTempBottomSheet(
                    false,
                ),
            )
        },
        onConfirm = {
            onAction.invoke(
                MakerTransferResultAction.OnClickSaveTempBottomSheet(
                    customercode = it,
                    confirm = CONFIRM_SAVE_TEMP_TRANSFER_NO,
                ),
            )
        },
        imageLoader = imageLoader,
        saveTemplateBottomSheetUiState = state.saveTemplateBottomSheetUiState,
    )
    CheckLimitedApprovalBottomSheet(
        isVisible = state.canShowBottomSheetCheckLimitedApproval == true,
        onDismiss = {
            onAction.invoke(
                MakerTransferResultAction.OnShowCheckLimitedApproval(
                    false,
                ),
            )
        },
        state = state.checkLimitedApprovalBottomSheetUiState,
        onSearchText = {
            onAction.invoke(
                MakerTransferResultAction.OnSearchLimitedApproval(
                    searchText = it,
                ),
            )
        },
    )
}

@Composable
fun ApprovalItem(
    item: UserApprovalItem,
    isLast: Boolean,
    linePendingColor: Color,
    isFirst: Boolean,
    lineSuccessColor: Color,
    onLastItemClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                top = if (isFirst) FDS.Sizer.Gap.gap24 else FDS.Sizer.Gap.gap0,
                bottom = if (isLast) FDS.Sizer.Gap.gap24 else FDS.Sizer.Gap.gap0,
            ),
    ) {
        Row {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.width(FDS.Sizer.Gap.gap32),
            ) {
                if (isFirst) {
                    Icon(
                        painter = painterResource(R.drawable.ic_commom_success_24),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                    DrawDashedLine(
                        colors = listOf(lineSuccessColor, linePendingColor),
                        visible = true,
                    )
                } else {
                    Icon(
                        painter = painterResource(R.drawable.ic_commom_pending_24),
                        contentDescription = null,
                        tint = Color.Unspecified,
                    )
                    DrawDashedLine(
                        colors = listOf(linePendingColor),
                        visible = !isLast,
                    )
                }
            }

            Column(modifier = Modifier.padding(start = FDS.Sizer.Gap.gap8)) {
                FoundationText(
                    text = item.fullname.orEmpty(),
                    style = FDS.Typography.bodyB1,
                    color = FDS.Colors.gray900,
                )
                Spacer(Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = item.username.orEmpty(),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.gray600,
                )
                Spacer(Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = if (isFirst) {
                        item.approverlevel.orEmpty()
                    } else {
                        stringResource(R.string.maker_transfer_result_pending)
                    },
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.gray600,
                )
            }
        }

        if (isLast) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .safeClickable { onLastItemClick() }
                    .padding(vertical = FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = stringResource(R.string.maker_transfer_account_result_check_limited_approval),
                    maxLines = 1,
                    color = FDS.Colors.characterHighlighted,
                    style = FDS.Typography.interactionSmallButton,
                )
                Spacer(Modifier.width(FDS.Sizer.Gap.gap4))
                Icon(
                    painter = painterResource(R.drawable.ic_right),
                    contentDescription = null,
                    tint = Color.Unspecified,
                )
            }
        }
    }
}

@Composable
private fun DrawDashedLine(
    colors: List<Color>,
    visible: Boolean,
) {
    if (!visible) return
    Canvas(
        modifier = Modifier
            .width(FDS.Sizer.Stroke.stroke1)
            .height(FDS.Sizer.Gap.gap48),
    ) {
        val safeColors = if (colors.size == 1) {
            listOf(colors[0], colors[0])
        } else {
            colors
        }

        val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
        drawLine(
            brush = Brush.linearGradient(
                colors = safeColors,
                start = Offset(0f, 0f),
                end = Offset(0f, size.height),
            ),
            start = Offset(0f, 0f),
            end = Offset(0f, size.height),
            strokeWidth = 2.dp.toPx(),
            pathEffect = pathEffect,
        )
    }
}