package com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.empty_list.EmptyListScreen
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ContactSavedBottomSheetUiState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

sealed class SavedTabType {
    data object Account : SavedTabType()
    data object Card : SavedTabType()
    data object Template : SavedTabType()
}

@Composable
fun ContactSavedScreen(
    imageLoader: CoilImageLoader,
    modifier: Modifier,
    onSearchContactText: (searchContactText: String) -> Unit,
    onSelectedTabIndex: (selectedTabIndex: Int) -> Unit,
    onClickItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    onClickItemTemp: (selectedTabIndex: TempTransactionDomains) -> Unit,
    onClickEditItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    contactSavedBottomSheetUiState: ContactSavedBottomSheetUiState,
) {
    val isTransferTypeAccount = contactSavedBottomSheetUiState.isTransferTypeKey == KEY_TRANSFER_TYPE_ACCOUNT

    val tabItems = if (isTransferTypeAccount) {
        listOf(SavedTabType.Account, SavedTabType.Card, SavedTabType.Template)
    } else {
        listOf(SavedTabType.Account, SavedTabType.Template)
    }

    Box(modifier = modifier) {
        Column {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            FoundationFieldType(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                    .background(FDS.Colors.gray50),
                value = contactSavedBottomSheetUiState.searchContactText,
                onValueChange = { onSearchContactText.invoke(it) },
                placeholder = stringResource(
                    com.vietinbank.core_ui.R.string
                        .maker_transfer_dashboard_find_contact_sample_template,
                ),
                leadingIcon = {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_search),
                        contentDescription = "Search",
                        tint = Color.Unspecified,
                    )
                },
            )

            Spacer(Modifier.height(FDS.Sizer.Gap.gap8))

            // TabRow
            TabRow(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                selectedTabIndex = contactSavedBottomSheetUiState.selectedTabIndex,
                contentColor = FDS.Colors.characterHighlightedLighter,
                indicator = { tabPositions ->
                    TabRowDefaults.SecondaryIndicator(
                        Modifier.tabIndicatorOffset(
                            tabPositions[contactSavedBottomSheetUiState.selectedTabIndex],
                        ),
                        color = FDS.Colors.characterHighlightedLighter,
                    )
                },
                divider = {
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                },
            ) {
                tabItems.forEachIndexed { index, tab ->
                    val title = when (tab) {
                        SavedTabType.Account -> stringResource(
                            com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_account,
                        )

                        SavedTabType.Card -> stringResource(
                            com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_card,
                        )

                        SavedTabType.Template -> stringResource(
                            com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_template,
                        )
                    }

                    Tab(
                        selected = contactSavedBottomSheetUiState.selectedTabIndex == index,
                        onClick = { onSelectedTabIndex.invoke(index) },
                        text = {
                            FoundationText(
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                text = title,
                                style = FDS.Typography.interactionLink,
                                color =
                                if (contactSavedBottomSheetUiState.selectedTabIndex == index) {
                                    FDS.Colors.characterHighlighted
                                } else {
                                    FDS.Colors.gray500
                                },
                            )
                        },
                    )
                }
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

            // Nội dung thay đổi theo tab type
            when (tabItems[contactSavedBottomSheetUiState.selectedTabIndex]) {
                SavedTabType.Account -> {
                    if (contactSavedBottomSheetUiState.isLoadingListContact == true) return
                    SavedTab(
                        imageLoader = imageLoader,
                        listContactDomains = contactSavedBottomSheetUiState.listContactDomains,
                        onClickItemContact = onClickItemContact,
                        onClickEditItemContact = onClickEditItemContact,
                        isEdited = contactSavedBottomSheetUiState.isEdited,
                        contentEmptyState = contactSavedBottomSheetUiState.contentEmptyState,
                    )
                }

                SavedTabType.Card -> {
                    if (contactSavedBottomSheetUiState.isLoadingListContact == true) return

                    SavedTab(
                        imageLoader = imageLoader,
                        listContactDomains = contactSavedBottomSheetUiState.listContactDomains.filter {
                            Tags.TransferType.TYPE_NAPAS_CARD == it.trantype
                        }.toMutableList(),
                        onClickItemContact = onClickItemContact,
                        onClickEditItemContact = onClickEditItemContact,
                        isEdited = contactSavedBottomSheetUiState.isEdited,
                        contentEmptyState = contactSavedBottomSheetUiState.contentEmptyState,

                    )
                }

                SavedTabType.Template -> {
                    if (contactSavedBottomSheetUiState.isLoadingListTemp == true) return

                    TempTab(
                        imageLoader = imageLoader,
                        listTempTransactionDomains = contactSavedBottomSheetUiState.listTempDomains,
                        onClickItemTemp = onClickItemTemp,
                        isTransferScreen = contactSavedBottomSheetUiState.isEdited,
                        contentEmptyState = contactSavedBottomSheetUiState.contentEmptyState,

                    )
                }
            }
        }
    }
}

@Composable
fun SavedTab(
    imageLoader: CoilImageLoader,
    listContactDomains: MutableList<ContactDomains>,
    onClickItemContact: (ContactDomains) -> Unit,
    onClickEditItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    isEdited: Boolean,
    contentEmptyState: Int,
) {
    if (listContactDomains.isNotEmpty()) {
        LazyColumn(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            items(listContactDomains) { item ->
                SavedItem(
                    imageLoader = imageLoader,
                    savedItem = item,
                    onClick = { onClickItemContact.invoke(item) },
                    onClickItemEdit = { onClickEditItemContact.invoke(item) },
                    isEdited = isEdited,
                )
            }
        }
    } else {
        EmptyListScreen(
            iconResourceID = R.mipmap.ic_feature_maker_transfer_contact_temp_list_null,
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null),
            content = stringResource(contentEmptyState),
        )
    }
}

@Composable
fun TempTab(
    imageLoader: CoilImageLoader,
    listTempTransactionDomains: MutableList<TempTransactionDomains>,
    onClickItemTemp: (TempTransactionDomains) -> Unit,
    isTransferScreen: Boolean,
    contentEmptyState: Int,
) {
    if (listTempTransactionDomains.isNotEmpty()) {
        LazyColumn(
            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            items(listTempTransactionDomains) { item ->
                TempItem(
                    imageLoader = imageLoader,
                    savedItem = item,
                    onClick = { onClickItemTemp.invoke(item) },
                    isTransferScreen = isTransferScreen,
                )
            }
        }
    } else {
        EmptyListScreen(
            iconResourceID = R.mipmap.ic_feature_maker_transfer_contact_temp_list_null,
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null),
            content = stringResource(contentEmptyState),
        )
    }
}

@Composable
fun TempItem(
    imageLoader: CoilImageLoader,
    savedItem: TempTransactionDomains,
    onClick: () -> Unit,
    isTransferScreen: Boolean,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable { onClick.invoke() }
            .padding(bottom = FDS.Sizer.Gap.gap16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40)
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            imageLoader.LoadUrl(
                url = savedItem.iconUrl ?: "",
                isCache = true,
                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Column(
            modifier = Modifier
                .weight(1f),
        ) {
            FoundationText(
                text = savedItem.toAccountName ?: "",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = savedItem.content ?: "",
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            FoundationText(
                text = Utils.g()
                    .getDotMoneyHasCcy(savedItem.amount ?: "", savedItem.currency ?: ""),
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        if (!isTransferScreen) {
//            Icon(
//                painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_edit),
//                contentDescription = "Edit",
//                tint = Color.Unspecified,
//                modifier = Modifier.size(FDS.Sizer.Gap.gap16),
//            )
        }
    }
}

@Composable
fun SavedItem(
    imageLoader: CoilImageLoader,
    savedItem: ContactDomains,
    onClick: () -> Unit,
    onClickItemEdit: () -> Unit,
    isEdited: Boolean,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = FDS.Sizer.Gap.gap16)
            .safeClickable { onClick.invoke() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40)
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            if (Tags.TransferType.TYPE_IN == savedItem.trantype) {
                Icon(
                    painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_vietinbank),
                    contentDescription = "ic_vietinbank",
                    tint = Color.Unspecified,
                    modifier = Modifier.size(FDS.Sizer.Gap.gap24),
                )
            } else {
                imageLoader.LoadUrl(
                    url = savedItem.iconUrl ?: "",
                    isCache = true,
                    placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                    errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                )
            }
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        Column(
            modifier = Modifier
                .weight(1f),
        ) {
            FoundationText(
                text = savedItem.customercode?.takeIf { it.isNotEmpty() }
                    ?: savedItem.payeename.orEmpty(),
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            if (Tags.TransferType.TYPE_IN == savedItem.trantype || Tags.TransferType.TYPE_NAPAS_ACCOUNT == savedItem.trantype || Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER == savedItem.trantype) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = savedItem.account ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
            } else {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = savedItem.cardnumber ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
            if (savedItem.bankName?.isNotEmpty() == true) {
                FoundationText(
                    text = savedItem.bankName ?: "",
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
        if (isEdited) {
//            Icon(
//                painter = painterResource(com.vietinbank.feature_maker.R.drawable.ic_feature_maker_edit),
//                contentDescription = "Edit",
//                tint = Color.Unspecified,
//                modifier = Modifier
//                    .size(FDS.Sizer.Gap.gap16)
//                    .safeClickable {
//                        onClickItemEdit.invoke()
//                    },
//            )
        }
    }
}
