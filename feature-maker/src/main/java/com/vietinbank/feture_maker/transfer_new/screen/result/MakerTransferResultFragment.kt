package com.vietinbank.feture_maker.transfer_new.screen.result

import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import coil3.ImageLoader
import coil3.request.allowHardware
import coil3.request.crossfade
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.FileOpenerHelper
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.CONFIRM_SAVE_TEMP_TRANSFER_YES
import com.vietinbank.feture_maker.transfer_new.screen.result.MakerTransferResultViewModel.MakerTransferResultAction.OnClickSaveTempBottomSheet
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject

@AndroidEntryPoint
class MakerTransferResultFragment : BaseFragment<MakerTransferResultViewModel>() {

    override val viewModel: MakerTransferResultViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    private lateinit var localImageLoader: CoilImageLoader
    private lateinit var fileOpener: FileOpenerHelper

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        observeEvents()
        fileOpener = FileOpenerHelper(this)
        val rawImageLoader = ImageLoader.Builder(requireContext()).crossfade(true)
            .allowHardware(false) // chỉ màn này vì khi chia sẻ ảnh lỗi với bitmap
            .build()

        localImageLoader = CoilImageLoader(rawImageLoader)
    }

    private fun initData() {
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
        viewModel.getContactList()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    MakerTransferResultViewModel.MakerTransferResultEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.ShowErrorMess -> {
                        showNoticeDialog(event.errorMess)
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.MakerNewTransferAccount -> {
                        val bankItemString = Utils.g().provideGson().toJson(event.bankDomain)
                        val encodedBankItem = URLEncoder.encode(bankItemString, UTF8)
                        appNavigator.goToMakerTransferBankChoosed(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT,
                            itemStringObj = encodedBankItem,
                        )
                    }

                    MakerTransferResultViewModel.MakerTransferResultEvent.MakerNewTransferCard -> {
                        appNavigator.goToMakerTransferCard(
                            typeTransfer = TransferConstants.Bundle.KEY_TRANSFER_TYPE_CARD,
                        )
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.HandleErrorCreateTemplateState -> {
                        showConfirmDialog(
                            message = event.errorMess,
                            positiveButtonText = getString(R.string.dialog_button_confirm),
                            negativeButtonText = getString(R.string.dialog_button_back),
                            positiveAction = {
                                viewModel::onAction.invoke(
                                    OnClickSaveTempBottomSheet(
                                        customercode = "",
                                        confirm = CONFIRM_SAVE_TEMP_TRANSFER_YES,
                                    ),
                                )
                            },
                            negativeAction = {
                                dismissNoticeDialog()
                            },
                        )
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.ShowToastMess -> {
                        Toast.makeText(requireContext(), event.toastMess, Toast.LENGTH_SHORT).show()
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.OnClickedItem -> {
                        val uri = event.uriFile.toUri()
                        val mimeType = requireContext().contentResolver.getType(uri)
                        onOpenFileClicked(uri, mimeType)
                    }

                    MakerTransferResultViewModel.MakerTransferResultEvent.MakerNewPaymentOrderTransfer -> {
                        appNavigator.goToMakerPaymentOrder(bundleOf())
                    }

                    MakerTransferResultViewModel.MakerTransferResultEvent.GotoTransactionManager -> {
                        appNavigator.gotoTransactionManager(
                            bundleOf().apply { putInt(Tags.TRANSACTION_BUNDLE, 1) },
                        )
                    }

                    is MakerTransferResultViewModel.MakerTransferResultEvent.HandleSuccessCreateSaveState -> {
                        showNoticeDialog(event.successMess)
                    }
                }
            }
        }
    }

    private fun onOpenFileClicked(uri: Uri, mimeType: String? = null) {
        fileOpener.openFile(requireContext(), uri, mimeType)
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            MakerTransferResultScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = localImageLoader,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}
