package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.textfield.FieldVariant
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldTypeTextFieldValue
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.BottomSheetItem
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ChooseAccountBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ChooseBankBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ContactSavedBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.DatePickerDialog
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.NextApproverBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.RadioBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.SplitTransferBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.TypeLoanBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseAction
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.MakerTransferBankChoosedViewModel.MakerTransferBankChooseState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun MakerTransferBankChoosedScreen(
    state: MakerTransferBankChooseState,
    onAction: ((MakerTransferBankChooseAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    var showDatePicker by remember { mutableStateOf(false) }
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .pointerInput(Unit) {
                detectTapGestures(onTap = {
                    focusManager.clearFocus()
                })
            },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .imePadding()
                .verticalScroll(scrollState)
                .padding(vertical = FDS.Sizer.Gap.gap16)
                // QUAN TRỌNG: Thêm bottom padding để tránh bị button che
                .padding(bottom = FDS.Sizer.Gap.gap80), // Chiều cao của button + padding
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = state.titleAppbar,
                onNavigationClick = { onAction.invoke(MakerTransferBankChooseAction.OnBackPressed) },
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            if (KEY_TRANSFER_TYPE_ACCOUNT == state.isTransferTypeKey) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer),
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap24)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        state.currentDataBankDomain?.let { bank ->
                            Box(
                                modifier = Modifier
                                    .clip(CircleShape)
                                    .background(FDS.Colors.gray50),
                                contentAlignment = Alignment.Center,
                            ) {
                                imageLoader.LoadUrl(
                                    url = bank.icon ?: "",
                                    isCache = true,
                                    placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                    errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                )
                            }

                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

                            // Bank Info
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .safeClickable {
                                        onAction.invoke(
                                            MakerTransferBankChooseAction.CanShowBottomSheetChooseBank(
                                                isShow = true,
                                            ),
                                        )
                                    },
                                verticalArrangement = Arrangement.SpaceBetween,
                            ) {
                                FoundationText(
                                    text = bank.shortName ?: "",
                                    style = FDS.Typography.bodyB2,
                                    color = FDS.Colors.characterPrimary,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                                FoundationText(
                                    text = bank.bankName ?: "",
                                    style = FDS.Typography.captionCaptionL,
                                    color = FDS.Colors.characterSecondary,
                                )
                            }
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
                            IconButton(onClick = {
                                onAction.invoke(
                                    MakerTransferBankChooseAction.CanShowBottomSheetChooseBank(
                                        isShow = true,
                                    ),
                                )
                            }) {
                                Icon(
                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                )
                            }
                        }
                    }
                    state.currentDataBankDomain?.let { bank ->
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                    }
                    FoundationText(
                        modifier = Modifier.padding(
                            horizontal = FDS.Sizer.Gap.gap24,
                        ),
                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationFieldTypeTextFieldValue(
                            value = "",
                            variant = FieldVariant.TRANSPARENT,
                            onValueChange = {},
                            textFieldValue = state.beneficiaryAccount,
                            onTextFieldValueChange = { newValue ->
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnBeneficiaryAccountTextChange(
                                        newValue,
                                    ),
                                )
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account_input),
                            onFocusChanged = { isFocused ->
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnBeneficiaryAccountFocusChange(
                                        isFocused,
                                    ),
                                )
                            },
                            modifier = Modifier
                                .weight(1f)
                                .focusRequester(state.focusRequesterAccountNumber),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Done,
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    if (state.isNormalTransferType) {
                                        onAction.invoke(
                                            MakerTransferBankChooseAction.OnFocusAccountOwnerChange(
                                                true,
                                            ),
                                        )
                                    }
                                    focusManager.clearFocus()
                                    keyboardController?.hide()
                                },
                            ),
                            isError = state.isErrorBeneficiaryAccount,
                            errorMessage = state.isErrorBeneficiaryAccountMessage,
                            trailingIcon =
                            {
                                if (state.beneficiaryAccount.text.isNotEmpty()) {
                                    IconButton(
                                        modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                                        onClick = {
                                            onAction.invoke(
                                                MakerTransferBankChooseAction.OnClearBeneficiaryAccount,
                                            )
                                        },
                                    ) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_close),
                                            contentDescription = "ic_feature_maker_clear_text",
                                            tint = Color.Unspecified,
                                            modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                                        )
                                    }
                                }
                            },
                        )
                        IconButton(onClick = {
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnClickContactBottomSheet(
                                    canShowBottomSheetContactSaved = true,
                                ),
                            )
                        }) {
                            Icon(
                                painter = painterResource(R.drawable.ic_feature_maker_contact),
                                contentDescription = "ic_feature_maker_contact",
                                tint = Color.Unspecified,
                            )
                        }
                    }

                    if (state.isNormalTransferType) {
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        FoundationEditText(
                            typingVietnames = false,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .focusRequester(state.focusRequesterAccountOwner),
                            onFocusChanged = { focused ->
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnFocusAccountOwnerChange(
                                        focused,
                                    ),
                                )
                            },
                            showCharacterCounter = false,
                            value = state.accountOwnerHolder,
                            onValueChange = {
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnAccountOwnerHolderChange(
                                        it,
                                    ),
                                )
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_account_name_title),
                            hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_find_account_name),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.OnFocusAmountChange(
                                            true,
                                        ),
                                    )
                                    focusManager.clearFocus()
                                    keyboardController?.hide()
                                },
                            ),
                        )
                    } else {
                        if (state.beneficiaryAccountName.isNotEmpty() == true) {
                            FoundationText(
                                modifier = Modifier.padding(
                                    start = FDS.Sizer.Gap.gap24,
                                    end = FDS.Sizer.Gap.gap24,
                                ),
                                text = state.beneficiaryAccountName,
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer),
                ) {
                    state.currentDataBankDomain?.let { bank ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap24)
                                .padding(horizontal = FDS.Sizer.Gap.gap24),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Box(
                                modifier = Modifier
                                    .clip(CircleShape)
                                    .background(FDS.Colors.gray50),
                                contentAlignment = Alignment.Center,
                            ) {
                                imageLoader.LoadUrl(
                                    url = bank.icon ?: "",
                                    isCache = true,
                                    placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                    errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                )
                            }

                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

                            // Bank Info
                            Column(
                                modifier = Modifier
                                    .weight(1f),
                                verticalArrangement = Arrangement.SpaceBetween,
                            ) {
                                FoundationText(
                                    text = bank.shortName ?: "",
                                    style = FDS.Typography.bodyB2,
                                    color = FDS.Colors.characterPrimary,
                                )
                                Spacer(modifier = Modifier.weight(1f))
                                FoundationText(
                                    text = bank.bankName ?: "",
                                    style = FDS.Typography.captionCaptionL,
                                    color = FDS.Colors.characterSecondary,
                                )
                            }
                        }
                    }
                    state.currentDataBankDomain?.let { bank ->
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                    }
                    FoundationText(
                        modifier = Modifier.padding(
                            start = FDS.Sizer.Gap.gap24,
                            end = FDS.Sizer.Gap.gap24,
                            top = FDS.Sizer.Gap.gap16,
                        ),
                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_card),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationFieldTypeTextFieldValue(
                            value = "",
                            variant = FieldVariant.TRANSPARENT,
                            onValueChange = {},
                            textFieldValue = state.cardNumber,
                            onTextFieldValueChange = { newValue ->
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnCardNumberTextChange(
                                        newValue,
                                    ),
                                )
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_card_input),
                            onFocusChanged = { isFocused ->
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnCardNumberChangeFocusChange(
                                        isFocused,
                                    ),
                                )
                            },
                            modifier = Modifier
                                .weight(1f)
                                .focusRequester(state.focusRequesterAccountNumber),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Number,
                                imeAction = ImeAction.Done,
                            ),
                            keyboardActions = KeyboardActions(
                                onDone = {
                                    focusManager.clearFocus()
                                    keyboardController?.hide()
                                },
                            ),
                            isError = false,
                            errorMessage = "",
                            trailingIcon =
                            {
                                if (state.cardNumber.text.isNotEmpty()) {
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            MakerTransferBankChooseAction.OnClearCardNumber,
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_close),
                                            contentDescription = "ic_feature_maker_clear_text",
                                            tint = Color.Unspecified,
                                            modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                                        )
                                    }
                                } else {
                                    null
                                }
                            },
                            pattern = Regex(TransferConstants.ValidationConstants.AMOUNT_REGEX),
                            inputType = InputType.NUMBER,
                        )
                        IconButton(onClick = {
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnClickContactBottomSheet(
                                    canShowBottomSheetContactSaved = true,
                                ),
                            )
                        }) {
                            Icon(
                                painter = painterResource(R.drawable.ic_feature_maker_contact),
                                contentDescription = "ic_feature_maker_contact",
                                tint = Color.Unspecified,
                            )
                        }
                    }
                    if (state.cardOwner.isNotEmpty() == true) {
                        FoundationText(
                            modifier = Modifier.padding(
                                start = FDS.Sizer.Gap.gap24,
                                end = FDS.Sizer.Gap.gap24,
                            ),
                            text = state.cardOwner,
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.captionCaptionL,
                        )
                    }
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                }
            }
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

            state.currentAccountDefaultDomain?.let { item ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer)
                        .padding(bottom = FDS.Sizer.Gap.gap24),
                ) {
                    Row(
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap24, bottom = FDS.Sizer.Gap.gap16),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Box(
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            Icon(
                                painter = painterResource(R.drawable.ic_vietinbank),
                                contentDescription = "ic_feature_maker_transfer_card",
                                tint = Color.Unspecified,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }

                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .safeClickable {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.CanShowBottomSheetChooseAccount(
                                            isShow = true,
                                        ),
                                    )
                                },
                            verticalArrangement = Arrangement.SpaceBetween,
                        ) {
                            FoundationText(
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account),
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                                modifier = Modifier.padding(
                                    vertical = FDS.Sizer.Gap.gap4,
                                ),
                            )
                            FoundationText(
                                text = if (item.aliasName?.isEmpty() == true) {
                                    item.accountName
                                        ?: ""
                                } else {
                                    item.aliasName ?: ""
                                },
                                color = FDS.Colors.characterHighlighted,
                                style = FDS.Typography.bodyB2,
                                modifier = Modifier.padding(
                                    vertical = FDS.Sizer.Gap.gap4,
                                ),
                            )

                            FoundationText(
                                text = item.accountNo ?: "",
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                                modifier = Modifier.padding(
                                    vertical = FDS.Sizer.Gap.gap4,
                                ),
                            )
                            FoundationText(
                                text = Utils.g().getDotMoneyHasCcy(
                                    (item.currentBalance ?: ""), item.currency ?: "",
                                ),
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                                modifier = Modifier.padding(
                                    vertical = FDS.Sizer.Gap.gap4,
                                ),
                            )
                        }
                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
                        IconButton(onClick = {
                            onAction.invoke(
                                MakerTransferBankChooseAction.CanShowBottomSheetChooseAccount(
                                    isShow = true,
                                ),
                            )
                        }) {
                            Icon(
                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                contentDescription = "ic_drop_down",
                                tint = Color.Unspecified,
                            )
                        }
                    }

                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                    FoundationEditText(
                        modifier = Modifier
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            )
                            .focusRequester(state.focusRequesterAmount),
                        onFocusChanged = { focused ->
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnFocusAmountChange(
                                    focused,
                                ),
                            )
                        },
                        value = state.amountHolder,
                        onValueChange = {
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnAmountHolderChange(
                                    it,
                                ),
                            )
                        },
                        placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                        hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount_hint),
                        inputType = InputType.AMOUNT,
                        currency = state.currentAccountDefaultDomain?.currency ?: "",
                        maxLength = 16,
                        keyboardActions = KeyboardActions(
                            onDone = {
                                focusManager.clearFocus()
                                keyboardController?.hide()
                                onAction.invoke(
                                    MakerTransferBankChooseAction.OnFocusContentChange(
                                        true,
                                    ),
                                )
                            },
                        ),
                    )
                    if (state.amountHolderReader.isNotEmpty() && state.amountHolder.isNotEmpty()) {
                        FoundationText(
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                            text = state.amountHolderReader,
                            color = FDS.Colors.characterHighlighted,
                            style = FDS.Typography.captionCaptionL,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                    }
                    HorizontalDivider(
                        color = if (state.isFocusedAmount) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                    FoundationEditText(
                        typingVietnames = false,
                        inputType = InputType.TEXT,
                        modifier = Modifier
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            )
                            .focusRequester(state.focusRequesterContent),
                        onFocusChanged = { focused ->
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnFocusContentChange(
                                    focused,
                                ),
                            )
                        },
                        value = state.contentHolder,
                        onValueChange = {
                            onAction.invoke(
                                MakerTransferBankChooseAction.OnContentHolderChange(
                                    it,
                                ),
                            )
                        },
                        placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                        hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content_hint),
                        maxLength = if (Tags.TransferType.TYPE_IN == state.currentDataBankDomain?.type) 146 else 210,
                        keyboardActions = KeyboardActions(
                            onDone = {
                                focusManager.clearFocus()
                                keyboardController?.hide()
                            },
                        ),
                    )

                    HorizontalDivider(
                        color = if (state.isFocusedContent) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                    if (KEY_TRANSFER_TYPE_ACCOUNT == state.isTransferTypeKey) {
                        if (state.visibilityBottomSheetTransferTime) {
                            FoundationText(
                                modifier = Modifier.padding(
                                    start = FDS.Sizer.Gap.gap24,
                                    end = FDS.Sizer.Gap.gap24,
                                ),
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                            )
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                                    .safeClickable {
                                        onAction.invoke(
                                            MakerTransferBankChooseAction.CanShowBottomSheetTransferTime,
                                        )
                                    },
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                FoundationText(
                                    modifier = Modifier.weight(1f),
                                    text = state.transferTime?.text ?: "",
                                    color = FDS.Colors.characterPrimary,
                                    style = FDS.Typography.bodyB1,
                                )
                                IconButton(onClick = {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.CanShowBottomSheetTransferTime,
                                    )
                                }) {
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                        contentDescription = "ic_drop_down",
                                        tint = Color.Unspecified,
                                    )
                                }
                            }
                            if (state.showTransferTimeSchedule) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .safeClickable { showDatePicker = true },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = state.transferTimeText,
                                        color = FDS.Colors.characterPrimary,
                                        style = FDS.Typography.bodyB1,
                                    )
                                    IconButton(onClick = { showDatePicker = true }) {
                                        Icon(
                                            painter = painterResource(R.drawable.ic_feature_maker_transfer_calendar_time),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                            }
                            HorizontalDivider(
                                color = FDS.Colors.divider,
                                thickness = FDS.Sizer.Stroke.stroke1,
                            )
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                        }
                    }
                    if (state.visibilityShowBottomSheetTransferType) {
                        FoundationText(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24),

                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.captionCaptionL,
                        )
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .safeClickable { onAction.invoke(MakerTransferBankChooseAction.CanShowBottomSheetTransferType) },
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                modifier = Modifier.weight(1f),
                                text = state.transferTypeFee?.text ?: "",
                                color = FDS.Colors.characterPrimary,
                                style = FDS.Typography.bodyB1,
                            )
                            IconButton(
                                onClick = { onAction.invoke(MakerTransferBankChooseAction.CanShowBottomSheetTransferType) },
                                modifier = Modifier
                                    .alpha(if (TransferConstants.Transfer.ID_TRANSFER_TIME_SCHEDULE != state.transferTime?.id) 1f else 0f),
                                enabled = TransferConstants.Transfer.ID_TRANSFER_TIME_SCHEDULE != state.transferTime?.id,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                )
                            }
                        }

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                    }
                    FoundationText(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .safeClickable {
                                if (state.nextApproversListString.isEmpty()) {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.OnSwitchNextApproversChange(
                                            true,
                                        ),
                                    )
                                } else {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.ShowBottomSheetNextApprovers,
                                    )
                                }
                            },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            modifier = Modifier.weight(1f),
                            text = if (state.nextApproversListString.isEmpty()) {
                                stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_register)
                            } else {
                                state.nextApproversListString
                            },
                            color = FDS.Colors.characterPrimary,
                            style = FDS.Typography.bodyB1,
                        )

                        IconButton(
                            onClick = {
                                if (state.nextApproversListString.isEmpty()) {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.OnSwitchNextApproversChange(
                                            true,
                                        ),
                                    )
                                } else {
                                    onAction.invoke(
                                        MakerTransferBankChooseAction.ShowBottomSheetNextApprovers,
                                    )
                                }
                            },
                        ) {
                            Icon(
                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                contentDescription = "ic_drop_down",
                                tint = Color.Unspecified,
                            )
                        }
                    }
                    LaunchedEffect(
                        state.isFocusedAccountNumber,
                        state.isFocusedAccountOwner,
                        state.isFocusedAmount,
                        state.isFocusedContent,
                    ) {
                        when {
                            state.isFocusedAccountNumber -> {
                                state.focusRequesterAccountNumber.requestFocus()
                            }

                            state.isFocusedAccountOwner -> {
                                state.focusRequesterAccountOwner.requestFocus()
                            }

                            state.isFocusedAmount -> {
                                scrollState.animateScrollTo(scrollState.maxValue)
                                state.focusRequesterAmount.requestFocus()
                            }

                            state.isFocusedContent -> {
                                state.focusRequesterContent.requestFocus()
                            }

                            else -> {
                                focusManager.clearFocus()
                            }
                        }
                    }
                }
            } ?: run {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer),
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = FDS.Sizer.Gap.gap32,
                                horizontal = FDS.Sizer.Gap.gap24,
                            )
                            .safeClickable { onAction.invoke(MakerTransferBankChooseAction.GetAccountList) },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            modifier = Modifier.weight(1f),
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_main_account),
                            color = FDS.Colors.gray600,
                            style = FDS.Typography.bodyB2,
                        )
                        Icon(
                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                            contentDescription = "ic_drop_down",
                            tint = Color.Unspecified,
                        )
                    }
                }
            }
        }
        // Bottom Button
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            FDS.Colors.dialogBackground.copy(alpha = 0f),
                            FDS.Colors.dialogBackground.copy(alpha = 0.5f),
                            FDS.Colors.dialogBackground.copy(alpha = 1f),
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY,
                    ),
                )
                .padding(
                    horizontal = FDS.Sizer.Gap.gap24,
                    vertical = FDS.Sizer.Gap.gap8,
                ),
        ) {
            FoundationButton(
                isLightButton = true,
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = { onAction.invoke(MakerTransferBankChooseAction.OnClickButtonContinue) },
                enabled = true,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
    ChooseBankBottomSheet(
        isVisible = state.canShowBottomSheetChooseBank == true,
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_list_bank),
        onItemSelected = { selectedItem ->
            onAction.invoke(MakerTransferBankChooseAction.OnCurrentBankDataChange(selectedItem))
        },
        onDismiss = {
            onAction.invoke(MakerTransferBankChooseAction.CanShowBottomSheetChooseBank(isShow = false))
        },
        modifier = Modifier
            .padding(FDS.Sizer.Gap.gap8)
            .navigationBarsPadding(),
        imageLoader = imageLoader,
        onAction = onAction,
        chooseAccountBottomSheetUiState = state.chooseBankBottomSheetUiState,
    )
    ChooseAccountBottomSheet(
        isVisible = state.canShowBottomSheetChooseAccount == true,
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_main_account),
        onItemSelected = { selectedItem ->
            onAction.invoke(
                MakerTransferBankChooseAction.OnCurrentAccountDataChange(
                    accountDefaultDomain = selectedItem,
                ),
            )
        },
        onDismiss = {
            onAction.invoke(MakerTransferBankChooseAction.CanShowBottomSheetChooseAccount(isShow = false))
        },
        onSearchAccountText = {
            onAction.invoke(
                MakerTransferBankChooseAction.OnSearchAccountText(
                    searchAccountText = it,
                ),
            )
        },
        chooseAccountBottomSheetUiState = state.chooseAccountBottomSheetUiState,
    )
    RadioBottomSheet(
        isVisible = state.canShowBottomSheetTransferTime == true,
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
        onItemSelected = { selectedItem ->
            state.radioBottomSheetTransferTimeUiState.items =
                state.radioBottomSheetTransferTimeUiState.items.map { item ->
                    item.copy(isSelected = item.id == selectedItem.id)
                } as MutableList<BottomSheetItem>
            if (TransferConstants.Transfer.ID_TRANSFER_TIME_NOW == selectedItem.id) {
                onAction.invoke(
                    MakerTransferBankChooseAction.OnChooseTransferTime(
                        selectedItem,
                    ),
                )
                onAction.invoke(
                    MakerTransferBankChooseAction.ShowTransferTimeSchedule(
                        show = false,
                    ),
                )
            } else {
                showDatePicker = true
            }
            onAction.invoke(MakerTransferBankChooseAction.DismissShowBottomSheetTransferTime)
        },
        onDismiss = {
            onAction.invoke(MakerTransferBankChooseAction.DismissShowBottomSheetTransferTime)
        },
        radioBottomSheetUiState = state.radioBottomSheetTransferTimeUiState,
    )
    RadioBottomSheet(
        isVisible = state.canShowBottomSheetTransferType == true,
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
        onItemSelected = { selectedItem ->
            state.radioBottomSheetTransferTypeFeeUiState.items =
                state.radioBottomSheetTransferTypeFeeUiState.items.map { item ->
                    item.copy(isSelected = item.id == selectedItem.id)
                } as MutableList<BottomSheetItem>
            onAction.invoke(
                MakerTransferBankChooseAction.OnChooseTransferType(
                    selectedItem,
                ),
            )
            onAction.invoke(MakerTransferBankChooseAction.DismissShowBottomSheetTransferType)
        },
        onDismiss = {
            onAction.invoke(MakerTransferBankChooseAction.DismissShowBottomSheetTransferType)
        },
        radioBottomSheetUiState = state.radioBottomSheetTransferTypeFeeUiState,

    )
    if (showDatePicker) {
        DatePickerDialog(
            showDatePicker = true,
            onDateSelected = { selectedDate ->
                showDatePicker = false
                onAction.invoke(
                    MakerTransferBankChooseAction.ShowTransferTimeSchedule(
                        show = true,
                    ),
                )
                state.radioBottomSheetTransferTimeUiState.items[1].let {
                    onAction.invoke(
                        MakerTransferBankChooseAction.OnChooseTransferTime(
                            it,
                        ),
                    )
                    onAction.invoke(
                        MakerTransferBankChooseAction.OnTransferTimeTextChange(
                            selectedDate,
                        ),
                    )
                }
            },
            onDismiss = {
                if (state.transferTimeText.isEmpty()) {
                    state.radioBottomSheetTransferTimeUiState.items =
                        state.radioBottomSheetTransferTimeUiState.items.map { item ->
                            item.copy(isSelected = item.id == TransferConstants.Transfer.ID_TRANSFER_TIME_NOW)
                        } as MutableList<BottomSheetItem>
                    onAction.invoke(
                        MakerTransferBankChooseAction.ShowTransferTimeSchedule(
                            show = false,
                        ),
                    )
                }
                showDatePicker = false
            },
        )
    }
    ContactSavedBottomSheet(
        isVisible = state.canShowBottomSheetContactSaved == true,
        onDismiss = {
            onAction.invoke(
                MakerTransferBankChooseAction.OnClickContactBottomSheet(
                    canShowBottomSheetContactSaved = false,
                ),
            )
        },
        imageLoader = imageLoader,
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.9f)
            .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
        onSearchContactText = { onAction.invoke(MakerTransferBankChooseAction.OnSearchContactText(it)) },
        onSelectedTabIndex = {
            onAction.invoke(MakerTransferBankChooseAction.OnSelectedTabContactIndex(it))
        },
        onClickItemContact = { onAction.invoke(MakerTransferBankChooseAction.ClickContactItem(it)) },
        onClickItemTemp = { onAction.invoke(MakerTransferBankChooseAction.ClickTempItem(it)) },
        contactSavedBottomSheetUiState = state.contactSavedBottomSheetUiState,
    )
    NextApproverBottomSheet(
        isVisible = state.canShowBottomSheetNextApprovers == true,
        title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver),
        onItemSelected = { approver ->
            onAction.invoke(MakerTransferBankChooseAction.OnClickItemApprove(approver))
        },

        onConfirm = {
            onAction.invoke(
                MakerTransferBankChooseAction.DismissShowBottomSheetNextApprovers(
                    isConfirm = true,
                ),
            )
        },
        onCancel = {
            onAction.invoke(
                MakerTransferBankChooseAction.DismissShowBottomSheetNextApprovers(
                    isConfirm = false,
                ),
            )
        },
        onClickAllItemSelected = {
            onAction.invoke(MakerTransferBankChooseAction.OnClickAllItemApprove(isAllItemSelected = !state.nextApproverBottomSheetUiState.onAllItemApproverSelected))
        },
        nextApproverBottomSheetUiState = state.nextApproverBottomSheetUiState,
    )
    SplitTransferBottomSheet(
        isVisible = state.canShowBottomSheetSplitTransfer == true,
        onDismiss = {
            onAction.invoke(
                MakerTransferBankChooseAction.OnShowSplitTransferBottomSheet(
                    canShowBottomSheetSplitTransfer = false,
                ),
            )
        },
        splitTransferBottomSheetUiState = state.splitTransferBottomSheetUiState,
        onTransferNormal = {
            onAction.invoke(MakerTransferBankChooseAction.MakeTransferNormalType)
        },
        onTransferSplit = {
            onAction.invoke(MakerTransferBankChooseAction.OnClickButtonContinueSPLIT)
        },
        onClickTypeLoan = {
            onAction.invoke(
                MakerTransferBankChooseAction.OnShowTypeLoanBottomSheet(
                    canShowBottomSheetTypeLoan = true,
                ),
            )
        },
    )
    TypeLoanBottomSheet(
        isVisible = state.canShowBottomSheetTypeLoan == true,
        onDismiss = {
            onAction.invoke(
                MakerTransferBankChooseAction.OnShowTypeLoanBottomSheet(
                    canShowBottomSheetTypeLoan = false,
                ),
            )
        },
    )
}

@Composable
fun ImageToggleButton(
    isToggled: Boolean,
    onToggle: (Boolean) -> Unit,
    onImageRes: Int,
    offImageRes: Int,
    modifier: Modifier = Modifier,
    imageSize: Dp = FDS.Sizer.Gap.gap48,
    animateScale: Boolean = true,
    tintColor: Color? = null,
) {
    val animatedScale by animateFloatAsState(
        targetValue = if (animateScale && isToggled) 1.1f else 1f,
        animationSpec = tween(durationMillis = 200),
        label = "image_scale",
    )

    Image(
        painter = painterResource(id = if (isToggled) onImageRes else offImageRes),
        contentDescription = if (isToggled) "Toggle On" else "Toggle Off",
        modifier = modifier
            .size(imageSize)
            .scale(animatedScale)
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
            ) { onToggle(!isToggled) },
        contentScale = ContentScale.Fit,
        colorFilter = tintColor?.let { ColorFilter.tint(it) },
    )
}
