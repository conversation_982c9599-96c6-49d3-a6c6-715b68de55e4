package com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.foundation.textfield.FieldVariant
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_maker.R
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.BottomSheetItem
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.BranchBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ChooseAccountBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ChooseTypeUploadBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.ContactSavedBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.DatePickerPaymentOrderDialog
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.HourPickerBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.NextApproverBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.bottomSheet.RadioBottomSheet
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_NEW
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_SAVED
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.ContactSavedScreen
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.RecentTransactionScreen
import com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard.PaymentOrderViewModel.PaymentOrderAction
import com.vietinbank.feture_maker.transfer_new.screen.maker_payment_order_dashboard.PaymentOrderViewModel.PaymentOrderState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun PaymentOrderScreen(
    state: PaymentOrderState,
    onAction: ((PaymentOrderAction) -> Unit),
    imageLoader: CoilImageLoader,
) {
    val scrollState = rememberScrollState()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    var showDatePicker by remember { mutableStateOf(false) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .pointerInput(Unit) {
                detectTapGestures(onTap = {
                    focusManager.clearFocus()
                })
            },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = FDS.Sizer.Gap.gap16, bottom = FDS.Sizer.Gap.gap8),
            // QUAN TRỌNG: Thêm bottom padding để tránh bị button che
            // Chiều cao của button + padding
        ) {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_title),
                onNavigationClick = { onAction.invoke(PaymentOrderAction.OnBackPressed) },
                navigationIcon = painterResource(com.vietinbank.core_ui.R.drawable.ic_core_ui_home),
            )
            FoundationTabs(
                tabs = listOf(
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_new),
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_saved),
                    stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_tab_recent),
                ),
                selectedIndex = state.selectedIndex,
                onTabSelected = { onAction.invoke(PaymentOrderAction.OnSelectedIndex(it)) },
                type = TabType.Pill,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap16, vertical = FDS.Sizer.Gap.gap16),
                horizontalItemPadding = FDS.Sizer.Padding.padding0,
            )
            if (TAB_NEW == state.selectedIndex) {
                Box {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .imePadding()
                            .verticalScroll(scrollState)
                            .padding(bottom = FDS.Sizer.Gap.gap80),
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = FDS.Sizer.Gap.gap8)
                                .clip(
                                    RoundedCornerShape(AppSizer.Radius.radius32),
                                )
                                .background(FDS.Colors.backgroundBgContainer)
                                .padding(vertical = FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationEditText(
                                typingVietnames = true,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                                    .focusRequester(state.focusRequesterBankName),

                                onFocusChanged = { focused ->
                                    onAction.invoke(
                                        PaymentOrderAction.OnFocusBankNameHolderChange(
                                            focused,
                                        ),
                                    )
                                },
                                showCharacterCounter = false,
                                value = state.bankNameHolder,
                                onValueChange = {
                                    onAction.invoke(
                                        PaymentOrderAction.OnBankNameHolderChange(
                                            it,
                                        ),
                                    )
                                },
                                placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_bank_name),
                                hintText = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_bank_name_input),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        onAction.invoke(PaymentOrderAction.OnBeneficiaryAccountFocusChange(true))
                                        focusManager.clearFocus()
                                        keyboardController?.hide()
                                    },
                                ),
                                maxLength = TransferConstants.MaxLengthPaymentOrder.MAX_BANK_NAME_LENGTH,
                            )
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                            HorizontalDivider(
                                color = if (state.isFocusBankNameHolder) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                                thickness = FDS.Sizer.Stroke.stroke1,
                            )

                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                            FoundationText(
                                modifier = Modifier.padding(
                                    horizontal = FDS.Sizer.Gap.gap24,
                                ),
                                text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account),
                                color = FDS.Colors.characterSecondary,
                                style = FDS.Typography.captionCaptionL,
                            )
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = FDS.Sizer.Gap.gap8, end = FDS.Sizer.Gap.gap24),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                FoundationFieldType(
                                    value = state.beneficiaryAccountHolder,
                                    variant = FieldVariant.TRANSPARENT,
                                    onValueChange = { newValue ->
                                        onAction.invoke(
                                            PaymentOrderAction.OnBeneficiaryAccountTextChange(
                                                newValue,
                                            ),
                                        )
                                    },

                                    placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_beneficiary_account_input),
                                    onFocusChanged = { isFocused ->
                                        onAction.invoke(
                                            PaymentOrderAction.OnBeneficiaryAccountFocusChange(
                                                isFocused,
                                            ),
                                        )
                                    },
                                    modifier = Modifier
                                        .weight(1f)
                                        .focusRequester(state.focusRequesterAccountNumber),
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                        imeAction = ImeAction.Done,
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = {
                                            onAction.invoke(PaymentOrderAction.OnFocusAccountNameHolderChange(true))
                                            focusManager.clearFocus()
                                            keyboardController?.hide()
                                        },
                                    ),
//                                    isError = state.isErrorBeneficiaryAccount,
//                                    errorMessage = state.isErrorBeneficiaryAccountMessage,
                                    trailingIcon = {
                                        if (state.beneficiaryAccountHolder.isNotEmpty()) {
                                            IconButton(
                                                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                                                onClick = {
                                                    onAction.invoke(
                                                        PaymentOrderAction.OnClearBeneficiaryAccount,
                                                    )
                                                },
                                            ) {
                                                Icon(
                                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_close),
                                                    contentDescription = "ic_feature_maker_clear_text",
                                                    tint = Color.Unspecified,
                                                    modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                                                )
                                            }
                                        }
                                    },
                                    pattern = Regex(TransferConstants.ValidationConstants.BENEFICIARY_REGEX),
                                    maxLength = TransferConstants.MaxLengthPaymentOrder.MAX_BENEFICIARY_ACCOUNT_LENGTH,
                                )
                                IconButton(onClick = {
                                    onAction.invoke(
                                        PaymentOrderAction.OnClickContactBottomSheet(
                                            canShowBottomSheetContactSaved = true,
                                        ),
                                    )
                                }) {
                                    Icon(
                                        painter = painterResource(R.drawable.ic_feature_maker_contact),
                                        contentDescription = "ic_feature_maker_contact",
                                        tint = Color.Unspecified,
                                    )
                                }
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                            HorizontalDivider(
                                color = if (state.isFocusBeneficiaryAccountHolder) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                                thickness = FDS.Sizer.Stroke.stroke1,
                            )

                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                            FoundationEditText(
                                typingVietnames = true,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap24)
                                    .focusRequester(state.focusRequesterAccountName),

                                onFocusChanged = { focused ->
                                    onAction.invoke(
                                        PaymentOrderAction.OnFocusAccountNameHolderChange(
                                            focused,
                                        ),
                                    )
                                },
                                showCharacterCounter = false,
                                value = state.accountNameHolder,
                                onValueChange = {
                                    onAction.invoke(
                                        PaymentOrderAction.OnAccountNameHolder(
                                            it,
                                        ),
                                    )
                                },
                                placeholder = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_name),
                                hintText = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_name_input),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        state.currentAccountDefaultDomain?.let {
                                            onAction.invoke(PaymentOrderAction.OnFocusAmountChange(true))
                                        }
                                        focusManager.clearFocus()
                                        keyboardController?.hide()
                                    },
                                ),
                                maxLength = TransferConstants.MaxLengthPaymentOrder.MAX_ACCOUNT_NAME_LENGTH,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        state.currentAccountDefaultDomain?.let { item ->
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = FDS.Sizer.Gap.gap8)
                                    .clip(
                                        RoundedCornerShape(AppSizer.Radius.radius32),
                                    )
                                    .background(FDS.Colors.backgroundBgContainer),
                            ) {
                                Row(
                                    modifier = Modifier
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .fillMaxWidth()
                                        .padding(
                                            top = FDS.Sizer.Gap.gap24,
                                            bottom = FDS.Sizer.Gap.gap16,
                                        ),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(FDS.Sizer.Icon.icon40)
                                            .clip(CircleShape)
                                            .background(FDS.Colors.gray50),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        Icon(
                                            painter = painterResource(R.drawable.ic_vietinbank),
                                            contentDescription = "ic_feature_maker_transfer_card",
                                            tint = Color.Unspecified,
                                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                        )
                                    }

                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

                                    Column(
                                        modifier = Modifier
                                            .weight(1f)
                                            .safeClickable {
                                                onAction.invoke(
                                                    PaymentOrderAction.CanShowBottomSheetChooseAccount(
                                                        isShow = true,
                                                        isOnlyTypeDDA = false,
                                                    ),
                                                )
                                            },
                                        verticalArrangement = Arrangement.SpaceBetween,
                                    ) {
                                        FoundationText(
                                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                        if (item.aliasName?.isEmpty() == false || item.accountName?.isEmpty() == false) {
                                            FoundationText(
                                                text = if (item.aliasName?.isEmpty() == true) {
                                                    item.accountName ?: ""
                                                } else {
                                                    item.aliasName ?: ""
                                                },
                                                color = FDS.Colors.characterHighlighted,
                                                style = FDS.Typography.bodyB2,
                                                modifier = Modifier.padding(
                                                    vertical = FDS.Sizer.Gap.gap4,
                                                ),
                                            )
                                        }
                                        FoundationText(
                                            text = item.accountNo ?: "",
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                        FoundationText(
                                            text = Utils.g().getDotMoneyHasCcy(
                                                (item.currentBalance ?: ""),
                                                item.currency ?: "",
                                            ),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                    }
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            PaymentOrderAction.CanShowBottomSheetChooseAccount(
                                                isShow = true,
                                                isOnlyTypeDDA = false,
                                            ),
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                                HorizontalDivider(
                                    color = FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )

                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                                Row(
                                    modifier = Modifier
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .fillMaxWidth()
                                        .padding(bottom = FDS.Sizer.Gap.gap16),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .weight(1f)
                                            .safeClickable {
                                                onAction.invoke(
                                                    PaymentOrderAction.CanShowBottomSheetChooseAccount(
                                                        isShow = true,
                                                        isOnlyTypeDDA = true,
                                                    ),
                                                )
                                            },
                                        verticalArrangement = Arrangement.SpaceBetween,
                                    ) {
                                        FoundationText(
                                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account_type_D),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                        val currentAccountDefaultDomainDDA =
                                            state.currentAccountDefaultDomainDDA
                                        if (currentAccountDefaultDomainDDA?.aliasName?.isEmpty() == false || currentAccountDefaultDomainDDA?.accountName?.isEmpty() == false) {
                                            FoundationText(
                                                text = if (currentAccountDefaultDomainDDA.aliasName?.isEmpty() == true) {
                                                    currentAccountDefaultDomainDDA.accountName ?: ""
                                                } else {
                                                    currentAccountDefaultDomainDDA.aliasName ?: ""
                                                },
                                                color = FDS.Colors.characterHighlighted,
                                                style = FDS.Typography.bodyB2,
                                                modifier = Modifier.padding(
                                                    vertical = FDS.Sizer.Gap.gap4,
                                                ),
                                            )
                                        }
                                        FoundationText(
                                            text = currentAccountDefaultDomainDDA?.accountNo ?: "",
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                    }
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            PaymentOrderAction.CanShowBottomSheetChooseAccountDDA(
                                                isShow = true,
                                            ),
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                                HorizontalDivider(
                                    color = FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )

                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))

                                Row(
                                    modifier = Modifier
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .fillMaxWidth()
                                        .padding(bottom = FDS.Sizer.Gap.gap16),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .weight(1f)
                                            .safeClickable {
                                                onAction.invoke(
                                                    PaymentOrderAction.CanShowBottomSheetChooseBranch(
                                                        isShow = true,
                                                    ),
                                                )
                                            },
                                        verticalArrangement = Arrangement.SpaceBetween,
                                    ) {
                                        FoundationText(
                                            text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_processing_branch),
                                            color = FDS.Colors.characterSecondary,
                                            style = FDS.Typography.captionCaptionL,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                        FoundationText(
                                            text = state.currentBranchDefaultDomain?.branchId + " - " + state.currentBranchDefaultDomain?.branchName,
                                            color = FDS.Colors.characterHighlighted,
                                            style = FDS.Typography.bodyB1,
                                            modifier = Modifier.padding(
                                                vertical = FDS.Sizer.Gap.gap4,
                                            ),
                                        )
                                    }
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            PaymentOrderAction.CanShowBottomSheetChooseBranch(
                                                isShow = true,
                                            ),
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                                HorizontalDivider(
                                    color = FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )

                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                                FoundationEditText(
                                    modifier = Modifier
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .focusRequester(state.focusRequesterAmount),
                                    onFocusChanged = { focused ->
                                        onAction.invoke(
                                            PaymentOrderAction.OnFocusAmountChange(
                                                focused,
                                            ),
                                        )
                                    },
                                    value = state.amountHolder,
                                    onValueChange = {
                                        onAction.invoke(
                                            PaymentOrderAction.OnAmountHolderChange(
                                                it,
                                            ),
                                        )
                                    },
                                    placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                                    hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount_hint),
                                    inputType = InputType.AMOUNT,
                                    currency = state.currentAccountDefaultDomain?.currency ?: "",
                                    maxLength = TransferConstants.MaxLengthPaymentOrder.MAX_AMOUNT_LENGTH,

                                    keyboardActions = KeyboardActions(
                                        onDone = {
                                            onAction.invoke(PaymentOrderAction.OnFocusContentChange(true))
                                            focusManager.clearFocus()
                                            keyboardController?.hide()
                                        },
                                    ),
                                )
                                if (state.amountHolderReader.isNotEmpty() && state.amountHolder.isNotEmpty()) {
                                    FoundationText(
                                        modifier = Modifier.padding(
                                            horizontal = FDS.Sizer.Gap.gap24,
                                        ),
                                        text = state.amountHolderReader,
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.captionCaptionL,
                                    )
                                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                                }
                                HorizontalDivider(
                                    color = if (state.isFocusedAmount) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))

                                FoundationEditText(
                                    typingVietnames = true,
                                    inputType = InputType.TEXT,
                                    modifier = Modifier
                                        .padding(
                                            horizontal = FDS.Sizer.Gap.gap24,
                                        )
                                        .focusRequester(state.focusRequesterContent),
                                    onFocusChanged = { focused ->
                                        onAction.invoke(
                                            PaymentOrderAction.OnFocusContentChange(
                                                focused,
                                            ),
                                        )
                                    },
                                    value = state.contentHolder,
                                    onValueChange = {
                                        onAction.invoke(
                                            PaymentOrderAction.OnContentHolderChange(
                                                it,
                                            ),
                                        )
                                    },
                                    placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content),
                                    hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_content_hint),
                                    maxLength = TransferConstants.MaxLengthPaymentOrder.MAX_CONTENT_LENGTH,
                                    keyboardActions = KeyboardActions(
                                        onDone = {
                                            focusManager.clearFocus()
                                            keyboardController?.hide()
                                        },
                                    ),
                                )

                                HorizontalDivider(
                                    color = if (state.isFocusedContent) FDS.Colors.characterHighlightedLighter else FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                                FoundationText(
                                    modifier = Modifier.padding(
                                        start = FDS.Sizer.Gap.gap24,
                                        end = FDS.Sizer.Gap.gap24,
                                    ),
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .safeClickable {
                                            onAction.invoke(
                                                PaymentOrderAction.CanShowBottomSheetTransferTime(
                                                    true,
                                                ),
                                            )
                                        },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = state.transferTime?.text ?: "",
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.bodyB1,
                                    )
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            PaymentOrderAction.CanShowBottomSheetTransferTime(true),
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                                if (state.showTransferTimeSchedule) {
                                    HorizontalDivider(color = FDS.Colors.divider, thickness = FDS.Sizer.Stroke.stroke1)
                                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                                    FoundationText(
                                        modifier = Modifier.padding(
                                            start = FDS.Sizer.Gap.gap24,
                                            end = FDS.Sizer.Gap.gap24,
                                        ),
                                        text = stringResource(com.vietinbank.core_ui.R.string.transaction_label_transfer_time),
                                        color = FDS.Colors.characterSecondary,
                                        style = FDS.Typography.captionCaptionL,
                                    )
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                                            .safeClickable { showDatePicker = true },
                                        verticalAlignment = Alignment.CenterVertically,
                                    ) {
                                        FoundationText(
                                            modifier = Modifier.weight(1f),
                                            text = state.transferTimeText,
                                            color = FDS.Colors.characterHighlighted,
                                            style = FDS.Typography.bodyB1,
                                        )
                                        IconButton(onClick = { showDatePicker = true }) {
                                            Icon(
                                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                                contentDescription = "ic_drop_down",
                                                tint = Color.Unspecified,
                                            )
                                        }
                                    }
                                }
                                HorizontalDivider(
                                    color = FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))
                                FoundationText(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),

                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .safeClickable {
                                            onAction.invoke(
                                                PaymentOrderAction.CanShowBottomSheetTransferType(
                                                    true,
                                                ),
                                            )
                                        },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = state.transferTypeFee?.text ?: "",
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.bodyB1,
                                    )
                                    IconButton(
                                        onClick = {
                                            onAction.invoke(
                                                PaymentOrderAction.CanShowBottomSheetTransferType(
                                                    true,
                                                ),
                                            )
                                        },
                                    ) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }

                                HorizontalDivider(
                                    color = FDS.Colors.divider,
                                    thickness = FDS.Sizer.Stroke.stroke1,
                                )
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap12))

                                FoundationText(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver),
                                    color = FDS.Colors.characterSecondary,
                                    style = FDS.Typography.captionCaptionL,
                                )

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                                        .safeClickable {
                                            if (state.nextApproversListString.isEmpty()) {
                                                onAction.invoke(
                                                    PaymentOrderAction.OnSwitchNextApproversChange(
                                                        true,
                                                    ),
                                                )
                                            } else {
                                                onAction.invoke(PaymentOrderAction.ShowBottomSheetNextApprovers)
                                            }
                                        },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = if (state.nextApproversListString.isEmpty()) {
                                            stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver_register)
                                        } else {
                                            state.nextApproversListString
                                        },
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.bodyB1,
                                    )

                                    IconButton(
                                        onClick = {
                                            if (state.nextApproversListString.isEmpty()) {
                                                onAction.invoke(PaymentOrderAction.OnSwitchNextApproversChange(true))
                                            } else {
                                                onAction.invoke(PaymentOrderAction.ShowBottomSheetNextApprovers)
                                            }
                                        },
                                    ) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                            contentDescription = "ic_drop_down",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                            if (state.imageBase64Object == null) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                                        .clip(
                                            RoundedCornerShape(AppSizer.Radius.radius32),
                                        )
                                        .background(FDS.Colors.backgroundBgContainer)
                                        .padding(
                                            vertical = FDS.Sizer.Gap.gap16,
                                            horizontal = FDS.Sizer.Gap.gap24,
                                        )
                                        .safeClickable {
                                            onAction.invoke(
                                                PaymentOrderAction.CanShowBottomSheetChooseTypeUpload(
                                                    true,
                                                ),
                                            )
                                        },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_upload_white),
                                        contentDescription = "ic_upload_white",
                                        tint = FDS.Colors.characterHighlighted,
                                    )
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file),
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.bodyB1,
                                    )
                                }
                            } else {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                                        .clip(
                                            RoundedCornerShape(AppSizer.Radius.radius32),
                                        )
                                        .background(FDS.Colors.backgroundBgContainer)
                                        .padding(
                                            vertical = FDS.Sizer.Gap.gap16,
                                            horizontal = FDS.Sizer.Gap.gap24,
                                        )
                                        .safeClickable {},
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_upload_file_done),
                                        contentDescription = "ic_upload_white",
                                        tint = Color.Unspecified,
                                    )
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                                    Column(modifier = Modifier.weight(1f)) {
                                        FoundationText(
                                            text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                                            color = FDS.Colors.characterHighlighted,
                                            style = FDS.Typography.bodyB1,
                                        )
                                        FoundationText(
                                            text = state.imageBase64Object?.fileName ?: "",
                                            color = FDS.Colors.characterHighlighted,
                                            style = FDS.Typography.bodyB1,
                                        )
                                    }

                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                                    IconButton(onClick = {
                                        onAction.invoke(
                                            PaymentOrderAction.OnDeleteFileUpload,
                                        )
                                    }) {
                                        Icon(
                                            painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_trash),
                                            contentDescription = "ic_upload_white",
                                            tint = Color.Unspecified,
                                        )
                                    }
                                }
                            }
                        } ?: run {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap8)
                                    .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                                    .background(FDS.Colors.backgroundBgContainer),
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(
                                            vertical = FDS.Sizer.Gap.gap32,
                                            horizontal = FDS.Sizer.Gap.gap24,
                                        )
                                        .safeClickable {
                                            onAction.invoke(
                                                PaymentOrderAction.GetAccountList,
                                            )
                                        },
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationText(
                                        modifier = Modifier.weight(1f),
                                        text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_main_account),
                                        color = FDS.Colors.gray600,
                                        style = FDS.Typography.bodyB1,
                                    )
                                    Icon(
                                        painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                        contentDescription = "ic_drop_down",
                                        tint = Color.Unspecified,
                                    )
                                }
                            }
                        }
                    }

                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .background(
                                Brush.verticalGradient(
                                    colors = listOf(
                                        FDS.Colors.dialogBackground.copy(alpha = 0f),
                                        FDS.Colors.dialogBackground.copy(alpha = 0.5f),
                                        FDS.Colors.dialogBackground.copy(alpha = 1f),
                                    ),
                                    startY = 0f,
                                    endY = Float.POSITIVE_INFINITY,
                                ),
                            )
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                                vertical = FDS.Sizer.Gap.gap8,
                            ),
                    ) {
                        FoundationButton(
                            isLightButton = true,
                            text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                            onClick = { onAction.invoke(PaymentOrderAction.OnClickContinue) },
                            enabled = true,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
                LaunchedEffect(
                    state.isFocusBankNameHolder,
                    state.isFocusBeneficiaryAccountHolder,
                    state.isFocusAccountNameHolder,
                    state.isFocusedAmount,
                    state.isFocusedContent,
                ) {
                    when {
                        state.isFocusBankNameHolder -> {
                            state.focusRequesterBankName.requestFocus()
                        }

                        state.isFocusBeneficiaryAccountHolder -> {
                            state.focusRequesterAccountNumber.requestFocus()
                        }

                        state.isFocusAccountNameHolder -> {
                            state.focusRequesterAccountName.requestFocus()
                        }

                        state.isFocusedAmount -> {
                            state.focusRequesterAmount.requestFocus()
                        }
                        state.isFocusedContent -> {
                            state.focusRequesterContent.requestFocus()
                        }

                        else -> {
                            focusManager.clearFocus()
                        }
                    }
                }
            } else if (TAB_SAVED == state.selectedIndex) {
                ContactSavedScreen(
                    imageLoader = imageLoader,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer),

                    onSearchContactText = {
                        onAction.invoke(PaymentOrderAction.OnSearchContactText(it))
                    },
                    onSelectedTabIndex = {
                        onAction.invoke(PaymentOrderAction.OnSelectedTabContactIndex(it))
                    },
                    onClickItemContact = {
                        onAction.invoke(PaymentOrderAction.ClickContactItem(it))
                    },
                    onClickItemTemp = {
                        onAction.invoke(PaymentOrderAction.ClickTempItem(it))
                    },
                    onClickEditItemContact = {
                        // todo hỏi api để update
//                        onAction.invoke(MakerTransferAccountAction.MapFieldContactToBank(it))
                    },
                    contactSavedBottomSheetUiState = state.contactSavedBottomSheetUiState,
                )
            } else {
                RecentTransactionScreen(
                    imageLoader = imageLoader,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer),
                    searchRecentText = state.searchRecentText,
                    onSearchRecentText = {
                        onAction.invoke(
                            PaymentOrderAction.OnSearchRecentText(
                                it,
                            ),
                        )
                    },
                    listLatest = state.transactionInfoList.toMutableList(),
                    onRecentItemClick = {
                        onAction.invoke(PaymentOrderAction.ClickRecentItem(it))
                    },
                    isLoadingListRecent = state.isLoadingListRecent,
                )
            }

            ChooseAccountBottomSheet(
                isVisible = state.canShowBottomSheetChooseAccount,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_choose_main_account),
                onItemSelected = { selectedItem ->
                    onAction.invoke(
                        PaymentOrderAction.OnCurrentAccountDataChange(
                            accountDefaultDomain = selectedItem,
                        ),
                    )
                },
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.CanShowBottomSheetChooseAccount(
                            isShow = false,
                            isOnlyTypeDDA = false,
                        ),
                    )
                },
                onSearchAccountText = {
                    onAction.invoke(
                        PaymentOrderAction.OnSearchAccountText(
                            searchAccountText = it,
                        ),
                    )
                },
                chooseAccountBottomSheetUiState = state.chooseAccountBottomSheetUiState,
            )
            ChooseAccountBottomSheet(
                isVisible = state.canShowBottomSheetChooseAccountDDA,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_choose_account_type_D),
                onItemSelected = { selectedItem ->
                    onAction.invoke(
                        PaymentOrderAction.OnCurrentAccountDDADataChange(
                            accountDefaultDomain = selectedItem,
                        ),
                    )
                },
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.CanShowBottomSheetChooseAccountDDA(
                            isShow = false,
                        ),
                    )
                },
                onSearchAccountText = {
                    onAction.invoke(
                        PaymentOrderAction.OnSearchAccountText(
                            searchAccountText = it,
                        ),
                    )
                },
                chooseAccountBottomSheetUiState = state.chooseAccountBottomSheetUiState,
            )

            BranchBottomSheet(
                isVisible = state.canShowBottomSheetChooseBranch,
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.CanShowBottomSheetChooseBranch(
                            isShow = false,
                        ),
                    )
                },
                branchBottomSheetUiState = state.branchBottomSheetUiState,
                onItemBranchSelected = {
                    onAction.invoke(
                        PaymentOrderAction.OnCurrentBranchDataChange(
                            it,
                        ),
                    )
                },
                onSearchBranchText = {
                    onAction.invoke(
                        PaymentOrderAction.OnSearchTextBranchText(
                            searchTextBranch = it,
                        ),
                    )
                },
            )
            RadioBottomSheet(
                isVisible = state.canShowBottomSheetTransferTime,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_transfer),
                onItemSelected = { selectedItem ->
                    state.radioBottomSheetTransferTimeUiState.items =
                        state.radioBottomSheetTransferTimeUiState.items.map { item ->
                            item.copy(isSelected = item.id == selectedItem.id)
                        } as MutableList<BottomSheetItem>
                    if (TransferConstants.Transfer.ID_TRANSFER_TIME_NOW == selectedItem.id) {
                        onAction.invoke(
                            PaymentOrderAction.OnChooseTransferTime(
                                selectedItem,
                            ),
                        )
                        onAction.invoke(
                            PaymentOrderAction.ShowTransferTimeSchedule(
                                show = false,
                            ),
                        )
                    } else {
                        showDatePicker = true
                    }
                    onAction.invoke(PaymentOrderAction.CanShowBottomSheetTransferTime(false))
                },
                onDismiss = {
                    onAction.invoke(PaymentOrderAction.CanShowBottomSheetTransferTime(false))
                },
                radioBottomSheetUiState = state.radioBottomSheetTransferTimeUiState,
            )
            if (showDatePicker) {
                DatePickerPaymentOrderDialog(
                    showDatePicker = true,
                    onDateSelected = { selectedDate ->
                        showDatePicker = false
                        state.radioBottomSheetTransferTimeUiState.items[1].let {
                            onAction.invoke(
                                PaymentOrderAction.OnTransferDateTextChange(
                                    selectedDate,
                                ),
                            )
                            onAction.invoke(
                                PaymentOrderAction.CanShowBottomSheetTransferHour(
                                    true,
                                ),
                            )
                        }
                    },
                    onDismiss = {
                        if (state.transferTimeText.isEmpty()) {
                            state.radioBottomSheetTransferTimeUiState.items =
                                state.radioBottomSheetTransferTimeUiState.items.map { item ->
                                    item.copy(isSelected = item.id == TransferConstants.Transfer.ID_TRANSFER_TIME_NOW)
                                } as MutableList<BottomSheetItem>
                            onAction.invoke(
                                PaymentOrderAction.ShowTransferTimeSchedule(
                                    show = false,
                                ),
                            )
                        }
                        showDatePicker = false
                    },
                    isAvailableToday = state.isAvailableToday,
                )
            }

            HourPickerBottomSheet(
                isVisible = state.canShowBottomSheetTransferHour,
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.CanShowBottomSheetTransferHour(
                            false,
                        ),
                    )
                },
                onItemHourSelected = {
                    onAction.invoke(
                        PaymentOrderAction.OnTransferTimeTextChange(
                            it,
                        ),
                    )
                },
                hourPickerBottomSheetUiState = state.hourPickerBottomSheetUiState,
            )
            RadioBottomSheet(
                isVisible = state.canShowBottomSheetTransferType,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_type_fee),
                onItemSelected = { selectedItem ->
                    state.radioBottomSheetTransferTypeFeeUiState.items =
                        state.radioBottomSheetTransferTypeFeeUiState.items.map { item ->
                            item.copy(isSelected = item.id == selectedItem.id)
                        } as MutableList<BottomSheetItem>
                    onAction.invoke(
                        PaymentOrderAction.OnChooseTransferType(
                            selectedItem,
                        ),
                    )
                    onAction.invoke(PaymentOrderAction.CanShowBottomSheetTransferType(false))
                },
                onDismiss = {
                    onAction.invoke(PaymentOrderAction.CanShowBottomSheetTransferType(false))
                },
                radioBottomSheetUiState = state.radioBottomSheetTransferTypeFeeUiState,

            )
            NextApproverBottomSheet(
                isVisible = state.canShowBottomSheetNextApprovers == true,
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_next_approver),
                onItemSelected = { approver ->
                    onAction.invoke(PaymentOrderAction.OnClickItemApprove(approver))
                },

                onConfirm = {
                    onAction.invoke(
                        PaymentOrderAction.DismissShowBottomSheetNextApprovers(
                            isConfirm = true,
                        ),
                    )
                },
                onCancel = {
                    onAction.invoke(
                        PaymentOrderAction.DismissShowBottomSheetNextApprovers(
                            isConfirm = false,
                        ),
                    )
                },
                onClickAllItemSelected = {
                    onAction.invoke(PaymentOrderAction.OnClickAllItemApprove(isAllItemSelected = !state.nextApproverBottomSheetUiState.onAllItemApproverSelected))
                },
                nextApproverBottomSheetUiState = state.nextApproverBottomSheetUiState,
            )
            ChooseTypeUploadBottomSheet(
                isVisible = state.canShowBottomSheetChooseTypeUpload,
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.CanShowBottomSheetChooseTypeUpload(
                            false,
                        ),
                    )
                },
                onClickGotoGallery = {
                    onAction.invoke(PaymentOrderAction.OnClickGotoGallery)
                },
                onClickGotoUploadFile = {
                    onAction.invoke(PaymentOrderAction.OnClickGotoUploadFile)
                },
            )
            ContactSavedBottomSheet(
                isVisible = state.canShowBottomSheetContactSaved == true,
                onDismiss = {
                    onAction.invoke(
                        PaymentOrderAction.OnClickContactBottomSheet(
                            canShowBottomSheetContactSaved = false,
                        ),
                    )
                },
                imageLoader = imageLoader,
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.9f)
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
                onSearchContactText = { onAction.invoke(PaymentOrderAction.OnSearchContactText(it)) },
                onSelectedTabIndex = {
                    onAction.invoke(PaymentOrderAction.OnSelectedTabContactIndex(it))
                },
                onClickItemContact = { onAction.invoke(PaymentOrderAction.ClickContactItem(it)) },
                onClickItemTemp = { onAction.invoke(PaymentOrderAction.ClickTempItem(it)) },
                contactSavedBottomSheetUiState = state.contactSavedBottomSheetUiState,
            )
        }
    }
}
