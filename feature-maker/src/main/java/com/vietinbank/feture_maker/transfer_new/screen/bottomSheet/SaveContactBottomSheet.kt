package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.SaveContactBottomSheetUiState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SaveContactBottomSheet(
    isVisible: Boolean,
    onCancel: () -> Unit,
    onConfirm: (String) -> Unit,
    imageLoader: CoilImageLoader? = null,
    saveContactBottomSheetUiState: SaveContactBottomSheetUiState,
) {
    var customerCode by remember { mutableStateOf("") }
    val scrollState = rememberScrollState()
    val coroutineScope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            containerColor = Color.Transparent,
            onDismissRequest = {
                onCancel()
                coroutineScope.launch {
                    delay(50)
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
            },
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(AppSizer.Radius.radius32)),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth().verticalScroll(scrollState),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = FDS.Colors.backgroundBgContainer,
                                shape = RoundedCornerShape(
                                    FDS.Sizer.Gap.gap32,

                                ),
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        // Title
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_account_result_save_contact),
                            style = FDS.Typography.headingH3,
                            color = FDS.Colors.characterHighlighted,
                            modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24),
                        )

                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        if (saveContactBottomSheetUiState.bankItem != null) {
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = FDS.Sizer.Gap.gap16)
                                    .border(
                                        width = FDS.Sizer.Stroke.stroke1,
                                        color = FDS.Colors.strokeDivider,
                                        shape = RoundedCornerShape(FDS.Sizer.Gap.gap16),
                                    )
                                    .padding(FDS.Sizer.Gap.gap16),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(FDS.Sizer.Icon.icon40)
                                        .clip(CircleShape)
                                        .background(FDS.Colors.gray50),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    imageLoader?.LoadUrl(
                                        url = saveContactBottomSheetUiState.bankItem?.icon ?: "",
                                        isCache = true,
                                        placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                        errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                    )
                                }
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                                // Text content
                                Column {
                                    FoundationText(
                                        text = saveContactBottomSheetUiState.accountName,
                                        color = FDS.Colors.characterPrimary,
                                        style = FDS.Typography.bodyB2Emphasized,
                                    )
                                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))

                                    FoundationText(
                                        text = (saveContactBottomSheetUiState.bankItem?.shortName ?: "") + " - " + (
                                            saveContactBottomSheetUiState.accountNo
                                            ),
                                        color = FDS.Colors.characterSecondary,
                                        style = FDS.Typography.captionCaptionL,
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        FoundationEditText(
                            modifier = Modifier.padding(
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                            value = customerCode,
                            onValueChange = { newValue ->
                                customerCode = newValue
                            },
                            placeholder = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            hintText = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_contact_input),
                            maxLength = 146,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        if (saveContactBottomSheetUiState.isEdit) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_trash),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                )
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                                FoundationText(
                                    text = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_saved_delete_contact),
                                    color = FDS.Colors.stateError,
                                    style = FDS.Typography.interactionSmallButton,
                                )
                            }
                            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        }
                    }
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16),
            ) {
                FoundationButton(
                    isLightButton = false,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_back),
                    onClick = {
                        onCancel.invoke()
                    },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.dialog_button_confirm),
                    onClick = { onConfirm.invoke(customerCode) },
                    enabled = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap4,
                        ),
                )
            }
        }
    }
}