package com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.R
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.TransactionInfo
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.GetPaymentTemplateListParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.maker.TempTransactionParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.KEY_TRANSFER_TYPE_ACCOUNT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_RECENT
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TAB_SAVED
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Transfer.TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ContactSavedBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.SaveContactBottomSheetUiState
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_dashboard.MakerTransferViewModel.MakerTransferAccountEvent.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MakerTransferViewModel @Inject constructor(
    private val transferUseCase: TransferUseCase,
    val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val homeUseCase: HomeUseCase,
) : BaseViewModel() {

    private val _state = MutableStateFlow(MakerTransferAccountState())
    val state: StateFlow<MakerTransferAccountState> = _state.asStateFlow()

    private val _events = Channel<MakerTransferAccountEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val originalBankList: MutableList<DataBankDomain> = mutableListOf()
    private val originalContactList: MutableList<ContactDomains> = mutableListOf()
    private val originalTempTransactionList: MutableList<TempTransactionDomains> = mutableListOf()
    private val originalTransactionInfoList: MutableList<TransactionInfo> = mutableListOf()

    private fun getListLatest() {
        updateIsLoadingListRecent(true)
        if (transferCacheManager.getLatTestList()?.isNotEmpty() == true) {
            val listLasted = transferCacheManager.getLatTestList()
                ?.filter {
                    Tags.TransferType.TYPE_IN == it.trxType ||
                        (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trxType) ||
                        (Tags.TransferType.TYPE_OUT == it.trxType)
                }
                ?.toMutableList() ?: mutableListOf()
            updateTransactionInfoList(
                listLasted,
            )
            originalTransactionInfoList.clear()
            originalTransactionInfoList.addAll(listLasted)
            updateIsLoadingListRecent(false)
            return
        }
        launchJob(showLoading = true) {
            val res = homeUseCase.listLatest(
                ListLatestParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    status = Tags.HOME_LIST_LATEST_PARAMS_STATUS,
                    pageSize = TRANSFER_LIST_LATEST_PARAMS_PAGE_SIZE,
                    pageNum = Tags.HOME_LIST_LATEST_PARAMS_PAGE_NUM,
                ),
            )
            handleResource(res) { data ->
                transferCacheManager.saveLatestList(
                    data.transactionInfoList?.toMutableList() ?: mutableListOf(),
                )
                val listLasted = data.transactionInfoList
                    ?.filter {
                        Tags.TransferType.TYPE_IN == it.trxType ||
                            (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trxType) ||
                            (Tags.TransferType.TYPE_OUT == it.trxType)
                    }
                    ?.toMutableList() ?: mutableListOf()

                updateTransactionInfoList(listLasted)
                originalTransactionInfoList.clear()
                originalTransactionInfoList.addAll(listLasted)
                updateIsLoadingListRecent(false)
            }
        }
    }

    private fun getPaymentTemplateList() {
        updateIsLoadingListTemp(true)
        if (transferCacheManager.getTempList()?.isNotEmpty() == true) {
            val tempList = transferCacheManager.getTempList()?.filter {
                Tags.TransferType.TYPE_IN == it.tranType ||
                    (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.tranType && it.toBankCode?.isNotEmpty() == true) ||
                    (Tags.TransferType.TYPE_OUT == it.tranType && it.toBankCode?.isNotEmpty() == true)
            }?.toMutableList() ?: mutableListOf()
            updateListTempDomains(tempList)
            originalTempTransactionList.clear()
            originalTempTransactionList.addAll(tempList)
            updateIsLoadingListTemp(false)
            return
        }
        launchJob(showLoading = true) {
            val params = GetPaymentTemplateListParams(
                TempTransactionParams(tranType = "all"),
                username = userProf.getUserName() ?: "",
            )
            val res = transferUseCase.getPaymentTemplateList(params)
            handleResource(res) { data ->
                transferCacheManager.saveTempList(data.tempTransactionList)
                val tempList = data.tempTransactionList.filter {
                    Tags.TransferType.TYPE_IN == it.tranType ||
                        (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.tranType && it.toBankCode?.isNotEmpty() == true) ||
                        (Tags.TransferType.TYPE_OUT == it.tranType && it.toBankCode?.isNotEmpty() == true)
                }.toMutableList()
                updateListTempDomains(tempList)
                originalTempTransactionList.clear()
                originalTempTransactionList.addAll(tempList)
                updateIsLoadingListTemp(false)
            }
        }
    }
    private fun updateIsTransferTypeKeyContactSaveBottomSheet() {
        _state.update { currentState ->
            val value =
                currentState.contactSavedBottomSheetUiState.copy(
                    isTransferTypeKey = KEY_TRANSFER_TYPE_ACCOUNT,
                    contentEmptyState = com.vietinbank.core_ui.R.string.maker_transfer_tab_saved_contact_list_null_content,
                )
            currentState.copy(
                contactSavedBottomSheetUiState = value,
            )
        }
    }
    private fun getContactList() {
        updateIsLoadingListContact(true)
        if (transferCacheManager.getContactList()?.isNotEmpty() == true) {
            val contactList = transferCacheManager.getContactList()?.filter {
                Tags.TransferType.TYPE_IN == it.trantype ||
                    (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trantype && it.bank?.isNotEmpty() == true) ||
                    (Tags.TransferType.TYPE_OUT == it.trantype && it.bank?.isNotEmpty() == true)
            }?.toMutableList() ?: mutableListOf()
            updateListContactDomains(contactList)
            originalContactList.clear()
            originalContactList.addAll(contactList)
            updateIsLoadingListContact(false)
            updateIsTransferTypeKeyContactSaveBottomSheet()

            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                transferCacheManager.saveContactist(data.contacts)

                val contactList = data.contacts.filter {
                    Tags.TransferType.TYPE_IN == it.trantype ||
                        (Tags.TransferType.TYPE_NAPAS_ACCOUNT == it.trantype && it.bank?.isNotEmpty() == true) ||
                        (Tags.TransferType.TYPE_OUT == it.trantype && it.bank?.isNotEmpty() == true)
                }.toMutableList()
                updateListContactDomains(contactList)
                originalContactList.clear()
                originalContactList.addAll(contactList)
                updateIsLoadingListContact(false)
                updateIsTransferTypeKeyContactSaveBottomSheet()
            }
        }
    }

    fun getNapasBankList() {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            updateListDataBanks(
                transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf(),
            )
            originalBankList.clear()
            originalBankList.addAll(transferCacheManager.getBankList()?.toMutableList() ?: mutableListOf())
            return
        }
        launchJob(showLoading = true) {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                originalBankList.clear()
                originalBankList.addAll(data.dataBanks)
                updateListDataBanks(data.dataBanks)
                transferCacheManager.saveBankList(data.dataBanks)
            }
        }
    }

    private fun updateListDataBanks(listDataBanks: MutableList<DataBankDomain>) {
        _state.update { currentState ->
            currentState.copy(
                listDataBanks = listDataBanks,
            )
        }
    }
    private fun updateSearchTextBank(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchTextBank = value,
            )
        }
    }

    private fun onSelectedIndexChange(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                selectedIndex = value,
            )
        }
    }

    private fun updateListContactDomains(value: MutableList<ContactDomains>) {
        _state.update { currentState ->
            val listContactDomains =
                currentState.contactSavedBottomSheetUiState.copy(isEdited = false, listContactDomains = value)
            currentState.copy(
                contactSavedBottomSheetUiState = listContactDomains,
            )
        }
    }
    private fun updateIsLoadingListContact(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListContact =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListContact = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListContact,
            )
        }
    }
    private fun updateIsLoadingListTemp(value: Boolean) {
        _state.update { currentState ->
            val isLoadingListTemp =
                currentState.contactSavedBottomSheetUiState.copy(isLoadingListTemp = value)
            currentState.copy(
                contactSavedBottomSheetUiState = isLoadingListTemp,
            )
        }
    }
    private fun updateIsLoadingListRecent(value: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                isLoadingListRecent = value,
            )
        }
    }
    private fun updateListTempDomains(value: MutableList<TempTransactionDomains>) {
        _state.update { currentState ->
            val listTempDomains =
                currentState.contactSavedBottomSheetUiState.copy(listTempDomains = value)
            currentState.copy(
                contactSavedBottomSheetUiState = listTempDomains,
            )
        }
    }

    private fun updateTransactionInfoList(value: MutableList<TransactionInfo>) {
        _state.update { currentState ->
            currentState.copy(
                transactionInfoList = value,
            )
        }
    }

    private fun updateSearchContactText(value: String) {
        _state.update { currentState ->
            val searchContactText =
                currentState.contactSavedBottomSheetUiState.copy(searchContactText = value)
            currentState.copy(
                contactSavedBottomSheetUiState = searchContactText,
            )
        }
    }

    private fun updateSearchRecentText(value: String) {
        _state.update { currentState ->
            currentState.copy(
                searchRecentText = value,
            )
        }
    }

    private fun updateSelectedTabContactIndex(value: Int) {
        _state.update { currentState ->
            val selectedTabContactIndex =
                currentState.contactSavedBottomSheetUiState.copy(selectedTabIndex = value)
            currentState.copy(
                contactSavedBottomSheetUiState = selectedTabContactIndex,
            )
        }
    }

    private fun canShowBottomSheetSaveContact(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBottomSheetSaveContact = show,
            )
        }
    }
    private fun updateBankItem(value: DataBankDomain) {
        _state.update { currentState ->
            val bankItem =
                currentState.saveContactBottomSheetUiState.copy(bankItem = value, isEdit = true)
            currentState.copy(
                saveContactBottomSheetUiState = bankItem,
            )
        }
    }

    private fun updateAccountNoContact(value: String) {
        _state.update { currentState ->
            val accountNo =
                currentState.saveContactBottomSheetUiState.copy(accountNo = value)
            currentState.copy(
                saveContactBottomSheetUiState = accountNo,
            )
        }
    }
    private fun updateAccountNameContact(value: String) {
        _state.update { currentState ->
            val accountName =
                currentState.saveContactBottomSheetUiState.copy(accountName = value)
            currentState.copy(
                saveContactBottomSheetUiState = accountName,
            )
        }
    }
    private fun mapMapFieldContactToBank(contactDomains: ContactDomains) {
        val bankItem = DataBankDomain(
            binCode = "",
            bankName = "",
            shortName = "Text", //
            ebankCode = "",
            icon = contactDomains.iconUrl ?: "", //
            name = "",
            type = "",
            status = "",
        )
        updateBankItem(bankItem)
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
                Constants.MB_CONTACT_LIST,
                -> { updateIsLoadingListContact(false) }
                Constants.MB_GET_PAYMENT_TEMPLATE_LIST,
                -> { updateIsLoadingListTemp(false) }
                Constants.MB_LIST_LATEST,
                -> { updateIsLoadingListRecent(false) }
            }
        }
        super.onDisplayErrorMessage(exception)
    }
    fun onAction(action: MakerTransferAccountAction) {
        when (action) {
            is MakerTransferAccountAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(NavigateBack)
                }
            }

            is MakerTransferAccountAction.OnSearchTextBankChange -> {
                updateSearchTextBank(action.searchTextBank)

                val searchText = action.searchTextBank.trim()
                val filteredBanks = if (searchText.isBlank()) {
                    originalBankList
                } else {
                    originalBankList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.shortName?.contains(searchText, ignoreCase = true) == true ||
                            bank.name?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListDataBanks(filteredBanks.toMutableList())
            }

            is MakerTransferAccountAction.ClickBankItem -> {
                viewModelScope.launch {
                    _events.send(ClickBankItem(action.bank))
                }
            }

            is MakerTransferAccountAction.OnSelectedIndex -> {
                onSelectedIndexChange(action.selectedIndex)
                if (TAB_SAVED == action.selectedIndex) {
                    getContactList()
                    getPaymentTemplateList()
                } else if (TAB_RECENT == action.selectedIndex) {
                    getListLatest()
                }
            }

            is MakerTransferAccountAction.OnSearchContactText -> {
                updateSearchContactText(action.searchContactText)
                val searchText = action.searchContactText.trim()
                val filteredContacts = if (searchText.isBlank()) {
                    originalContactList
                } else {
                    originalContactList.filter { bank ->
                        bank.bankName?.contains(searchText, ignoreCase = true) == true ||
                            bank.account?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeename?.contains(searchText, ignoreCase = true) == true ||
                            bank.payeenickname?.contains(searchText, ignoreCase = true) == true ||
                            bank.cardnumber?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListContactDomains(filteredContacts.toMutableList())

                val filteredTemps = if (searchText.isBlank()) {
                    originalTempTransactionList
                } else {
                    originalTempTransactionList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.content?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateListTempDomains(filteredTemps.toMutableList())
            }

            is MakerTransferAccountAction.OnSelectedTabContactIndex -> {
                updateSelectedTabContactIndex(action.selectedTabIndex)
            }

            is MakerTransferAccountAction.OnSearchRecentText -> {
                updateSearchRecentText(action.searchRecentText)
                val searchText = action.searchRecentText.trim()
                val filteredTemps = if (searchText.isBlank()) {
                    originalTransactionInfoList
                } else {
                    originalTransactionInfoList.filter { bank ->
                        bank.toAccountName?.contains(searchText, ignoreCase = true) == true ||
                            bank.toAccount?.contains(searchText, ignoreCase = true) == true ||
                            bank.bankName?.contains(searchText, ignoreCase = true) == true
                    }
                }
                updateTransactionInfoList(filteredTemps.toMutableList())
            }

            is MakerTransferAccountAction.ClickContactItem -> {
                viewModelScope.launch {
                    _events.send(ClickContactItem(action.contactItem))
                }
            }

            is MakerTransferAccountAction.ClickTempItem -> {
                viewModelScope.launch {
                    _events.send(ClickTempItem(action.tempTransactionItem))
                }
            }
            is MakerTransferAccountAction.ClickRecentItem -> {
                viewModelScope.launch {
                    _events.send(ClickRecentItem(action.transactionInfo))
                }
            }

            is MakerTransferAccountAction.ClickTransferCard -> {
                viewModelScope.launch {
                    _events.send(ClickTransferCard)
                }
            }

            is MakerTransferAccountAction.OnShowSaveContactBottomSheet -> {
                canShowBottomSheetSaveContact(action.canShowBottomSheetSaveContact)
            }
            is MakerTransferAccountAction.MapFieldContactToBank -> {
                updateAccountNoContact(value = action.contactDomains.account ?: "")
                if (Tags.TransferType.TYPE_IN == action.contactDomains.trantype || Tags.TransferType.TYPE_NAPAS_ACCOUNT == action.contactDomains.trantype) {
                    updateAccountNameContact(value = action.contactDomains.payeename ?: "")
                } else {
                    updateAccountNameContact(value = action.contactDomains.cardnumber ?: "")
                }
                mapMapFieldContactToBank(contactDomains = action.contactDomains)
                canShowBottomSheetSaveContact(true)
            }
        }
    }

    sealed class MakerTransferAccountEvent {
        data object NavigateBack : MakerTransferAccountEvent()
        data class ClickBankItem(val bank: DataBankDomain) : MakerTransferAccountEvent()
        data class ClickContactItem(val contactItem: ContactDomains) : MakerTransferAccountEvent()
        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) : MakerTransferAccountEvent()
        data class ClickRecentItem(val transactionInfo: TransactionInfo) : MakerTransferAccountEvent()
        data object ClickTransferCard : MakerTransferAccountEvent()
    }

    sealed class MakerTransferAccountAction {
        data object OnBackPressed : MakerTransferAccountAction()
        data class ClickBankItem(val bank: DataBankDomain) : MakerTransferAccountAction()
        data class ClickContactItem(val contactItem: ContactDomains) : MakerTransferAccountAction()
        data class ClickTempItem(val tempTransactionItem: TempTransactionDomains) : MakerTransferAccountAction()
        data class ClickRecentItem(val transactionInfo: TransactionInfo) : MakerTransferAccountAction()
        data class OnSearchTextBankChange(val searchTextBank: String) : MakerTransferAccountAction()
        data class OnSelectedIndex(val selectedIndex: Int) : MakerTransferAccountAction()
        data class OnSearchContactText(val searchContactText: String) : MakerTransferAccountAction()
        data class OnSearchRecentText(val searchRecentText: String) : MakerTransferAccountAction()
        data class OnSelectedTabContactIndex(val selectedTabIndex: Int) : MakerTransferAccountAction()
        data object ClickTransferCard : MakerTransferAccountAction()
        data class OnShowSaveContactBottomSheet(val canShowBottomSheetSaveContact: Boolean) :
            MakerTransferAccountAction()
        data class MapFieldContactToBank(val contactDomains: ContactDomains) :
            MakerTransferAccountAction()
    }

    data class MakerTransferAccountState(
        val listDataBanks: MutableList<DataBankDomain> = mutableListOf(),
        var searchTextBank: String = "",
        var selectedIndex: Int = 0,
        val transactionInfoList: MutableList<TransactionInfo> = mutableListOf(),
        val searchRecentText: String = "",
        val isLoadingListRecent: Boolean = true,
        val canShowBottomSheetSaveContact: Boolean = false,
        var contactSavedBottomSheetUiState: ContactSavedBottomSheetUiState = ContactSavedBottomSheetUiState(),
        var saveContactBottomSheetUiState: SaveContactBottomSheetUiState = SaveContactBottomSheetUiState(),
    )
}