package com.vietinbank.feture_maker.transfer_new.screen.bottomSheet

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feture_maker.transfer_new.screen.contact_template_recent.ContactSavedScreen
import com.vietinbank.feture_maker.transfer_new.screen.maker_transfer_bank_choosed.ContactSavedBottomSheetUiState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactSavedBottomSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    imageLoader: CoilImageLoader,
    modifier: Modifier,
    onSearchContactText: (searchContactText: String) -> Unit,
    onSelectedTabIndex: (selectedTabIndex: Int) -> Unit,
    onClickItemContact: (selectedTabIndex: ContactDomains) -> Unit,
    onClickItemTemp: (selectedTabIndex: TempTransactionDomains) -> Unit,
    contactSavedBottomSheetUiState: ContactSavedBottomSheetUiState,
) {
    if (isVisible) {
        BaseBottomSheet(
            visible = true,
            onDismissRequest = onDismiss,
            containerColor = FoundationDesignSystem.Colors.backgroundBgContainer,
            containerPadding = PaddingValues(
                horizontal = 0.dp,
                vertical = 0.dp,
            ),
        ) {
            ContactSavedScreen(
                imageLoader = imageLoader,
                modifier = modifier,
                onSearchContactText = onSearchContactText,
                onSelectedTabIndex = onSelectedTabIndex,
                onClickItemContact = onClickItemContact,
                onClickItemTemp = onClickItemTemp,
                onClickEditItemContact = {},
                contactSavedBottomSheetUiState = contactSavedBottomSheetUiState,
            )
        }
    }
}