package com.vietinbank.feture_maker.transfer_new.screen.confirm

import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.google.gson.JsonSyntaxException
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feture_maker.transfer_new.screen.constant.FileOpenerHelper
import com.vietinbank.feture_maker.transfer_new.screen.constant.TransferConstants.Bundle.UTF8
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject

@AndroidEntryPoint
class MakerTransferConfirmFragment : BaseFragment<MakerTransferConfirmViewModel>() {
    override val viewModel: MakerTransferConfirmViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader
    private lateinit var fileOpener: FileOpenerHelper

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fileOpener = FileOpenerHelper(this)
        initData()
        observeEvents()
    }

    private fun initData() {
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    MakerTransferConfirmViewModel.MakerTransferConfirmEvent.NavigateBack -> {
                        appNavigator.popBackStack()
                    }

                    is MakerTransferConfirmViewModel.MakerTransferConfirmEvent.OnConfirmPressed -> {
                        try {
                            val resultItemString =
                                Utils.g().provideGson().toJson(event.createTransferDomain)
                            val encodedResultItem = URLEncoder.encode(resultItemString, UTF8)
                            val encodedConfirmItem = URLEncoder.encode(event.stringDomain, UTF8)

                            appNavigator.goToMakerTransferResult(
                                typeTransfer = event.keyTransferType,
                                confirmItem = encodedConfirmItem,
                                resultItem = encodedResultItem,
                            )
                        } catch (_: JsonSyntaxException) {
                        }
                    }

                    is MakerTransferConfirmViewModel.MakerTransferConfirmEvent.OnClickedItem -> {
                        val uri = event.uriFile.toUri()
                        val mimeType = requireContext().contentResolver.getType(uri)
                        onOpenFileClicked(uri, mimeType)
                    }
                }
            }
        }
    }

    private fun onOpenFileClicked(uri: Uri, mimeType: String? = null) {
        fileOpener.openFile(requireContext(), uri, mimeType)
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            MakerTransferConfirmScreen(
                state = uiState,
                onAction = viewModel::onAction,
                imageLoader = imageLoader,
            )
        }
    }
}