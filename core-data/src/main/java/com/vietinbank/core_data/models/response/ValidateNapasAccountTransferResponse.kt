package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName

data class ValidateNapasAccountTransferResponse(
    @SerializedName("amount") val amount: String,
    @SerializedName("binCode") val binCode: String,
    @SerializedName("currency") val currency: String,
    @SerializedName("feeAmt") val feeAmt: String,
    @SerializedName("feeCode") val feeCode: String,
    @SerializedName("feePayMethod") val feePayMethod: String,
    @SerializedName("feeVat") val feeVat: String,
    @SerializedName("totalFee") val totalFee: String,
    @SerializedName("fromAcctNo") val fromAcctNo: String,
    @SerializedName("isQRTransfer") val isQRTransfer: String,
    @SerializedName("processDate") val processDate: String,
    @SerializedName("remark") val remark: String,
    @SerializedName("sendBank") val sendBank: String,
    @SerializedName("toAcctName") val toAcctName: String,
    @SerializedName("toAcctNo") val toAcctNo: String,
    @SerializedName("username") val username: String,
    @SerializedName("toBankName") val toBankName: String,
    @SerializedName("trxType") val trxType: String,
    @SerializedName("nextApprovers") val nextApprovers: List<ApproverDomains>,
    @SerializedName("isSplit") val isSplit: String,
    @SerializedName("splitMin") val splitMin: String,
    @SerializedName("splitMax") val splitMax: String,
    @SerializedName("subSplitTransAmount") val subSplitTransAmount: String,
    @SerializedName("subTransactionCount") val subTransactionCount: String,
    @SerializedName("subTransactionsRemainderCount") val subTransactionsRemainderCount: String,
    @SerializedName("subTransactionsRemainderAmount") val subTransactionsRemainderAmount: String,
    @SerializedName("feeSplitNonVat") val feeSplitNonVat: String,
    @SerializedName("feeSplitVat") val feeSplitVat: String,
    @SerializedName("feeSplitType") val feeSplitType: String,
) {
    fun getIsSplit(): String {
        return isSplit
    }
}
