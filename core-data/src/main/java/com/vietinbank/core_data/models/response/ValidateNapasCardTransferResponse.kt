package com.vietinbank.core_data.models.response

import com.google.gson.annotations.SerializedName
import com.vietinbank.core_domain.models.maker.ApproverDomains

data class ValidateNapasCardTransferResponse(
    @SerializedName("amount") val amount: String,
    @SerializedName("currency") val currency: String,
    @SerializedName("feeAmt") val feeAmt: String,
    @SerializedName("feeCode") val feeCode: String,
    @SerializedName("feePayMethod") val feePayMethod: String,
    @SerializedName("feeVat") val feeVat: String,
    @SerializedName("totalFee") val totalFee: String,
    @SerializedName("fromAcctNo") val fromAcctNo: String,
    @SerializedName("isQRTransfer") val isQRTransfer: String,
    @SerializedName("processDate") val processDate: String,
    @SerializedName("remark") val remark: String,
    @SerializedName("sendBank") val sendBank: String,
    @SerializedName("toBankName") val toBankName: String,
    @SerializedName("toCardName") val toCardName: String,
    @SerializedName("toCardNo") val toCardNo: String,
    @SerializedName("username") val username: String,
    @SerializedName("nextApprovers") val nextApprovers: List<ApproverDomains>?,
)
