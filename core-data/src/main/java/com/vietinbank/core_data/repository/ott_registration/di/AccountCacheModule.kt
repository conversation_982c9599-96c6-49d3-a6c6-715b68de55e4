package com.vietinbank.core_data.repository.ott_registration.di

import com.vietinbank.core_common.cache.CacheFactory
import com.vietinbank.core_data.repository.QRCacheManagerImpl
import com.vietinbank.core_data.repository.TransferCacheManagerImpl
import com.vietinbank.core_domain.repository.cache.IQRCacheManager
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AccountCacheModule {
    @Provides
    @Singleton
    fun provideAccountCache(
        cacheFactory: CacheFactory,
    ): ITransferCacheManager {
        return TransferCacheManagerImpl(cacheFactory)
    }

    @Provides
    @Singleton
    fun provideAccountQRCache(
        cacheFactory: CacheFactory,
    ): IQRCacheManager {
        return QRCacheManagerImpl(cacheFactory)
    }
}