package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Server
import com.vietinbank.core_common.environment.IEnvironmentProvider
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.encryption.PasswordEncryptor
import com.vietinbank.core_data.mapper.soft.handleSDKResultCode
import com.vietinbank.core_data.models.request.KeypassRequest
import com.vietinbank.core_data.models.request.VBlockSoftOtpRequest
import com.vietinbank.core_data.models.request.VGetSoftOtpActivationCodeRequest
import com.vietinbank.core_data.models.response.SoftResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.soft.CheckUserExistsParams
import com.vietinbank.core_domain.models.soft.GetChallengeOTPParams
import com.vietinbank.core_domain.models.soft.GetTransactionInfoParams
import com.vietinbank.core_domain.models.soft.GetTransactionInfoResponse
import com.vietinbank.core_domain.models.soft.InstallSoftOTPParams
import com.vietinbank.core_domain.models.soft.InstallSoftOTPResponse
import com.vietinbank.core_domain.models.soft.KeypassParams
import com.vietinbank.core_domain.models.soft.SetSelectUserIdParams
import com.vietinbank.core_domain.models.soft.SoftEntity
import com.vietinbank.core_domain.models.soft.SoftOtpActivationType
import com.vietinbank.core_domain.models.soft.SoftSDKErrorCode
import com.vietinbank.core_domain.models.soft.VBlockSoftOtpParams
import com.vietinbank.core_domain.models.soft.VGetSoftOtpActivationCodeParams
import com.vietinbank.core_domain.models.soft.VSoftResponseParams
import com.vietinbank.core_domain.models.soft.VerifyActivationCodeParams
import com.vietinbank.core_domain.models.soft.VerifyPinParam
import com.vietinbank.core_domain.repository.SoftRepository
import vn.mk.token.sdk.SdkNative
import javax.inject.Inject

class SoftRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
    private val passwordEncryptor: PasswordEncryptor,
    private val sdkNative: SdkNative,
    private val dataSourceProperties: DataSourceProperties,
    private val envProvider: IEnvironmentProvider,
) : SoftRepository {

    companion object {
        private const val VTB_OTP_PREFIX = "5200"
        private const val VTB_OTP_METHOD = "tokenpush"
        private const val VTB_OTP_APP_ID = "4"
    }

    inline fun <reified T> callSDKSafe(block: () -> Any): Resource<T> {
        return runCatching {
            val result = block()
            when (result) {
                is Resource<*> -> {
                    @Suppress("UNCHECKED_CAST")
                    result as Resource<T>
                }
                else -> Resource.Success(result as T)
            }
        }.getOrElse { throwable ->
            Resource.Error(
                code = SoftSDKErrorCode.FAIL.code.toString(),
                message = SoftSDKErrorCode.FAIL.message,
                exception = AppException.UnknownException(
                    message = SoftSDKErrorCode.FAIL.message,
                    cause = throwable,
                ),
            )
        }
    }

    override suspend fun getActivationCode(params: VGetSoftOtpActivationCodeParams): Resource<VSoftResponseParams> {
        val request = VGetSoftOtpActivationCodeRequest(
            if (envProvider.getServerType() == Server.PPE ||
                envProvider.getServerType() == Server.UAT ||
                envProvider.isBuildConfigDebug()
            ) {
                SoftOtpActivationType.EMAIL.type
            } else {
                params.sendType.type
            },
            params.username,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun blockToken(params: VBlockSoftOtpParams): Resource<VSoftResponseParams> {
        val request = VBlockSoftOtpRequest(params.tokenIds)

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun forgotKeypassPin(params: KeypassParams): Resource<VSoftResponseParams> {
        val request = KeypassRequest(
            puk = params.puk,
            sendType = params.sendType,
            username = params.username,
        )

        val result = apiClient.makeApiCall(
            request = request,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<SoftResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val responseData = base.data as? SoftResponse
                Resource.Success(
                    VSoftResponseParams(
                        responseData?.status?.code,
                        responseData?.status?.message,
                    ),
                )
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun verifyActivationCode(
        params: VerifyActivationCodeParams,
    ): Resource<Unit> {
        return callSDKSafe {
            val resultCode = sdkNative.doActive(
                VTB_OTP_PREFIX + params.code,
                VTB_OTP_METHOD,
                VTB_OTP_APP_ID,
                dataSourceProperties.getSoftOTPUrl(),
            )
            handleSDKResultCode(
                resultBytes = resultCode,
                sdkFuncName = SoftRepository::verifyActivationCode.name,
            )
        }
    }

    override suspend fun setSelectedUserId(params: SetSelectUserIdParams): Resource<Unit> {
        return callSDKSafe {
            sdkNative.setSelectedUserId(params.id)
        }
    }

    override suspend fun deleteAllTokensSmartOTP(): Resource<Unit> {
        return callSDKSafe {
            val isSuccess = sdkNative.deleteAllExistingTokens()
            if (isSuccess) {
                Resource.Success(Unit)
            } else {
                Resource.Error(
                    code = SoftSDKErrorCode.FAIL.code.toString(),
                    message = SoftSDKErrorCode.FAIL.message,
                    exception = AppException.UnknownException(
                        message = SoftSDKErrorCode.FAIL.message,
                    ),
                )
            }
        }
    }

    override suspend fun installSoftOTP(
        params: InstallSoftOTPParams,
    ): Resource<InstallSoftOTPResponse> {
        return callSDKSafe {
            sdkNative.setPin(params.pin)
            setSelectedUserId(SetSelectUserIdParams(params.userId))
            val token = sdkNative.tokenSn
            InstallSoftOTPResponse(String(token))
        }
    }

    override suspend fun syncSoftOTP(): Resource<Unit> {
        return callSDKSafe {
            val resultCode = sdkNative.doSyncTime()
            handleSDKResultCode(
                resultCode,
                sdkFuncName = SoftRepository::syncSoftOTP.name,
            )
        }
    }

    override suspend fun getTransactionInfo(params: GetTransactionInfoParams): Resource<GetTransactionInfoResponse> {
        return callSDKSafe {
            val result = sdkNative.getTransactionInfo(params.transactionId, params.messageId)
            val resultConvert =
                gsonProvider.provideGson().fromJson(String(result), SoftEntity::class.java)
            GetTransactionInfoResponse(resultConvert)
        }
    }

    override suspend fun verifyPin(params: VerifyPinParam): Resource<Unit> {
        return callSDKSafe {
            val resultCode = sdkNative.loginPin(params.otp)
            handleSDKResultCode(
                resultCode,
                sdkFuncName = SoftRepository::verifyPin.name,
            )
        }
    }

    override suspend fun checkActiveToken(): Resource<Unit> {
        return callSDKSafe {
            handleSDKResultCode(
                sdkNative.checkActiveToken(),
                sdkFuncName = SoftRepository::checkActiveToken.name,
            )
        }
    }

    override suspend fun checkUserExistsById(params: CheckUserExistsParams): Resource<Boolean> {
        return callSDKSafe {
            sdkNative.checkUserIdExistence(params.id)
        }
    }

    override suspend fun getChallengeResponseOTP(params: GetChallengeOTPParams): Resource<String> {
        return callSDKSafe {
            String(
                sdkNative.getCRotpWithTransactionInfo(
                    params.transactionData,
                    params.challengeCode,
                ),
            )
        }
    }

    override suspend fun getOTPExpiredTime(): Resource<Int> {
        return callSDKSafe {
            sdkNative.timeStep
        }
    }
}
