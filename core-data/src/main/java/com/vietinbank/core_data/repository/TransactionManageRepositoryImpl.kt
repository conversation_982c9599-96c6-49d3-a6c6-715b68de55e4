package com.vietinbank.core_data.repository

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.utils.GsonProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.mapper.filter_report.DetailReportResponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.FilterReportResponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.InquiryApproverResponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.ListReportReponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.ReportDetailResponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.TraceCreateResponseDataMapper
import com.vietinbank.core_data.mapper.filter_report.TraceInquiryResponseDataMapper
import com.vietinbank.core_data.models.request.DetailReportRequest
import com.vietinbank.core_data.models.request.FilterReportRequest
import com.vietinbank.core_data.models.request.HistoryApprovalRequest
import com.vietinbank.core_data.models.request.ListReportRequest
import com.vietinbank.core_data.models.request.ReportDetailRequest
import com.vietinbank.core_data.models.request.TraceCreateRequest
import com.vietinbank.core_data.models.request.TraceInquiryRequest
import com.vietinbank.core_data.models.response.CreateTransferResponse
import com.vietinbank.core_data.models.response.DetailReportResponse
import com.vietinbank.core_data.models.response.FilterReportResponse
import com.vietinbank.core_data.models.response.InquiryApproverResponse
import com.vietinbank.core_data.models.response.ListReportResponse
import com.vietinbank.core_data.models.response.ReportDetailsResponse
import com.vietinbank.core_data.models.response.TraceInquiryResponse
import com.vietinbank.core_data.network.api.ApiService
import com.vietinbank.core_data.network.api.IApiClient
import com.vietinbank.core_data.network.di.Default
import com.vietinbank.core_domain.models.maker.CreateTransferDomain
import com.vietinbank.core_domain.models.manage.DetailReportParams
import com.vietinbank.core_domain.models.manage.FilterReportDomain
import com.vietinbank.core_domain.models.manage.FilterReportParams
import com.vietinbank.core_domain.models.manage.HistoryApprovalParams
import com.vietinbank.core_domain.models.manage.InquiryApproverDomains
import com.vietinbank.core_domain.models.manage.ListReportDomain
import com.vietinbank.core_domain.models.manage.ListReportParams
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsDomains
import com.vietinbank.core_domain.models.trace_payment.ReportDetailsParams
import com.vietinbank.core_domain.models.trace_payment.TraceCreateParams
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryDomains
import com.vietinbank.core_domain.models.trace_payment.TraceInquiryParams
import com.vietinbank.core_domain.repository.TransactionManageRepository
import javax.inject.Inject

class TransactionManageRepositoryImpl @Inject constructor(
    @Default private val apiClient: IApiClient,
    private val apiService: ApiService,
    private val gsonProvider: GsonProvider,
) : TransactionManageRepository {
    override suspend fun getPreFilterTransaction(params: FilterReportParams): Resource<FilterReportDomain> {
        val filterReportRequest = FilterReportRequest(
            params.userName,
            params.distributorNo,
            params.type,
            params.searchAdvance,
        )

        val result = apiClient.makeApiCall(
            request = filterReportRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<FilterReportResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val filterResponse = base.data as? FilterReportResponse
                    ?: return Resource.Error("Cannot parse to FilterReportDomain", "999")
                val filterReportDomain =
                    FilterReportResponseDataMapper.INSTANCE.toDomain(filterResponse)
                Resource.Success(filterReportDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getListFilterTransaction(params: ListReportParams): Resource<ListReportDomain> {
        val lstReportRequest = ListReportRequest(
            creditacc = params.creditacc,
            creditname = params.creditname,
            dateType = params.dateType,
            fromdate = params.fromdate,
            maxAmt = params.maxAmt,
            minAmt = params.minAmt,
            receiveBankName = params.receiveBankName,
            status = params.status,
            toAccountNo = params.toAccountNo,
            todate = params.todate,
            trantype = params.trantype,
            username = params.username,
            fromPage = params.fromPage,
            toPage = params.toPage,
            orderBy = params.orderBy,
            orderDirect = params.orderDirect,
        )

        val result = apiClient.makeApiCall(
            request = lstReportRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ListReportResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val lstResponse = base.data as? ListReportResponse
                    ?: return Resource.Error("Cannot parse to FilterReportDomain", "999")
                val lstReportDomain = ListReportReponseDataMapper.INSTANCE.toDomain(lstResponse)
                Resource.Success(lstReportDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getHistoryApproval(params: HistoryApprovalParams): Resource<InquiryApproverDomains> {
        val params = HistoryApprovalRequest(
            mtId = params.mtId,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<InquiryApproverResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val inquiryApproverReponse = base.data as? InquiryApproverResponse
                    ?: return Resource.Error("Cannot parse to DetailReportResponse", "999")
                val detailDomain =
                    InquiryApproverResponseDataMapper.INSTANCE.toDomain(inquiryApproverReponse)
                Resource.Success(detailDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun traceInquiry(params: TraceInquiryParams): Resource<TraceInquiryDomains> {
        val params = TraceInquiryRequest(
            mtId = params.mtId,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<TraceInquiryResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val traceInquiryResponse = base.data as? TraceInquiryResponse
                    ?: return Resource.Error("Cannot parse to DetailReportResponse", "999")
                val detailDomain =
                    TraceInquiryResponseDataMapper.INSTANCE.toDomain(traceInquiryResponse)
                Resource.Success(detailDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun reportDetail(params: ReportDetailsParams): Resource<ReportDetailsDomains> {
        val params = ReportDetailRequest(
            mtId = params.mtId,
            serviceType = params.serviceType,
            signType = params.signType,
            trantype = params.trantype,
            type = params.type,
            username = params.username,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<ReportDetailsResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? ReportDetailsResponse
                    ?: return Resource.Error("Cannot parse to DetailReportResponse", "999")
                val detailDomain = ReportDetailResponseDataMapper.INSTANCE.toDomain(response)
                Resource.Success(detailDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun traceCreate(params: TraceCreateParams): Resource<CreateTransferDomain> {
        val params = TraceCreateRequest(
            mtId = params.mtId,
            username = params.username,
            traceMsg = params.traceMsg,
            feeAccountNo = params.feeAccountNo,
        )
        val result = apiClient.makeApiCall(
            request = params,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<CreateTransferResponse>() {},
        )
        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val response = base.data as? CreateTransferResponse
                    ?: return Resource.Error("Cannot parse to DetailReportResponse", "999")
                val detailDomain = TraceCreateResponseDataMapper.INSTANCE.toDomain(response)
                Resource.Success(detailDomain)
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }

    override suspend fun getReportDetail(params: DetailReportParams): Resource<TransDetailDomain?> {
        val lstReportRequest = DetailReportRequest(
            mtId = params.mtId,
            tranType = params.tranType,
            serviceType = params.serviceType,
            username = params.username,
            mtIdList = params.mtIdList,
        )

        val result = apiClient.makeApiCall(
            request = lstReportRequest,
            apiCall = { reqBody -> apiService.callSITEnv(reqBody) },
            parseType = object : TypeToken<DetailReportResponse>() {},
        )

        return when (result) {
            is Resource.Success -> {
                val base = result.data
                val detailReponse = base.data as? DetailReportResponse
                    ?: return Resource.Error("Cannot parse to DetailReportResponse", "999")

                if (detailReponse.transaction != null) {
                    Resource.Success(
                        DetailReportResponseDataMapper.INSTANCE.toDomain(
                            detailReponse.transaction,
                        ),
                    )
                } else {
                    Resource.Success(
                        DetailReportResponseDataMapper.INSTANCE.toDomain(
                            detailReponse.btxTranItem,
                        ),
                    )
                }
            }

            is Resource.Error -> Resource.Error(result.message, result.code, result.exception)
        }
    }
}