package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class UnLockUserProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType?.uppercase()
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName
            creator = transaction.creator
            activityLogs = transaction.activityLogs
            cifno = transaction.cifno
            accountPayment = transaction.accountPayment
            paymentAccount = transaction.paymentAccount
            idCard = transaction.idCard
            typeSend = transaction.typeSend
            statusCode = transaction.statusCode
        }
    }
}
