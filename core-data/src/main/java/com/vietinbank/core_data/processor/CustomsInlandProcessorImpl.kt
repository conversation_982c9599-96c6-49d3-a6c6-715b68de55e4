package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class CustomsInlandProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            fromAccountNo = transaction.fromAccountNo
            currency = transaction.currency
            amount = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            )
            amountInWords = moneyHelper.convertAmountToWords(
                transaction.amount ?: "",
                transaction.currency ?: "",
            )
            feeAmount = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            )
            activityLogs = transaction.activityLogs
            payMethod = transaction.payMethod
            payname = transaction.payname
            payCode = transaction.payCode
            payAddress = transaction.payAddress
            provinceCode = transaction.provinceCode
            provinceName = transaction.provinceName
            areaCode = transaction.areaCode
            areaName = transaction.areaName
            treasuryCode = transaction.treasuryCode
            treasuryName = transaction.treasuryName
            collectingAccount = transaction.collectingAccount
            collectionAgencyCode = transaction.collectionAgencyCode
            collectionAgencyName = transaction.collectionAgencyName
            oficode = transaction.oficode
            ofiName = transaction.ofiName
            bucode = transaction.bucode
            buName = transaction.buName
            chapterCode = transaction.chapterCode
            declareNumber = transaction.declareNumber
            declareDate = transaction.declareDate
            ieType = transaction.ieType
            ieName = transaction.ieName
            uid = transaction.uid
            taxType = transaction.taxType
            branchName = transaction.branchName
            items = transaction.items
            districtCode = transaction.districtCode
            districtName = transaction.districtName
            idNumber = transaction.idNumber
            collectAcctName = transaction.collectAcctName
            chapterName = transaction.chapterName
            assetCode = transaction.assetCode
            machineNo = transaction.machineNo
            machineFeatures = transaction.machineFeatures
            propertyAdd = transaction.propertyAdd
            // Tra soat
            taxMethodName = transaction.taxMethodName
            paycode = transaction.paycode
            payadd = transaction.payadd
            collectionAccountNo = transaction.collectionAccountNo
            collectionAccountName = transaction.collectionAccountName
            collectAgencyCode = transaction.collectAgencyCode
            buname = transaction.buname
            chapCode = transaction.chapCode
            customTableNo = transaction.customTableNo
            customDate = transaction.customDate
            ofiname = transaction.ofiname
            collectAgencyName = transaction.collectAgencyName
            chapName = transaction.chapName
        }
    }
}
