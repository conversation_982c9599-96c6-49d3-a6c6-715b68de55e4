package com.vietinbank.core_data.processor

import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.processor.DisbursementOnlineProcessorImpl.Companion.FLAG_Y
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

class DisbursementOnlineProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {

    companion object {
        const val FLAG_Y = "Y"
    }
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            currency = when (transaction.subType) {
                "EXT" -> transaction.transferLoanCurType ?: ""
                else -> transaction.currencyCode ?: ""
            }
            amount = when (transaction.subType) {
                "EXT" -> Utils.g().getDotMoneyHasCcy(
                    transaction.transferLoanAmountCal ?: "",
                    transaction.transferLoanCurType ?: "",
                )

                else -> Utils.g().getDotMoneyHasCcy(
                    transaction.amountProposed ?: "",
                    transaction.currencyCode ?: "",
                )
            }
            amountInWords = moneyHelper.convertAmountToWords(
                when (transaction.subType) {
                    "EXT" -> transaction.transferLoanAmountCal ?: ""
                    else -> transaction.amountProposed ?: ""
                },
                when (transaction.subType) {
                    "EXT" -> transaction.transferLoanCurType ?: ""
                    else -> transaction.currencyCode ?: ""
                },
            )
            creator = transaction.creator
            manager = transaction.manager
            companyName = transaction.companyName
            representativeName = transaction.representativeName
            authority = transaction.authority
            authorityNumber = transaction.authorityNumber
            contractNo = transaction.contractNo
            contractDate = transaction.contractDate
            loanLimit = Utils.g().getDotMoneyHasCcy(
                transaction.loanLimit ?: "",
                transaction.loanCurTyp ?: "",
            )
            loanLimitInWord = moneyHelper.convertAmountToWords(
                transaction.loanLimit?.getAmountServer() ?: "",
                transaction.loanCurTyp,
            )
            disbursedAmount = Utils.g().getDotMoneyHasCcy(
                transaction.disbursedAmount ?: "",
                transaction.disbursedCurTyp ?: "",
            )
            disbursedAmountInWord = moneyHelper.convertAmountToWords(
                transaction.disbursedAmount?.getAmountServer() ?: "",
                transaction.disbursedCurTyp,
            )
            disbursedBranchCode = transaction.disbursedBranchCode
            disbursedBranchName = transaction.disbursedBranchName
            transferAmount = Utils.g().getDotMoneyHasCcy(
                transaction.transferAmount ?: "",
                transaction.transferCurType ?: "",
            )
            transferAmountInWord = moneyHelper.convertAmountToWords(
                transaction.transferAmount?.getAmountServer() ?: "",
                transaction.transferCurType ?: "",
            )
            transferLoanAmount = Utils.g().getDotMoneyHasCcy(
                transaction.transferLoanAmount ?: "",
                transaction.transferLoanCurType ?: "",
            )
            transferLoanAmountInWord = moneyHelper.convertAmountToWords(
                transaction.transferLoanAmount?.getAmountServer() ?: "",
                transaction.transferLoanCurType ?: "",
            )
            transferCurType = transaction.transferCurType
            transferLoanCurType = transaction.transferLoanCurType
            purpose = transaction.purpose
            loanRate = transaction.loanRate?.plus("%/năm") ?: ""
            loanTerm = transaction.loanTerm
            autoRepayment = if ("N" == transaction.autoRepayment) "Không" else "Có"
            accountId = transaction.accountId
            firstPaymentDate = transaction.firstPaymentDate
            dataFx = transaction.dataFx
            disbursementDate = transaction.disbursementDate
            fileGNN = transaction.fileGNN
            currencyCode = transaction.currencyCode
            listDocumentFileVND = transaction.listDocumentFileVND
            activityLogs = transaction.activityLogs // transaction
            dueDate = transaction.dueDate
            companyCode = transaction.companyCode
            exchangeRateType = transaction.exchangeRateType
            exchangeRateTypeDesc = if (!transaction.exchangeRateTypeDesc.isNullOrEmpty()) {
                transaction.exchangeRateTypeDesc
            } else {
                when (transaction.exchangeRateType) {
                    "0" -> "Tỷ giá ưu đãi"
                    "1" -> "Tỷ giá thỏa thuận"
                    else -> ""
                }
            }
            disbursementPurposeCode = transaction.disbursementPurposeCode
            disbursementPurposeName =
                if (!transaction.disbursementPurposeName.isNullOrEmpty()) {
                    transaction.disbursementPurposeName
                } else {
                    when (transaction.disbursementPurposeCode) {
                        "1" -> "Thanh toán trong nước VNĐ"
                        "2" -> "Thanh toán chi lương"
                        "3" -> "Thanh toán bù đắp cho TK khách hàng"
                        "4" -> "Thanh toán Ngân sách nhà nước/ nghĩa vụ khác"
                        "5" -> "Chuyển tiền biên mậu CNY/Chuyển tiền ngoại tệ trong Vietinbank"
                        "6" -> "Thanh toán TT chuyển tiền ngoại tệ ngoài VTB"
                        else -> ""
                    }
                }
            subType = transaction.subType
            feeAccount = transaction.feeAccount
            transferListFile = transaction.transferListFile
//                doTransferVNDList = transaction.doTransferVNDList
            transferListFileVND = transaction.transferListFileVND
            exchangeRate = Utils.g().getDotMoney(transaction.exchangeRate ?: "")
            companyAddress = transaction.companyAddress
            createDateDesc = transaction.createDateDesc
            verifiedDateDesc = transaction.verifiedDateDesc
            beneficiaryOnly = transaction.beneficiaryOnly
            receiveAccount = transaction.receiveAccount
            chargeMethod = transaction.chargeMethod
            receiveAccount = transaction.receiveAccount
            fileList = transaction.fileList
        }
    }
}

val TransactionDomain.isBeneficiaryOnly: Boolean
    get() = this.beneficiaryOnly == FLAG_Y