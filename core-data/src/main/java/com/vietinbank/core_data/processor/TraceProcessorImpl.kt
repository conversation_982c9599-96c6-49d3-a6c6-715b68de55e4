package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

/**
 * Created by vandz on 4/4/25.
 */
class TraceProcessorImpl
@Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            tranSubTypeName = transaction.tranSubTypeName
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            currency = transaction.currency
            feeAmount = if (transaction.feeAmount.isNullOrEmpty() || transaction.feeAmount == "0") {
                "Được tính bởi chi nhánh xử lý giao dịch"
            } else {
                Utils.g().getDotMoneyHasCcy(
                    transaction.feeAmount ?: "",
                    transaction.currency ?: "",
                )
            }
            remark = transaction.remark
            amountInWords =
                moneyHelper.convertAmountToWords(transaction.amount, transaction.currency)
            activityLogs = transaction.activityLogs
//            pref = prefTransactionDomain
            preferenceNo = transaction.preferenceNo
            feeAccountNo =
                if (!transaction.feeAmount.isNullOrEmpty() && transaction.feeAmount != "0") {
                    transaction.feeAccountNo
                } else {
                    null
                }
        }
    }
}
