package com.vietinbank.core_data.processor

import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import javax.inject.Inject

/**
 * Created by vandz on 4/4/25.
 */
class PaymentProcessorImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : TransactionProcessor {
    override suspend fun processTransaction(transaction: TransactionListDomain): TransactionDomain {
        return TransactionDomain().apply {
            tranTypeName = transaction.tranTypeName
            tranType = transaction.tranType
            serviceId = transaction.serviceId
            serviceType = transaction.serviceType
            groupType = transaction.groupType
            createdDate = transaction.createdDate
            status = transaction.status
            mtId = transaction.mtId
            statusName = transaction.statusName ?: transaction.status
            fromAccountNo = transaction.fromAccountNo
            fromAccountName = transaction.fromAccountName
            feePayMethod = transaction.feePayMethod
            feePayMethodDesc1 = transaction.feePayMethodDesc1
            currency = transaction.currency
            toAccountNo = transaction.toAccountNo
            receiveBankName = transaction.receiveBankName
            receiveName = transaction.receiveName
            amount =
                Utils.g().getDotMoneyHasCcy(transaction.amount ?: "", transaction.currency ?: "")
            amountInWords =
                moneyHelper.convertAmountToWords(transaction.amount, transaction.currency)
            feeAmount =
                Utils.g().getDotMoneyHasCcy(transaction.feeAmount ?: "", transaction.currency ?: "")
            remark = transaction.remark
            process_time = transaction.process_time
            activityLogs = transaction.activityLogs
            feeAccountNo = transaction.feeAccountNo // tai khoan thu phi
            branchName = transaction.branchName // chi nhanh xu ly
            branchNo = transaction.branchNo // ma chi nhanh xu ly
            fromAccountFeeNo = transaction.fromAccountFeeNo // so tai khoan thu phi
            fromAccountFeeName = transaction.fromAccountFeeName // ten tai khoan thu phi
            listFile = transaction.listFile //
        }
    }
}
