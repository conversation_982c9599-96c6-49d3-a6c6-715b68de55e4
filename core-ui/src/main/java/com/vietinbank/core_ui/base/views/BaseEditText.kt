package com.vietinbank.core_ui.base.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.view.ViewCompat
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R

/**
 * Created by vandz on 19/2/25.
 */
/**
 * Custom EditText với khả năng nhận các attr tuỳ chỉnh (drawable, font, v.v.)
 * và implements EFASTEditTextInterface.
 *
 * Sử dụng trong XML:
 *   <com.vietinbank.core_ui.base.views.BaseEditText
 *       android:layout_width="match_parent"
 *       android:layout_height="wrap_content"
 *       app:left="@drawable/ic_left"
 *       app:fontCus="1"
 *       ... />
 */
class BaseEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle,
) : AppCompatEditText(context, attrs, defStyleAttr) {

    // Danh sách callback khi text thay đổi
    private var addTextCallback: ArrayList<(text: String) -> Unit> = arrayListOf()

    // Callback khi focus thay đổi
    private var focusCallback: ArrayList<(Boolean) -> Unit> = arrayListOf()

    private var iconPadding : Float = Utils.getDPtoPX(context, 4f).toFloat()

    init {
        // Ẩn view này khỏi screen reader, tuỳ logic
        ViewCompat.setImportantForAccessibility(
            this,
            ViewCompat.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS,
        )
        initAttrs(attrs)
    }

    private fun initAttrs(attrs: AttributeSet?) {
//        printLog("BaseEditText", "==== initAttrs START ====")

        if (attrs == null) {
            printLog("BaseEditText", "attrs == null -> bỏ qua parse styleable")
            return
        }

        try {
            val a = context.obtainStyledAttributes(attrs, R.styleable.BaseTextView)

            // 1) Background
            if (a.hasValue(R.styleable.BaseTextView_background)) {
                val bgRes = a.getResourceId(R.styleable.BaseTextView_background, 0)
                if (bgRes != 0) {
//                    printLog("BaseEditText Set backgroundRes = $bgRes")

                    background = AppCompatResources.getDrawable(context, bgRes)
                }
            }

            // 2) Drawable left/top/right/bottom
            var left: Drawable? = null
            var top: Drawable? = null
            var right: Drawable? = null
            var bottom: Drawable? = null

            var foundDrawable = false

            val leftResId = a.getResourceId(R.styleable.BaseTextView_left, 0)
            if (leftResId != 0) {
                left = AppCompatResources.getDrawable(context, leftResId)
                foundDrawable = true
//                printLog("BaseEditText leftResId=$leftResId")
            }

            val topResId = a.getResourceId(R.styleable.BaseTextView_top, 0)
            if (topResId != 0) {
                top = AppCompatResources.getDrawable(context, topResId)
                foundDrawable = true
//                printLog("BaseEditText topResId=$topResId")
            }

            val rightResId = a.getResourceId(R.styleable.BaseTextView_right, 0)
            if (rightResId != 0) {
                right = AppCompatResources.getDrawable(context, rightResId)
                foundDrawable = true
//                printLog("BaseEditText rightResId=$rightResId")
            }

            val bottomResId = a.getResourceId(R.styleable.BaseTextView_bottom, 0)
            if (bottomResId != 0) {
                bottom = AppCompatResources.getDrawable(context, bottomResId)
                foundDrawable = true
//                printLog("BaseEditText bottomResId=$bottomResId")
            }

            iconPadding = a.getDimension(R.styleable.BaseTextView_iconPadding, iconPadding)

            if (foundDrawable) {
                setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom)
                compoundDrawablePadding = Utils.getDPtoPX(context, iconPadding)
            }

            // 3) check -> isSelected
            if (a.hasValue(R.styleable.BaseTextView_check)) {
                val checkVal = a.getBoolean(R.styleable.BaseTextView_check, false)
                isSelected = checkVal
//                printLog("BaseEditText check=$checkVal -> isSelected=$checkVal")
            }

            // 4) fontCus
            if (a.hasValue(R.styleable.BaseTextView_fontCus)) {
                val type = a.getInt(R.styleable.BaseTextView_fontCus, 0)
//                printLog("BaseEditText fontCus=$type")
                typeface = Utils.g().getFont(type, context)
            } else {
//                printLog("BaseEditText Không có fontCus -> default")
                typeface = Utils.g().getFont(1, context)
            }

            a.recycle()
        } catch (e: Exception) {
            printLog("BaseEditText Lỗi trong initAttrs: ${e.message}")
        } finally {
//            printLog("BaseEditText ==== initAttrs END ====")
        }

        includeFontPadding = false
    }
}