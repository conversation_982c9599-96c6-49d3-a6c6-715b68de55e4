package com.vietinbank.core_ui.base

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.DrawableRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewbinding.ViewBinding
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.ForceUpdateEvent
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.session.InactivityManager
import com.vietinbank.core_common.utils.SessionExpiredTracker
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.LoadingDialog
import com.vietinbank.core_ui.components.dialog.DialogType
import com.vietinbank.core_ui.components.dialog.FoundationDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.TrackUserInteraction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Created by vandz on 18/12/24.
 */

abstract class BaseFragment<VM : BaseViewModel> : Fragment() {
    protected abstract val viewModel: VM

    abstract val appNavigator: IAppNavigator

    @Inject
    lateinit var inactivityManager: InactivityManager

    /**
     * Dialog hiện tại (nếu có) để quản lý lifecycle.
     */
    var noticeDialog: FoundationDialog? = null

    /**
     * Bật/tắt chế độ Compose.
     * - `true` -> Fragment sẽ hiển thị UI bằng Jetpack Compose.
     * - `false` -> Fragment sẽ hiển thị UI bằng ViewBinding (XML).
     */
    open val useCompose: Boolean = false

    private var onBackPressedCallback: OnBackPressedCallback? = null

    /**
     *  Mặc định: xử lý khi Back được bấm. Trả về true nếu fragment con
     *  đã handle xong (chặn pop), false => cho Activity popBackStack.
     */
    open fun onBackPressed(): Boolean {
        // Mặc định: return false => pop
        return false
    }

    fun handleSingleEvent(block: (OneTimeEvent) -> Unit) {
        viewLifecycleOwner.lifecycleScope.launch(Dispatchers.Main.immediate) {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.oneTimeEvent.onEach {
                    printLog("handleSingleEvent: $it ")
                }.collect {
                    block.invoke(it)
                }
            }
        }
    }

    /**
     * Nếu `useCompose = false`, Fragment sẽ gọi hàm này để inflate layout XML bằng ViewBinding.
     * Child fragment override hàm này (trả về 1 instance của ...Binding).
     */
    open fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): ViewBinding? = null

    // Giữ binding (nếu dùng XML)
    private var _binding: ViewBinding? = null
    protected val binding: ViewBinding
        get() = _binding
            ?: throw IllegalStateException(
                "Binding is null. Ensure useCompose=false and inflateViewBinding() is implemented",
            )

    /**
     * Nếu `useCompose = true`, Fragment sẽ phải gọi hàm này để hiển thị UI Compose.
     * Child fragment override hàm này để thiết kế UI bằng @Composable.
     */
    @Composable
    open fun ComposeScreen() {
        // Mặc định để trống (child override)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return if (useCompose) {
            // Trường hợp su dung UI Compose
            ComposeView(requireContext()).apply {
                setContent {
                    AppTheme(appConfigManager = viewModel.getAppConfigManager()) {
                        // Track any user interaction at the Compose root level
                        TrackUserInteraction(
                            onInteract = { inactivityManager.onUserInteraction() },
                        ) {
                            ComposeScreen()
                        }
                    }
                }
            }
        } else {
            // Trường hợp su dung inflate XML + ViewBinding
            _binding = inflateViewBinding(inflater, container)
            _binding?.root
                ?: error("inflateViewBinding() trả về null - hãy kiểm tra việc override.")
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeViewModel(viewModel)

        // Cài đặt touch listener cho tất cả Fragment để ẩn bàn phím khi chạm ra ngoài EditText
        setupTouchToDismissKeyboard(view)
        // -- Đăng ký callback onBackPressed --
        registerBackPressedDispatcher()
    }

    override fun onResume() {
        super.onResume()

        // Skip monitoring for login/prelogin screens to prevent timeout loops
        if (shouldStartInactivityMonitoring()) {
            inactivityManager.startMonitoring()
        }

        viewModel.setupOtt()
    }

    /**
     * Determine if this fragment should observe session expiry events
     * Only observe if user is logged in - prevents infinite loop in login screens
     */
    protected open fun shouldObserveSessionExpiry(): Boolean {
        // Only observe session expiry if user has active session
        // Login/PreLogin screens will automatically return false (no session)
        return viewModel.isUserLoggedIn()
    }

    /**
     * Determine if inactivity monitoring should be started for this fragment
     * Check if user has active session - no session means login/prelogin screen
     */
    protected open fun shouldStartInactivityMonitoring(): Boolean {
        // Only monitor if user is logged in (has active session)
        // This automatically handles login/prelogin screens without needing overrides
        return viewModel.isUserLoggedIn()
    }

    private fun registerBackPressedDispatcher() {
        onBackPressedCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Gọi hàm onBackPressed() của fragment.
                // Nếu trả về false => popBackStack
                if (!<EMAIL>()) {
                    // Cho callback vô hiệu (để tránh loop), gọi pop
                    isEnabled = false
                    requireActivity().onBackPressed()
                }
            }
        }
        // attach callback vào dispatcher
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            onBackPressedCallback!!,
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Xoá callback để tránh leak khi fragment destroy
        onBackPressedCallback?.remove()
        onBackPressedCallback = null
        _binding = null
        permissionGalleryListener = null
        permissionExternalStoreListener = null
        accessPermissionListener = null
    }

    /**
     * Phương thức tiện ích để ẩn bàn phím từ Fragment
     * Sử dụng phương thức này khi cần ẩn bàn phím theo cách thủ công
     */
    protected fun hideKeyboard() {
        val activity = activity as? BaseActivity
        if (activity != null) {
            // Sử dụng phương thức từ BaseActivity
            activity.hideKeyboard()
        } else {
            // Trường hợp dự phòng
            view?.let { view ->
                val imm =
                    requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.windowToken, 0)
                view.findFocus()?.clearFocus()
            }
        }
    }

    /**
     * Cài đặt touch listener để ẩn bàn phím khi touch ra ngoài EditText
     * Phương thức này được gọi tự động trong onViewCreated
     */
    private fun setupTouchToDismissKeyboard(view: View) {
        view.setOnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                // Notify InactivityManager về user interaction
                inactivityManager.onUserInteraction()
                // Ẩn bàn phím bằng cách sử dụng phương thức tập trung
                hideKeyboard()
            }
            false // Không tiêu thụ sự kiện để nó vẫn được truyền đến các view con
        }
    }

    private var loadingDialog: LoadingDialog? = null

    /**
     * Hàm để quan sát các LiveData từ ViewModel.
     * @param viewModel ViewModel cần quan sát.
     */
    protected fun observeViewModel(viewModel: BaseViewModel) {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            handleLoading(isLoading)
        }
        // Quan sát các event chung
        viewModel.noInternetConnectionEvent.observe(
            viewLifecycleOwner,
            Observer { msg: String? ->
                if (msg != null) {
                    showNoticeDialog(
                        message = msg,
                        type = DialogType.ERROR,
                    )
                }
            },
        )

        viewModel.lostInternetConnectionEvent.observe(
            viewLifecycleOwner,
            Observer { msg: String? ->
                if (msg != null) {
                    showNoticeDialog(
                        message = msg,
                        type = DialogType.ERROR,
                    )
                }
            },
        )

        viewModel.connectTimeoutEvent.observe(
            viewLifecycleOwner,
            Observer { isError: Boolean? ->
                if (isError == true) {
                    showNoticeDialog(
                        message = getString(R.string.error_network_disconnect),
                        type = DialogType.ERROR,
                    )
                }
            },
        )

        viewModel.invalidCertificateEvent.observe(
            viewLifecycleOwner,
            Observer { isError: Boolean? ->
                if (isError == true) {
                    showNoticeDialog(
                        message = "Invalid SSL certificate.",
                        type = DialogType.ERROR,
                    )
                }
            },
        )

        viewModel.serverErrorEvent.observe(
            viewLifecycleOwner,
            Observer { isError: Boolean? ->
                if (isError == true) {
                    showNoticeDialog(
                        message = getString(R.string.error_at_server),
                        type = DialogType.ERROR,
                    )
                }
            },
        )

        // Only observe session expiry events if this fragment should handle them
        // Pre-login and login screens should not observe to prevent infinite loops
        if (shouldObserveSessionExpiry()) {
            // Use Channel/Flow for better reliability (replacing SingleLiveEvent)
            viewLifecycleOwner.lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.STARTED) {
                    viewModel.sessionExpiredFlow.collect {
                        handleSessionExpired()
                    }
                }
            }
        }

        // Quan sát message lỗi từ server (nếu có)
        viewModel.apiErrorMessage.observe(
            viewLifecycleOwner,
            Observer { errorMessage: ErrorMessage? ->
                printLog("API Error: ${errorMessage?.message}")
                if (errorMessage != null) {
                    showNoticeDialog(
                        message = errorMessage.message,
                        type = errorMessage.type,
                    )
                } else {
                    printLog("API Error is null")
                    showNoticeDialog(
                        message = "An error occurred.",
                        type = DialogType.ERROR,
                    ) // Thông báo mặc định
                }
            },
        )

        viewModel.forceUpdateEvent.observe(viewLifecycleOwner) { event ->
            when (event) {
                is ForceUpdateEvent.ShowForceFDialog -> showForceFDialog(event.message)
                is ForceUpdateEvent.ShowForceODialog -> showForceODialog(event.message)
                is ForceUpdateEvent.ShowForceNDialog -> showForceNDialog(event.message)
            }
        }
    }

    /**
     * Hàm để xử lý trạng thái loading.
     * @param isLoading Trạng thái loading hiện tại.
     */
    private fun handleLoading(isLoading: Boolean) {
        if (isLoading) {
            showLoadingDialog()
        } else {
            hideLoadingDialog()
        }
    }

    /**
     * Hàm để hiển thị loading dialog.
     */
    private fun showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = LoadingDialog()
            loadingDialog?.show(childFragmentManager, "LoadingDialog")
        }
    }

    /**
     * Hàm để ẩn loading dialog.
     */
    private fun hideLoadingDialog() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
    }

    // Track last dialog time for debounce
    private var lastDialogTime = 0L
    private val DIALOG_DEBOUNCE_TIME = 1000L // 1 second

    /**
     * Handle session expired event - Navigate to Login screen which will show dialog
     * Security-first: Navigate immediately, Login screen handles message display
     */
    private fun handleSessionExpired() {
        // Use tracker to prevent duplicate navigation
        if (SessionExpiredTracker.canShowDialog()) {
            try {
                // Mark as handled to prevent duplicates
                SessionExpiredTracker.dialogShown()

                printLog("Session expired - navigating to login")

                // Navigate to login IMMEDIATELY
                // Login screen will check flag and show dialog with localized message
                appNavigator.goToLoginAndPopAll()

                // Reset tracker after successful navigation
                SessionExpiredTracker.resetState()
            } catch (e: Exception) {
                printLog("Error handling session timeout: ${e.message}")
                // Reset tracker on error to allow retry
                SessionExpiredTracker.resetState()

                // Fallback: Navigate without Bundle if error occurs
                try {
                    appNavigator.goToLoginAndPopAll()
                } catch (navError: Exception) {
                    printLog("Critical: Failed to navigate to login: ${navError.message}")
                }
            }
        }
    }

    /**
     * Hàm để hiển thị NoticeDialog với một nút Xác nhận.
     * @param message Thông điệp cần hiển thị.
     * @param positiveAction Hành động khi nhấn nút Xác nhận.
     */
    fun showNoticeDialog(
        message: String,
        title: String = getString(R.string.title_notice_dialog),
        @DrawableRes icon: Int? = null,
        positiveButtonText: String? = null,
        negativeButtonText: String? = null,
        cancelable: Boolean = false,
        type: DialogType = DialogType.WARNING,
        positiveAction: (() -> Unit)? = null,
    ) {
        if (!isAdded() || isRemoving()) {
            // Không hiển thị dialog nếu fragment không còn attached
            return
        }

        // Debounce logic to prevent dialog spam
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastDialogTime < DIALOG_DEBOUNCE_TIME) {
            printLog("Dialog debounced - too soon since last dialog")
            return
        }
        lastDialogTime = currentTime

        // Đảm bảo đã dismiss hết các dialog khác trước khi hiển thị dialog mới
        dismissNoticeDialog()

        printLog("ShowNoticeDialog: $message")

        // Tạo và hiển thị dialog với positiveButtonText tùy chỉnh
        val actualPositiveText = positiveButtonText ?: getString(R.string.common_close)
        noticeDialog = FoundationDialog.newInstance(
            message = message,
            title = title,
            positiveButtonText = actualPositiveText,
            negativeButtonText = negativeButtonText,
            showNegativeButton = false,
            cancelable = cancelable,
            type = type,
            icon = icon,
        )
        noticeDialog?.lifecycle?.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                if (noticeDialog == owner) {
                    noticeDialog = null // Clear reference khi dialog destroy
                }
            }
        })

        // Wrap positive action để clear reference
        val wrappedAction = {
            positiveAction?.invoke()
            noticeDialog = null // Clear sau khi action
        }

        noticeDialog?.setOnPositiveClickListener(wrappedAction)
        noticeDialog?.show(childFragmentManager, "NOTICE_DIALOG")
    }

    /**
     * Hàm để hiển thị NoticeDialog với hai nút Xác nhận và Từ chối.
     * @param message Thông điệp cần hiển thị.
     * @param positiveButtonText Text của nút xác nhận.
     * @param negativeButtonText Text của nút từ chối.
     * @param positiveAction Hành động khi nhấn nút Xác nhận.
     * @param negativeAction Hành động khi nhấn nút Từ chối.
     * @param dismissAction Hành động khi đóng dialog bằng nút X.
     */
    fun showConfirmDialog(
        message: String,
        positiveButtonText: String? = null,
        negativeButtonText: String? = null,
        cancelable: Boolean = true,
        type: DialogType = DialogType.WARNING,
        positiveAction: (() -> Unit)? = null,
        negativeAction: (() -> Unit)? = null,
        dismissAction: (() -> Unit)? = null,
    ) {
        // Đảm bảo đã dismiss hết các dialog khác trước khi hiển thị dialog mới
        dismissNoticeDialog()

        // Tạo và hiển thị dialog
        val actualPositiveText = positiveButtonText ?: getString(R.string.common_confirm)
        val actualNegativeText = negativeButtonText ?: getString(R.string.common_reject)
        noticeDialog = FoundationDialog.newInstance(
            message = message,
            title = "Thông báo",
            positiveButtonText = actualPositiveText,
            negativeButtonText = actualNegativeText,
            showNegativeButton = true,
            cancelable = cancelable,
            type = type,
        )
        // Set lifecycle observer
        noticeDialog?.lifecycle?.addObserver(object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                if (noticeDialog == owner) {
                    noticeDialog = null
                }
            }
        })
        // Wrap all actions
        val wrappedPositive = {
            positiveAction?.invoke()
            noticeDialog = null
        }
        val wrappedNegative = {
            negativeAction?.invoke()
            noticeDialog = null
        }
        val wrappedDismiss = {
            dismissAction?.invoke()
            noticeDialog = null
        }

        noticeDialog?.setOnPositiveClickListener(wrappedPositive)
        noticeDialog?.setOnNegativeClickListener(wrappedNegative)
        noticeDialog?.setOnDismissClickListener(wrappedDismiss)

        noticeDialog?.show(childFragmentManager, "NOTICE_DIALOG")
    }

    /**
     * Hàm để ẩn NoticeDialog.
     */
    fun dismissNoticeDialog() {
        try {
            if (noticeDialog?.dialog?.isShowing == true) {
                noticeDialog?.dismissAllowingStateLoss()
            }
        } catch (e: Exception) {
            // Xử lý ngoại lệ nếu có
            printLog("Error dismissing dialog: ${e.message}")
        } finally {
            noticeDialog = null // Đảm bảo luôn null dù có lỗi
        }
    }

    /**
     * Hàm để ẩn tất cả dialog.
     */
    fun dismissAllDialogs() {
        parentFragmentManager.fragments.forEach { fragment ->
            if (fragment is DialogFragment) {
                fragment.dismissAllowingStateLoss()
            }
        }
    }

    fun isNetworkAvailable(): Boolean {
        val connectivityManager =
            activity?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities =
            connectivityManager.getNetworkCapabilities(network) ?: return false

        return when {
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            // for other devices which are able to connect with Ethernet
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            else -> false
        }
    }

    // open gallery
    private var permissionGalleryListener: ((Uri?) -> Unit)? = null

    protected fun requestPermissionGalleryListener(listener: (Uri?) -> Unit) {
        permissionGalleryListener = listener
    }

    private val openGalleryLauncher =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) {
            it?.let { uri ->
                permissionGalleryListener?.invoke(uri)
            }
        }

    protected fun openGallery() {
        openGalleryLauncher.launch(arrayOf("image/*"))
    }

    // truy cap bo nho
    private val requestPermissionExternalStoreLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            if (isGranted) {
                // granted permission
                permissionExternalStoreListener?.invoke(true)
            } else {
                permissionExternalStoreListener?.invoke(false)
            }
        }

    protected fun requestPermissionExternalStore() {
        val context = requireContext()
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                permissionExternalStoreListener?.invoke(true)
            }

            else -> {
                if (ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    requestPermissionExternalStoreLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                } else {
                    permissionExternalStoreListener?.invoke(true)
                }
            }
        }
    }

    private var permissionExternalStoreListener: ((Boolean) -> Unit)? = null

    protected fun requestPermissionExternalStoreListener(listener: (Boolean) -> Unit) {
        permissionExternalStoreListener = listener
    }

    // more permission
    private var accessPermissionListener: ((Boolean) -> Unit)? = null
    protected fun requestAccessPermissionListener(listener: (Boolean) -> Unit) {
        accessPermissionListener = listener
    }

    private val accessPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted: Boolean ->
            if (isGranted) {
                accessPermissionListener?.invoke(true)
            } else {
                accessPermissionListener?.invoke(false)
            }
        }

    protected fun requestAccessPermission(permission: String) {
        if (ContextCompat.checkSelfPermission(
                requireContext(), permission,
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            accessPermissionLauncher.launch(permission)
        } else {
            accessPermissionListener?.invoke(true)
        }
    }

    private fun showForceFDialog(message: String) {
        showNoticeDialog(
            message = message,
            positiveAction = {
                Utils.openAppInPlayStore(requireContext(), requireContext().packageName)
            },
            positiveButtonText = getString(R.string.common_update_app),
            cancelable = false,
        )
    }

    private fun showForceODialog(message: String) {
        showConfirmDialog(
            message = message,
            positiveButtonText = getString(R.string.common_update_app),
            negativeButtonText = getString(R.string.common_close),
            positiveAction = {
                Utils.openAppInPlayStore(requireContext(), requireContext().packageName)
            },
            negativeAction = {
                dismissNoticeDialog()
            },
        )
    }

    private fun showForceNDialog(message: String) {
        showNoticeDialog(
            message = message,
            positiveAction = { /* không làm gì */ },
            positiveButtonText = getString(R.string.common_close),
            cancelable = false,
        )
    }
}
