package com.vietinbank.core_ui.base

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.LoadingDialog
import com.vietinbank.core_ui.base.dialog.NoticeDialog
import com.vietinbank.core_ui.components.dialog.DialogType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

abstract class BaseBottomSheetFragment : BottomSheetDialogFragment() {
    open var showFullDialog = false
    private var textSearch = ""
    private var jobTextSearch: Job? = null
    private var noticeDialog: NoticeDialog? = null
    private var loadingDialog: LoadingDialog? = null

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        initData()
        initView()
        initListener()
        clickOutSide(view)
        val modalBehavior = (dialog as? BottomSheetDialog)?.behavior
        modalBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
        modalBehavior?.skipCollapsed = true
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        dialog.setOnShowListener {
            val bottomSheetDialog = it as BottomSheetDialog
            val parentLayout =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            parentLayout?.let { it ->
                val behaviour = BottomSheetBehavior.from(it)
                setupFullHeight(it)
                behaviour.state = BottomSheetBehavior.STATE_EXPANDED

                // Disable dragging
                behaviour.isDraggable = true
            }
        }
        return dialog
    }

    private fun setupFullHeight(bottomSheet: View) {
        val layoutParams = bottomSheet.layoutParams
        if (showFullDialog) {
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
        } else {
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
        }
        bottomSheet.layoutParams = layoutParams
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun clickOutSide(view: View) {
        view.setOnTouchListener { _, event ->

            if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                view.clearFocus()
                val imm =
                    context?.getSystemService(AppCompatActivity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            }
            true
        }
    }

    override fun getTheme(): Int {
        return R.style.CustomBottomSheetDialogTheme
    }

    open fun EditText.searchText(
        timeDelay: Long = 1000,
        callBack: (String) -> Unit,
    ) {
        this.addTextChangedListener(
            object : TextWatcher {
                override fun beforeTextChanged(
                    p0: CharSequence?,
                    p1: Int,
                    p2: Int,
                    p3: Int,
                ) {
                }

                override fun onTextChanged(
                    p0: CharSequence?,
                    p1: Int,
                    p2: Int,
                    p3: Int,
                ) {
                }

                override fun afterTextChanged(s: Editable?) {
                    val currentText = s.toString()
                    if (textSearch != currentText) {
                        jobTextSearch?.cancel()
                        textSearch = currentText
                        jobTextSearch = default(timeDelay) {
                            withContext(Dispatchers.Main) {
                                callBack(currentText)
                            }
                        }
                    }
                }
            },
        )
    }

    fun default(
        milliSecond: Long,
        work: suspend (() -> Unit),
    ): Job {
        var value = milliSecond
        return CoroutineScope(Dispatchers.Default).launch {
            while (value > 0) {
                delay(1)
                value--
            }
            work()
        }
    }

    protected fun observeViewModel(viewModel: BaseViewModel) {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            handleLoading(isLoading)
        }
        // Quan sát các event chung
        viewModel.noInternetConnectionEvent.observe(
            viewLifecycleOwner,
            Observer {
                showNoticeDialog("No internet connection!")
            },
        )

        viewModel.connectTimeoutEvent.observe(
            viewLifecycleOwner,
            Observer {
                showNoticeDialog("Connection timeout.")
            },
        )

        viewModel.invalidCertificateEvent.observe(
            viewLifecycleOwner,
            Observer {
                showNoticeDialog("Invalid SSL certificate.")
            },
        )

        viewModel.serverErrorEvent.observe(
            viewLifecycleOwner,
            Observer {
                showNoticeDialog("Unknown server error.")
            },
        )

        // Quan sát message lỗi từ server (nếu có)
        viewModel.apiErrorMessage.observe(viewLifecycleOwner) { errorMessage: ErrorMessage? ->
            printLog("API Error: ${errorMessage?.message}")
            errorMessage?.let { showNoticeDialog(it.message) }
        }
    }

    /**
     * Hàm để hiển thị NoticeDialog.
     * @param message Thông điệp cần hiển thị.
     * @param type Loại dialog (ERROR, WARNING, etc.)
     */
    fun showNoticeDialog(message: String, type: DialogType = DialogType.WARNING) {
        if (!isAdded) return
        dismissNoticeDialog()
        noticeDialog = NoticeDialog.newInstance(message)
        noticeDialog?.show(childFragmentManager, "NOTICE_DIALOG")
    }

    /**
     * Hàm để ẩn NoticeDialog.
     */
    fun dismissNoticeDialog() {
        noticeDialog?.dismissAllowingStateLoss()
        noticeDialog = null
    }

    private fun handleLoading(isLoading: Boolean) {
        if (isLoading) {
            showLoadingDialog()
        } else {
            hideLoadingDialog()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        jobTextSearch?.cancel()
        jobTextSearch = null
        dismissNoticeDialog()
        hideLoadingDialog()
    }

    /**
     * Hàm để hiển thị loading dialog.
     */
    private fun showLoadingDialog() {
        if (loadingDialog == null) {
            loadingDialog = LoadingDialog()
            loadingDialog?.show(childFragmentManager, "LoadingDialog")
        }
    }

    /**
     * Hàm để ẩn loading dialog.
     */
    private fun hideLoadingDialog() {
        loadingDialog?.dismiss()
        loadingDialog = null
    }

    protected abstract fun initView()

    protected abstract fun initData()

    protected abstract fun initListener()

    fun getResourceString(resourceString: Int): String = resources.getString(resourceString)
}