package com.vietinbank.core_ui.base.sheet

import android.app.Activity
import android.view.WindowManager
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetProperties
import androidx.compose.material3.SheetValue
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.components.common.ColumnModalContainer
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Default values for BaseBottomSheet configuration
 * These are non-composable constants that can be used in default parameters
 */
private object BottomSheetDefault {
    const val MAX_WIDTH_DP = 600 // Maximum width for tablets (same as FDS.Sizer.Dialog.maxWidth)
    const val DEFAULT_SCRIM_ALPHA = 0.80f // Default scrim transparency (consistent with FDS)
}

/**
 * Base Bottom Sheet component with edge-to-edge support and banking security features.
 *
 * IMPORTANT: Background is transparent by default. Child dialogs MUST provide their own background:
 * - Simple dialog: Single background color wrapping all content
 * - Complex dialog: Multiple background sections as needed
 *
 * Features:
 * - Edge-to-edge scrim that covers status bar
 * - Optional FLAG_SECURE for sensitive content
 * - Drag to dismiss with customizable behavior
 * - IME-friendly with automatic keyboard handling
 * - Nested scroll support
 * - Type-safe result handling similar to BaseDialog
 *
 * @param R Result type for the bottom sheet
 * @param visible Whether the sheet is visible
 * @param onDismissRequest Called when user attempts to dismiss the sheet
 * @param onResult Called when a result is ready (sheet will auto-dismiss)
 * @param skipPartiallyExpanded Skip partial expanded state (default true for full or dismissed only)
 * @param secureFlag Enable FLAG_SECURE for sensitive banking screens
 * @param scrimAlpha Scrim transparency (0.0 to 1.0, default: 0.52)
 * @param maxWidthDp Maximum width in dp (useful for tablets, recommended: 600dp)
 * @param shape Shape of the bottom sheet
 * @param containerColor Background color of the sheet (default: transparent - dialogs control their own background)
 * @param content Sheet content with ColumnScope receiver and onResult function
 *
 * Example usage for simple picker:
 * ```kotlin
 * BaseBottomSheet<String>(
 *     visible = showPicker,
 *     onDismissRequest = { showPicker = false },
 *     onResult = { selected ->
 *         handleSelection(selected)
 *         showPicker = false
 *     }
 * ) { onResult ->
 *     // Sheet content can call onResult(value)
 * }
 * ```
 *
 * For sheets without results, use BaseBottomSheet<Unit> and call onResult(Unit)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <R> BaseBottomSheet(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    onResult: (R) -> Unit,
    skipPartiallyExpanded: Boolean = true,
    secureFlag: Boolean = false,
    scrimAlpha: Float = BottomSheetDefault.DEFAULT_SCRIM_ALPHA,
    maxWidthDp: Int? = null,
    horizontalPadding: Dp = FDS.Sizer.Padding.padding8,
    shape: Shape = RoundedCornerShape(
        topStart = FDS.Sizer.Radius.radius32,
        topEnd = FDS.Sizer.Radius.radius32,
    ),
    containerColor: Color = Color.Transparent,
    containerPadding: PaddingValues? = null,
    scrimColor: Color? = null,
    allowTouchDismiss: Boolean = true,
    extraBottomSpacing: Dp = 0.dp,
    content: @Composable ColumnScope.(onResult: (R) -> Unit) -> Unit,
) {
    if (!visible) return

    // Track if result has been delivered to prevent duplicates
    var isResultDelivered by remember { mutableStateOf(false) }

    // Reset result delivery flag when sheet becomes visible
    LaunchedEffect(visible) {
        if (visible) {
            isResultDelivered = false
        }
    }

    // Toggle FLAG_SECURE when sheet is visible for banking security
    val view = LocalView.current
    DisposableEffect(visible, secureFlag) {
        val window = (view.context as? Activity)?.window
        if (visible && secureFlag) {
            window?.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        }
        onDispose {
            if (secureFlag) {
                window?.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
            }
        }
    }

    // Chặn swipe-down ẩn sheet khi allowTouchDismiss = false
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = skipPartiallyExpanded,
        confirmValueChange = { newValue ->
            if (!allowTouchDismiss) newValue != SheetValue.Hidden else true
        },
    )

    // Track animation để tránh double onResult trong lúc đang animate
    var isAnimating by remember { mutableStateOf(false) }
    LaunchedEffect(sheetState.currentValue, sheetState.targetValue) {
        isAnimating = sheetState.currentValue != sheetState.targetValue
    }

    // Scrim + padding theo "modal container"
    val resolvedScrim = (scrimColor ?: FDS.Colors.dialogBackground).copy(alpha = scrimAlpha)
    val resolvedPadding = containerPadding ?: PaddingValues(0.dp)

    // Không cho click outside đóng sheet nếu allowTouchDismiss = false
    val handleDismiss: () -> Unit = {
        if (allowTouchDismiss) onDismissRequest()
    }

    ModalBottomSheet(
        onDismissRequest = handleDismiss,
        sheetState = sheetState,
        shape = RectangleShape, // tránh double clip — clip ở container
        containerColor = Color.Transparent, // vẽ nền thật ở ColumnModalContainer
        scrimColor = resolvedScrim,
        dragHandle = null,
        properties = ModalBottomSheetProperties(
            shouldDismissOnBackPress = allowTouchDismiss,
        ),
    ) {
        // Box mỏng để canh giữa + áp dụng insets, container bên trong mới widthIn/max
        Box(
            modifier = Modifier
                .windowInsetsPadding(
                    WindowInsets.safeDrawing.only(WindowInsetsSides.Horizontal),
                )
                .imePadding()
                .consumeWindowInsets(WindowInsets.ime)
                .fillMaxWidth(),
            contentAlignment = Alignment.TopCenter,
        ) {
            ColumnModalContainer(
                modifier = Modifier
                    .padding(horizontal = horizontalPadding)
                    .then(
                        if (maxWidthDp != null) {
                            Modifier.widthIn(max = maxWidthDp.dp)
                        } else {
                            Modifier.fillMaxWidth()
                        },
                    ),
                shape = shape, // dùng shape của sheet cho container
                background = containerColor, // nền thật của sheet
                shadowElevation = 0.dp, // giữ nguyên như trước
                contentPadding = resolvedPadding,
            ) {
                val safeOnResult: (R) -> Unit = { result ->
                    if (!isResultDelivered && !isAnimating) {
                        isResultDelivered = true
                        onResult(result)
                        onDismissRequest()
                    }
                }

                content(safeOnResult)

                // ModalBottomSheet already handles navigation bar insets automatically
                // Add configurable bottom spacing for content separation
                if (extraBottomSpacing > 0.dp) {
                    Spacer(modifier = Modifier.height(extraBottomSpacing))
                }
            }
        }
    }
}

/**
 * Simplified version for sheets that don't return results
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BaseBottomSheet(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    skipPartiallyExpanded: Boolean = true,
    secureFlag: Boolean = false,
    scrimAlpha: Float = BottomSheetDefault.DEFAULT_SCRIM_ALPHA,
    maxWidthDp: Int? = null,
    horizontalPadding: Dp = FDS.Sizer.Padding.padding8,
    shape: Shape = RoundedCornerShape(
        topStart = FDS.Sizer.Radius.radius32,
        topEnd = FDS.Sizer.Radius.radius32,
    ),
    containerColor: Color = Color.Transparent,
    containerPadding: PaddingValues? = null,
    scrimColor: Color? = null,
    allowTouchDismiss: Boolean = true,
    extraBottomSpacing: Dp = 0.dp,
    content: @Composable ColumnScope.(close: () -> Unit) -> Unit,
) {
    BaseBottomSheet<Unit>(
        visible = visible,
        onDismissRequest = onDismissRequest,
        onResult = { /* no-op */ },
        skipPartiallyExpanded = skipPartiallyExpanded,
        secureFlag = secureFlag,
        scrimAlpha = scrimAlpha,
        maxWidthDp = maxWidthDp,
        horizontalPadding = horizontalPadding,
        shape = shape,
        containerColor = containerColor,
        containerPadding = containerPadding,
        scrimColor = scrimColor,
        allowTouchDismiss = allowTouchDismiss,
        extraBottomSpacing = extraBottomSpacing,
    ) { _ ->
        val close = { onDismissRequest() }
        content(close)
    }
}
