package com.vietinbank.core_ui.components.dialog

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.components.common.BoxModalContainer
import com.vietinbank.core_ui.theme.LocalAppConfigManager
import com.vietinbank.core_ui.utils.appNavBarsOrMin
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Default values for DialogMotion
 */
private object DialogMotionDefaults {
    const val DEFAULT_SCRIM_ALPHA = 0.80f // Consistent scrim transparency across dialogs
}

/**
 * Dialog layout modes
 */
enum class DialogLayout {
    Center, // Centered dialog with padding
    BottomSheet, // Bottom sheet style
    FullScreen, // Full screen dialog
}

/**
 * Dialog motion configuration
 */
data class DialogMotion(
    val enterDurationMs: Int = 220,
    val exitDurationMs: Int = 160,
    val initialScale: Float = 0.92f,
    val targetScale: Float = 0.98f,
    val scrimAlpha: Float = DialogMotionDefaults.DEFAULT_SCRIM_ALPHA,
    val springStiffness: Float = Spring.StiffnessLow,
    val springDamping: Float = 0.86f,
)

/**
 * Interface for dialogs that can provide meaningful cancellation results
 */
interface CancellableResult {
    fun getCancellationResult(): String
}

/**
 * Main dialog frame that handles layout, animations, and scrim.
 * This component provides the container for dialog content with proper animations
 * and system insets handling.
 *
 * @param visible Controls visibility state
 * @param layout Dialog layout mode (Center, BottomSheet, FullScreen)
 * @param motion Animation configuration
 * @param transitionState Animation state for enter/exit coordination
 * @param maxWidthDp Maximum width in dp for tablets (null = no limit)
 * @param allowTouchDismiss Whether dialog can be dismissed by touching outside
 * @param animateOnFirstShow Whether to animate on first show (false = instant appear for IME orchestration)
 * @param onDismissRequest Called when user taps outside or presses back
 * @param content The actual dialog content
 */
@Composable
fun DialogFrame(
    visible: Boolean,
    layout: DialogLayout = DialogLayout.Center,
    motion: DialogMotion = DialogMotion(),
    transitionState: MutableTransitionState<Boolean> = remember { MutableTransitionState(false) },
    maxWidthDp: Int? = null,
    allowTouchDismiss: Boolean = true,
    animateOnFirstShow: Boolean = true,
    onDismissRequest: () -> Unit = {},
    horizontalPadding: Dp = FDS.Sizer.Padding.padding8,
    containerShape: Shape? = null,
    containerColor: Color? = null,
    containerElevation: Dp? = null,
    containerPadding: PaddingValues? = null,
    bottomSafeMinPadding: Dp? = FDS.Sizer.Padding.padding24,
    content: @Composable BoxScope.() -> Unit,
) {
    // Access AppConfigManager for navigation bar height
    val appCfg = LocalAppConfigManager.current

    // Track if dialog has been shown (to prevent auto-dismiss on first render)
    var hasShown by remember { mutableStateOf(false) }

    LaunchedEffect(visible) {
        if (visible && !hasShown) {
            hasShown = true
        }
    }

    // Full screen box WITHOUT systemBarsPadding to allow scrim to cover status bar
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                // Block all touch events to prevent interaction with content behind
                awaitPointerEventScope {
                    while (true) {
                        awaitPointerEvent()
                        // Consume all events
                    }
                }
            },
    ) {
        // Scrim (background overlay) - covers entire screen including status bar
        AnimatedVisibility(
            visibleState = transitionState,
            enter = if (animateOnFirstShow) {
                fadeIn(
                    animationSpec = tween(
                        durationMillis = motion.enterDurationMs,
                        easing = LinearOutSlowInEasing,
                    ),
                )
            } else {
                fadeIn(animationSpec = tween(0)) // Instant appear
            },
            exit = fadeOut(
                animationSpec = tween(
                    durationMillis = motion.exitDurationMs,
                ),
            ),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize() // Full screen including status bar
                    .background(FDS.Colors.dialogBackground.copy(alpha = motion.scrimAlpha))
                    .pointerInput(hasShown, allowTouchDismiss) {
                        awaitPointerEventScope {
                            while (true) {
                                val event = awaitPointerEvent(
                                    if (allowTouchDismiss) PointerEventPass.Main else PointerEventPass.Initial,
                                )
                                if (allowTouchDismiss) {
                                    // For cancelable dialogs: check for touch and dismiss
                                    if (hasShown && event.changes.any { it.pressed }) {
                                        onDismissRequest()
                                    }
                                } else {
                                    // For non-cancelable: consume all touches to block them
                                    event.changes.forEach { it.consume() }
                                }
                            }
                        }
                    },
            )
        }

        // Dialog content with layout-specific positioning
        // Apply WindowInsets only to content, not the scrim
        AnimatedVisibility(
            visibleState = transitionState,
            enter = if (animateOnFirstShow) {
                when (layout) {
                    DialogLayout.Center -> fadeIn(
                        animationSpec = tween(motion.enterDurationMs),
                    ) + scaleIn(
                        initialScale = motion.initialScale,
                        animationSpec = spring(
                            stiffness = motion.springStiffness,
                            dampingRatio = motion.springDamping,
                        ),
                    )
                    DialogLayout.BottomSheet -> fadeIn(
                        animationSpec = tween(motion.enterDurationMs),
                    ) + slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = spring(
                            stiffness = motion.springStiffness,
                            dampingRatio = motion.springDamping,
                        ),
                    )
                    DialogLayout.FullScreen -> fadeIn(
                        animationSpec = tween(motion.enterDurationMs),
                    )
                }
            } else {
                // No animation on first show - instant appear for IME orchestration
                fadeIn(animationSpec = tween(0))
            },
            exit = when (layout) {
                DialogLayout.Center -> fadeOut(
                    animationSpec = tween(motion.exitDurationMs),
                ) + scaleOut(
                    targetScale = motion.targetScale,
                    animationSpec = tween(motion.exitDurationMs),
                )
                DialogLayout.BottomSheet -> fadeOut(
                    animationSpec = tween(motion.exitDurationMs),
                ) + slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(motion.exitDurationMs),
                )
                DialogLayout.FullScreen -> fadeOut(
                    animationSpec = tween(motion.exitDurationMs),
                )
            },
            modifier = Modifier.fillMaxSize(),
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .then(
                        when (layout) {
                            DialogLayout.Center -> Modifier.windowInsetsPadding(
                                WindowInsets.safeDrawing.only(
                                    WindowInsetsSides.Vertical + WindowInsetsSides.Horizontal,
                                ),
                            )
                            DialogLayout.BottomSheet -> {
                                // For BottomSheet: Always use imePadding() which handles keyboard visibility automatically
                                // Combined with safeDrawing for horizontal protection
                                // IMPORTANT: consumeWindowInsets prevents IME insets from propagating to children
                                // This avoids double padding when combined with SOFT_INPUT_ADJUST_NOTHING
                                Modifier
                                    .windowInsetsPadding(
                                        WindowInsets.safeDrawing.only(WindowInsetsSides.Horizontal),
                                    )
                                    .appNavBarsOrMin(appCfg, bottomSafeMinPadding)
                                    .imePadding() // This automatically adjusts when keyboard shows/hides
                                    .consumeWindowInsets(WindowInsets.ime) // Prevent IME insets from affecting children
                            }
                            DialogLayout.FullScreen -> Modifier.windowInsetsPadding(WindowInsets.safeDrawing)
                        },
                    ),
                contentAlignment = when (layout) {
                    DialogLayout.Center -> Alignment.Center
                    DialogLayout.BottomSheet -> Alignment.BottomCenter
                    DialogLayout.FullScreen -> Alignment.Center
                },
            ) {
                val shouldClip = (containerShape == null) && (layout == DialogLayout.Center)
                val dialogModifier = when (layout) {
                    DialogLayout.Center -> {
                        Modifier
                            .padding(horizontal = horizontalPadding)
                            .fillMaxWidth()
                            .then(if (maxWidthDp != null) Modifier.widthIn(max = maxWidthDp.dp) else Modifier)
                            .then(if (shouldClip) Modifier.clip(RoundedCornerShape(FDS.Sizer.Radius.radius16)) else Modifier)
                            .pointerInput(Unit) { awaitPointerEventScope { while (true) { awaitPointerEvent() } } }
                    }
                    DialogLayout.BottomSheet -> {
                        Modifier
                            .padding(horizontal = horizontalPadding) // <-- 8dp mỗi cạnh
                            .fillMaxWidth()
                            .then(if (maxWidthDp != null) Modifier.widthIn(max = maxWidthDp.dp) else Modifier)
                            .pointerInput(Unit) { awaitPointerEventScope { while (true) { awaitPointerEvent() } } }
                    }
                    DialogLayout.FullScreen -> Modifier.fillMaxSize()
                }

                // 1) Resolve defaults theo layout, rồi áp override nếu có
                val defaultShape: Shape = when (layout) {
                    DialogLayout.Center -> RoundedCornerShape(FDS.Sizer.Radius.radius16)
                    DialogLayout.BottomSheet -> RoundedCornerShape(
                        topStart = FDS.Sizer.Radius.radius16,
                        topEnd = FDS.Sizer.Radius.radius16,
                    )
                    DialogLayout.FullScreen -> RectangleShape
                }

                val resolvedShape = containerShape ?: defaultShape
                val resolvedElevation: Dp = containerElevation ?: when (layout) {
                    DialogLayout.BottomSheet, DialogLayout.FullScreen -> 0.dp
                    else -> FDS.Effects.elevationXl
                }
                val resolvedColor: Color = containerColor ?: Color.Transparent
                val resolvedPadding: PaddingValues = containerPadding ?: PaddingValues(0.dp)

                BoxModalContainer(
                    modifier = dialogModifier,
                    shape = resolvedShape,
                    background = resolvedColor,
                    shadowElevation = resolvedElevation,
                    contentPadding = resolvedPadding,
                    content = { content() },
                )
            }
        }
    }
}