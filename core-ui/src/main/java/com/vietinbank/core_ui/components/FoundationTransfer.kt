package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationTransfer(
    modifier: Modifier = Modifier,
    accountFrom: TransferResult? = null,
    accountTo: TransferResult? = null,
    lstContent: List<ItemResult> = emptyList(),
    imageLoader: CoilImageLoader? = null,
    contentTop: (@Composable () -> Unit)? = null,
    contentBottom: (@Composable () -> Unit)? = null,
    verticalPadding: Dp = FDS.Sizer.Gap.gap24,
    horizontalPadding: Dp = FDS.Sizer.Gap.gap24,
    onClick: ((String) -> Unit)? = null,
    isShowImageWaterMark: Boolean = true,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
            .background(FDS.Colors.backgroundBgContainer),
    ) {
        if (isShowImageWaterMark) { // confirm screen không cần show
            Image(
                painter = painterResource(id = R.drawable.bg_common_water_mark),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap16),
                // đoạn này k cần ở giữa vì design ở trên top thui
//                    .align(Alignment.Center)
                contentScale = ContentScale.FillWidth,
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = verticalPadding),
        ) {
            contentTop?.let {
                it()
                FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap16))
            }

            accountFrom?.let { account ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    account.bankIconURL?.let {
                        // icon bank
                        Box(
                            modifier = Modifier
                                .padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            imageLoader?.LoadUrl(
                                url = it,
                                isCache = true,
                                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }
                    account.bankIconResource?.let {
                        // icon bank
                        Box(
                            modifier = Modifier
                                .padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            Icon(
                                painter = painterResource(it),
                                contentDescription = "ic_feature_maker_transfer_card",
                                tint = Color.Unspecified,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }

                    Column(modifier = Modifier.weight(1f)) {
                        // tên tài khoản
                        account.accountName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterPrimary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        // số tài khoản
                        account.accountNo?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        if (account.fromAccBalance?.isNotEmpty() == true) {
                            FoundationText(
                                text = account.fromAccBalance ?: "",
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                    }
                }
            }

            accountTo?.let { account ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Gap.gap8, horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.transaction_label_transfer_to),
                        style = FDS.Typography.captionCaptionM,
                        color = FDS.Colors.characterTertiary,
                    )
                    Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                    FoundationDivider(
                        modifier = Modifier
                            .weight(1f)
                            .background(FDS.Colors.gray100),
                    )
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    account.bankIconURL?.let {
                        Box(
                            modifier = Modifier
                                .padding(end = FDS.Sizer.Gap.gap8)
                                .size(FDS.Sizer.Icon.icon40)
                                .clip(CircleShape)
                                .background(FDS.Colors.gray50),
                            contentAlignment = Alignment.Center,
                        ) {
                            imageLoader?.LoadUrl(
                                url = it,
                                isCache = true,
                                placeholderRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                errorRes = com.vietinbank.core_ui.R.drawable.ic_bank_default,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        }
                    }

                    Column(modifier = Modifier.weight(1f)) {
                        account.accountName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterPrimary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        account.bankName?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                        account.accountNo?.let {
                            FoundationText(
                                text = it,
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                    }
                }
                // Di chuyển vào trong để khi không có thì ẩn luôn FoundationDivider
                FoundationDivider(
                    modifier = Modifier
                        .padding(top = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = horizontalPadding),
                )
            }

            lstContent.forEachIndexed { index, item ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .safeClickable {
                            item.clickType?.let { onClick?.invoke(it) }
                        }
                        .padding(top = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = horizontalPadding),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    FoundationInfoHorizontal(
                        modifier = Modifier.weight(1f),
                        title = item.title ?: "",
                        value = item.value ?: "",
                        titleColor = item.titleColor,
                        valueColor = item.valueColor,
                        titleExtra = item.valueExtra,
                        isValueUnderline = item.isTitleUnderline,
                    )

                    item.icon?.let {
                        Image(
                            painter = painterResource(it),
                            contentDescription = "",
                        )
                    }
                }
            }
            contentBottom?.let {
                it()
            }
        }
    }
}

@Preview
@Composable
fun FoundationTransferPreview() {
    Column {
        FoundationTransfer(
            contentTop = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.common_transaction),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.bodyB2,
                    )

                    FoundationText(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = FDS.Sizer.Gap.gap4),
                        text = "info.first",
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.bodyB2Emphasized,
                    )
                    FoundationStatus(statusCode = Status.Success)
                }
            },
            accountFrom = TransferResult(
                "",
                null,
                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            accountTo = TransferResult(
                "",
                null,

                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            lstContent = listOf(
                ItemResult("Nội dung", "Le Thanh Truc Phuong chuyen tien"),
                ItemResult("Phí giao dịch", "0 VND"),
                ItemResult("Hình thức thu phí", "Phí ngoài"),
            ),
        )

        FoundationTransfer(
            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
            accountFrom = TransferResult(
                "",
                null,

                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            accountTo = TransferResult(
                "",
                null,
                "Vietinbank",
                "Lê Thanh Trúc Phương",
                "*********",
                "Tài khoản nguồn",
            ),
            lstContent = listOf(
                ItemResult("Nội dung", "Le Thanh Truc Phuong chuyen tien"),
                ItemResult("Phí giao dịch", "0 VND"),
                ItemResult("Hình thức thu phí", "Phí ngoài"),
            ),
        )
    }
}