package com.vietinbank.core_ui.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ShimmerLoading(
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Avatar tròn
        ShimmerBlock(
            modifier = Modifier.size(FDS.Sizer.Padding.padding32),
            shape = CircleShape,
        )

        Spacer(Modifier.width(FoundationDesignSystem.Sizer.Padding.padding12))

        Column(Modifier.fillMaxWidth()) {
            // Thanh dài
            ShimmerBlock(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(FoundationDesignSystem.Sizer.Padding.padding16),
            )

            Spacer(Modifier.height(FoundationDesignSystem.Sizer.Padding.padding10))

            // Thanh ngắn hơn
            ShimmerBlock(
                modifier = Modifier
                    .fillMaxWidth(0.6f)
                    .height(FoundationDesignSystem.Sizer.Padding.padding16),
            )
        }
    }
}

@Composable
fun TitleShimmerLoading() {
    ShimmerBlock(
        modifier = Modifier.fillMaxWidth()
            .height(FDS.Sizer.Padding.padding64),
    )
}

@Composable
fun ShimmerBlock(
    modifier: Modifier = Modifier,
    shape: RoundedCornerShape = RoundedCornerShape(FoundationDesignSystem.Sizer.Padding.padding32), // bo tròn mép
) {
    Box(
        modifier = modifier
            .clip(shape)
            .shimmer(),
    )
}

fun Modifier.shimmer(
    durationMillis: Int = 1200,
): Modifier = composed {
    val baseColor: Color = FDS.Colors.homeBackgroundIcon // màu nền xám nhạt
    val highlightColor: Color = FDS.Colors.stateActiveLighter // màu sáng chạy qua

    var size by remember { mutableStateOf(IntSize.Zero) }

    // animation chạy vô tận
    val transition = rememberInfiniteTransition(label = "shimmer")
    val anim by transition.animateFloat(
        initialValue = -1f,
        targetValue = 2f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "shimmer-progress",
    )

    val brush = remember(size, anim) {
        Brush.linearGradient(
            colors = listOf(baseColor, highlightColor, baseColor),
            start = Offset(x = size.width * anim, y = 0f),
            end = Offset(x = size.width * anim + size.width, y = 0f),
        )
    }

    this
        .onGloballyPositioned { size = it.size }
        .background(brush)
}

@Preview(showBackground = true)
@Composable
fun PreviewCustomShimmerItem() {
    Column {
        repeat(3) {
            ShimmerLoading()
        }
    }
}
