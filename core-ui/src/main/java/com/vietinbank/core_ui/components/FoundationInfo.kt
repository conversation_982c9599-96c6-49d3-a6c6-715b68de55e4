package com.vietinbank.core_ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable

@Composable
fun FoundationInfoHorizontal(
    modifier: Modifier = Modifier,
    title: String? = null,
    titleExtra: String? = null,
    value: String? = null,
    titleColor: Color? = null,
    valueColor: Color? = null,
    titleExtraColor: Color? = null,
    titleStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2,
    valueStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
    titleExtraStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2,
    isValueUnderline: Boolean = false,
    onClick: (() -> Unit)? = null,
    textAlign: TextAlign = TextAlign.End,
) {
    val columnAlignment = when (textAlign) {
        TextAlign.Start -> Alignment.Start
        TextAlign.Center -> Alignment.CenterHorizontally
        else -> Alignment.End
    }

    val modifierValue = if (onClick != null) {
        Modifier.safeClickable { onClick.invoke() }
    } else {
        Modifier
    }
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(
            FoundationDesignSystem.Sizer.Gap.gap8,
        ),
    ) {
        title?.let {
            Text(
                modifier = Modifier.weight(1f),
                text = it,
                color = titleColor ?: FoundationDesignSystem.Colors.characterSecondary,
                style = titleStyle,
            )
        }

        Column(
            modifier = modifierValue.weight(1f),
            horizontalAlignment = columnAlignment,
        ) {
            value?.let {
                Text(
                    text = it ?: "",
                    color = valueColor ?: FoundationDesignSystem.Colors.characterPrimary,
                    style = if (isValueUnderline) {
                        valueStyle.copy(textDecoration = TextDecoration.Underline)
                    } else {
                        valueStyle
                    },
                    textAlign = textAlign,
                )
            }
            titleExtra?.let {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap4))
                FoundationText(
                    text = titleExtra,
                    color = titleExtraColor ?: FoundationDesignSystem.Colors.characterSecondary,
                    style = titleExtraStyle,
                    textAlign = textAlign,
                )
            }
        }
    }
}

@Composable
fun FoundationInfoVertical(
    modifier: Modifier = Modifier,
    title: String? = null,
    value: String? = null,
    icon: Int? = null,
    titleColor: Color = FoundationDesignSystem.Colors.textSecondary,
    valueColor: Color = FoundationDesignSystem.Colors.textPrimary,
    titleStyle: TextStyle = FoundationDesignSystem.Typography.captionCaptionL,
    valueStyle: TextStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
    isShowLine: Boolean = false,
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
    onClick: (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable {
                onClick?.invoke()
            },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Padding.padding16),
        ) {
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = horizontalAlignment,
                verticalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap4),
            ) {
                title?.let {
                    Text(text = it, color = titleColor, style = titleStyle)
                }

                value?.let {
                    Text(text = it, color = valueColor, style = valueStyle)
                }
            }

            icon?.let {
                Image(
                    painter = painterResource(icon),
                    contentDescription = null,
                )
            }
        }

        if (isShowLine) {
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap4))
        }
    }
}

@Preview
@Composable
fun BaseInfoHorizontalPreview() {
    FoundationInfoHorizontal(
        title = "Số tài khoản",
        titleExtra = "CT TNHH ARISTON THERMO VIET NAM",
        value = "12345678987654",
    )
}

@Preview
@Composable
fun BaseInfoVerticalPreview() {
    Column {
        FoundationInfoVertical(
            title = "Số tài khoản",
            value = "12345678987654",
            icon = R.drawable.ic_user,
            isShowLine = true,
        )

        FoundationInfoVertical(
            modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
            title = "Số tài khoản",
            value = "12345678987654",
            icon = R.drawable.ic_user,
            horizontalAlignment = Alignment.End,
        )
    }
}