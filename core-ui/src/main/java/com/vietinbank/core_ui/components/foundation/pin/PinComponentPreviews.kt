package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Preview demonstrations for PIN components with weight-based layout
 * Tests even distribution across various screen widths
 */

@Preview(name = "PIN Show - Small Screen (320dp)", widthDp = 320)
@Preview(name = "PIN Show - Normal Screen (360dp)", widthDp = 360)
@Preview(name = "PIN Show - Large Screen (411dp)", widthDp = 411)
@Preview(name = "PIN Show - Tablet (600dp)", widthDp = 600)
@Composable
private fun PinShowComponentPreview() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgScreen)
            .padding(FDS.Sizer.Padding.padding16),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Typing state with partial input
        PinShowComponent(
            state = PinShowState.TYPING,
            pinValues = listOf("1", "6", "5", "_", "_", "_"),
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Typed state with full input
        PinShowComponent(
            state = PinShowState.TYPED,
            pinValues = listOf("1", "6", "5", "4", "3", "2"),
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Expired state
        PinShowComponent(
            state = PinShowState.EXPIRED,
            pinValues = listOf("1", "6", "5", "4", "3", "2"),
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview(name = "PIN Hidden - Small Screen (320dp)", widthDp = 320)
@Preview(name = "PIN Hidden - Normal Screen (360dp)", widthDp = 360)
@Preview(name = "PIN Hidden - Large Screen (411dp)", widthDp = 411)
@Preview(name = "PIN Hidden - Tablet (600dp)", widthDp = 600)
@Composable
private fun PinHiddenComponentPreview() {
    var showNumbers by remember { mutableStateOf(false) }
    var digits by remember { mutableStateOf("165") }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgScreen)
            .padding(FDS.Sizer.Padding.padding16),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Input state - hidden dots
        PinHiddenComponent(
            state = PinState.INPUT,
            showNumbers = false,
            digits = digits,
            length = 6,
            onToggleVisibility = { showNumbers = !showNumbers },
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Input state - visible numbers
        PinHiddenComponent(
            state = PinState.INPUT,
            showNumbers = true,
            digits = digits,
            length = 6,
            onToggleVisibility = { showNumbers = !showNumbers },
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Success state
        PinHiddenComponent(
            state = PinState.SUCCESS,
            showNumbers = false,
            digits = "165432",
            length = 6,
            onToggleVisibility = { },
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Error state
        PinHiddenComponent(
            state = PinState.ERROR,
            showNumbers = false,
            digits = "165432",
            length = 6,
            onToggleVisibility = { },
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview(name = "All PIN States Comparison", widthDp = 360, showBackground = true)
@Composable
private fun PinSizeComparisonPreview() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgScreen)
            .padding(FDS.Sizer.Padding.padding16),
    ) {
        androidx.compose.material3.Text(
            text = "PIN Hidden Closed Eye (Dots - 24x32dp)",
            style = FDS.Typography.captionCaptionM,
            color = FDS.Colors.characterSecondary,
        )
        Spacer(modifier = Modifier.height(4.dp))

        // Hidden dots - Fixed small size
        OtpRowHidden(
            count = 6,
            activeCount = 3,
            activeColor = FDS.Colors.blue500,
            inactiveColor = FDS.Colors.blue100,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        androidx.compose.material3.Text(
            text = "PIN Hidden Open Eye (Small Boxes - 36x48dp)",
            style = FDS.Typography.captionCaptionM,
            color = FDS.Colors.characterSecondary,
        )
        Spacer(modifier = Modifier.height(4.dp))

        // PIN Hidden with visible numbers - Smaller boxes
        val otpItem = listOf(
            OtpItem(OTPCellType.DIGIT, "1"),
            OtpItem(OTPCellType.DIGIT, "6"),
            OtpItem(OTPCellType.DIGIT, "8"),
            OtpItem(OTPCellType.DOT),
            OtpItem(OTPCellType.DASH),
            OtpItem(OTPCellType.DASH),
        )
        OtpRowVisibleSmall(
            otpItems = otpItem,
            backgroundColor = FDS.Colors.blue100,
            textColor = FDS.Colors.characterHighlighted,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        androidx.compose.material3.Text(
            text = "PIN Show (Large Boxes - Weight-based, max 345dp)",
            style = FDS.Typography.captionCaptionM,
            color = FDS.Colors.characterSecondary,
        )
        Spacer(modifier = Modifier.height(4.dp))

        // PIN Show - Weight-based with max width constraint
        OtpRowVisible(
            otpItems = otpItem,
            backgroundColor = FDS.Colors.blue100,
            textColor = FDS.Colors.characterHighlighted,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview(name = "PIN States on Different Screens", widthDp = 360, showBackground = true)
@Composable
private fun PinStatesComparisonPreview() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgScreen)
            .padding(FDS.Sizer.Padding.padding16),
    ) {
        // PIN Hidden Component
        Column(modifier = Modifier.fillMaxWidth()) {
            androidx.compose.material3.Text(
                text = "PIN Hidden Component (Fixed dots)",
                style = FDS.Typography.captionCaptionM,
                color = FDS.Colors.characterSecondary,
            )
            Spacer(modifier = Modifier.height(4.dp))
            PinHiddenComponent(
                state = PinState.INPUT,
                showNumbers = false,
                digits = "165",
                length = 6,
                onToggleVisibility = { },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // PIN Show Component
        Column(modifier = Modifier.fillMaxWidth()) {
            androidx.compose.material3.Text(
                text = "PIN Show Component (Even distribution, max 345dp)",
                style = FDS.Typography.captionCaptionM,
                color = FDS.Colors.characterSecondary,
            )
            Spacer(modifier = Modifier.height(4.dp))
            PinShowComponent(
                state = PinShowState.TYPING,
                pinValues = listOf("1", "6", "5", "_", "_", "_"),
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

@Preview(name = "Narrow Screen Test", widthDp = 280, showBackground = true)
@Composable
private fun NarrowScreenPreview() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(FDS.Colors.backgroundBgScreen)
            .padding(FDS.Sizer.Padding.padding8),
    ) {
        // Test on very narrow screen (< 345dp threshold)
        PinShowComponent(
            state = PinShowState.TYPING,
            pinValues = listOf("1", "6", "5", "_", "_", "_"),
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        PinHiddenComponent(
            state = PinState.INPUT,
            showNumbers = false,
            digits = "165",
            length = 6,
            onToggleVisibility = { },
            modifier = Modifier.fillMaxWidth(),
        )
    }
}