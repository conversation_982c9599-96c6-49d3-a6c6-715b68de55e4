package com.vietinbank.core_ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.AppEffects
import com.vietinbank.core_ui.theme.SvnGilroyFamily
import com.vietinbank.core_ui.utils.ellipticalGradientBackground
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationButton(
    modifier: Modifier = Modifier,
    isLightButton: Boolean = true,
    text: String,
    onClick: () -> Unit,
    enabled: Boolean = true,
    size: ButtonSize = ButtonSize.Large,
    leadingIcon: Painter? = null,
    iconOnly: Boolean = false,
    showBgButtonDark: Boolean = false,
    scaleFactor: Float = 1f, // Allow scale adjustment for different screen densities
) {
    if (isLightButton) {
        FoundationButtonLight(
            modifier = modifier,
            text = text,
            enabled = enabled,
            onClick = onClick,
            size = size,
            scaleFactor = scaleFactor,
            leadingIcon = leadingIcon,
            iconOnly = iconOnly,
        )
    } else {
        FoundationButtonDark(
            modifier = modifier,
            text = text,
            enabled = enabled,
            onClick = onClick,
            size = size,
            scaleFactor = scaleFactor,
            leadingIcon = leadingIcon,
            iconOnly = iconOnly,
        )
    }
}

/**
 * Foundation Button Light Component
 * Primary button with light background based on Foundation Design System
 */
@Composable
fun FoundationButtonLight(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    size: ButtonSize = ButtonSize.Large,
    leadingIcon: Painter? = null,
    iconOnly: Boolean = false,
    scaleFactor: Float = 1f, // Allow scale adjustment for different screen densities
) {
    var isPressed by remember { mutableStateOf(false) }

    // Get dimension values from sealed class
    val horizontalPadding = dimensionResource(size.horizontalPaddingResId) * scaleFactor
    val verticalPadding = dimensionResource(size.verticalPaddingResId) * scaleFactor
    val buttonHeight = dimensionResource(size.heightResId) * scaleFactor
    val iconSize = dimensionResource(size.iconSizeResId) * scaleFactor
    val fontSize = size.fontSize

    // Direct color values for light button
    val textColor = if (enabled) AppColors.blue900 else AppColors.textDisabled
    val borderColor = Color.Transparent

    // Get dimension values outside of drawBehind
    val density = LocalDensity.current
    val cornerRadiusPx =
        with(density) { dimensionResource(R.dimen.foundation_radius_default).toPx() }

    // Resolve colors for drawing operations - MUST be done in Composable context
    val backgroundColor = AppColors.white
    val (gradientColor, gradientTransparentColor) = if (isPressed) {
        Pair(AppColors.buttonGradientPrimaryPressed, AppColors.buttonGradientPrimary)
    } else {
        Pair(AppColors.buttonGradientPrimary, AppColors.buttonGradientPrimaryTransparent)
    }
    val gradientOpacityFactor = if (enabled) 1f else 0.2f

    Box(
        modifier = modifier
            .height(buttonHeight)
            .shadow(
                elevation = if (enabled) AppEffects.elevationButton else AppEffects.elevationNone,
                spotColor = AppColors.shadowSm, // 10% opacity black from Foundation
                ambientColor = AppColors.shadowSm, // 10% opacity black from Foundation
                shape = RoundedCornerShape(dimensionResource(R.dimen.foundation_radius_default)),
                clip = false,
            )
            .clip(RoundedCornerShape(dimensionResource(R.dimen.foundation_radius_default)))
            .drawBehind {
                drawRoundRect(
                    color = backgroundColor,
                    cornerRadius = CornerRadius(cornerRadiusPx),
                    size = this.size,
                )
                val center = Offset(this.size.width / 2f, this.size.height)
                val radius = this.size.height

                // Gradient logic for light button
                val gradient = if (isPressed) {
                    // Pressed state - simple 2-color gradient
                    Brush.radialGradient(
                        0f to gradientColor, // #8DD8FF
                        1f to gradientTransparentColor, // #BBE8FF
                        center = center,
                        radius = radius * ButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                } else {
                    // Default state with fade effect
                    Brush.radialGradient(
                        0f to gradientColor.copy(alpha = gradientColor.alpha * gradientOpacityFactor),
                        ButtonConstants.GRADIENT_FADE_STOP to gradientTransparentColor.copy(alpha = 0f),
                        1f to backgroundColor,
                        center = center,
                        radius = radius * ButtonConstants.GRADIENT_RADIUS_MULTIPLIER,
                    )
                }
                // Scale horizontally
                val scaleX = this.size.width / this.size.height
                withTransform({
                    scale(
                        scaleX = scaleX,
                        scaleY = 1f,
                        pivot = center,
                    )
                }) {
                    drawRoundRect(
                        brush = gradient,
                        cornerRadius = CornerRadius(cornerRadiusPx),
                    )
                }
            }
            .safeClickable(
                enabled = enabled,
                onSafeClick = onClick,
                onPressedChanged = {
                    isPressed = it
                },
            )
            .padding(horizontal = horizontalPadding, vertical = verticalPadding),
        contentAlignment = Alignment.Center,
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (leadingIcon != null && !iconOnly) {
                Icon(
                    painter = leadingIcon,
                    contentDescription = null,
                    tint = textColor,
                    modifier = Modifier.size(iconSize),
                )
                Spacer(modifier = Modifier.width(dimensionResource(R.dimen.foundation_spacing_sm))) // 8dp gap
            }

            if (!iconOnly) {
                Text(
                    text = text,
                    color = textColor,
                    fontSize = fontSize,
                    fontFamily = SvnGilroyFamily,
                    fontWeight = FontWeight.SemiBold,
                    lineHeight = (size.fontSize.value * ButtonConstants.LINE_HEIGHT_MULTIPLIER).sp,
                    letterSpacing = ButtonConstants.LETTER_SPACING_PERCENT.em,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
                )
            } else if (leadingIcon != null) {
                Icon(
                    painter = leadingIcon,
                    contentDescription = text,
                    tint = textColor,
                    modifier = Modifier.size(dimensionResource(size.iconOnlySizeResId) * scaleFactor),
                )
            }
        }
    }
}

@Composable
private fun FoundationButtonDark(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    size: ButtonSize = ButtonSize.Large,
    leadingIcon: Painter? = null,
    iconOnly: Boolean = false,
    scaleFactor: Float = 1f, // Allow scale adjustment for different screen densities
    showBgButtonDark: Boolean = false,
) {
    var isPressed by remember { mutableStateOf(false) }

    // Get dimension values from sealed class
    val buttonHeight = dimensionResource(size.heightResId) * scaleFactor
    val fontSize = size.fontSize
    val horizontalPadding = dimensionResource(size.horizontalPaddingResId) * scaleFactor
    val verticalPadding = dimensionResource(size.verticalPaddingResId) * scaleFactor

    val list = when {
        !enabled -> listOf(
            FDS.Colors.foundationDarkButtonPrimary.copy(alpha = 0.15F),
            FDS.Colors.foundationDarkButtonPrimary.copy(alpha = 0.15F),
        )

        isPressed -> listOf(
            FDS.Colors.foundationDarkButtonPressedPrimary,
            FDS.Colors.foundationDarkButtonPressesSecondary.copy(alpha = 0.15F),
        )

        else -> listOf(
            FDS.Colors.foundationDarkButtonPressedPrimary.copy(alpha = 0.5F),
            FDS.Colors.white.copy(alpha = 0F),
        )
    }

    val textColor = if (enabled) FDS.Colors.white else FDS.Colors.foundationDarkButtonTextDisable

    val scaleX = if (enabled) 2F else 3F

    Box(
        modifier = modifier
            .height(buttonHeight)
            .clip(RoundedCornerShape(50))
            .background(if (showBgButtonDark) FDS.Colors.backgroundBgScreen else Color.Transparent),
        contentAlignment = Alignment.Center,
    ) {
        Box(
            modifier = Modifier
                .matchParentSize()
                .gradientBorder()
                .clip(RoundedCornerShape(50))
                .innerShadow(
                    shape = CircleShape,
                    color = FDS.Colors.white.copy(0.56f),
                    offsetY = (-2).dp,
                )
                .innerShadow(
                    shape = CircleShape,
                    color = FDS.Colors.white.copy(0.56f),
                    offsetY = (2).dp,
                )
                .ellipticalGradientBackground(
                    colors = list,
                    scaleX = scaleX,
                )
                .safeClickable(
                    enabled = enabled,
                    onPressedChanged = {
                        isPressed = it
                    },
                    onSafeClick = onClick,
                ),

        )
        Row(
            modifier = Modifier.padding(horizontal = horizontalPadding, vertical = verticalPadding),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            if (leadingIcon != null && !iconOnly) {
                Icon(
                    painter = leadingIcon,
                    contentDescription = null,
                    tint = textColor,
                    modifier = Modifier.size(dimensionResource(size.iconSizeResId) * scaleFactor),
                )
                Spacer(modifier = Modifier.width(dimensionResource(R.dimen.foundation_spacing_sm))) // 8dp gap
            }

            if (!iconOnly) {
                Text(
                    text = text,
                    fontWeight = FontWeight.SemiBold,
                    fontSize = fontSize,
                    color = textColor,
                    fontFamily = SvnGilroyFamily,
                    lineHeight = (size.fontSize.value * ButtonConstants.LINE_HEIGHT_MULTIPLIER).sp,
                    letterSpacing = ButtonConstants.LETTER_SPACING_PERCENT.em,
                    textAlign = TextAlign.Center,
                    maxLines = 1,
                    overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
                )
            } else if (leadingIcon != null) {
                Icon(
                    painter = leadingIcon,
                    contentDescription = text,
                    tint = textColor,
                    modifier = Modifier.size(dimensionResource(size.iconOnlySizeResId) * scaleFactor),
                )
            }
        }
    }
}

/**
 * Sealed class for Button sizes with encapsulated properties
 * Using @Immutable for Compose compiler optimizations
 */
@Immutable
sealed class ButtonSize(
    val heightResId: Int,
    val horizontalPaddingResId: Int,
    val verticalPaddingResId: Int,
    val fontSize: TextUnit,
    val iconSizeResId: Int,
    val iconOnlySizeResId: Int,
) {
    data object Small : ButtonSize(
        heightResId = R.dimen.foundation_button_height_small, // 32dp
        horizontalPaddingResId = R.dimen.foundation_spacing_lg, // 24dp
        verticalPaddingResId = R.dimen.foundation_spacing_xs, // 4dp
        fontSize = 14.sp,
        iconSizeResId = R.dimen.foundation_icon_xs, // 16dp
        iconOnlySizeResId = R.dimen.foundation_icon_sm, // 20dp
    )

    data object Medium : ButtonSize(
        heightResId = R.dimen.foundation_button_height_medium, // 48dp
        horizontalPaddingResId = R.dimen.foundation_spacing_lg, // 24dp
        verticalPaddingResId = R.dimen.foundation_spacing_sm, // 8dp
        fontSize = 16.sp,
        iconSizeResId = R.dimen.foundation_icon_sm, // 20dp
        iconOnlySizeResId = R.dimen.foundation_icon_md, // 24dp
    )

    data object Large : ButtonSize(
        heightResId = R.dimen.foundation_button_height_large, // 56dp
        horizontalPaddingResId = R.dimen.foundation_spacing_lg, // 24dp
        verticalPaddingResId = R.dimen.foundation_spacing_md, // 16dp
        fontSize = 16.sp,
        iconSizeResId = R.dimen.foundation_icon_md, // 24dp
        iconOnlySizeResId = R.dimen.foundation_icon_lg, // 32dp
    )
}

/**
 * Constants for FoundationButtonLight styling
 */
private object ButtonConstants {
    // Gradient parameters
    const val GRADIENT_RADIUS_MULTIPLIER = 1.2f
    const val GRADIENT_FADE_STOP = 0.6f

    // Text styling
    const val LETTER_SPACING_PERCENT = -0.02f
    const val LINE_HEIGHT_MULTIPLIER = 1.5f
}

/**
 * Preview functions for FoundationButtonLight
 */
@Preview(name = "Light Button - Enabled")
@Composable
private fun FoundationButtonLightPreview() {
    FoundationButtonLight(
        text = "Light Button",
        onClick = { },
    )
}

@Preview(name = "Light Button - Disabled")
@Composable
private fun FoundationButtonLightDisabledPreview() {
    FoundationButtonLight(
        text = "Light Button Disabled",
        onClick = { },
        enabled = false,
    )
}

@Preview(name = "Light Button - Pressed")
@Composable
private fun FoundationButtonLightPressedPreview() {
    FoundationButtonLight(
        text = "Light Button Pressed",
        onClick = { },
    )
}
