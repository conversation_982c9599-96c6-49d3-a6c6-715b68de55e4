package com.vietinbank.core_ui.components.foundation.pin

import android.os.Bundle
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.withFrameNanos
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.dialog.DialogMotion
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import kotlinx.coroutines.Job
import kotlinx.coroutines.android.awaitFrame
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for PinAuthDialog
 */
private object PinAuthDialogConstants {
    // Dialog-specific constants only
    const val OVERLAY_HEIGHT_DP = 100 // Loading overlay height
    const val MAX_PIN_ATTEMPTS = 3
}

/**
 * PIN Authentication Dialog
 *
 * A unified dialog that handles both PIN entry and OTP display
 * in a single flow with smooth transitions.
 *
 * Features:
 * - PIN input with show/hide toggle
 * - OTP display with countdown timer
 * - Resend functionality with cooldown
 * - Smooth state transitions
 * - Secure input handling
 *
 * Usage:
 * ```kotlin
 * // Show dialog
 * PinAuthDialog.show(
 *     fragmentManager = childFragmentManager,
 *     pinLength = 6,
 *     otpValiditySeconds = 270
 * )
 *
 * // Listen for result
 * childFragmentManager.setFragmentResultListener(
 *     PinAuthConstants.RESULT_KEY,
 *     viewLifecycleOwner
 * ) { _, bundle ->
 *     val result = bundle.getParcelable<PinAuthResult>("key_result")
 *     if (result?.isSuccess == true) {
 *         // Authentication successful
 *     }
 * }
 * ```
 */
/**
 * Simple PIN input dialog following Clean Architecture
 * Only handles UI - no business logic
 * Returns entered PIN for verification in ViewModel
 */
class PinAuthDialog : BaseDialog<PinAuthResult>() {

    companion object {
        private const val ARG_PIN_LENGTH = "arg_pin_length"
        private const val ARG_OTP_LENGTH = "arg_otp_length"
        private const val ARG_OTP_VALIDITY = "arg_otp_validity"
        private const val ARG_RESEND_COOLDOWN = "arg_resend_cooldown"
        private const val ARG_TRANSACTION_DATA = "arg_transaction_data"
        private const val ARG_USER_ID = "arg_user_id"
        private const val ARG_TRANSACTION_ID = "arg_transaction_id"
        private const val ARG_MESSAGE_ID = "arg_message_id"
        private const val ARG_SHOW_OTP_DIRECTLY = "arg_show_otp_directly"
        private const val ARG_OTP_CODE = "arg_otp_code"

        // Store reference to current dialog instance
        private var currentInstance: PinAuthDialog? = null

        fun show(
            fragmentManager: FragmentManager,
            pinLength: Int = PinAuthConstants.DEFAULT_PIN_LENGTH,
            otpValiditySeconds: Int = PinAuthConstants.DEFAULT_OTP_VALIDITY,
            resendCooldown: Int = PinAuthConstants.DEFAULT_RESEND_COOLDOWN,
            transactionData: Bundle? = null,
            userId: String? = null,
            transactionId: String? = null,
            messageId: String? = null,
        ) {
            val dialog = PinAuthDialog().apply {
                arguments = Bundle().apply {
                    putInt(ARG_PIN_LENGTH, pinLength)
                    putInt(ARG_OTP_VALIDITY, otpValiditySeconds)
                    putInt(ARG_RESEND_COOLDOWN, resendCooldown)
                    transactionData?.let { putBundle(ARG_TRANSACTION_DATA, it) }
                    userId?.let { putString(ARG_USER_ID, it) }
                    transactionId?.let { putString(ARG_TRANSACTION_ID, it) }
                    messageId?.let { putString(ARG_MESSAGE_ID, it) }
                }
            }

            currentInstance = dialog
            dialog.show(fragmentManager, PinAuthConstants.DIALOG_TAG)
        }

        /**
         * Update existing dialog with OTP after PIN verification
         * This keeps the same dialog instance and transitions to OTP state
         */
        fun updateWithOtp(otpCode: String, otpExpireTime: Int = 60) {
            currentInstance?.updateToOtpState(otpCode, otpExpireTime)
        }

        /**
         * Dismiss dialog after approval completes
         */
        fun dismissAfterCompletion() {
            currentInstance?.dismissAfterApproval()
        }

        /**
         * Update existing dialog to reflect a PIN verification error
         */
        fun updateWithPinError(errorMessage: String) {
            currentInstance?.updatePinErrorState(errorMessage)
        }

        /**
         * Show dialog directly with OTP display
         * Used after PIN verification succeeds in ViewModel
         */
        @Deprecated("Use updateWithOtp() to update existing dialog instead")
        fun showWithOtp(
            fragmentManager: FragmentManager,
            otpCode: String,
            otpValiditySeconds: Int = PinAuthConstants.DEFAULT_OTP_VALIDITY,
            resendCooldown: Int = PinAuthConstants.DEFAULT_RESEND_COOLDOWN,
        ) {
            val dialog = PinAuthDialog().apply {
                arguments = Bundle().apply {
                    putBoolean(ARG_SHOW_OTP_DIRECTLY, true)
                    putString(ARG_OTP_CODE, otpCode)
                    putInt(ARG_OTP_VALIDITY, otpValiditySeconds)
                    putInt(ARG_RESEND_COOLDOWN, resendCooldown)
                }
            }
            dialog.show(fragmentManager, PinAuthConstants.DIALOG_TAG)
        }
    }

    // Track if first frame for animation optimization
    private var isFirstFrame = true

    // Dialog configuration
    override val resultKey: String = PinAuthConstants.RESULT_KEY
    override val layout: DialogLayout = DialogLayout.BottomSheet
    override val motion: DialogMotion = DialogMotion(
        enterDurationMs = PinAuthConstants.ENTER_DURATION_MS,
        exitDurationMs = PinAuthConstants.EXIT_DURATION_MS,
        springStiffness = 600f,
        initialScale = 1f, // No scale to avoid GPU re-raster
        scrimAlpha = 0.52f,
    )
    override val allowTouchDismiss: Boolean = false // Force user to complete or explicitly cancel
    override val requiresSecureFlag: Boolean = true // Prevent screenshots for security
    override val maxWidthDp: Int = PinAuthConstants.MAX_WIDTH_DP // For tablets
    override val animateOnFirstShow: Boolean get() = !isFirstFrame // Disable animation on first frame
    override val bottomSafeMinPadding: Dp? = 0.dp // Avoid external bottom margin; handle spacing as internal padding

    // Use parentFragmentManager for result delivery
    override val resultFragmentManager: FragmentManager
        get() = parentFragmentManager

    // State update callback
    private var otpUpdateCallback: ((String, Int) -> Unit)? = null
    private var pinErrorCallback: ((String) -> Unit)? = null

    /**
     * Update dialog to OTP state after PIN verification
     */
    fun updateToOtpState(otpCode: String, otpExpireTime: Int) {
        otpUpdateCallback?.invoke(otpCode, otpExpireTime)
    }

    /**
     * Update dialog UI to show PIN error and stop loading
     */
    fun updatePinErrorState(errorMessage: String) {
        pinErrorCallback?.invoke(errorMessage)
    }

    /**
     * Dismiss dialog after approval completes
     */
    fun dismissAfterApproval() {
        dismiss()
    }

    /**
     * Prewarm assets to avoid decode lag - but delayed to not block first frame
     * This renders components invisibly to trigger resource decoding
     */
    @Composable
    private fun DelayedAssetPrewarm() {
        var shouldPrewarm by remember { mutableStateOf(false) }

        LaunchedEffect(Unit) {
            // Wait for enter animation + IME to finish
            delay(400)
            // Now trigger prewarming when dialog is stable
            shouldPrewarm = true
        }

        // Invisible rendering to trigger resource decode
        if (shouldPrewarm) {
            Box(
                modifier = Modifier
                    .size(1.dp) // Minimal size
                    .graphicsLayer { alpha = 0f }, // Completely invisible
            ) {
                // Render icons that will be used to trigger decode
                Icon(
                    painter = painterResource(id = R.drawable.ic_dangnhap_eye_open),
                    contentDescription = null,
                    modifier = Modifier.size(1.dp),
                )
                Icon(
                    painter = painterResource(id = R.drawable.ic_dangnhap_eye_close),
                    contentDescription = null,
                    modifier = Modifier.size(1.dp),
                )
                // Render text with styles to prewarm fonts
                FoundationText(
                    text = "",
                    style = FDS.Typography.headingH3,
                    modifier = Modifier.size(1.dp),
                )
            }
        }
    }

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (PinAuthResult) -> Unit,
    ) {
        // Update first frame flag after first composition
        LaunchedEffect(Unit) {
            withFrameNanos { }
            isFirstFrame = false
        }

        // Hoisted input controls - single source of truth
        val focusRequester = remember { FocusRequester() }
        val keyboardController = LocalSoftwareKeyboardController.current
        var imeShownOnce by rememberSaveable { mutableStateOf(false) }
        val scope = rememberCoroutineScope()

        // Get arguments
        val pinLength = arguments?.getInt(ARG_PIN_LENGTH, PinAuthConstants.DEFAULT_PIN_LENGTH)
            ?: PinAuthConstants.DEFAULT_PIN_LENGTH
        val otpLength = arguments?.getInt(ARG_OTP_LENGTH, PinAuthConstants.DEFAULT_OTP_LENGTH)
            ?: PinAuthConstants.DEFAULT_OTP_LENGTH
        val otpValiditySeconds = arguments?.getInt(ARG_OTP_VALIDITY, PinAuthConstants.DEFAULT_OTP_VALIDITY)
            ?: PinAuthConstants.DEFAULT_OTP_VALIDITY
        val resendCooldown = arguments?.getInt(ARG_RESEND_COOLDOWN, PinAuthConstants.DEFAULT_RESEND_COOLDOWN)
            ?: PinAuthConstants.DEFAULT_RESEND_COOLDOWN
        val transactionData = arguments?.getBundle(ARG_TRANSACTION_DATA)
        val userId = arguments?.getString(ARG_USER_ID)
        val transactionId = arguments?.getString(ARG_TRANSACTION_ID)
        val messageId = arguments?.getString(ARG_MESSAGE_ID)
        val showOtpDirectly = arguments?.getBoolean(ARG_SHOW_OTP_DIRECTLY, false) ?: false
        val otpCodeFromArgs = arguments?.getString(ARG_OTP_CODE)

        // State management
        var currentStep by remember {
            mutableStateOf<PinAuthStep>(
                if (showOtpDirectly && !otpCodeFromArgs.isNullOrEmpty()) {
                    // Show OTP directly if passed from ViewModel
                    PinAuthStep.DisplayOtp(
                        state = PinShowState.TYPING,
                        pinValues = otpCodeFromArgs.toPinValuesList(otpLength),
                        validitySeconds = otpValiditySeconds,
                        currentValidityRemaining = otpValiditySeconds,
                        canResend = false,
                        resendCooldown = resendCooldown,
                        currentResendRemaining = resendCooldown,
                    )
                } else {
                    PinAuthStep.InputPin(length = pinLength)
                },
            )
        }
        var pinAttempts by remember { mutableStateOf(0) }
        var generatedOtp by remember { mutableStateOf("") }
        var validityTimerJob by remember { mutableStateOf<Job?>(null) }
        var resendTimerJob by remember { mutableStateOf<Job?>(null) }

        // Track if this is first render for optimized animations
        val isFirstRender = remember { mutableStateOf(true) }
        LaunchedEffect(Unit) {
            delay(100)
            isFirstRender.value = false
        }

        // PIN verification state
        var isVerifyingPin by remember { mutableStateOf(false) }
        var actualOtpCode by remember { mutableStateOf(otpCodeFromArgs ?: "") }
        var waitingForOtp by remember { mutableStateOf(false) }

        // Register OTP update callback
        DisposableEffect(Unit) {
            otpUpdateCallback = { otpCode, otpExpireTime ->
                // Update to OTP state when OTP arrives from ViewModel
                actualOtpCode = otpCode
                currentStep = PinAuthStep.DisplayOtp(
                    state = PinShowState.TYPING,
                    pinValues = otpCode.toPinValuesList(otpLength),
                    validitySeconds = otpExpireTime,
                    currentValidityRemaining = otpExpireTime,
                    canResend = false,
                    resendCooldown = resendCooldown,
                    currentResendRemaining = resendCooldown,
                )
                waitingForOtp = false

                // IMPORTANT: Dismiss keyboard when showing OTP
                // OTP is displayed, not typed by user
                keyboardController?.hide()
                // Clear focus to prevent keyboard from reappearing
                try {
                    focusRequester.freeFocus()
                } catch (e: Exception) {
                    // Ignore if focus wasn't set
                }
            }
            pinErrorCallback = { errorMsg ->
                waitingForOtp = false
                // Show error on PIN input step, stop loading, and CLEAR the digits for immediate re-entry
                val currentInput = currentStep as? PinAuthStep.InputPin
                if (currentInput != null) {
                    currentStep = currentInput.copy(
                        isVerifying = false,
                        state = PinState.ERROR,
                        errorMessage = errorMsg,
                    )
                    // Bring keyboard back for re-entry and ensure focus is requested
                    try {
                        scope.launch {
                            awaitFrame()
                            focusRequester.requestFocus()
                            // Delay error shaking animation done
                            delay(180)
                            keyboardController?.show()
                        }
                    } catch (_: Exception) { }
                }
            }
            onDispose {
                otpUpdateCallback = null
                pinErrorCallback = null
                currentInstance = null
            }
        }

        // Clean Architecture: No business logic in UI
        // PIN verification handled in ViewModel

        // Handle PIN verification - send PIN but DON'T dismiss dialog
        LaunchedEffect(isVerifyingPin) {
            if (isVerifyingPin) {
                val currentInputStep = currentStep as? PinAuthStep.InputPin
                if (currentInputStep != null) {
                    // IMPORTANT: Don't call onResult here as it dismisses the dialog
                    // Instead, send PIN through a different mechanism
                    waitingForOtp = true

                    // Send PIN result to Fragment WITHOUT dismissing dialog
                    // Use setFragmentResult directly instead of onResult
                    val bundle = Bundle().apply {
                        putParcelable(
                            "key_result",
                            PinAuthResult(
                                isSuccess = true,
                                pinCode = currentInputStep.digits,
                                verificationType = PinVerificationType.PIN_ONLY,
                                transactionId = transactionId,
                            ),
                        )
                    }
                    parentFragmentManager.setFragmentResult(PinAuthConstants.RESULT_KEY, bundle)

                    // Keep dialog in loading state while waiting for OTP
                    currentStep = currentInputStep.copy(
                        isVerifying = true,
                        state = PinState.INPUT,
                    )
                    isVerifyingPin = false
                }
            }
        }

        // Handle resend OTP
        fun handleResendOtp() {
            val currentDisplayStep = currentStep as? PinAuthStep.DisplayOtp ?: return

            // Request new OTP from backend through Fragment/ViewModel
            currentStep = currentDisplayStep.copy(isResending = true)

            // Return resend request to Fragment for processing
            onResult(
                PinAuthResult(
                    isSuccess = false,
                    verificationType = PinVerificationType.OTP_ONLY,
                    transactionId = transactionId,
                    // Special flag to indicate resend request
                    timestamp = -1L,
                ),
            )
        }

        // Timer management for OTP display
        LaunchedEffect(currentStep) {
            val displayStep = currentStep as? PinAuthStep.DisplayOtp ?: return@LaunchedEffect

            // Validity timer
            validityTimerJob?.cancel()
            validityTimerJob = scope.launch {
                var remaining = displayStep.currentValidityRemaining
                while (remaining > 0) {
                    delay(1000)
                    remaining--
                    currentStep = (currentStep as? PinAuthStep.DisplayOtp)?.copy(
                        currentValidityRemaining = remaining,
                        state = if (remaining == 0) PinShowState.EXPIRED else displayStep.state,
                    ) ?: currentStep
                }
            }

            // Resend timer
            resendTimerJob?.cancel()
            if (!displayStep.canResend) {
                resendTimerJob = scope.launch {
                    var cooldown = displayStep.currentResendRemaining
                    while (cooldown > 0) {
                        delay(1000)
                        cooldown--
                        currentStep = (currentStep as? PinAuthStep.DisplayOtp)?.copy(
                            currentResendRemaining = cooldown,
                            canResend = cooldown == 0,
                        ) ?: currentStep
                    }
                }
            }
        }

        // IME activation with proper timing to avoid animation collision
        LaunchedEffect(visible) {
            if (visible && !imeShownOnce) {
                // 1) Focus immediately (lightweight, sets caret)
                withFrameNanos { } // Ensure attached
                focusRequester.requestFocus()

                // 2) Wait for enter animation & scrim to settle
                delay(150)

                // 3) Now show IME (single source of truth)
                try {
                    keyboardController?.show()
                } catch (e: Exception) {
                    // Optional: retry once if failed
                    delay(50)
                    try {
                        keyboardController?.show()
                    } catch (e: Exception) {
                        // Ignore
                    }
                }
                imeShownOnce = true
            }
        }

        DelayedAssetPrewarm()

        // Clear focus when dialog closes
        DisposableEffect(visible) {
            onDispose {
                if (!visible) {
                    // Safe focus clearing - wrap in try-catch to prevent crash
                    try {
                        focusRequester.freeFocus()
                    } catch (e: Exception) {
                        // Ignore - FocusRequester might not have focus
                    }
                    keyboardController?.hide()
                    validityTimerJob?.cancel()
                    resendTimerJob?.cancel()
                }
            }
        }

        // Track entering state for optimized rendering
        var entering by remember { mutableStateOf(true) }
        LaunchedEffect(Unit) {
            delay(PinAuthConstants.ENTER_DURATION_MS.toLong())
            entering = false
        }

        // Main content container
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .then(
                    if (entering) Modifier.graphicsLayer() else Modifier,
                ),
        ) {
            // Visual UI layer - render FIRST (bottom layer)
            val step = currentStep
            if (isFirstRender.value && step is PinAuthStep.InputPin) {
                // First frame: Render directly without AnimatedContent
                InputPinVisual(
                    step = step,
                    onToggleVisibility = {
                        currentStep = step.copy(showNumbers = !step.showNumbers)
                    },
                    onBackClick = {
                        onResult(
                            PinAuthResult(
                                isSuccess = false,
                                verificationType = PinVerificationType.PIN_ONLY,
                            ),
                        )
                    },
                    onContinueClick = {
                        if (step.digits.length == step.length && !isVerifyingPin) {
                            currentStep = step.copy(isVerifying = true)
                            isVerifyingPin = true
                        }
                    },
                    onRowTap = {
                        focusRequester.requestFocus()
                        keyboardController?.show()
                    },
                    modifier = Modifier.fillMaxWidth(),
                    onForgot = {
                        onResult(
                            PinAuthResult(
                                isSuccess = false,
                                isForgot = true,
                            ),
                        )
                    },
                )
            } else {
                // Subsequent frames: Use AnimatedContent for transitions
                AnimatedContent(
                    targetState = currentStep,
                    contentKey = { it::class },
                    transitionSpec = {
                        (
                            fadeIn(animationSpec = tween(PinAuthConstants.STATE_TRANSITION_MS)) +
                                slideInVertically(
                                    initialOffsetY = { height -> height / 20 },
                                    animationSpec = tween(PinAuthConstants.STATE_TRANSITION_MS),
                                )
                            ).togetherWith(
                            fadeOut(animationSpec = tween(150)) +
                                slideOutVertically(
                                    targetOffsetY = { height -> -height / 20 },
                                    animationSpec = tween(150),
                                ),
                        )
                    },
                    label = "pin_auth_step_transition",
                ) { step ->
                    when (step) {
                        is PinAuthStep.InputPin -> {
                            InputPinVisual(
                                step = step,
                                onToggleVisibility = {
                                    currentStep = step.copy(showNumbers = !step.showNumbers)
                                },
                                onBackClick = {
                                    onResult(
                                        PinAuthResult(
                                            isSuccess = false,
                                            verificationType = PinVerificationType.PIN_ONLY,
                                        ),
                                    )
                                },
                                onContinueClick = {
                                    if (step.digits.length == step.length && !isVerifyingPin) {
                                        currentStep = step.copy(isVerifying = true)
                                        isVerifyingPin = true
                                    }
                                },
                                onRowTap = {
                                    focusRequester.requestFocus()
                                    keyboardController?.show()
                                },
                                modifier = Modifier.fillMaxWidth(),
                                onForgot = {
                                    onResult(
                                        PinAuthResult(
                                            isSuccess = false,
                                            isForgot = true,
                                        ),
                                    )
                                },
                            )
                        }

                        is PinAuthStep.DisplayOtp -> {
                            DisplayOtpVisual(
                                step = step,
                                onResendClick = {
                                    if (step.canResend && !step.isResending) {
                                        currentStep = step.copy(isResending = true)
                                        // Simulate resend delay
                                        scope.launch {
                                            delay(1000)
                                            handleResendOtp()
                                        }
                                    }
                                },
                                onBackClick = {
                                    // User cancels OTP verification
                                    onResult(
                                        PinAuthResult(
                                            isSuccess = false,
                                            verificationType = PinVerificationType.PIN_AND_OTP,
                                        ),
                                    )
                                },
                                onConfirmClick = {
                                    // Return OTP for verification WITHOUT dismissing dialog
                                    val otpCode = step.pinValues.filter { it != "_" }.joinToString("")
                                    if (otpCode.length == step.pinValues.size) {
                                        // Do NOT change UI to VERIFYING; ViewModel handles loading
                                        val bundle = Bundle().apply {
                                            putParcelable(
                                                "key_result",
                                                PinAuthResult(
                                                    isSuccess = true,
                                                    otpCode = otpCode,
                                                    transactionId = transactionId,
                                                    verificationType = PinVerificationType.PIN_AND_OTP,
                                                ),
                                            )
                                        }
                                        parentFragmentManager.setFragmentResult(PinAuthConstants.RESULT_KEY, bundle)
                                    }
                                },
                                onRowTap = {
                                    // Don't show keyboard for OTP display - just viewing
                                    // OTP is set programmatically, not typed
                                },
                                modifier = Modifier.fillMaxWidth(),
                            )
                        }
                    }
                }
            }

            // Single hoisted input field overlay - render LAST (top layer as true overlay)
            val visualLen = when (val s = currentStep) {
                is PinAuthStep.InputPin -> s.length
                is PinAuthStep.DisplayOtp -> s.pinValues.size
            }
            val valueForInput = when (val s = currentStep) {
                is PinAuthStep.InputPin -> s.digits
                is PinAuthStep.DisplayOtp -> s.pinValues.filter { it != "_" }.joinToString("")
            }

            StealthOtpInputField(
                value = valueForInput,
                length = visualLen,
                onValueChange = { new ->
                    when (val s = currentStep) {
                        is PinAuthStep.InputPin -> {
                            // Clear error when typing
                            val newState = if (s.state == PinState.ERROR && new != s.digits) {
                                PinState.INPUT
                            } else {
                                s.state
                            }
                            currentStep = s.copy(
                                digits = new,
                                state = newState,
                                errorMessage = if (newState == PinState.INPUT) null else s.errorMessage,
                            )
                        }
                        is PinAuthStep.DisplayOtp -> {
                            // Don't allow typing in OTP display state
                            // OTP is programmatically set, not user-typed
                            // User can only view and confirm
                        }
                    }
                },
                focusRequester = focusRequester,
                enabled = when (val s = currentStep) {
                    is PinAuthStep.InputPin -> !s.isVerifying && s.state != PinState.SUCCESS
                    is PinAuthStep.DisplayOtp -> false // Disable input for OTP display
                },
                onDone = {
                    when (val s = currentStep) {
                        is PinAuthStep.InputPin -> {
                            if (valueForInput.length == s.length && !isVerifyingPin) {
                                currentStep = s.copy(isVerifying = true)
                                isVerifyingPin = true
                            }
                        }
                        is PinAuthStep.DisplayOtp -> {
                            // No action on done for display state
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    // TODO: Hardcoded overlay height - temporary until design is finalized
                    .height(PinAuthDialogConstants.OVERLAY_HEIGHT_DP.dp)
                    .zIndex(1f), // Ensure overlay is on top
            )
        }
    }
}
