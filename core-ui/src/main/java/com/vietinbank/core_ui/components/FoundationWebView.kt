package com.vietinbank.core_ui.components

import android.annotation.SuppressLint
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun FoundationWebview(
    url: String,
    modifier: Modifier = Modifier,
    enableJavaScript: Boolean = false,
    onPageStarted: (() -> Unit)? = null,
    onPageFinished: (() -> Unit)? = null,
    onError: ((errorCode: Int, description: String?, failingUrl: String?) -> Unit)? = null,
) {
    val context = LocalContext.current

    val webView = remember {
        WebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
            )

            if (enableJavaScript) {
                settings.javaScriptEnabled = true
            }

            webViewClient = object : WebViewClient() {
                override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    onPageStarted?.invoke()
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    onPageFinished?.invoke()
                }

                override fun onReceivedError(
                    view: WebView?,
                    request: android.webkit.WebResourceRequest?,
                    error: android.webkit.WebResourceError?,
                ) {
                    super.onReceivedError(view, request, error)
                    onError?.invoke(error?.errorCode ?: -1, error?.description?.toString(), request?.url.toString())
                }
            }

            webChromeClient = WebChromeClient()
        }
    }

    AndroidView(
        modifier = modifier,
        factory = { webView },
        update = { it.loadUrl(url) },
    )
}
