package com.vietinbank.core_ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.vietinbank.core_ui.utils.glassBottomGradient
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun FoundationAreaBottomSheet(
    contentBlur: (@Composable () -> Unit)? = null,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .glassBottomGradient(
                endColor = FDS.Colors.blue900,
                alpha = 0.675f,
            )
            .padding(FDS.Sizer.Gap.gap16),
    ) {
        contentBlur?.invoke()
    }
}