package com.vietinbank.core_ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.innerShadow
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun UserProfileCard(
    modifier: Modifier = Modifier,
    title: String = stringResource(R.string.account_card_account_name),
    content: String = "",
    isLightCard: Boolean = false,
    icon: Painter? = null,
    iconTint: Color = Color.White,
    onClickAvatar: () -> Unit,
    onClickIcon: () -> Unit,
) {
    val (titleColor, contentColor) = if (isLightCard) {
        FDS.Colors.tabTextActiveInline to FDS.Colors.tabTextSecondary
    } else {
        Color.White to Color.White.copy(0.8f)
    }
    Row(
        modifier = modifier
            .padding(horizontal = FDS.Sizer.Padding.padding24)
            .height(FDS.Sizer.Padding.padding64)
            .widthIn(min = FDS.Sizer.Padding.padding280)
            .clip(RoundedCornerShape(50))
            .then(
                if (isLightCard) {
                    Modifier.background(Color.White)
                } else {
                    Modifier
                        .gradientBorder()
                        .innerShadow(
                            shape = CircleShape,
                            color = Color.White.copy(0.56f),
                            offsetY = (-2).dp,
                        )
                        .innerShadow(
                            shape = CircleShape,
                            color = Color.White.copy(0.56f),
                            offsetY = (2).dp,
                        )
                },
            )
            .padding(FDS.Sizer.Padding.padding8),
        horizontalArrangement = Arrangement.spacedBy(
            FDS.Sizer.Padding.padding8,
            Alignment.CenterHorizontally,
        ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon48)
                .background(
                    color = FDS.Colors.blue100,
                    shape = CircleShape,
                )
                .safeClickable {
                    onClickAvatar()
                },
            contentAlignment = Alignment.Center,
        ) {
            val initials = title.getInitials()

            FoundationText(
                text = initials,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
        }

        Column(
            modifier = Modifier
                .weight(1f)
                .safeClickable {
                    onClickAvatar()
                },
        ) {
            FoundationText(
                text = title,
                color = titleColor,
                style = FDS.Typography.bodyB2Emphasized,
                modifier = Modifier
                    .fillMaxWidth()
                    .basicMarquee(
                        iterations = Int.MAX_VALUE,
                        repeatDelayMillis = 0,
                    ),
            )
            FoundationText(
                text = content,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Padding.padding8)
                    .basicMarquee(
                        iterations = Int.MAX_VALUE,
                        repeatDelayMillis = 0,
                    ),
                color = contentColor,
                style = FDS.Typography.captionCaptionL,
            )
        }

        icon?.let {
            Box(
                modifier = Modifier
                    .padding(vertical = FDS.Sizer.Padding.padding8)
                    .width(FDS.Sizer.Padding.padding2)
                    .fillMaxHeight()
                    .background(Color.White.copy(0.1f)),
            )

            Icon(
                painter = it,
                contentDescription = "",
                modifier = Modifier
                    .padding(end = FDS.Sizer.Padding.padding8)
                    .size(FDS.Sizer.Padding.padding32)
                    .padding(FDS.Sizer.Padding.padding2)
                    .safeClickable {
                        onClickIcon()
                    },
                tint = iconTint,
            )
        }
    }
}