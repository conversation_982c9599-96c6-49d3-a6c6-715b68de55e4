package com.vietinbank.core_ui.components.date

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CalendarLocale
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DatePickerFormatter
import androidx.compose.material3.DateRangePicker
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDateRangePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.toDateString
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import java.util.Calendar
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoundationDateRangePicker(
    startDate: String? = null,
    endDate: String? = null,
    rangeValidate: Int = 0,
    onDismiss: () -> Unit,
    onSelected: (String?, String?) -> Unit,
) {
    val context = LocalContext.current
    var isShowModeToggle by remember { mutableStateOf(false) }
    val todayTimeMillis = remember { System.currentTimeMillis() }
    val initStart = remember(startDate) { startDate?.toTimeInMillis(dd_MM_yyyy_1) }
    val initEnd = remember(endDate) { endDate?.toTimeInMillis(dd_MM_yyyy_1) }
    var endLimitTimeMillis by remember { mutableLongStateOf(todayTimeMillis) }
    val selectableDates = remember(endLimitTimeMillis) {
        object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                return if (endLimitTimeMillis <= todayTimeMillis) {
                    utcTimeMillis <= endLimitTimeMillis
                } else {
                    utcTimeMillis <= todayTimeMillis
                }
            }
        }
    }

    val dateRangePickerState = rememberDateRangePickerState(
        initialDisplayedMonthMillis = todayTimeMillis,
        initialSelectedStartDateMillis = initStart,
        initialSelectedEndDateMillis = initEnd,
        selectableDates = selectableDates,
    )

    LaunchedEffect(
        dateRangePickerState.selectedStartDateMillis,
        dateRangePickerState.selectedEndDateMillis,
    ) {
        val currentStart = dateRangePickerState.selectedStartDateMillis
        val currentEnd = dateRangePickerState.selectedEndDateMillis
        when {
            // lần đầu => luôn chọn ngày bắt đầu
            // user chọn Start Date -> cập nhật giới hạn End Date
            currentStart != null && currentEnd == null && rangeValidate != 0 -> {
                endLimitTimeMillis = currentStart.plus(rangeValidate * 86_400_000L)
            }
            // chọn range khong phải range vừa chọn => tự dọng dismiss dialog
            currentStart != null && currentEnd != null && (currentStart != initStart || currentEnd != initEnd) -> {
                endLimitTimeMillis = todayTimeMillis
                onDismiss()
                onSelected(
                    currentStart.toDateString(),
                    currentEnd.toDateString(),
                )
            }

            else -> {
            }
        }
    }
    DatePickerDialog(
        colors = DatePickerDefaults.colors().copy(
            containerColor = FDS.Colors.white,
        ),
        onDismissRequest = {
            onDismiss()
        },
        dismissButton = {
            TextButton(onClick = { onDismiss() }) {
                FoundationText(
                    text = stringResource(R.string.common_cancel),
                    style = FDS.Typography.interactionButton,
                    color = FDS.Colors.characterHighlighted,
                )
            }
        },
        confirmButton = {
            TextButton(onClick = {
                if (dateRangePickerState.selectedStartDateMillis != null && dateRangePickerState.selectedEndDateMillis != null) {
                    onDismiss()
                }
            }) {
                FoundationText(
                    text = stringResource(R.string.common_agree),
                    style = FDS.Typography.interactionButton,
                    color = FDS.Colors.characterHighlighted,
                )
            }
        },
    ) {
        DateRangePicker(
            state = dateRangePickerState,
            dateFormatter = object : DatePickerFormatter {
                override fun formatDate(
                    dateMillis: Long?,
                    locale: CalendarLocale,
                    forContentDescription: Boolean,
                ): String? {
                    return dateMillis?.toDateString(dd_MM_yyyy_1)
                }

                override fun formatMonthYear(
                    monthMillis: Long?,
                    locale: CalendarLocale,
                ): String? {
                    return monthMillis?.let {
                        val calendar = Calendar.getInstance().apply { timeInMillis = monthMillis }
                        val month = calendar.get(Calendar.MONTH) + 1
                        val year = calendar.get(Calendar.YEAR)
                        context.getString(R.string.common_format_date, month, year)
                    }
                }
            },
            title = {
                FoundationText(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap24)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    text = stringResource(R.string.common_select_date),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                )
            },
            colors = DatePickerDefaults.colors(
                containerColor = FDS.Colors.white,
                weekdayContentColor = FDS.Colors.characterPrimary,
                dayContentColor = FDS.Colors.characterPrimary,
                disabledDayContentColor = FDS.Colors.characterTertiary,
                selectedDayContentColor = FDS.Colors.white,
                todayContentColor = FDS.Colors.stateActive,
                todayDateBorderColor = FDS.Colors.stateActive,
                dayInSelectionRangeContainerColor = FDS.Colors.gray50,
                dayInSelectionRangeContentColor = FDS.Colors.characterPrimary,
                selectedDayContainerColor = FDS.Colors.stateActive,
            ).copy(),
            headline = {
                val startDate =
                    if (dateRangePickerState.selectedStartDateMillis != null && dateRangePickerState.selectedStartDateMillis != 0L) {
                        dateRangePickerState.selectedStartDateMillis?.toDateString() ?: ""
                    } else {
                        stringResource(R.string.common_start_date)
                    }

                val endDate =
                    if (dateRangePickerState.selectedEndDateMillis != null && dateRangePickerState.selectedEndDateMillis != 0L) {
                        dateRangePickerState.selectedEndDateMillis?.toDateString() ?: ""
                    } else {
                        stringResource(R.string.common_end_date)
                    }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap24,
                            vertical = FDS.Sizer.Gap.gap16,
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        FoundationText(
                            text = startDate,
                            style = FDS.Typography.interactionButton,
                            color = FDS.Colors.characterHighlighted,
                        )
                        FoundationText(" - ")
                        FoundationText(
                            text = endDate,
                            style = FDS.Typography.interactionButton,
                            color = FDS.Colors.characterHighlighted,
                        )
                    }
                }
            },
            showModeToggle = true,
        )
    }
}