@file:OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)

package com.vietinbank.core_ui.components

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.invisibleToUser
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.lerp
import androidx.compose.ui.util.lerp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for FoundationAppBar animations and layout
 */
private object AppBarConstants {
    // Animation easing curves - Material Design standard
    val STANDARD_EASING_X1 = 0.4f
    val STANDARD_EASING_Y1 = 0.0f
    val STANDARD_EASING_X2 = 0.2f
    val STANDARD_EASING_Y2 = 1.0f

    val DECELERATE_EASING_X1 = 0.0f
    val DECELERATE_EASING_Y1 = 0.0f
    val DECELERATE_EASING_X2 = 0.2f
    val DECELERATE_EASING_Y2 = 1.0f

    // Animation thresholds
    const val SMALL_TITLE_THRESHOLD = 0.3f
    const val LARGE_TITLE_THRESHOLD = 0.7f
    const val ALPHA_VISIBILITY_THRESHOLD = 0.01f

    // Scale values
    const val LARGE_TITLE_SCALE_MIN = 0.97f
    const val LARGE_TITLE_SCALE_MAX = 1.0f

    // Transform origins
    const val TRANSFORM_ORIGIN_LEFT = 0f
    const val TRANSFORM_ORIGIN_CENTER = 0.5f
    const val TRANSFORM_ORIGIN_TOP = 0f

    // Text display
    const val SINGLE_LINE_MAX_LINES = 1
    const val MULTI_LINE_MAX_LINES = 2

    // Position offsets (in dp) - will be converted using FDS multipliers
    const val OFFSET_X_MULTIPLIER = 1.5f // 24dp = gap16 * 1.5
    const val OFFSET_Y_MULTIPLIER = -2.25f // -36dp = gap16 * -2.25
}

/**
 * Foundation App Bar Component
 * Transparent app bar with glass morphism buttons based on Foundation Design System
 *
 * @param title The title text to display (left-aligned below buttons)
 * @param onNavigationClick Navigation click handler (required if navigationIcon is null and not using default)
 * @param navigationIcon Optional custom navigation icon painter
 * @param isBackHome Whether to show home icon instead of back icon
 * @param actions List of action buttons to display on the right side
 * @param customActions Optional custom composable for right side actions
 * @param modifier Modifier to be applied to the app bar
 * @param isLightIcon Whether to use light icon theme (true) or dark icon theme (false)
 * @param showLogo Whether to show logo in the center (only for multi-line mode)
 * @param logoIcon Optional custom logo icon painter (default: bank success logo)
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FoundationAppBar(
    title: String,
    onNavigationClick: () -> Unit,
    modifier: Modifier = Modifier,
    navigationIcon: Painter? = null,
    isBackHome: Boolean = false,
    actions: List<AppBarAction> = emptyList(),
    customActions: @Composable (() -> Unit)? = null,
    isLightIcon: Boolean = true,
    titleStyle: TextStyle = FDS.Typography.headingH2,
    isSingleLineAppBar: Boolean = false,
    isCustomActionUseSafeClick: Boolean = true,
    // Optional scroll state to enable large-title collapse on scroll (wrapper to avoid leaking experimental types)
    scrollState: FoundationAppBarScrollState? = null,
    collapsedTitleStyle: TextStyle = FDS.Typography.headingH3,
    showLogo: Boolean = false,
    logoIcon: Painter? = null,
) {
    val titleColor = if (isLightIcon) FDS.Colors.tabTextActiveInline else FDS.Colors.white
    val defaultNavIcon = if (isBackHome) {
        painterResource(R.drawable.ic_home)
    } else {
        painterResource(R.drawable.ic_common_back_24dp)
    }
    val defaultLogoIcon = logoIcon ?: painterResource(R.drawable.ic_common_success_logo_bank)

    if (isSingleLineAppBar) {
        // Single-line layout: nav, title, and actions
        Row(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Navigation button
            CircularIconButton(
                icon = navigationIcon ?: defaultNavIcon,
                onClick = onNavigationClick,
                contentDescription = stringResource(
                    if (isBackHome) {
                        R.string.content_description_home
                    } else {
                        R.string.content_description_back
                    },
                ),
                size = CircularIconButtonSize.Medium, // Use medium size for app bar
                isLightIcon = isLightIcon,
            )

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))

            // Title next to navigation button with ellipsis
            Text(
                text = title,
                style = titleStyle,
                color = titleColor,
                modifier = Modifier.weight(1f),
                maxLines = AppBarConstants.SINGLE_LINE_MAX_LINES,
                overflow = TextOverflow.Ellipsis,
            )

            // Custom actions or default actions
            if (customActions != null) {
                customActions()
            } else {
                actions.forEachIndexed { index, action ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                    }
                    CircularIconButton(
                        icon = painterResource(action.icon),
                        onClick = action.onClick,
                        contentDescription = action.contentDescription,
                        size = CircularIconButtonSize.Medium,
                        isLightIcon = isLightIcon,
                        isUseSafeClick = isCustomActionUseSafeClick,
                    )
                    if (index < actions.size - 1) {
                        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                    }
                }
            }
        }
    } else {
        // Multi-line layout: navigation row, then large title below
        // Material Design standard easing curves - smoother and more natural
        // FastOutSlowIn: Standard Material curve for most animations
        val standardEasing = CubicBezierEasing(
            AppBarConstants.STANDARD_EASING_X1,
            AppBarConstants.STANDARD_EASING_Y1,
            AppBarConstants.STANDARD_EASING_X2,
            AppBarConstants.STANDARD_EASING_Y2,
        )
        // DecelerateEasing: For elements entering the screen
        val decelerateEasing = CubicBezierEasing(
            AppBarConstants.DECELERATE_EASING_X1,
            AppBarConstants.DECELERATE_EASING_Y1,
            AppBarConstants.DECELERATE_EASING_X2,
            AppBarConstants.DECELERATE_EASING_Y2,
        )

        // Get scroll fraction directly for responsive animation
        val scrollFraction = scrollState?.material?.state?.collapsedFraction ?: 0f

        // Apply smoother easing with less aggressive curves
        // Use standard Material easing for most properties
        val positionProgress = standardEasing.transform(scrollFraction)
        // Slower opacity change for less jarring transition
        val opacityProgress = decelerateEasing.transform(scrollFraction)
        // Very subtle scale to avoid distortion
        val scaleProgress = scrollFraction // Linear for scale to avoid warping

        // Calculate dimensions
        val spacerHeight = lerp(FDS.Sizer.Gap.gap16, FDS.Sizer.Gap.gap0, scrollFraction)
        val density = LocalDensity.current
        var largeTitleHeightPx by remember { mutableStateOf(0) }
        var largeTitleHeightDp by remember { mutableStateOf(0.dp) }
        var hasMeasuredLargeTitle by remember { mutableStateOf(false) }

        // Set scroll height offset limit so collapsedFraction can progress based on scroll
        if (scrollState != null) {
            val spacerPx = with(density) { FDS.Sizer.Gap.gap16.toPx() }
            val collapseRange = (largeTitleHeightPx.toFloat() + spacerPx).coerceAtLeast(0f)
            // Negative limit as per TopAppBarState contract
            scrollState.material.state.heightOffsetLimit = -collapseRange
        }

        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding16),
        ) {
            // Top row area wrapped in Box so we can overlay the collapsed title aligned left (after nav icon)
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center,
            ) {
                // Navigation + actions row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    CircularIconButton(
                        icon = navigationIcon ?: defaultNavIcon,
                        onClick = onNavigationClick,
                        contentDescription = stringResource(
                            if (isBackHome) {
                                R.string.content_description_home
                            } else {
                                R.string.content_description_back
                            },
                        ),
                        size = CircularIconButtonSize.Medium,
                        isLightIcon = isLightIcon,
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    // Custom actions or default actions
                    if (customActions != null) {
                        customActions()
                    } else {
                        actions.forEachIndexed { index, action ->
                            CircularIconButton(
                                icon = painterResource(action.icon),
                                onClick = action.onClick,
                                contentDescription = action.contentDescription,
                                size = CircularIconButtonSize.Medium,
                                isLightIcon = isLightIcon,
                                isUseSafeClick = isCustomActionUseSafeClick,
                            )
                            if (index < actions.size - 1) {
                                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))
                            }
                        }
                    }
                }

                // Center logo with fade animation (only for multi-line with showLogo enabled)
                if (showLogo) {
                    // Logo fades out synchronized with large title using same timing and easing
                    val largeTitleThreshold = AppBarConstants.LARGE_TITLE_THRESHOLD
                    val logoProgress = (scrollFraction / largeTitleThreshold).coerceIn(0f, 1f)
                    val logoAlpha = (1f - logoProgress).coerceIn(0f, 1f)

                    // Apply same easing as other elements for consistent animation
                    val easedLogoAlpha = decelerateEasing.transform(logoAlpha)

                    // Subtle scale animation for smoother transition
                    val logoScale = lerp(1f, 0.95f, logoProgress)

                    if (easedLogoAlpha > AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) {
                        androidx.compose.foundation.Image(
                            painter = defaultLogoIcon,
                            contentDescription = "Bank Logo",
                            modifier = Modifier
                                .align(Alignment.Center)
                                .graphicsLayer {
                                    alpha = easedLogoAlpha
                                    scaleX = logoScale
                                    scaleY = logoScale
                                    transformOrigin = TransformOrigin(
                                        AppBarConstants.TRANSFORM_ORIGIN_CENTER,
                                        AppBarConstants.TRANSFORM_ORIGIN_CENTER,
                                    )
                                }
                                .semantics {
                                    if (easedLogoAlpha <= AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) invisibleToUser()
                                },
                        )
                    }
                }

                // Collapsed (small) title with smooth fade animation
                // Delay the appearance slightly to reduce flicker
                val smallTitleThreshold = AppBarConstants.SMALL_TITLE_THRESHOLD
                val adjustedProgress = ((scrollFraction - smallTitleThreshold) / (AppBarConstants.LARGE_TITLE_SCALE_MAX - smallTitleThreshold)).coerceIn(0f, AppBarConstants.LARGE_TITLE_SCALE_MAX)
                val smallTitleAlpha = if (scrollFraction > smallTitleThreshold) adjustedProgress else 0f

                if (smallTitleAlpha > AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) {
                    Text(
                        text = title,
                        style = collapsedTitleStyle,
                        color = titleColor,
                        maxLines = AppBarConstants.SINGLE_LINE_MAX_LINES,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(start = FDS.Sizer.Icon.icon48 + FDS.Sizer.Gap.gap16)
                            .graphicsLayer {
                                alpha = smallTitleAlpha
                                // Remove scale animation to reduce complexity
                                // Just use fade for cleaner transition
                                transformOrigin = TransformOrigin(
                                    AppBarConstants.TRANSFORM_ORIGIN_LEFT,
                                    AppBarConstants.TRANSFORM_ORIGIN_CENTER,
                                )
                            }
                            .semantics {
                                if (smallTitleAlpha <= AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) invisibleToUser()
                            },
                    )
                }
            }

            // Animate space between row and large title to create a smooth collapse
            Spacer(modifier = Modifier.height(spacerHeight))

            // Large title with transform animations for smooth transition
            // Fade out earlier to reduce overlap with small title
            val largeTitleThreshold = AppBarConstants.LARGE_TITLE_THRESHOLD
            val adjustedLargeProgress = (scrollFraction / largeTitleThreshold).coerceIn(0f, AppBarConstants.LARGE_TITLE_SCALE_MAX)
            val largeTitleAlpha = (AppBarConstants.LARGE_TITLE_SCALE_MAX - adjustedLargeProgress).coerceIn(0f, AppBarConstants.LARGE_TITLE_SCALE_MAX)

            // More subtle scale - almost imperceptible to avoid distortion
            val largeTitleScale = lerp(
                AppBarConstants.LARGE_TITLE_SCALE_MAX,
                AppBarConstants.LARGE_TITLE_SCALE_MIN,
                scaleProgress,
            )

            // Smaller position offsets for less dramatic movement
            val offsetX = with(density) {
                lerp(
                    FDS.Sizer.Gap.gap0,
                    FDS.Sizer.Gap.gap16 * AppBarConstants.OFFSET_X_MULTIPLIER,
                    positionProgress,
                ).toPx()
            }
            val offsetY = with(density) {
                lerp(
                    FDS.Sizer.Gap.gap0,
                    FDS.Sizer.Gap.gap16 * AppBarConstants.OFFSET_Y_MULTIPLIER,
                    positionProgress,
                ).toPx()
            }

            if (largeTitleAlpha > AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .graphicsLayer {
                            // Apply all transformations in paint phase for performance
                            alpha = largeTitleAlpha
                            translationX = offsetX
                            translationY = offsetY
                            scaleX = largeTitleScale
                            scaleY = largeTitleScale
                            transformOrigin = TransformOrigin(
                                AppBarConstants.TRANSFORM_ORIGIN_LEFT,
                                AppBarConstants.TRANSFORM_ORIGIN_TOP,
                            )
                        },
                ) {
                    Text(
                        text = title,
                        style = titleStyle,
                        color = titleColor,
                        maxLines = AppBarConstants.MULTI_LINE_MAX_LINES,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .fillMaxWidth()
                            .then(
                                if (!hasMeasuredLargeTitle) {
                                    Modifier.onGloballyPositioned { layoutCoordinates ->
                                        largeTitleHeightPx = layoutCoordinates.size.height
                                        largeTitleHeightDp = with(density) { largeTitleHeightPx.toDp() }
                                        hasMeasuredLargeTitle = largeTitleHeightPx > 0
                                    }
                                } else {
                                    Modifier
                                },
                            )
                            .semantics {
                                if (largeTitleAlpha <= AppBarConstants.ALPHA_VISIBILITY_THRESHOLD) invisibleToUser()
                            },
                    )
                }
            }
        }
    }
}

/**
 * Preview functions for FoundationAppBar
 */
@Preview(name = "AppBar with Back Navigation", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarBackPreview() {
    FoundationAppBar(
        title = stringResource(R.string.preview_title_transfer),
        onNavigationClick = { },
    )
}

@Preview(name = "AppBar with Home Navigation", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarHomePreview() {
    FoundationAppBar(
        title = stringResource(R.string.preview_title_dashboard),
        onNavigationClick = { },
        isBackHome = true,
    )
}

@Preview(name = "AppBar with Actions", backgroundColor = 0xFF000000, showBackground = true)
@Composable
private fun FoundationAppBarActionsPreview() {
    FoundationAppBar(
        title = stringResource(R.string.preview_title_settings),
        onNavigationClick = { },
        actions = listOf(
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = stringResource(R.string.content_description_notifications),
                onClick = { },
            ),
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = stringResource(R.string.content_description_settings),
                onClick = { },
            ),
        ),
    )
}

@Preview(name = "AppBar with Dark Icons", backgroundColor = 0xFFE8F0F8, showBackground = true)
@Composable
private fun FoundationAppBarDarkIconsPreview() {
    FoundationAppBar(
        title = stringResource(R.string.preview_title_dark_theme),
        onNavigationClick = { },
        isLightIcon = false,
        actions = listOf(
            AppBarAction(
                icon = R.drawable.ic_common_back_24dp,
                contentDescription = stringResource(R.string.content_description_notifications),
                onClick = { },
            ),
        ),
    )
}
