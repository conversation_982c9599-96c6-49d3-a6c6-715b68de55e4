package com.vietinbank.core_ui.components.foundation.pin

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import kotlinx.coroutines.delay
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * PIN state enum for PinHiddenComponent
 */
enum class PinState {
    INPUT, // User is entering PIN
    SUCCESS, // PIN entry successful
    ERROR, // PIN entry error
    EXPIRED, // PIN entry EXPIRED
}

/**
 * PIN Hidden Component - A stateless UI component for PIN input with hide/show functionality
 *
 * This component displays a PIN input interface that can toggle between hidden (dots)
 * and visible (numbers) modes. It supports different visual states (input, success, error)
 * and includes animations for state transitions.
 *
 * Features:
 * - Toggle between hidden dots and visible numbers
 * - 3 visual states: INPUT, SUCCESS, ERROR
 * - Scale animation on visibility toggle
 * - Shake animation on error state
 * - Haptic feedback on success
 * - Responsive layout for narrow screens
 * - Accessibility support with content descriptions
 *
 * @param state Current PIN state (INPUT, SUCCESS, ERROR)
 * @param showNumbers Whether to show actual numbers (true) or dots (false)
 * @param digits Current PIN digits entered (e.g., "165" for 3 digits entered)
 * @param length Total PIN length (default 6)
 * @param onToggleVisibility Callback when eye icon is tapped to toggle visibility
 * @param onRowTap Optional callback when PIN row is tapped (for focus management)
 * @param hideToggleIcon Optional flag to hide the eye toggle icon (default false for backward compatibility)
 * @param modifier Optional modifier for the component
 *
 * Usage example:
 * ```kotlin
 * var pinState by remember { mutableStateOf(PinState.INPUT) }
 * var showNumbers by remember { mutableStateOf(false) }
 * var digits by remember { mutableStateOf("") }
 *
 * PinHiddenComponent(
 *     state = pinState,
 *     showNumbers = showNumbers,
 *     digits = digits,
 *     length = 6,
 *     onToggleVisibility = { showNumbers = !showNumbers },
 *     onRowTap = { focusRequester.requestFocus() }
 * )
 * ```
 */
@Composable
fun PinHiddenComponent(
    modifier: Modifier = Modifier,
    state: PinState,
    showNumbers: Boolean,
    digits: String,
    length: Int = 6,
    onToggleVisibility: () -> Unit,
    onRowTap: () -> Unit = {},
    hideToggleIcon: Boolean = false,
) {
    val hapticFeedback = LocalHapticFeedback.current

    // Guard input to prevent overflow
    val safeDigits = digits.take(length)

    // Use LocalConfiguration instead of BoxWithConstraints for better performance
    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp.dp
    val isNarrow = remember(screenWidthDp) { screenWidthDp < OtpCellConstants.MIN_CONTAINER_WIDTH }

    // Single Animatable instance to avoid allocations
    val shakeAnimation = remember { Animatable(0f) }

    // Animate shake only when state changes to ERROR
    LaunchedEffect(state) {
        when (state) {
            PinState.ERROR -> {
                // Reset and animate shake
                shakeAnimation.snapTo(0f)
                shakeAnimation.animateTo(
                    targetValue = 0f,
                    animationSpec = keyframes {
                        durationMillis = PinHiddenConstants.SHAKE_DURATION_MS
                        0f at 0 using FastOutSlowInEasing
                        PinHiddenConstants.SHAKE_AMPLITUDE_DP.toFloat() at 45 using FastOutSlowInEasing
                        -PinHiddenConstants.SHAKE_AMPLITUDE_DP.toFloat() at 90 using FastOutSlowInEasing
                        4f at 135 using FastOutSlowInEasing
                        0f at 180 using FastOutSlowInEasing
                    },
                )
            }
            PinState.SUCCESS -> {
                // Haptic feedback when PIN is successful
                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
            }
            PinState.INPUT -> {
                // Reset shake animation when returning to input state
                shakeAnimation.snapTo(0f)
            }

            PinState.EXPIRED -> {}
        }
    }

    // Defer eye toggle scale animation - only animate when actually toggling
    val toggleScale by animateFloatAsState(
        targetValue = if (showNumbers) PinHiddenConstants.TOGGLE_SCALE_MAX else 1f,
        animationSpec = tween(
            durationMillis = if (showNumbers) PinHiddenConstants.TOGGLE_SCALE_DURATION else 0,
            easing = FastOutSlowInEasing,
        ),
        label = "EyeToggleScale",
    )

    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            // PIN cells row with animations
            Box(
                modifier = Modifier
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                        onClick = onRowTap,
                    )
                    .graphicsLayer {
                        translationX = shakeAnimation.value.dp.toPx()
                        scaleX = toggleScale
                        scaleY = toggleScale
                    }
                    .semantics {
                        contentDescription = "PIN entry field with $length digits"
                    },
            ) {
                PinVisibleRow(
                    state = state,
                    digits = safeDigits,
                    length = length,
                    isNarrow = isNarrow,
                    isHidden = !showNumbers,
                )
            }

            // Eye icon for visibility toggle (only if not hidden)
            if (!hideToggleIcon) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

                // Cache icon painters to avoid repeated loading
                val eyeOpenPainter = painterResource(R.drawable.ic_dangnhap_eye_open)
                val eyeClosePainter = painterResource(R.drawable.ic_dangnhap_eye_close)

                IconButton(
                    onClick = onToggleVisibility,
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon24)
                        .semantics {
                            contentDescription = if (showNumbers) {
                                "Ẩn mã PIN"
                            } else {
                                "Hiện mã PIN"
                            }
                        },
                ) {
                    Icon(
                        painter = if (showNumbers) eyeOpenPainter else eyeClosePainter,
                        contentDescription = null,
                        tint = FDS.Colors.characterHighlighted,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    )
                }
            }
        }
    }
}

/**
 * Row of visible PIN boxes
 */
@Composable
private fun PinVisibleRow(
    state: PinState,
    digits: String,
    length: Int,
    isHidden: Boolean = false,
    isNarrow: Boolean,
) {
    val (backgroundColor, textColor) = when (state) {
        PinState.INPUT -> FDS.Colors.blue100 to FDS.Colors.characterHighlighted
        PinState.SUCCESS -> FDS.Colors.stateSuccessLighter to FDS.Colors.stateSuccess
        PinState.ERROR -> FDS.Colors.stateErrorLighter to FDS.Colors.stateError
        PinState.EXPIRED -> {
            FDS.Colors.stateWarningLighter to FDS.Colors.stateWarning
        }
    }

    // Only animate colors when state changes, not on initial load
    val isFirstLoad = remember { mutableStateOf(true) }
    LaunchedEffect(Unit) {
        isFirstLoad.value = false
    }

    val animatedBgColor by animateColorAsState(
        targetValue = backgroundColor,
        animationSpec = if (isFirstLoad.value) tween(0) else tween(300),
        label = "BackgroundColorAnimation",
    )

    val animatedTextColor by animateColorAsState(
        targetValue = textColor,
        animationSpec = if (isFirstLoad.value) tween(0) else tween(300),
        label = "TextColorAnimation",
    )

    // Build text list with digits and underscores (optimized for recomposition)
    val texts = remember(digits, length) {
        List(length) { i ->
            if (i < digits.length) digits[i].toString() else "_"
        }
    }

    // Use smaller boxes for PIN Hidden with visible numbers
    OtpRowVisibleSmall(
        otpItems = rememberOtpItems(texts, isHidden),
        backgroundColor = animatedBgColor,
        textColor = animatedTextColor,
        isNarrow = isNarrow,
    )
}

@Composable
private fun rememberOtpItems(
    list: List<String>,
    isHidden: Boolean,
): List<OtpItem> {
    val lastIndex = remember(list) { list.indexOfLast { it != "_" } }
    var forceDotAtLast by remember { mutableStateOf(false) }

    // effect: hiển thị số sau 400ms thì đổi về DOT nếu là hidden pin
    LaunchedEffect(list, isHidden, lastIndex) {
        if (isHidden && lastIndex != -1) {
            forceDotAtLast = false
            delay(400)
            forceDotAtLast = true
        } else {
            forceDotAtLast = false
        }
    }

    return remember(list, isHidden, forceDotAtLast) {
        list.mapIndexed { index, value ->
            when {
                value == "_" -> OtpItem(OTPCellType.DASH)
                index == lastIndex -> {
                    if (isHidden && forceDotAtLast) {
                        OtpItem(OTPCellType.DOT)
                    } else {
                        OtpItem(OTPCellType.DIGIT, value)
                    }
                }
                isHidden -> OtpItem(OTPCellType.DOT)
                else -> OtpItem(OTPCellType.DIGIT, value)
            }
        }
    }
}

/**
 * Row of hidden PIN dots
 */
@Composable
private fun PinHiddenRow(
    state: PinState,
    digits: String,
    length: Int,
) {
    when (state) {
        PinState.INPUT -> {
            val activeColor = FDS.Colors.blue500
            val inactiveColor = FDS.Colors.blue100

            OtpRowHidden(
                count = length,
                activeCount = digits.length,
                activeColor = activeColor,
                inactiveColor = inactiveColor,
            )
        }
        PinState.SUCCESS -> {
            val successColor by animateColorAsState(
                targetValue = FDS.Colors.palettePinGreen500,
                animationSpec = tween(300),
                label = "SuccessColorAnimation",
            )

            OtpRowUniformDots(
                count = length,
                color = successColor,
            )
        }
        PinState.ERROR -> {
            // Show hidden row with error palette but respect current digits length (often 0 after clear)
            val activeColor by animateColorAsState(
                targetValue = FDS.Colors.stateError,
                animationSpec = tween(300),
                label = "ErrorActiveColorAnimation",
            )
            val inactiveColor by animateColorAsState(
                targetValue = FDS.Colors.stateErrorLighter,
                animationSpec = tween(300),
                label = "ErrorInactiveColorAnimation",
            )

            OtpRowHidden(
                count = length,
                activeCount = digits.length,
                activeColor = activeColor,
                inactiveColor = inactiveColor,
            )
        }

        PinState.EXPIRED -> {
            val activeColor = FDS.Colors.stateWarning
            val inactiveColor = FDS.Colors.stateWarningLighter

            OtpRowHidden(
                count = length,
                activeCount = digits.length,
                activeColor = activeColor,
                inactiveColor = inactiveColor,
            )
        }
    }
}

/**
 * Private constants for animations
 */
private object PinHiddenConstants {
    const val TOGGLE_SCALE_MAX = 1.1f
    const val TOGGLE_SCALE_DURATION = 300 // Updated to match spec
    const val SHAKE_AMPLITUDE_DP = 6
    const val SHAKE_DURATION_MS = 180
    const val COLOR_ANIMATION_DURATION = 300
}
