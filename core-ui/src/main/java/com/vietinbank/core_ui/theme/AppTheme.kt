package com.vietinbank.core_ui.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 2/3/25.
 */

/**
 * AppTheme sử dụng Foundation Design System tokens
 *
 * Material3 components sẽ tự động dùng:
 * - Button: primary = stateActive (character/highlighted-lighter)
 * - Surface: surface = backgroundBgContainer
 * - Text: onSurface = characterPrimary
 * - Error states: error = stateError
 *
 * Custom components PHẢI dùng functional tokens trực tiếp:
 * - AppColors.characterHighlighted, AppColors.characterPrimary, etc.
 * - AppTypography.headingH1, AppTypography.bodyB1, etc.
 *
 * @param darkTheme Boolean to enable dark theme
 * @param content The composable content to be themed
 */
@Composable
fun AppTheme(
    darkTheme: Boolean = false, // isSystemInDarkTheme(),
    appConfigManager: IAppConfigManager? = null, // Optional to maintain backward compatibility
    // dynamicColor: Boolean = true, // Dynamic color option nếu cần
    content: @Composable () -> Unit,
) {
    // Color scheme using Foundation Design System functional tokens
    val colorScheme = if (darkTheme) {
        darkColorScheme(
            // Primary colors - using state tokens for interactive elements
            primary = FDS.Colors.stateActive, // character/highlighted-lighter
            onPrimary = FDS.Colors.characterInverse, // white text on primary
            primaryContainer = FDS.Colors.stateActiveLighter, // lighter variant
            onPrimaryContainer = FDS.Colors.characterPrimary, // text on primary container

            // Surface colors - using background tokens
            surface = FDS.Colors.backgroundBgContainer, // container background
            onSurface = FDS.Colors.characterPrimary, // primary text on surface
            surfaceVariant = FDS.Colors.backgroundBgContainer,
            onSurfaceVariant = FDS.Colors.characterSecondary, // secondary text

            // Background colors
            background = FDS.Colors.backgroundBgScreen, // screen background
            onBackground = FDS.Colors.characterInverse, // text on dark background

            // Error colors - using state tokens
            error = FDS.Colors.stateError, // error state
            onError = FDS.Colors.characterInverse, // white text on error
            errorContainer = FDS.Colors.stateErrorLighter, // error container
            onErrorContainer = FDS.Colors.characterPrimary, // text on error container

            // Other semantic colors
            outline = FDS.Colors.strokeDivider, // divider/border color
            outlineVariant = FDS.Colors.strokeDivider,
            scrim = FDS.Colors.shadowOuter,
        )
    } else {
        lightColorScheme(
            // Primary colors - using state tokens for interactive elements
            primary = FDS.Colors.stateActive, // character/highlighted-lighter
            onPrimary = FDS.Colors.characterInverse, // white text on primary
            primaryContainer = FDS.Colors.stateActiveLighter, // lighter variant
            onPrimaryContainer = FDS.Colors.characterPrimary, // text on primary container

            // Surface colors - using background tokens
            surface = FDS.Colors.backgroundBgContainer, // container background
            onSurface = FDS.Colors.characterPrimary, // primary text on surface
            surfaceVariant = FDS.Colors.backgroundBgContainer,
            onSurfaceVariant = FDS.Colors.characterSecondary, // secondary text

            // Background colors
            background = FDS.Colors.backgroundBgContainer, // light theme uses container
            onBackground = FDS.Colors.characterPrimary, // primary text

            // Error colors - using state tokens
            error = FDS.Colors.stateError, // error state
            onError = FDS.Colors.characterInverse, // white text on error
            errorContainer = FDS.Colors.stateErrorLighter, // error container
            onErrorContainer = FDS.Colors.characterPrimary, // text on error container

            // Other semantic colors
            outline = FDS.Colors.strokeDivider, // divider/border color
            outlineVariant = FDS.Colors.strokeDivider,
            scrim = FDS.Colors.shadowOuter,
        )
    }

    // Provide AppConfigManager if available
    val providers = buildList {
        appConfigManager?.let {
            add(LocalAppConfigManager provides it)
        }
    }

    // Apply MaterialTheme with Foundation Design System
    if (providers.isNotEmpty()) {
        CompositionLocalProvider(*providers.toTypedArray()) {
            MaterialTheme(
                colorScheme = colorScheme,
                typography = FDS.Typography.material3Typography,
                // shapes = Shapes, // Nếu cần định nghĩa riêng Shapes
                content = content,
            )
        }
    } else {
        // No providers, just apply theme directly
        MaterialTheme(
            colorScheme = colorScheme,
            typography = FDS.Typography.material3Typography,
            // shapes = Shapes, // Nếu cần định nghĩa riêng Shapes
            content = content,
        )
    }
}
