package com.vietinbank.core_ui.theme

import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.R

/**
 * Foundation Design System
 *
 * Usage:
 * - Import: `import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS`
 * - Access: `FDS.Colors.primary`, `FDS.Typography.headingH1`, `FDS.Sizer.padding16`
 *
 * Structure follows Figma design system organization:
 * - Colors: Semantic colors, palettes, component-specific colors
 * - Typography: Text styles with functional tokens
 * - Sizer: Dimensions (padding, gap, radius, stroke, etc.)
 * - Effects: Elevations and shadows
 */
object FoundationDesignSystem {

    // ========================================
    // COLORS
    // ========================================
    object Colors {
        // Helper function
        @Composable
        fun colorResource(@ColorRes id: Int): Color {
            return Color(LocalContext.current.getColor(id))
        }

        // ====== SEMANTIC COLORS ======
        /* Brand colors */
        val primary @Composable get() = colorResource(R.color.foundation_primary)
        val secondary @Composable get() = colorResource(R.color.foundation_secondary)
        val error @Composable get() = colorResource(R.color.foundation_error)
        val success @Composable get() = colorResource(R.color.foundation_success)
        val warning @Composable get() = colorResource(R.color.foundation_warning)
        val info @Composable get() = colorResource(R.color.foundation_info)

        /* Surface & Background */
        val background @Composable get() = colorResource(R.color.foundation_background)
        val surface @Composable get() = colorResource(R.color.foundation_surface)
        val glassOverlay @Composable get() = colorResource(R.color.foundation_glass_overlay)
        val glassBorder @Composable get() = colorResource(R.color.foundation_glass_border)

        /* Text colors */
        val textPrimary @Composable get() = colorResource(R.color.foundation_text_primary)
        val textSecondary @Composable get() = colorResource(R.color.foundation_text_secondary)
        val textTertiary @Composable get() = colorResource(R.color.foundation_text_tertiary)
        val textDisabled @Composable get() = colorResource(R.color.foundation_text_disabled)
        val textLink @Composable get() = colorResource(R.color.foundation_text_link)
        val textOnPrimary @Composable get() = colorResource(R.color.foundation_on_primary)
        val textSelected @Composable get() = colorResource(R.color.text_selected)

        /* Border & Divider */
        val outline @Composable get() = colorResource(R.color.foundation_outline)
        val divider @Composable get() = colorResource(R.color.foundation_divider)

        // ====== PALETTE COLORS ======
        /* Blue palette (Primary) */
        val blue50 @Composable get() = colorResource(R.color.foundation_primary_blue50)
        val blue100 @Composable get() = colorResource(R.color.foundation_primary_blue100)
        val blue200 @Composable get() = colorResource(R.color.foundation_primary_blue200)
        val blue300 @Composable get() = colorResource(R.color.foundation_primary_blue300)
        val blue400 @Composable get() = colorResource(R.color.foundation_primary_blue400)
        val blue500 @Composable get() = colorResource(R.color.foundation_primary_blue500)
        val blue600 @Composable get() = colorResource(R.color.foundation_primary_blue600)
        val blue700 @Composable get() = colorResource(R.color.foundation_primary_blue700)
        val blue800 @Composable get() = colorResource(R.color.foundation_primary_blue800)
        val blue900 @Composable get() = colorResource(R.color.foundation_primary_blue900)

        /* Red palette (Secondary) */
        val red50 @Composable get() = colorResource(R.color.foundation_secondary_red50)
        val red300 @Composable get() = colorResource(R.color.foundation_secondary_red300)
        val red400 @Composable get() = colorResource(R.color.foundation_secondary_red400)
        val red500 @Composable get() = colorResource(R.color.foundation_secondary_red500)
        val red600 @Composable get() = colorResource(R.color.foundation_secondary_red600)

        /* Gray palette (Neutrals) */
        val gray50 @Composable get() = colorResource(R.color.foundation_neutrals_gray50)
        val gray100 @Composable get() = colorResource(R.color.foundation_neutrals_gray100)
        val gray200 @Composable get() = colorResource(R.color.foundation_neutrals_gray200)
        val gray500 @Composable get() = colorResource(R.color.foundation_neutrals_gray500)
        val gray600 @Composable get() = colorResource(R.color.foundation_neutrals_gray600)
        val gray700 @Composable get() = colorResource(R.color.foundation_neutrals_gray700)
        val gray800 @Composable get() = colorResource(R.color.foundation_neutrals_gray800)
        val gray900 @Composable get() = colorResource(R.color.foundation_neutrals_gray900)
        val white @Composable get() = colorResource(R.color.foundation_neutrals_white)
        val black @Composable get() = colorResource(R.color.black)
        val transparent @Composable get() = colorResource(R.color.transparent)

        /* green palatte */
        val green100 @Composable get() = colorResource(R.color.foundation_success_green100)
        val green700 @Composable get() = colorResource(R.color.foundation_success_green700)
        val green800 @Composable get() = colorResource(R.color.foundation_success_green800)

        /* PIN/OTP Component Specific Palette */
        val palettePinGreen500 @Composable get() = colorResource(R.color.foundation_palette_pin_green500)
        val palettePinRed400 @Composable get() = colorResource(R.color.foundation_palette_pin_red400)

        // ====== FUNCTIONAL TOKENS ======
        /* Character Tokens */
        val characterHighlighted @Composable get() = colorResource(R.color.foundation_character_highlighted)
        val characterHighlightedLighter @Composable get() = colorResource(R.color.foundation_character_highlighted_lighter)
        val characterPrimary @Composable get() = colorResource(R.color.foundation_character_primary)
        val characterSecondary @Composable get() = colorResource(R.color.foundation_character_secondary)
        val characterTertiary @Composable get() = colorResource(R.color.foundation_character_tertiary)
        val characterInverse @Composable get() = colorResource(R.color.foundation_character_inverse)
        val characterDark @Composable get() = colorResource(R.color.foundation_character_dark)
        val characterDarkTransparent @Composable get() = colorResource(R.color.foundation_character_dark_transparent)
        val characterHighlightSoft @Composable get() = colorResource(R.color.foundation_character_highlight_soft)

        /* Background Tokens */
        val backgroundBgHighlight @Composable get() = colorResource(R.color.foundation_background_bg_highlight)
        val backgroundBgContainer @Composable get() = colorResource(R.color.foundation_background_bg_container)
        val backgroundBgScreen @Composable get() = colorResource(R.color.foundation_background_bg_screen)
        val backgroundBgOnColor @Composable get() = colorResource(R.color.foundation_background_bg_on_color)
        val backgroundHitbox @Composable get() = colorResource(R.color.foundation_background_hitbox)
        val backgroundBgContainerGrey @Composable get() = colorResource(R.color.foundation_background_bg_container_grey)
        val backgroundBgHighlightSoft @Composable get() = colorResource(R.color.foundation_background_bg_highlight_soft)

        /* Stroke Tokens */
        val strokeDivider @Composable get() = colorResource(R.color.foundation_stroke_divider)
        val strokeBadge @Composable get() = colorResource(R.color.foundation_stroke_badge)
        val strokeTransparent @Composable get() = colorResource(R.color.foundation_stroke_transparent)
        val strokeTransparent2 @Composable get() = colorResource(R.color.foundation_stroke_transparent_2)

        /* State Tokens */
        val stateError @Composable get() = colorResource(R.color.foundation_state_error)
        val stateWarning @Composable get() = colorResource(R.color.foundation_state_warning)
        val stateSuccess @Composable get() = colorResource(R.color.foundation_state_success)
        val stateActive @Composable get() = colorResource(R.color.foundation_state_active)
        val stateErrorLighter @Composable get() = colorResource(R.color.foundation_state_error_lighter)
        val stateWarningLighter @Composable get() = colorResource(R.color.foundation_state_warning_lighter)
        val stateSuccessLighter @Composable get() = colorResource(R.color.foundation_state_success_lighter)
        val stateActiveLighter @Composable get() = colorResource(R.color.foundation_state_active_lighter)
        val stateBadgeCounting @Composable get() = colorResource(R.color.foundation_state_badge_counting)

        // ====== PALETTE COLORS ======
        /* Neutral Palette */
        val paletteNeutral0 @Composable get() = colorResource(R.color.foundation_palette_neutral_0)
        val paletteNeutral50 @Composable get() = colorResource(R.color.foundation_palette_neutral_50)
        val paletteNeutral100 @Composable get() = colorResource(R.color.foundation_palette_neutral_100)
        val paletteNeutral200 @Composable get() = colorResource(R.color.foundation_palette_neutral_200)
        val paletteNeutral300 @Composable get() = colorResource(R.color.foundation_palette_neutral_300)
        val paletteNeutral400 @Composable get() = colorResource(R.color.foundation_palette_neutral_400)
        val paletteNeutral500 @Composable get() = colorResource(R.color.foundation_palette_neutral_500)
        val paletteNeutral600 @Composable get() = colorResource(R.color.foundation_palette_neutral_600)
        val paletteNeutral700 @Composable get() = colorResource(R.color.foundation_palette_neutral_700)
        val paletteNeutral800 @Composable get() = colorResource(R.color.foundation_palette_neutral_800)
        val paletteNeutral900 @Composable get() = colorResource(R.color.foundation_palette_neutral_900)

        /* Blue Palette */
        val paletteBlue50 @Composable get() = colorResource(R.color.foundation_palette_blue_50)
        val paletteBlue100 @Composable get() = colorResource(R.color.foundation_palette_blue_100)
        val paletteBlue200 @Composable get() = colorResource(R.color.foundation_palette_blue_200)
        val paletteBlue300 @Composable get() = colorResource(R.color.foundation_palette_blue_300)
        val paletteBlue400 @Composable get() = colorResource(R.color.foundation_palette_blue_400)
        val paletteBlue500 @Composable get() = colorResource(R.color.foundation_palette_blue_500)
        val paletteBlue600 @Composable get() = colorResource(R.color.foundation_palette_blue_600)
        val paletteBlue700 @Composable get() = colorResource(R.color.foundation_palette_blue_700)
        val paletteBlue800 @Composable get() = colorResource(R.color.foundation_palette_blue_800)
        val paletteBlue900 @Composable get() = colorResource(R.color.foundation_palette_blue_900)

        /* Red Palette */
        val paletteRed50 @Composable get() = colorResource(R.color.foundation_palette_red_50)
        val paletteRed100 @Composable get() = colorResource(R.color.foundation_palette_red_100)
        val paletteRed200 @Composable get() = colorResource(R.color.foundation_palette_red_200)
        val paletteRed300 @Composable get() = colorResource(R.color.foundation_palette_red_300)
        val paletteRed400 @Composable get() = colorResource(R.color.foundation_palette_red_400)
        val paletteRed500 @Composable get() = colorResource(R.color.foundation_palette_red_500)
        val paletteRed600 @Composable get() = colorResource(R.color.foundation_palette_red_600)
        val paletteRed700 @Composable get() = colorResource(R.color.foundation_palette_red_700)
        val paletteRed800 @Composable get() = colorResource(R.color.foundation_palette_red_800)
        val paletteRed900 @Composable get() = colorResource(R.color.foundation_palette_red_900)

        /* Yellow Palette */
        val paletteYellow50 @Composable get() = colorResource(R.color.foundation_palette_yellow_50)
        val paletteYellow100 @Composable get() = colorResource(R.color.foundation_palette_yellow_100)
        val paletteYellow200 @Composable get() = colorResource(R.color.foundation_palette_yellow_200)
        val paletteYellow300 @Composable get() = colorResource(R.color.foundation_palette_yellow_300)
        val paletteYellow400 @Composable get() = colorResource(R.color.foundation_palette_yellow_400)
        val paletteYellow500 @Composable get() = colorResource(R.color.foundation_palette_yellow_500)
        val paletteYellow600 @Composable get() = colorResource(R.color.foundation_palette_yellow_600)
        val paletteYellow700 @Composable get() = colorResource(R.color.foundation_palette_yellow_700)
        val paletteYellow800 @Composable get() = colorResource(R.color.foundation_palette_yellow_800)
        val paletteYellow900 @Composable get() = colorResource(R.color.foundation_palette_yellow_900)

        /* Green Palette */
        val paletteGreen50 @Composable get() = colorResource(R.color.foundation_palette_green_50)
        val paletteGreen100 @Composable get() = colorResource(R.color.foundation_palette_green_100)
        val paletteGreen200 @Composable get() = colorResource(R.color.foundation_palette_green_200)
        val paletteGreen300 @Composable get() = colorResource(R.color.foundation_palette_green_300)
        val paletteGreen400 @Composable get() = colorResource(R.color.foundation_palette_green_400)
        val paletteGreen500 @Composable get() = colorResource(R.color.foundation_palette_green_500)
        val paletteGreen600 @Composable get() = colorResource(R.color.foundation_palette_green_600)
        val paletteGreen700 @Composable get() = colorResource(R.color.foundation_palette_green_700)
        val paletteGreen800 @Composable get() = colorResource(R.color.foundation_palette_green_800)
        val paletteGreen900 @Composable get() = colorResource(R.color.foundation_palette_green_900)

        // ====== COMPONENT-SPECIFIC COLORS ======
        /* Shadow colors */
        val shadowSm @Composable get() = colorResource(R.color.foundation_shadow_sm)
        val shadowMd @Composable get() = colorResource(R.color.foundation_shadow_md)
        val shadowLg @Composable get() = colorResource(R.color.foundation_shadow_lg)
        val shadowOuter @Composable get() = colorResource(R.color.foundation_shadow_outer)
        val shadowInnerWhite @Composable get() = colorResource(R.color.foundation_shadow_inner_white)
        val shadowInnerWhiteSoft @Composable get() = colorResource(R.color.foundation_shadow_inner_white_soft)

        /* Button gradient colors */
        val buttonGradientPrimary @Composable get() = colorResource(R.color.foundation_button_gradient_primary)
        val buttonGradientPrimaryTransparent @Composable get() = colorResource(R.color.foundation_button_gradient_primary_transparent)
        val buttonGradientPrimaryPressed @Composable get() = colorResource(R.color.foundation_button_gradient_primary_pressed)
        val buttonGradientSecondary @Composable get() = colorResource(R.color.foundation_button_gradient_secondary)
        val buttonGradientSecondaryTransparent @Composable get() = colorResource(R.color.foundation_button_gradient_secondary_transparent)
        val buttonDarkDisablePrimary @Composable get() = colorResource(R.color.foundation_button_dark_disable_primary)
        val buttonDarkDisableSecondary @Composable get() = colorResource(R.color.foundation_button_dark_disable_secondary)
        val darkButtonPressedState @Composable get() = colorResource(R.color.foundation_button_dark_pressed)
        val darkButtonEnableState @Composable get() = colorResource(R.color.foundation_button_dark_enable)

        /* Tab Component Colors */
        val tabTextActiveInline @Composable get() = colorResource(R.color.foundation_tab_text_active_inline)
        val tabTextSecondary @Composable get() = colorResource(R.color.foundation_tab_text_secondary)
        val tabTextActivePill @Composable get() = colorResource(R.color.foundation_tab_text_active_pill)
        val tabTextInactive @Composable get() = colorResource(R.color.foundation_tab_text_inactive)
        val tabBorderActive @Composable get() = colorResource(R.color.foundation_tab_border_active)
        val tabDividerInactive @Composable get() = colorResource(R.color.foundation_tab_divider_inactive)
        val tabBgActivePill @Composable get() = colorResource(R.color.foundation_tab_bg_active_pill)
        val tabBgContainerInline @Composable get() = colorResource(R.color.foundation_tab_bg_container_inline)

        /* Tab gradient colors */
        val tabGradient1 @Composable get() = colorResource(R.color.foundation_tab_gradient_1)
        val tabGradient2 @Composable get() = colorResource(R.color.foundation_tab_gradient_2)
        val tabGradient3 @Composable get() = colorResource(R.color.foundation_tab_gradient_3)
        val tabGradient4 @Composable get() = colorResource(R.color.foundation_tab_gradient_4)
        val tabGradient5 @Composable get() = colorResource(R.color.foundation_tab_gradient_5)

        /* Glass Morphism gradient colors */
        val glassGradient1 @Composable get() = colorResource(R.color.foundation_glass_gradient_1)
        val glassGradient2 @Composable get() = colorResource(R.color.foundation_glass_gradient_2)
        val glassGradient3 @Composable get() = colorResource(R.color.foundation_glass_gradient_3)
        val glassGradient4 @Composable get() = colorResource(R.color.foundation_glass_gradient_4)
        val glassGradient5 @Composable get() = colorResource(R.color.foundation_glass_gradient_5)

        val homeBorderButton @Composable get() = colorResource(R.color.home_border_button)
        val homeTextButton @Composable get() = colorResource(R.color.home_text_button)
        val homeBackgroundIcon @Composable get() = colorResource(R.color.home_background_icon)

        /* Timeline Component Gradient Colors */
        val timelineGradientGreen @Composable get() = colorResource(R.color.foundation_timeline_gradient_green)
        val timelineGradientOrange @Composable get() = colorResource(R.color.foundation_timeline_gradient_orange)

        val foundationDarkButtonPrimary @Composable get() = colorResource(R.color.foundation_dark_button_primary)
        val foundationDarkButtonPressedPrimary @Composable get() = colorResource(R.color.foundation_dark_button_pressed_state_primary)
        val foundationDarkButtonPressesSecondary @Composable get() = colorResource(R.color.foundation_dark_button_pressed_state_secondary)
        val foundationDarkButtonTextDisable @Composable get() = colorResource(R.color.foundation_dark_button_text_disable)

        /* Dialog colors */
        val dialogBackground @Composable get() = colorResource(R.color.foundation_dialog_background)

        /* New Login UI colors */
        val backgroundDarkBlue @Composable get() = colorResource(R.color.foundation_background_dark_blue)
        val borderGradientStart @Composable get() = colorResource(R.color.foundation_border_gradient_start)
        val avatarGradientEnd @Composable get() = colorResource(R.color.foundation_avatar_gradient_end)
        val textLinkBlue @Composable get() = colorResource(R.color.foundation_text_link_blue)
        val fillTextPrimary @Composable get() = colorResource(R.color.foundation_fill_text_primary)
        val textGuideLine @Composable get() = colorResource(R.color.foundation_text_guide_line)
    }

    // ========================================
    // TYPOGRAPHY - Foundation Design System Typography Tokens
    // ========================================
    /**
     * Typography System - Figma Foundation Design System
     *
     * - Heading: H1-H6 (SemiBold 600) cho titles và section headers (6 tokens)
     * - Body: B1, B1 Emphasized, B2, B2 Emphasized cho main content (4 tokens)
     * - Interaction: Button, Small Button, Text Link cho interactive elements (3 tokens)
     * - Caption: L/M/S với Medium/SemiBold/Bold variants cho metadata (9 tokens)
     *
     * Font Family: SVN Gilroy (Medium 500, SemiBold 600, Bold 700)
     * Letter Spacing: Tùy theo size, thường âm để tăng độ đọc
     * Line Height: Tối ưu theo từng token size
     *
     * Usage:
     * Text(text = "Title", style = FDS.Typography.headingH1)
     * Text(text = "Content", style = FDS.Typography.bodyB1Emphasized)
     *
     * Backward Compatibility: Legacy tokens có @Deprecated annotations
     */
    object Typography {
        // Material3 Typography instance
        val material3Typography = Typography(
            displayLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 32.sp,
                lineHeight = 40.sp,
                letterSpacing = 0.sp,
            ),
            displayMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 28.sp,
                lineHeight = 36.sp,
                letterSpacing = 0.sp,
            ),
            displaySmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 24.sp,
                lineHeight = 32.sp,
                letterSpacing = 0.sp,
            ),
            headlineLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 24.sp,
                lineHeight = 32.sp,
                letterSpacing = 0.sp,
            ),
            headlineMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 20.sp,
                lineHeight = 28.sp,
                letterSpacing = 0.sp,
            ),
            headlineSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 18.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.sp,
            ),
            titleLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 20.sp,
                lineHeight = 28.sp,
                letterSpacing = 0.sp,
            ),
            titleMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.15.sp,
            ),
            titleSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp,
            ),
            bodyLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                letterSpacing = 0.15.sp,
            ),
            bodyMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.25.sp,
            ),
            bodySmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.4.sp,
            ),
            labelLarge = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                lineHeight = 20.sp,
                letterSpacing = 0.1.sp,
            ),
            labelMedium = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 12.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp,
            ),
            labelSmall = TextStyle(
                fontFamily = SvnGilroyFamily,
                fontWeight = FontWeight.Medium,
                fontSize = 10.sp,
                lineHeight = 16.sp,
                letterSpacing = 0.5.sp,
            ),
        )

        // ====== FUNCTIONAL TOKENS ======
        /* Heading Tokens */
        val headingH1 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 32.sp,
            lineHeight = 40.sp,
            letterSpacing = (-0.01).sp,
        )

        val headingH2 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 24.sp,
            lineHeight = 32.sp,
            letterSpacing = (-0.01).sp,
        )

        val headingH3 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 20.sp,
            lineHeight = 28.sp, // Updated from 24 to match Figma
            letterSpacing = (-0.01).sp,
        )

        val headingH4 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 18.sp, // Updated from 16 to match Figma
            lineHeight = 24.sp,
            letterSpacing = (-0.01).sp,
        )
        val headingH5 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 16.sp, // Updated from 16 to match Figma
            lineHeight = 20.sp,
            letterSpacing = (-0.01).sp,
        )
        val headingH6 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 14.sp, // Updated from 16 to match Figma
            lineHeight = 16.sp,
            letterSpacing = (-0.01).sp,
        )

        // ====== BODY TOKENS ======
        /**
         * B1 (18sp SemiBold): Primary body text
         * B1 Emphasized (18sp Bold): Emphasized variant của B1
         * B2 (16sp SemiBold): Secondary body text
         * B2 Emphasized (16sp Bold): Emphasized variant của B2
         */
        val bodyB1 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // Updated from Medium to match Figma
            fontSize = 18.sp, // Updated from 16 to match Figma
            lineHeight = 28.sp, // Updated from 24 to match Figma
            letterSpacing = (-0.015).sp,
        )

        val bodyB2 = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // Updated from Medium to match Figma
            fontSize = 16.sp, // Updated from 14 to match Figma
            lineHeight = 24.sp, // Updated from 20 to match Figma
            letterSpacing = (-0.015).sp,
        )

        val bodyB1Emphasized = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma "B1 Emphasized"
            fontSize = 18.sp,
            lineHeight = 28.sp,
            letterSpacing = (-0.015).sp,
        )

        val bodyB2Emphasized = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma "B2 Emphasized"
            fontSize = 16.sp,
            lineHeight = 24.sp,
            letterSpacing = (-0.015).sp,
        )

        // ====== INTERACTION TOKENS ======
        /**
         * Interactive Elements: Text for buttons, links, and clickable elements
         * interactionButton (16sp): Primary button text
         * interactionSmallButton (14sp): Secondary/small button text
         * interactionLink (14sp): Link text and clickable elements
         * Font Weight: SemiBold (600) for buttons, Bold (700) for small buttons
         */
        val interactionButton = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 16.sp,
            lineHeight = 24.sp,
            letterSpacing = (-0.02).sp,
        )

        val interactionSmallButton = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // Updated from SemiBold to match Figma
            fontSize = 14.sp, // Updated from 13 to match Figma
            lineHeight = 16.sp,
            letterSpacing = (-0.015).sp,
        )

        val interactionLink = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 14.sp, // Updated from 12 to match Figma
            lineHeight = 16.sp,
            letterSpacing = (-0.015).sp,
        )

        // ====== CAPTION TOKENS ======
        /**
         * Caption Elements: Small text for hints, metadata, and secondary information
         * Complete Figma implementation with size and weight variants:
         *
         * Caption L (14sp): Large captions
         * - captionL: Medium weight (500)
         * - captionLSemibold: SemiBold weight (600)
         * - captionLBold: Bold weight (700)
         *
         * Caption M (12sp): Medium captions
         * - captionM: Medium weight (500)
         * - captionMSemibold: SemiBold weight (600)
         * - captionMBold: Bold weight (700)
         * - captionMBoldItalic: Bold Italic variant
         *
         * Caption S (10sp): Small captions
         * - captionSSemibold: SemiBold weight (600)
         * - captionSBold: Bold weight (700)
         */

        // Caption L variants (14sp)
        val captionL = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Medium, // 500 weight as per Figma
            fontSize = 14.sp,
            lineHeight = 18.sp,
            letterSpacing = (-0.0008).sp,
        )

        val captionLSemibold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // 600 weight as per Figma
            fontSize = 14.sp,
            lineHeight = 18.sp,
            letterSpacing = (-0.0008).sp,
        )

        val captionLBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma
            fontSize = 14.sp,
            lineHeight = 18.sp,
            letterSpacing = (-0.0008).sp,
        )
        val captionCaptionMSemiBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold,
            fontSize = 12.sp, // Updated from 10 to match Figma
            lineHeight = 16.sp, // Updated from 12 to match Figma
            letterSpacing = (-0.0008).sp,
        )

        // Caption M variants (12sp)
        val captionM = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Medium, // 500 weight as per Figma
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = (-0.0008).sp,
        )

        val captionMSemibold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // 600 weight as per Figma
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = (-0.0008).sp,
        )

        val captionMBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = (-0.0008).sp,
        )

        val captionMBoldItalic = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma
            fontStyle = FontStyle.Italic,
            fontSize = 12.sp,
            lineHeight = 16.sp,
            letterSpacing = (-0.0008).sp,
        )

        // Caption S variants (10sp)
        val captionSBold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.Bold, // 700 weight as per Figma
            fontSize = 10.sp,
            lineHeight = 14.sp,
            letterSpacing = (-0.0005).sp,
        )

        val captionSSemibold = TextStyle(
            fontFamily = SvnGilroyFamily,
            fontWeight = FontWeight.SemiBold, // 600 weight as per Figma
            fontSize = 10.sp,
            lineHeight = 14.sp,
            letterSpacing = (-0.0005).sp,
        )

        // ====== BACKWARD COMPATIBILITY ======
        /**
         * Legacy tokens maintained for backward compatibility
         * These will be removed in future versions - use new token names instead
         */

        // Legacy Caption tokens for backward compatibility
        @Deprecated("Use captionLSemibold for Figma consistency", ReplaceWith("captionLSemibold"))
        val captionCaptionL = captionLSemibold

        @Deprecated("Use captionLBold for Figma consistency", ReplaceWith("captionLBold"))
        val captionCaptionLBold = captionLBold

        @Deprecated("Use captionM for Figma consistency", ReplaceWith("captionM"))
        val captionCaptionM = captionM

        @Deprecated("Use captionMBold for Figma consistency", ReplaceWith("captionMBold"))
        val captionCaptionMBold = captionMBold

        @Deprecated("Use captionSSemibold for Figma consistency", ReplaceWith("captionSSemibold"))
        val captionCaptionS = captionSSemibold

        @Deprecated("Use captionSBold for Figma consistency", ReplaceWith("captionSBold"))
        val captionCaptionSBold = captionSBold
    }

    // ========================================
    // SIZER (Dimensions)
    // ========================================
    object Sizer {
        /**
         * Padding values - Direct mapping with Figma Sizer/Padding
         */
        object Padding {
            val padding0 @Composable get() = 0.dp
            val padding2 @Composable get() = 2.dp
            val padding4 @Composable get() = 4.dp
            val padding6 @Composable get() = 6.dp
            val padding8 @Composable get() = 8.dp
            val padding10 @Composable get() = 10.dp
            val padding12 @Composable get() = 12.dp
            val padding16 @Composable get() = 16.dp
            val padding20 @Composable get() = 20.dp
            val padding24 @Composable get() = 24.dp
            val padding32 @Composable get() = 32.dp
            val padding40 @Composable get() = 40.dp
            val padding48 @Composable get() = 48.dp
            val padding56 @Composable get() = 56.dp
            val padding64 @Composable get() = 64.dp
            val padding80 @Composable get() = 80.dp
            val padding96 @Composable get() = 96.dp
            val padding100 @Composable get() = 100.dp
            val padding144 @Composable get() = 144.dp
            val padding200 @Composable get() = 200.dp
            val padding260 @Composable get() = 260.dp
            val padding280 @Composable get() = 280.dp
        }

        /**
         * Gap values - Direct mapping with Figma Sizer/Gap
         * Used for spacing between elements (Row/Column arrangements)
         */
        object Gap {
            val gap0 @Composable get() = 0.dp
            val gap1 @Composable get() = 1.dp
            val gap2 @Composable get() = 2.dp
            val gap4 @Composable get() = 4.dp
            val gap6 @Composable get() = 6.dp
            val gap8 @Composable get() = 8.dp
            val gap11 @Composable get() = 11.dp
            val gap12 @Composable get() = 12.dp
            val gap16 @Composable get() = 16.dp
            val gap20 @Composable get() = 20.dp
            val gap24 @Composable get() = 24.dp
            val gap32 @Composable get() = 32.dp
            val gap36 @Composable get() = 36.dp
            val gap40 @Composable get() = 40.dp
            val gap44 @Composable get() = 44.dp
            val gap48 @Composable get() = 48.dp
            val gap56 @Composable get() = 56.dp
            val gap60 @Composable get() = 60.dp
            val gap64 @Composable get() = 64.dp
            val gap80 @Composable get() = 80.dp
            val gap96 @Composable get() = 96.dp
            val gap100 @Composable get() = 100.dp
            val gap108 @Composable get() = 108.dp
            val gap140 @Composable get() = 140.dp
            val gap144 @Composable get() = 144.dp
            val gap240 @Composable get() = 240.dp
            val gap300 @Composable get() = 300.dp
            val gap600 @Composable get() = 600.dp
        }

        /**
         * Icon sizes - Direct mapping with Figma Sizer/Icon
         */
        object Icon {
            val icon4 @Composable get() = 4.dp // xxs - indicators
            val icon8 @Composable get() = 8.dp // xxs - indicators
            val icon14 @Composable get() = 14.dp // xs- - small flags/badges
            val icon16 @Composable get() = 16.dp // xs
            val icon18 @Composable get() = 18.dp // sm-
            val icon20 @Composable get() = 20.dp // sm
            val icon24 @Composable get() = 24.dp // md - default
            val icon32 @Composable get() = 32.dp // lg
            val icon40 @Composable get() = 40.dp // xl
            val icon48 @Composable get() = 48.dp // xxl
            val icon56 @Composable get() = 56.dp // 3xl
            val icon72 @Composable get() = 72.dp // icon
            val icon96 @Composable get() = 96.dp // 3xl
        }

        /**
         * Border radius values - Direct mapping with Figma Sizer/Radius
         */
        object Radius {
            val radius0 @Composable get() = 0.dp
            val radius4 @Composable get() = 4.dp // xs
            val radius8 @Composable get() = 8.dp // sm
            val radius10 @Composable get() = 10.dp // md
            val radius16 @Composable get() = 16.dp // lg
            val radius20 @Composable get() = 20.dp // xl
            val radius32 @Composable get() = 32.dp // default
            val radiusFull @Composable get() = 999.dp // full - circular
        }

        /**
         * Stroke/Border width values - Direct mapping with Figma Sizer/Stroke
         */
        object Stroke {
            val stroke05 @Composable get() = 0.5.dp // xs - thin
            val stroke1 @Composable get() = 1.dp // sm - default
            val stroke105 @Composable get() = 1.5.dp // sm - default
            val stroke2 @Composable get() = 2.dp // md - medium
            val stroke4 @Composable get() = 4.dp // lg - thick
        }

        /**
         * Text size values - Direct mapping with Figma typography sizes
         * Note: These are for quick access. For full typography, use Typography
         */
        object Text {
            val text12 @Composable get() = 12.sp // xs
            val text14 @Composable get() = 14.sp // sm - default body
            val text16 @Composable get() = 16.sp // md - large body
            val text18 @Composable get() = 18.sp // lg
            val text20 @Composable get() = 20.sp // xl
            val text24 @Composable get() = 24.sp // 2xl
            val text30 @Composable get() = 30.sp // 3xl
            val text36 @Composable get() = 36.sp // 4xl
        }

        /**
         * Tab component dimensions - Direct mapping with foundation_tab dimensions
         */
        object Tab {
            val tabHeight @Composable get() = 32.dp // foundation_tab_height
            val containerHeight @Composable get() = 48.dp // foundation_tab_container_height
        }

        /**
         * Dialog and Modal dimensions - Maximum constraints for responsive design
         */
        object Dialog {
            val maxWidth @Composable get() = 600.dp // Maximum width for dialogs on tablets
            val maxListHeight @Composable get() = 400.dp // Maximum height for scrollable lists in dialogs
            val minWidth @Composable get() = 280.dp // Minimum width for dialogs
        }

        /**
         * Button dimensions - Common button sizes
         */
        object Button {
            val smallButtonWidth @Composable get() = 61.dp // Small elliptical buttons (language selector)
            val defaultButtonHeight @Composable get() = 48.dp // Default button height
            val largeButtonHeight @Composable get() = 56.dp // Large button height
        }

        /**
         * Offset values - For positioning adjustments
         */
        object Offset {
            val offsetNegative2 @Composable get() = (-2).dp // Small negative offset
            val offsetPositive2 @Composable get() = 2.dp // Small positive offset
            val offsetNegative6 @Composable get() = (-6).dp // Medium negative offset
            val offsetPositive6 @Composable get() = 6.dp // Medium positive offset
        }

        /**
         * Legacy spacing values - Use Gap for new code
         * Kept for backward compatibility during migration
         */
        object Spacing {
            val spacingMini @Composable get() = 4.dp // xs - same as gap4
            val spacingSmall @Composable get() = 8.dp // sm - same as gap8
            val spacingMedium @Composable get() = 16.dp // md - same as gap16
            val spacingLarge @Composable get() = 24.dp // lg - same as gap24
            val spacingXLarge @Composable get() = 32.dp // xl - same as gap32
        }

        /**
         * Container dimensions for content areas
         * Used for loading states, error states, and minimum content heights
         */
        object Container {
            val contentMinHeight @Composable get() = 400.dp // Minimum height for loading/error states
        }

        /**
         * Screen breakpoint values for responsive design
         * Note: These are not from Figma design tokens but necessary for responsive layouts
         */
        object ScreenBreakpoint {
            val tabletMinWidth @Composable get() = 600.dp // Standard tablet breakpoint
        }

        /**
         * Helper function to read dimension resources
         */
        @Composable
        fun dimensionResource(@DimenRes id: Int): Dp {
            return LocalContext.current.resources.getDimension(id).dp
        }
    }

    // ========================================
    // EFFECTS
    // ========================================
    object Effects {
        // Elevation levels from Foundation Design
        val elevationSm: Dp = 2.dp // Small shadow for cards
        val elevationMd: Dp = 4.dp // Medium shadow for dialogs, popups
        val elevationLg: Dp = 8.dp // Large shadow for modals, overlays
        val elevationButton: Dp = 10.dp // Button elevation per Foundation Design

        // Additional standard elevations
        val elevationNone: Dp = 0.dp
        val elevationXs: Dp = 1.dp
        val elevationXl: Dp = 12.dp
        val elevationXxl: Dp = 16.dp
    }
}

// Type alias for convenience
typealias FDS = FoundationDesignSystem