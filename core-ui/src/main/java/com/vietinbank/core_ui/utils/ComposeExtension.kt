package com.vietinbank.core_ui.utils

import android.graphics.BlurMaskFilter
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.snap
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.BoxWithConstraintsScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.windowsizeclass.ExperimentalMaterial3WindowSizeClassApi
import androidx.compose.material3.windowsizeclass.WindowHeightSizeClass
import androidx.compose.material3.windowsizeclass.WindowSizeClass
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.withFrameNanos
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.drawOutline
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.drawscope.inset
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.StatusType
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import kotlin.coroutines.cancellation.CancellationException

/**
 * Created by vandz on 1/3/25.
 *
 * Ngăn double-click liên tiếp trong vòng [timeWindow] ms.
 *
 * Usage:
 *   Button(
 *       onClick = { /* normal click logic */ },
 *       modifier = Modifier.safeClickable(timeWindow = 1000) {
 *           // code handle click
 *       }
 *   ) { ... }
 *
 * Hoặc:
 *   Box(modifier = Modifier.safeClickable { ... }) { ... }
 */
fun Modifier.safeClickable(
    isDelayClick: Boolean = true,
    timeWindow: Long = 1000L,
    enabled: Boolean = true,
    enableHaptic: Boolean = true,
    onPressedChanged: (Boolean) -> Unit = {},
    onLongClick: (() -> Unit)? = null,
    onSafeClick: () -> Unit,
): Modifier = composed {
    if (!enabled) return@composed this

    var lastClickTime by remember { mutableLongStateOf(0L) }
    val haptic = LocalHapticFeedback.current
    val time = if (isDelayClick) {
        timeWindow
    } else {
        100L
    }

    this
        .pointerInput(Unit) {
            detectTapGestures(
                onPress = {
                    onPressedChanged(true)
                    try {
                        // wait for tap or cancel
                        val success = tryAwaitRelease()
                        onPressedChanged(false)

                        // handle click if released (not cancelled)
                        if (success) {
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastClickTime >= time) {
                                lastClickTime = currentTime
                                onSafeClick()
                            }
                        }
                    } catch (e: CancellationException) {
                        onPressedChanged(false)
                    }
                },
                onLongPress = onLongClick?.let { longClick ->
                    {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastClickTime >= timeWindow) {
                            lastClickTime = currentTime
                            if (enableHaptic) {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                            }
                            longClick()
                        }
                    }
                },
            )
        }
}

fun Modifier.dismissKeyboardOnClickOutside(onClickOutSide: (() -> Unit)? = null): Modifier =
    composed {
        val focusManager = LocalFocusManager.current
        val keyboardController = LocalSoftwareKeyboardController.current
        val interactionSource = remember { MutableInteractionSource() }

        this.clickable(
            interactionSource = interactionSource,
            indication = null, // Không hiển thị hiệu ứng ripple
        ) {
            focusManager.clearFocus()
            keyboardController?.hide()
            onClickOutSide?.invoke()
        }
    }

fun Modifier.dismissRippleClickable(enabled: Boolean = true, onClick: () -> Unit): Modifier =
    composed {
        this.clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            enabled = enabled,
        ) {
            onClick()
        }
    }

fun Modifier.innerShadow(
    shape: Shape,
    color: Color = Color.Black,
    blur: Dp = 4.dp,
    offsetX: Dp = 0.5.dp,
    offsetY: Dp = 2.dp,
    spread: Dp = 0.dp,
) = this.drawWithContent {
    drawContent()

    drawIntoCanvas { canvas ->

        val shadowSize = Size(size.width + spread.toPx(), size.height + spread.toPx())
        val shadowOutline = shape.createOutline(shadowSize, layoutDirection, this)

        val paint = Paint()
        paint.color = color

        canvas.saveLayer(size.toRect(), paint)
        canvas.drawOutline(shadowOutline, paint)

        paint.asFrameworkPaint().apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
            if (blur.toPx() > 0) {
                maskFilter = BlurMaskFilter(blur.toPx(), BlurMaskFilter.Blur.NORMAL)
            }
        }

        paint.color = Color.Black

        canvas.translate(offsetX.toPx(), offsetY.toPx())
        canvas.drawOutline(shadowOutline, paint)
        canvas.restore()
    }
}

fun Modifier.gradientBorder(
    cornerRadius: Dp = 34.dp,
    borderWidth: Dp = 1.5.dp,
    shadowColor: Color = Color.White,
): Modifier = this.then(
    Modifier.drawBehind {
        val borderPx = borderWidth.toPx()
        val radiusPx = cornerRadius.toPx()

        // Vẽ border trắng nhạt
        drawRoundRect(
            brush = Brush.linearGradient(
                listOf(
                    shadowColor.copy(alpha = 0f), // Top
                    shadowColor.copy(alpha = 0.56f), // Center
                    shadowColor.copy(alpha = 0.2f), // Bottom
                ),
                start = Offset(0f, size.height * 0.5f),
                end = Offset(size.width, size.height * 0.5f),
            ),
            cornerRadius = CornerRadius(radiusPx),
            style = Stroke(width = borderPx),
        )
    },
)

fun Modifier.ellipticalGradientBackground(
    colors: List<Color>,
    centerFraction: Offset = Offset(0.5f, 1.5f), // center in percent (0..1)
    radiusFraction: Float = 1.2f,
    scaleX: Float = 3.5f, // >1: kéo dãn theo ngang, <1: dãn theo dọc
): Modifier = this.then(
    Modifier.drawBehind {
        val width = size.width
        val height = size.height
        val radius = size.minDimension * radiusFraction

        val center = Offset(width * centerFraction.x, height * centerFraction.y)
        val brush = Brush.radialGradient(
            colors = colors,
            center = center,
            radius = radius,
        )

        // scale canvas to simulate elliptical effect
        withTransform({
            scale(scaleX, 1f, pivot = center) // Scale theo X
        }) {
            drawRect(brush = brush)
        }
    },
)

/**
 * Extension function to draw circular inner shadow with gradient
 * Useful for glass morphism effects
 *
 * @param alpha The alpha value for the shadow color
 * @param gradientEndY The Y position where gradient ends
 */
fun androidx.compose.ui.graphics.drawscope.DrawScope.drawCircularInnerShadow(
    alpha: Float = 0.1f,
    gradientEndY: Float = 20f,
) {
    inset(0f, 0f) {
        drawCircle(
            brush = Brush.verticalGradient(
                colors = listOf(
                    Color.White.copy(alpha = alpha),
                    Color.Transparent,
                ),
                startY = 0f,
                endY = gradientEndY,
            ),
        )
    }
}

/**
 * Extension function to add system bars (status bar + navigation bar) padding
 * Useful for edge-to-edge layouts that need to respect system UI
 *
 * Usage:
 * Box(
 *     modifier = Modifier
 *         .fillMaxSize()
 *         .systemBarsPadding()
 * )
 *
 * Note: For now, we keep using the manual status bar height calculation
 * from BaseActivity to maintain consistency with existing XML layouts
 */
@Composable
fun Modifier.systemBarsPadding(): Modifier = this.windowInsetsPadding(WindowInsets.systemBars)

/**
 * Extension function to apply glass morphism effect
 * Creates a multi-layer glass effect with radial gradient
 *
 * @param glassColors Pre-resolved colors for the gradient (should include alpha)
 * @param centerYOffset Y position offset for gradient center (as fraction of height)
 * @param radiusMultiplier Multiplier for gradient radius relative to width
 */
fun Modifier.glassMorphismEffect(
    glassColors: List<Color>,
    centerYOffset: Float = -0.05f,
    radiusMultiplier: Float = 1.6f,
): Modifier = this.drawBehind {
    val gradient = Brush.radialGradient(
        colors = glassColors,
        center = Offset(
            x = size.width * 0.5f,
            y = size.height * centerYOffset,
        ),
        radius = size.width * radiusMultiplier,
    )
    drawRect(brush = gradient)
}

/**
 * Extension function to draw gradient border with fade effect
 * Creates a border that fades from one opacity to another
 * Useful for glass morphism components
 *
 * @param borderColor The base color for the border
 * @param cornerRadius The corner radius for the border
 * @param strokeWidth The width of the border stroke
 * @param fadeStartAlpha Alpha at the fade start point
 * @param fadeEndAlpha Alpha at the fade end point
 * @param fadeEndPosition Position where fade ends (as fraction of width)
 */
fun Modifier.gradientFadeBorder(
    borderColor: Color,
    cornerRadius: Dp,
    strokeWidth: Dp = 1.dp,
    fadeStartAlpha: Float = 1.0f,
    fadeEndAlpha: Float = 0.1f,
    fadeEndPosition: Float = 0.3f,
): Modifier = this.drawWithContent {
    drawContent()

    // Draw border with gradient fade
    val borderGradient = Brush.linearGradient(
        colors = listOf(
            borderColor.copy(alpha = fadeEndAlpha), // Fade at bottom left
            borderColor.copy(alpha = fadeStartAlpha), // Full opacity
        ),
        start = Offset(0f, size.height), // Bottom left
        end = Offset(size.width * fadeEndPosition, size.height * 0.7f),
    )

    drawRoundRect(
        brush = borderGradient,
        cornerRadius = CornerRadius(cornerRadius.toPx()),
        style = Stroke(width = strokeWidth.toPx()),
    )
}

/**
 * Extension function for BoxWithConstraintsScope to calculate adaptive spacing
 * Based on AppWindowSizeClass and available height
 *
 * @param windowSizeClass The current app window size class
 * @param baseCompact Base percentage for compact screens (small phones)
 * @param baseMedium Base percentage for medium screens (normal phones)
 * @param baseExpanded Base percentage for expanded screens (tablets)
 * @param minSpacing Minimum spacing to ensure readability
 * @param maxSpacing Maximum spacing to prevent excessive whitespace
 * @return Calculated adaptive spacing as Dp
 *
 * Example usage:
 * BoxWithConstraints {
 *     val windowSizeClass = rememberAppWindowSizeClass()
 *     val spacing = adaptiveSpacingFor(windowSizeClass)
 * }
 */
fun BoxWithConstraintsScope.adaptiveSpacingFor(
    windowSizeClass: AppWindowSizeClass,
    baseCompact: Float = 0.15f,
    baseMedium: Float = 0.17f,
    baseExpanded: Float = 0.20f,
    minSpacing: Dp = 48.dp,
    maxSpacing: Dp = 180.dp,
): Dp {
    // Use height class for vertical spacing calculation
    val percentage = when (windowSizeClass.height) {
        AppWindowHeightClass.Compact -> baseCompact
        AppWindowHeightClass.Medium -> baseMedium
        AppWindowHeightClass.Expanded -> baseExpanded
    }

    // Calculate spacing based on percentage of visible height
    val calculatedSpacing = maxHeight * percentage

    // Additional constraint for landscape mode with low height
    val clampedSpacing = calculatedSpacing.coerceIn(minSpacing, maxSpacing)

    // Extra protection for "short but wide" screens (landscape phones)
    return when {
        windowSizeClass.isLandscape && maxHeight < 400.dp -> {
            clampedSpacing.coerceAtMost(96.dp)
        }

        maxWidth < 600.dp -> {
            // Phone portrait - slightly more conservative max
            clampedSpacing.coerceAtMost(maxSpacing.coerceAtMost(120.dp))
        }

        else -> {
            // Tablet or desktop - use full range
            clampedSpacing
        }
    }
}

/**
 * Composable function to remember AppWindowSizeClass
 * Provides app-specific window size classification without exposing Material3 API
 *
 * Uses Configuration to calculate approximate window size class
 * Note: This is a simplified implementation that works for most cases
 * For more accurate results (foldables, multi-window), consider using WindowMetrics
 *
 * @return AppWindowSizeClass based on current configuration
 */
@OptIn(ExperimentalMaterial3WindowSizeClassApi::class)
@Composable
fun rememberAppWindowSizeClass(): AppWindowSizeClass {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    return remember(configuration, density) {
        val screenWidthDp = configuration.screenWidthDp.dp
        val screenHeightDp = configuration.screenHeightDp.dp

        // Use Material3 WindowSizeClass internally for calculation
        val m3WindowSizeClass = WindowSizeClass.calculateFromSize(
            size = DpSize(screenWidthDp, screenHeightDp),
        )

        // Convert Material3 classes to our app-specific classes
        val width = when (m3WindowSizeClass.widthSizeClass) {
            WindowWidthSizeClass.Compact -> AppWindowWidthClass.Compact
            WindowWidthSizeClass.Medium -> AppWindowWidthClass.Medium
            WindowWidthSizeClass.Expanded -> AppWindowWidthClass.Expanded
            else -> AppWindowWidthClass.Medium // Default fallback
        }

        val height = when (m3WindowSizeClass.heightSizeClass) {
            WindowHeightSizeClass.Compact -> AppWindowHeightClass.Compact
            WindowHeightSizeClass.Medium -> AppWindowHeightClass.Medium
            WindowHeightSizeClass.Expanded -> AppWindowHeightClass.Expanded
            else -> AppWindowHeightClass.Medium // Default fallback
        }

        AppWindowSizeClass(width = width, height = height)
    }
}

/**
 * Overload for rememberAppWindowSizeClass that accepts explicit DpSize
 * Useful when you have WindowMetrics from Activity for more accurate sizing
 *
 * @param windowDpSize The window size in Dp
 * @return AppWindowSizeClass based on provided size
 */
@OptIn(ExperimentalMaterial3WindowSizeClassApi::class)
@Composable
fun rememberAppWindowSizeClass(windowDpSize: DpSize): AppWindowSizeClass {
    return remember(windowDpSize) {
        // Use Material3 WindowSizeClass internally for calculation
        val m3WindowSizeClass = WindowSizeClass.calculateFromSize(windowDpSize)

        // Convert Material3 classes to our app-specific classes
        val width = when (m3WindowSizeClass.widthSizeClass) {
            WindowWidthSizeClass.Compact -> AppWindowWidthClass.Compact
            WindowWidthSizeClass.Medium -> AppWindowWidthClass.Medium
            WindowWidthSizeClass.Expanded -> AppWindowWidthClass.Expanded
            else -> AppWindowWidthClass.Medium
        }

        val height = when (m3WindowSizeClass.heightSizeClass) {
            WindowHeightSizeClass.Compact -> AppWindowHeightClass.Compact
            WindowHeightSizeClass.Medium -> AppWindowHeightClass.Medium
            WindowHeightSizeClass.Expanded -> AppWindowHeightClass.Expanded
            else -> AppWindowHeightClass.Medium
        }

        AppWindowSizeClass(width = width, height = height)
    }
}

/**
 * Extension function to calculate tablet-friendly width
 * Centers content on tablets while using full width on phones
 *
 * @param maxWidth Maximum width for tablets (typically 600-720.dp for desktop)
 * @param windowSizeClass Current app window size class
 * @return Modifier with appropriate width constraints
 *
 * Example usage:
 * Card(
 *     modifier = Modifier.tabletFriendlyWidth()
 * )
 */
@Composable
fun Modifier.tabletFriendlyWidth(
    maxWidth: Dp = 600.dp,
    windowSizeClass: AppWindowSizeClass = rememberAppWindowSizeClass(),
): Modifier = this.then(
    when (windowSizeClass.width) {
        AppWindowWidthClass.Expanded -> {
            // For tablets/desktop: limit width and center
            Modifier
                .fillMaxWidth()
                .wrapContentWidth(androidx.compose.ui.Alignment.CenterHorizontally)
                .widthIn(max = maxWidth)
        }

        AppWindowWidthClass.Medium -> {
            // For medium screens (small tablets, large phones in landscape)
            Modifier
                .fillMaxWidth()
                .wrapContentWidth(androidx.compose.ui.Alignment.CenterHorizontally)
                .widthIn(max = maxWidth.coerceAtMost(680.dp))
        }

        AppWindowWidthClass.Compact -> {
            // For phones: use full width
            Modifier.fillMaxWidth()
        }
    },
)

/**
 * Extension function to apply VietinBank eFAST logo background
 * Uses the existing bg_common_efast_logo drawable resource
 *
 * @return Modifier with eFAST logo background painted across the entire area
 *
 * Example usage:
 * Box(
 *     modifier = Modifier
 *         .fillMaxSize()
 *         .eFastBackground()
 *         .systemBarsPadding()
 * )
 */
@Composable
fun Modifier.eFastBackground(): Modifier = composed {
    // painterResource is @Composable func → can direct call
    val painter = painterResource(R.drawable.bg_common_efast_logo) // cached

    drawBehind {
        with(painter) { // cover all Modifier
            draw(size = size)
        }
    }
}

@Composable
fun Modifier.eFastBackgroundLevel2(): Modifier = composed {
    // painterResource is @Composable func → can direct call
    val painter = painterResource(R.drawable.bg_common_level2) // cached

    drawBehind {
        with(painter) { // cover all Modifier
            draw(size = size)
        }
    }
}

@Composable
fun Modifier.commonRoundedCornerCard(
    background: Color = FoundationDesignSystem.Colors.background,
): Modifier = this
    .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Padding.padding32))
    .background(background)
    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding24)

/**
 * Animates adaptive spacing changes with protection for large deltas
 * Snaps instead of animating when the change is too large (>56dp)
 * to prevent jarring animations on rotation or IME changes
 *
 * IMPORTANT: Snaps on first frame to prevent jank during screen transitions
 *
 * @param targetSpacing The target spacing to animate to
 * @param threshold Maximum delta for animation (larger changes will snap)
 * @return Animated spacing value
 *
 * Example usage:
 * val spacing = animateAdaptiveSpacing(
 *     targetSpacing = adaptiveSpacingFor(windowSizeClass)
 * )
 */
@Composable
fun animateAdaptiveSpacing(
    targetSpacing: Dp,
    threshold: Dp = 56.dp,
): Dp {
    // Track if this is the first composition to avoid animating on initial render
    var isFirstComposition by remember { mutableStateOf(true) }
    var lastSpacing by remember { mutableStateOf(targetSpacing) }

    val animatedSpacing by animateDpAsState(
        targetValue = targetSpacing,
        animationSpec = if (isFirstComposition) {
            // SNAP on first frame - no animation to prevent jank
            snap()
        } else if (kotlin.math.abs(targetSpacing.value - lastSpacing.value) > threshold.value) {
            // Snap for large changes (rotation, IME, etc.)
            snap()
        } else {
            // Smooth animation for small changes
            tween(durationMillis = 300, easing = FastOutSlowInEasing)
        },
        label = "adaptive_spacing_animation",
    )

    LaunchedEffect(targetSpacing) {
        if (isFirstComposition) {
            isFirstComposition = false
        }
        lastSpacing = targetSpacing
    }

    return animatedSpacing
}

/**
 * Modifier long-press “chuẩn”, có haptic, tránh dính onClick.
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun Modifier.appCombinedClickable(
    onClick: (() -> Unit)? = null,
    onLongClick: (() -> Unit)? = null,
    onDoubleClick: (() -> Unit)? = null,
    enableHaptic: Boolean = true,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
): Modifier {
    val haptic = LocalHapticFeedback.current

    return this.combinedClickable(
        interactionSource = interactionSource,
        indication = null,
        onClick = { onClick?.invoke() },
        onLongClick = {
            if (enableHaptic) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
            }
            onLongClick?.invoke()
        },
        onDoubleClick = { onDoubleClick?.invoke() },
    )
}

/**
 * Remembers and latches navigation bar height after first frame.
 * Returns null until value is latched to avoid using incorrect 0 value.
 */
@Composable
private fun rememberLatchedNavBarDpOnce(): Dp? {
    val view = LocalView.current
    val density = LocalDensity.current
    var cached by remember { mutableStateOf<Dp?>(null) }

    // Wait for first frame to ensure insets are ready
    LaunchedEffect(view, density) {
        withFrameNanos { } // Wait one frame
        val px = ViewCompat.getRootWindowInsets(view)
            ?.getInsets(WindowInsetsCompat.Type.navigationBars())
            ?.bottom ?: 0
        cached = with(density) { px.toDp() }
    }
    return cached
}

/**
 * Gets navigation bar height with priority from AppConfig.
 * Falls back to latching if not available and saves back to config.
 *
 * @param appCfg IAppConfigManager instance, can be null
 * @return Navigation bar height in Dp or null if not yet available
 */
@Composable
fun rememberAppNavBarDp(appCfg: IAppConfigManager?): Dp? {
    val density = LocalDensity.current

    // Observe the StateFlow to trigger recomposition when value changes
    val config by appCfg?.configFlow?.collectAsState() ?: remember { mutableStateOf(null) }
    val cachedPx = config?.navigationBarHeight ?: 0

    if (cachedPx > 0) return with(density) { cachedPx.toDp() }

    // Fallback to latched value if no cached value
    val latched = rememberLatchedNavBarDpOnce()

    // Save back to AppConfig if latched successfully
    if (latched != null && appCfg != null && cachedPx == 0) {
        LaunchedEffect(latched) {
            val px = with(density) { latched.toPx().toInt() }
            appCfg.updateNavigationBarHeight(px)
        }
    }

    return latched
}

/**
 * Smart navigation bar padding that uses cached value when available.
 * Automatically handles IME state to prevent double padding.
 *
 * @param appCfg IAppConfigManager instance for cached navigation bar height
 * @param minBottom Minimum bottom padding to ensure
 * @return Modifier with appropriate bottom padding
 */
@Composable
fun Modifier.appNavBarsOrMin(
    appCfg: IAppConfigManager?,
    minBottom: Dp?,
): Modifier {
    if (minBottom == null) return this
    val navDp = rememberAppNavBarDp(appCfg)
    val imeBottom = WindowInsets.ime.asPaddingValues().calculateBottomPadding()

    return if (navDp != null && imeBottom == 0.dp) {
        // IME closed - use cached navigation bar height
        padding(bottom = maxOf(navDp, minBottom))
    } else {
        // Fallback to dynamic when no cache or IME is open
        this
            .windowInsetsPadding(WindowInsets.navigationBars.only(WindowInsetsSides.Bottom))
            .padding(
                bottom = (
                    minBottom - WindowInsets.navigationBars.asPaddingValues()
                        .calculateBottomPadding()
                    ).coerceAtLeast(0.dp),
            )
    }
}

/**
 * Displays text with conditional marquee effect when text overflows.
 * Only animates when the text is too long to fit in the available space.
 * Maintains style consistency with FoundationText component.
 *
 * @param text The text to display
 * @param modifier Modifier for the text component
 * @param style Text style from FDS.Typography (default: bodyB2)
 * @param color Text color from FDS.Colors (default: textPrimary)
 * @param textAlign Alignment of the text
 * @param textDecoration Decoration to apply to text
 * @param initialDelayMillis Delay before marquee starts (default 1000ms)
 * @param repeatDelayMillis Delay between marquee iterations (default 2000ms)
 * @param velocity Scroll speed in dp per second (default 30dp)
 *
 * Example usage:
 * ConditionalMarqueeText(
 *     text = fullName,
 *     style = FDS.Typography.bodyB2,
 *     color = FDS.Colors.characterPrimary,
 *     modifier = Modifier.fillMaxWidth()
 * )
 */
@Composable
fun ConditionalMarqueeText(
    text: String,
    modifier: Modifier = Modifier,
    style: androidx.compose.ui.text.TextStyle = com.vietinbank.core_ui.theme.FoundationDesignSystem.Typography.bodyB2,
    color: Color = com.vietinbank.core_ui.theme.FoundationDesignSystem.Colors.textPrimary,
    textAlign: androidx.compose.ui.text.style.TextAlign? = null,
    textDecoration: androidx.compose.ui.text.style.TextDecoration? = null,
    initialDelayMillis: Int = 1000,
    repeatDelayMillis: Int = 2000,
    velocity: Dp = 30.dp,
) {
    // Apply text decoration if provided (same as FoundationText)
    val finalStyle = style.let { baseStyle ->
        if (textDecoration != null) {
            baseStyle.copy(textDecoration = textDecoration)
        } else {
            baseStyle
        }
    }

    // Simply apply basicMarquee always - it only activates when text overflows naturally
    androidx.compose.material3.Text(
        text = text,
        style = finalStyle,
        color = color,
        textAlign = textAlign,
        maxLines = 1,
        overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
        softWrap = false, // Important: false for marquee to work
        modifier = modifier.basicMarquee(
            iterations = Int.MAX_VALUE,
            repeatDelayMillis = repeatDelayMillis,
            initialDelayMillis = initialDelayMillis,
            velocity = velocity,
        ),
    )
}

@Composable
fun String?.getColorStatus(): Color {
    return when (StatusType.fromValue(this)) {
        StatusType.SUCCESS, StatusType.DISBURSED_OF_DOCUMENTS -> {
            FoundationDesignSystem.Colors.stateSuccess
        }

        StatusType.KTT_CTK_REJECT, StatusType.DELETED_13, StatusType.DELETED_93, StatusType.ERROR, StatusType.USER_DELETE_DOCUMENTS, StatusType.USER_CANCEL_DOCUMENTS -> {
            FoundationDesignSystem.Colors.stateError
        }

        else -> {
            FoundationDesignSystem.Colors.stateWarning
        }
    }
}

/**
 * Extension function to add glass morphism bottom gradient background
 * Useful for dialog button areas, bottom sheets, and similar UI patterns
 *
 * @param endColor The bottom color of the gradient (usually FDS color token)
 * @param startColor The top color of the gradient (default: transparent)
 * @param alpha Alpha value applied to endColor (0.0 to 1.0)
 * @param gradientHeight Gradient coverage as fraction of height (0.0 to 1.0, default: 1.0 = full height)
 * @param cornerRadius Optional corner radius for clipping (e.g., for rounded bottom edges)
 *
 * Usage examples:
 * ```
 * // Dialog button area
 * .glassBottomGradient(
 *     endColor = FDS.Colors.backgroundBgScreen,
 *     alpha = 0.675f,
 *     cornerRadius = FDS.Sizer.Radius.radius32
 * )
 *
 * // Bottom sheet background
 * .glassBottomGradient(
 *     endColor = FDS.Colors.blue900,
 *     alpha = 0.675f
 * )
 * ```
 */
fun Modifier.glassBottomGradient(
    endColor: Color,
    startColor: Color = Color.Transparent,
    alpha: Float = 1f,
    gradientHeight: Float = 1f,
    cornerRadius: Dp? = null,
): Modifier = this.then(
    Modifier
        .let { modifier ->
            if (cornerRadius != null) {
                modifier.clip(
                    RoundedCornerShape(
                        bottomStart = cornerRadius,
                        bottomEnd = cornerRadius,
                    ),
                )
            } else {
                modifier
            }
        }
        .background(
            brush = Brush.verticalGradient(
                colors = listOf(
                    startColor,
                    endColor.copy(alpha = alpha),
                ),
                startY = 0f,
                endY = Float.POSITIVE_INFINITY * gradientHeight,
            ),
        ),
)