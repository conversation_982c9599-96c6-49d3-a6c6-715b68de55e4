<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="BaseTextView">
        <attr name="right" format="reference" />
        <attr name="top" format="reference" />
        <attr name="bottom" format="reference" />
        <attr name="left" format="reference" />
        <attr name="check" format="boolean" />
        <attr name="iconPadding" format="dimension" />
        <attr name="tint" format="color" />
        <attr name="background" format="reference" />
        <attr name="html" format="string" />
        <attr name="light" format="boolean" />
        <attr name="fontCus" format="integer">
            <flag name="regular" value="0" />
            <flag name="semi_bold" value="2" />
            <flag name="heavy_italic" value="3" />
            <flag name="medium" value="4" />
            <flag name="regular_bold" value="5" />
            <flag name="x_bold" value="6" />
            <flag name="semi_bold_italic" value="7" />
            <flag name="heavy_normal" value="8" />
        </attr>
    </declare-styleable>


</resources>
