<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M12,23.5C18.351,23.5 23.5,18.351 23.5,12C23.5,5.649 18.351,0.5 12,0.5C5.649,0.5 0.5,5.649 0.5,12C0.5,18.351 5.649,23.5 12,23.5Z"
      android:fillAlpha="0.1">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="12"
          android:centerY="0"
          android:gradientRadius="18"
          android:type="radial">
        <item android:offset="0" android:color="#FF2EB2F7"/>
        <item android:offset="1" android:color="#0CFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M12,23.5C18.351,23.5 23.5,18.351 23.5,12C23.5,5.649 18.351,0.5 12,0.5C5.649,0.5 0.5,5.649 0.5,12C0.5,18.351 5.649,23.5 12,23.5Z"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="0"
          android:startY="24"
          android:endX="24"
          android:endY="0"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FF8FD9FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M8,14L12,10L16,14"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0F4C7A"
      android:strokeLineCap="round"/>
</vector>
