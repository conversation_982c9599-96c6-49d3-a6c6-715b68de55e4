package com.vietinbank.feature_login.ui.screen.newui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.CircularGlassMorphismButton
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.EllipticalGlassMorphismButton
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.glassMorphismEffect
import com.vietinbank.core_ui.utils.gradientFadeBorder
import com.vietinbank.core_ui.utils.rememberAppWindowSizeClass
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for marquee animation
 * Non-design values for text scrolling behavior
 */
private object MarqueeConstants {
    const val INITIAL_DELAY_MS = 1000 // Delay before marquee starts
    const val REPEAT_DELAY_MS = 2000 // Pause between iterations
    const val VELOCITY_DP = 30f // Scroll speed in dp/second
}

/**
 * Constants for glass morphism effect
 * Extract all hardcoded values for maintainability
 */
private object GlassMorphismConstants {
    // Alpha values for gradient layers
    const val GLASS_ALPHA_PRIMARY = 0.4f
    const val GLASS_ALPHA_SECONDARY = 0.2f
    const val GLASS_ALPHA_TERTIARY = 0.08f

    // Gradient positioning
    const val GRADIENT_CENTER_Y_OFFSET = -0.05f // Offset above component for top sheen
    const val GRADIENT_RADIUS_MULTIPLIER = 1.6f // Wider spread horizontally

    // Border gradient fade
    const val BORDER_FADE_START_ALPHA = 1.0f
    const val BORDER_FADE_END_ALPHA = 0.1f // Almost transparent at bottom left
    const val BORDER_FADE_END_POSITION = 0.3f // Fade covers 30% of width
}

/**
 * Unified pre-login screen that handles both new users and returning users
 * Based on isActive state, shows appropriate UI elements
 */
@Composable
fun NewUIPreLoginScreen(
    userRole: String,
    isActive: Boolean,
    userName: String? = null,
    fullName: String? = null,
    listQuickAction: List<QuickActionItemModel>,
    onAction: (NewUIPreLoginAction) -> Unit,
) {
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding(),
    ) {
        val windowSizeClass = rememberAppWindowSizeClass()

//        // Dynamic spacing between welcome and cards section
//        // From Figma: 88dp is the actual spacing that needs to be responsive
//        val targetSectionSpacing = adaptiveSpacingFor(
//            windowSizeClass = windowSizeClass,
//            baseCompact = 0.08f, // 8% for small phones (reduced from 88dp)
//            baseMedium = 0.103f, // ~10.3% for normal phones (88px/852px from Figma)
//            baseExpanded = 0.12f, // 12% for tablets for better proportion
//            minSpacing = FDS.Sizer.Padding.padding64, // Minimum 64dp
//            maxSpacing = FDS.Sizer.Padding.padding100, // Maximum 100dp (capped for very large screens)
//        )
//        // Animate section spacing with protection for large changes
//        val animatedSectionSpacing = animateAdaptiveSpacing(targetSectionSpacing)

        // Main content - Center content on tablets with max width
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.TopCenter,
        ) {
            Column(
                modifier = Modifier
                    .widthIn(max = FDS.Sizer.ScreenBreakpoint.tabletMinWidth) // Max width for tablets
                    .verticalScroll(rememberScrollState()) // Add scroll capability for small screens
                    .padding(
                        start = FDS.Sizer.Padding.padding8,
                        end = FDS.Sizer.Padding.padding8,
                        bottom = FDS.Sizer.Padding.padding24,
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                // Header notification buttons - now part of natural flow
                HeaderNotificationButtons(
                    onAction = onAction,
                    modifier = Modifier.padding(
                        top = FDS.Sizer.Padding.padding8,
                        bottom = FDS.Sizer.Padding.padding48,
                    ),
                )

                // Welcome header
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_welcome_message),
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.white,
                        textAlign = TextAlign.Center,
                    )

                    // VietinBank eFAST logo
                    Image(
                        painter = painterResource(id = R.drawable.ic_common_logobank),
                        contentDescription = "VietinBank eFAST",
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Padding.padding16),
                    )
                }

                // Dynamic spacing between welcome and cards (88dp from Figma, responsive)
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap48))

                // Main content section - changes based on isActive
                if (isActive && fullName != null) {
                    // Returning user content
                    ReturningUserContent(
                        fullName = fullName,
                        onAction = onAction,
                        maxWidth = <EMAIL>,
                    )
                } else {
                    // New user content
                    NewUserContent(
                        onAction = onAction,
                        maxWidth = <EMAIL>,
                    )
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                // Quick actions card with animated visibility
                AnimatedVisibility(
                    visible = true, // Always visible but with smooth entrance
                    enter = fadeIn(animationSpec = tween(300)) + expandVertically(animationSpec = tween(350)),
                    exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(animationSpec = tween(250)),
                ) {
                    QuickActionsCard(
                        listQuickAction = listQuickAction,
                        onAction = onAction,
                    )
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                // Bottom action buttons - same for both states
                BottomActionButtons(
                    isShowSoftOtp = (isActive && fullName != null && userRole != Tags.ROLE_MAKER),
                    onAction = onAction,
                )
            }
        }
    }
}

@Composable
private fun NewUserContent(
    onAction: (NewUIPreLoginAction) -> Unit,
    maxWidth: Dp,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        // Login prompt card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .safeClickable { onAction(NewUIPreLoginAction.OnLoginClick) },
            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
            colors = CardDefaults.cardColors(
                containerColor = FDS.Colors.white,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = FDS.Effects.elevationSm,
            ),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding16,
                        vertical = FDS.Sizer.Padding.padding16,
                    ),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f), // Allow left side to take available space
                ) {
                    // VietinBank icon
                    Image(
                        painter = painterResource(id = R.drawable.ic_common_vtblogo_32dp),
                        contentDescription = null,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon32),
                    )

                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_existing_account_prompt),
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier.weight(1f, fill = false), // Allow text to shrink if needed
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                // Right side with fixed width for login button
                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.wrapContentWidth(), // Only take needed space
                ) {
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_login_action),
                        style = FDS.Typography.interactionSmallButton,
                        color = FDS.Colors.characterHighlighted,
                        maxLines = 1,
                    )

                    Icon(
                        painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_24dp),
                        contentDescription = null,
                        tint = FDS.Colors.blue600,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    )
                }
            }
        }

        // Promotion card with glass morphism effect using extensions
        val promoGlassColors = listOf(
            FDS.Colors.blue300.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_PRIMARY),
            FDS.Colors.blue600.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_SECONDARY),
            FDS.Colors.white.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_TERTIARY),
            Color.Transparent,
        )
        val promoBorderColor = FDS.Colors.blue800 // Darker border for more intensity
        val promoCornerRadius = FDS.Sizer.Radius.radius32

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(promoCornerRadius))
                .background(FDS.Colors.backgroundDarkBlue) // Dark blue base
                .glassMorphismEffect(
                    glassColors = promoGlassColors,
                    centerYOffset = GlassMorphismConstants.GRADIENT_CENTER_Y_OFFSET,
                    radiusMultiplier = GlassMorphismConstants.GRADIENT_RADIUS_MULTIPLIER,
                )
                .gradientFadeBorder(
                    borderColor = promoBorderColor,
                    cornerRadius = promoCornerRadius,
                    strokeWidth = FDS.Sizer.Stroke.stroke1,
                    fadeStartAlpha = GlassMorphismConstants.BORDER_FADE_START_ALPHA,
                    fadeEndAlpha = GlassMorphismConstants.BORDER_FADE_END_ALPHA,
                    fadeEndPosition = GlassMorphismConstants.BORDER_FADE_END_POSITION,
                ),
        ) {
            // Content on the left with padding
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                ) {
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_start_with_vietinbank),
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterHighlightedLighter,
                    )
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_open_online_account),
                        style = FDS.Typography.headingH4,
                        color = FDS.Colors.white,
                        modifier = Modifier.fillMaxWidth(0.65f), // Limit width to prevent overlap with illustration
                    )
                }

                Box(
                    modifier = Modifier.wrapContentWidth(Alignment.Start),
                ) {
                    FoundationButton(
                        isLightButton = false,
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_register_action),
                        onClick = { onAction(NewUIPreLoginAction.OnRegisterClick) },
                        size = ButtonSize.Small,
                    )
                }
            }

            // Illustration positioned at top-end, naturally clipped by card boundary
            // Following Figma design: image sits at right edge and gets cut off
            Image(
                painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_illustration),
                contentDescription = null,
                modifier = Modifier
                    .size(144.dp) // Fixed container size like Figma (36 * 4 = 144px)
                    .align(Alignment.TopEnd)
                    .offset(y = 8.dp), // Only slight vertical offset, no horizontal offset needed
                contentScale = ContentScale.Crop,
            )
        }
    }
}

@Composable
private fun ReturningUserContent(
    fullName: String,
    onAction: (NewUIPreLoginAction) -> Unit,
    maxWidth: Dp,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        // User info card with fingerprint - Unique to returning users
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessLow,
                    ),
                ),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // User info card
            Card(
                modifier = Modifier
                    .weight(1f)
                    .safeClickable { onAction(NewUIPreLoginAction.OnLoginClick) },
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                colors = CardDefaults.cardColors(
                    containerColor = FDS.Colors.white,
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = FDS.Effects.elevationSm,
                ),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = FDS.Sizer.Padding.padding12,
                            end = FDS.Sizer.Padding.padding16,
                            top = FDS.Sizer.Padding.padding8,
                            bottom = FDS.Sizer.Padding.padding8,
                        ),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.weight(1f), // Prevent overflow, take available space
                    ) {
                        // Avatar
                        Box(
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon48)
                                .clip(CircleShape)
                                .background(
                                    Brush.linearGradient(
                                        colors = listOf(
                                            FDS.Colors.blue100,
                                            FDS.Colors.white,
                                        ),
                                    ),
                                ),
                            contentAlignment = Alignment.Center,
                        ) {
                            val initials = fullName.getInitials()

                            FoundationText(
                                text = initials,
                                style = FDS.Typography.bodyB1Emphasized,
                                color = FDS.Colors.characterHighlighted,
                            )
                        }

                        // Name and login text - constrained to prevent overflow
                        Column(
                            modifier = Modifier.weight(1f), // Take available space
                        ) {
                            // Use basicMarquee directly for testing
                            FoundationText(
                                text = fullName,
                                style = FDS.Typography.bodyB2Emphasized,
                                color = FDS.Colors.characterHighlighted,
                                maxLines = 1,
                                overflow = TextOverflow.Clip, // Use Clip for marquee
                                softWrap = false, // Important: Must be false for marquee to work
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .basicMarquee(
                                        iterations = Int.MAX_VALUE,
                                        repeatDelayMillis = MarqueeConstants.REPEAT_DELAY_MS,
                                        initialDelayMillis = MarqueeConstants.INITIAL_DELAY_MS,
                                        velocity = MarqueeConstants.VELOCITY_DP.dp,
                                    ),
                            )
                            FoundationText(
                                text = stringResource(id = com.vietinbank.feature_login.R.string.login_action),
                                style = FDS.Typography.captionCaptionL,
                                color = FDS.Colors.characterHighlightedLighter,
                            )
                        }
                    }

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        // Divider before switch user icon
                        VerticalDivider(
                            modifier = Modifier
                                .height(FDS.Sizer.Icon.icon24)
                                .padding(
                                    start = FDS.Sizer.Padding.padding8,
                                    end = FDS.Sizer.Padding.padding8,
                                ),
                            thickness = FDS.Sizer.Stroke.stroke1,
                            color = FDS.Colors.strokeDivider,
                        )

                        // Switch user icon
                        Icon(
                            painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_common_swich_horizontal_24),
                            contentDescription = "Switch user",
                            tint = FDS.Colors.blue700,
                            modifier = Modifier
                                .size(FDS.Sizer.Icon.icon24)
                                .safeClickable { onAction(NewUIPreLoginAction.OnSwitchUserClick) },
                        )
                    }
                }
            }

            // Fingerprint button
            CircularIconButton(
                icon = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_common_face_id_24),
                onClick = { onAction(NewUIPreLoginAction.OnFingerprintClick) },
                size = CircularIconButtonSize.Large,
                isLightIcon = true,
            )
        }

        // Promotion card - Same as NewUserContent
        val promoGlassColors = listOf(
            FDS.Colors.blue300.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_PRIMARY),
            FDS.Colors.blue600.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_SECONDARY),
            FDS.Colors.white.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_TERTIARY),
            Color.Transparent,
        )
        val promoBorderColor = FDS.Colors.blue800
        val promoCornerRadius = FDS.Sizer.Radius.radius32

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(promoCornerRadius))
                .background(FDS.Colors.backgroundDarkBlue)
                .glassMorphismEffect(
                    glassColors = promoGlassColors,
                    centerYOffset = GlassMorphismConstants.GRADIENT_CENTER_Y_OFFSET,
                    radiusMultiplier = GlassMorphismConstants.GRADIENT_RADIUS_MULTIPLIER,
                )
                .gradientFadeBorder(
                    borderColor = promoBorderColor,
                    cornerRadius = promoCornerRadius,
                    strokeWidth = FDS.Sizer.Stroke.stroke1,
                    fadeStartAlpha = GlassMorphismConstants.BORDER_FADE_START_ALPHA,
                    fadeEndAlpha = GlassMorphismConstants.BORDER_FADE_END_ALPHA,
                    fadeEndPosition = GlassMorphismConstants.BORDER_FADE_END_POSITION,
                ),
        ) {
            // Content on the left with padding
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                ) {
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_start_with_vietinbank),
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterHighlightedLighter,
                    )
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_open_online_account),
                        style = FDS.Typography.headingH4,
                        color = FDS.Colors.white,
                        modifier = Modifier.fillMaxWidth(0.65f), // Limit width to prevent overlap with illustration
                    )
                }

                Box(
                    modifier = Modifier.wrapContentWidth(Alignment.Start),
                ) {
                    FoundationButton(
                        isLightButton = false,
                        text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_register_action),
                        onClick = { onAction(NewUIPreLoginAction.OnRegisterClick) },
                        size = ButtonSize.Small,
                    )
                }
            }

            // Illustration positioned at top-end, naturally clipped by card boundary
            // Following Figma design: image sits at right edge and gets cut off
            Image(
                painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_illustration),
                contentDescription = null,
                modifier = Modifier
                    .size(144.dp) // Fixed container size like Figma (36 * 4 = 144px)
                    .align(Alignment.TopEnd)
                    .offset(y = 8.dp), // Only slight vertical offset, no horizontal offset needed
                contentScale = ContentScale.Crop,
            )
        }
    }
}

@Composable
private fun QuickActionsCard(
    listQuickAction: List<QuickActionItemModel>,
    onAction: (NewUIPreLoginAction) -> Unit,
) {
    // To-do component with glass morphism effect using extensions
    // Pre-resolve colors for the glass effect
    val glassColors = listOf(
        FDS.Colors.blue300.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_PRIMARY),
        FDS.Colors.blue600.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_SECONDARY),
        FDS.Colors.white.copy(alpha = GlassMorphismConstants.GLASS_ALPHA_TERTIARY),
        Color.Transparent,
    )
    val borderColor = FDS.Colors.blue800 // Darker border for more intensity
    val cornerRadius = FDS.Sizer.Radius.radius32

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(cornerRadius))
            .background(FDS.Colors.backgroundDarkBlue) // Dark blue base
            .glassMorphismEffect(
                glassColors = glassColors,
                centerYOffset = GlassMorphismConstants.GRADIENT_CENTER_Y_OFFSET,
                radiusMultiplier = GlassMorphismConstants.GRADIENT_RADIUS_MULTIPLIER,
            )
            .gradientFadeBorder(
                borderColor = borderColor,
                cornerRadius = cornerRadius,
                strokeWidth = FDS.Sizer.Stroke.stroke1,
                fadeStartAlpha = GlassMorphismConstants.BORDER_FADE_START_ALPHA,
                fadeEndAlpha = GlassMorphismConstants.BORDER_FADE_END_ALPHA,
                fadeEndPosition = GlassMorphismConstants.BORDER_FADE_END_POSITION,
            ),
    ) {
        Column(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // if there are more columns consider changing the view to LazyVerticalGrid
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                listQuickAction.forEach { item ->
                    QuickActionItem(
                        icon = item.icon,
                        text = stringResource(id = item.text),
                        onClick = { onAction(item.action) },
                        modifier = Modifier.weight(1f),
                    )
                }
            }
        }
    }
}

@Composable
private fun BottomActionButtons(
    isShowSoftOtp: Boolean = false,
    onAction: (NewUIPreLoginAction) -> Unit,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Thị trường
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            CircularIconButton(
                isLightIcon = false,
                icon = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_find_branch_24),
                onClick = { onAction(NewUIPreLoginAction.OnBranchLocationClick) },
                size = CircularIconButtonSize.Large,
            )

            FoundationText(
                text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_thi_truong),
                style = FDS.Typography.interactionSmallButton,
                color = FDS.Colors.white,
                textAlign = TextAlign.Center,
            )
        }

        // Soft OTP
        if (isShowSoftOtp) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                CircularIconButton(
                    icon = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_soft_otp),
                    onClick = { onAction(NewUIPreLoginAction.OnContactClick) },
                    size = CircularIconButtonSize.Large,
                    isLightIcon = false,
                )

                FoundationText(
                    text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_soft_otp),
                    style = FDS.Typography.interactionSmallButton,
                    color = FDS.Colors.white,
                    textAlign = TextAlign.Center,
                )
            }
        }

        // Hỗ trợ
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            CircularIconButton(
                icon = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_support),
                onClick = { onAction(NewUIPreLoginAction.OnContactClick) },
                size = CircularIconButtonSize.Large,
                isLightIcon = false,
            )

            FoundationText(
                text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_contact_support),
                style = FDS.Typography.interactionSmallButton,
                color = FDS.Colors.white,
                textAlign = TextAlign.Center,
            )
        }
    }
}

data class QuickActionItemModel(
    val icon: Int,
    val text: Int,
    val action: NewUIPreLoginAction,
)

@Composable
private fun QuickActionItem(
    icon: Int,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .safeClickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        // Icon directly from resource with original colors
        Image(
            painter = painterResource(id = icon),
            contentDescription = null,
            modifier = Modifier.size(FDS.Sizer.Icon.icon32),
        )

        FoundationText(
            text = text,
            style = FDS.Typography.interactionSmallButton,
            color = FDS.Colors.characterInverse,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

// All dimensions are now in FDS.Sizer.Offset
// No component-specific dimension constants allowed

/**
 * Header notification and language selector buttons
 * Centered at top of screen with glass morphism effect
 * Based on Figma design: 2 buttons total
 */
@Composable
private fun HeaderNotificationButtons(
    onAction: (NewUIPreLoginAction) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        EllipticalGlassMorphismButton(
            onClick = { onAction(NewUIPreLoginAction.OnLanguageClick) },
            cornerRadius = FDS.Sizer.Radius.radius16,
            horizontalPadding = FDS.Sizer.Padding.padding16,
            verticalPadding = FDS.Sizer.Padding.padding8,
            isLightButton = false,
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8), // 8px gap per Figma
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Vietnam flag in red circular badge (18px badge containing flag)
                Box(
                    modifier = Modifier
                        .size(18.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Image(
                        painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_flagvn),
                        contentDescription = "Vietnam",
                        modifier = Modifier.size(18.dp),
                    )
                }

                // World icon - 16x16 per Figma
                Icon(
                    painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_world_17),
                    contentDescription = "Chọn khu vực",
                    tint = FDS.Colors.white,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                )
            }
        }

        // Second Button: Notification (circular 32dp button)
        // Using CircularGlassMorphismButton to achieve exact 32dp size
        Box {
            CircularGlassMorphismButton(
                size = FDS.Sizer.Icon.icon32, // Exact 32dp as per Figma
                onClick = { onAction(NewUIPreLoginAction.OnNotificationClick) },
                isLightButton = false,
            ) {
                Icon(
                    painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_common_bell_has_notif_17),
                    contentDescription = "Thông báo",
                    tint = FDS.Colors.white,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                )
            }

            // Blue notification dot
            Box(
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon8)
                    .align(Alignment.TopEnd)
                    .offset(
                        x = FDS.Sizer.Offset.offsetNegative6,
                        y = FDS.Sizer.Offset.offsetPositive6,
                    )
                    .background(
                        color = FDS.Colors.stateBadgeCounting,
                        shape = CircleShape,
                    ),
            )
        }
    }
}

/**
 * Unified actions for Pre-login screen
 * Combines actions from both Welcome and HomePreLogin screens
 * Actions vary based on isActive state per business requirements
 */
sealed class NewUIPreLoginAction {
    // Common actions
    object OnLoginClick : NewUIPreLoginAction()
    object OnSyncTokenClick : NewUIPreLoginAction()
    object OnUnlockUserClick : NewUIPreLoginAction()
    object OnBranchLocationClick : NewUIPreLoginAction()
    object OnGuideClick : NewUIPreLoginAction()
    object OnContactClick : NewUIPreLoginAction()

    // New user specific actions
    object OnRegisterClick : NewUIPreLoginAction()
    object OnReissuePasswordClick : NewUIPreLoginAction() // Cấp lại mật khẩu (not active)

    // Returning user specific actions
    object OnSwitchUserClick : NewUIPreLoginAction()
    object OnFingerprintClick : NewUIPreLoginAction()
    object OnSearchClick : NewUIPreLoginAction()
    object OnVoiceClick : NewUIPreLoginAction()
    object OnInternationalTransferClick : NewUIPreLoginAction()
    object OnRestorePasswordClick : NewUIPreLoginAction() // Khôi phục mật khẩu (active)

    // Header notification/language actions
    object OnLanguageClick : NewUIPreLoginAction()
    object OnNotificationClick : NewUIPreLoginAction()

    // Soft OTP Quick Access
    object OnSoftOtpClick : NewUIPreLoginAction()

    // OTT Notification Settings
    object OnOttNotificationClick : NewUIPreLoginAction()
    data object OnTransferClick : NewUIPreLoginAction()
    data object OnMyRequestClick : NewUIPreLoginAction()
}
