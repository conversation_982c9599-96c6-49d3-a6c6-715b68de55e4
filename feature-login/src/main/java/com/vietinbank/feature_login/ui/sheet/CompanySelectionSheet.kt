package com.vietinbank.feature_login.ui.sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Company Selection Sheet - Pure Compose replacement for CompanySelectionDialog
 *
 * Background Architecture:
 * - BaseBottomSheet provides transparent container (behavior only)
 * - This sheet defines its own visual design with gray background
 * - Content section has white background, buttons float on gray
 *
 * Height Behavior:
 * - Sheet auto-expands based on content (no fixed height)
 * - ModalBottomSheet handles max height (~90% screen)
 * - Scrollable when content exceeds available space
 */
@Composable
fun CompanySelectionSheet(
    visible: Boolean,
    companies: List<CifSharedDomain>,
    selectedId: String? = null,
    onDismiss: () -> Unit,
    onCompanySelected: (CifSharedDomain) -> Unit,
) {
    // Map CifSharedDomain to internal Company model
    val companyList = remember(companies) {
        companies.map { cif ->
            Company(
                id = cif.enterpriseid ?: "",
                name = cif.enterprisename ?: "",
                taxCode = cif.enterpriseid ?: "", // Use enterpriseid as tax code display
                originalDomain = cif,
            )
        }
    }

    // Selection state: default to first company when sheet opens
    var selectedCompany by remember(visible, selectedId) {
        mutableStateOf(
            companyList.firstOrNull { it.id == selectedId } ?: companyList.firstOrNull(),
        )
    }

    BaseBottomSheet(
        visible = visible,
        onDismissRequest = onDismiss,
        allowTouchDismiss = false, // Banking requirement: explicit action needed
        secureFlag = true, // Hide from screenshots
        // maxWidthDp uses default 600dp from BaseBottomSheet (tablet support)
    ) { close ->
        CompanySelectionContent(
            companies = companyList,
            selectedCompany = selectedCompany,
            onCompanySelect = { selectedCompany = it },
            onBack = onDismiss,
            onConfirm = {
                selectedCompany?.let { company ->
                    // originalDomain is always set when we create Company from CifSharedDomain
                    company.originalDomain?.let { domain ->
                        close() // close sheet
                        onCompanySelected(domain)
                    }
                }
            },
        )
    }
}

/**
 * Internal content composable - reusable UI
 */
@Composable
private fun CompanySelectionContent(
    companies: List<Company>,
    selectedCompany: Company?,
    onCompanySelect: (Company) -> Unit,
    onBack: () -> Unit,
    onConfirm: () -> Unit,
) {
    // Wrap entire content with background since BaseBottomSheet is now transparent
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = FDS.Colors.backgroundBgScreen,
                shape = RoundedCornerShape(
                    topStart = FDS.Sizer.Radius.radius20,
                    topEnd = FDS.Sizer.Radius.radius20,
                    bottomStart = 0.dp,
                    bottomEnd = 0.dp,
                ),
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Content section with white background (title + list)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f, fill = false)
                .background(
                    FDS.Colors.backgroundBgContainer,
                    RoundedCornerShape(FDS.Sizer.Radius.radius16),
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Title
            FoundationText(
                text = stringResource(id = com.vietinbank.feature_login.R.string.login_select_company_title),
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Padding.padding24),
            )

            // Divider
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
            HorizontalDivider(
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            // Company list - let ModalBottomSheet handle max height
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false),
                contentPadding = PaddingValues(
                    horizontal = FDS.Sizer.Padding.padding24,
                    vertical = FDS.Sizer.Padding.padding24,
                ),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap0),
            ) {
                itemsIndexed(companies) { index, company ->
                    Column {
                        CompanyItem(
                            company = company,
                            isSelected = selectedCompany?.id == company.id,
                            onSelect = { onCompanySelect(company) },
                        )

                        // Divider between items (not after last)
                        if (index < companies.size - 1) {
                            HorizontalDivider(
                                color = FDS.Colors.strokeDivider,
                                thickness = FDS.Sizer.Stroke.stroke1,
                                modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap16),
                            )
                        }
                    }
                }
            }
        }

        // Bottom section (buttons) - outside the white background
        // Add spacing between content and buttons
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Padding.padding24),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
        ) {
            // "Quay lại" button - dark style
            FoundationButton(
                text = stringResource(id = com.vietinbank.feature_login.R.string.dialog_button_back),
                onClick = onBack,
                isLightButton = false, // Dark button style
                modifier = Modifier.weight(1f),
            )

            // "Đăng nhập" button - light/primary style
            FoundationButton(
                text = stringResource(id = com.vietinbank.feature_login.R.string.login_action),
                onClick = onConfirm,
                isLightButton = true, // Light/primary button style
                enabled = selectedCompany != null,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

@Composable
private fun CompanyItem(
    company: Company,
    isSelected: Boolean,
    onSelect: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable { onSelect() }
            .padding(vertical = FDS.Sizer.Gap.gap16),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
    ) {
        // Company info on the left
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            // Company name
            FoundationText(
                text = company.name,
                style = FDS.Typography.bodyB2Emphasized, // 14sp SemiBold from Figma
                color = if (isSelected) FDS.Colors.characterPrimary else FDS.Colors.characterSecondary,
            )

            // Tax code (Mã số thuế)
            FoundationText(
                text = company.taxCode,
                style = FDS.Typography.captionCaptionL, // 12sp Medium from Figma
                color = FDS.Colors.characterSecondary,
            )
        }

        // Radio button on the right (per Figma design)
        RadioButton(
            selected = isSelected,
            onClick = onSelect,
            colors = RadioButtonDefaults.colors(
                selectedColor = FDS.Colors.stateActive,
                unselectedColor = FDS.Colors.characterTertiary,
            ),
        )
    }
}

/**
 * Company data model (same as Dialog version for consistency)
 */
data class Company(
    val id: String,
    val name: String,
    val taxCode: String,
    val originalDomain: CifSharedDomain? = null, // Keep reference to original domain object
)

/**
 * Extension function to show CompanySelectionSheet in Fragment with ComposeView
 * This helps with migration from Dialog to Sheet
 *
 * Usage in Fragment:
 * ```kotlin
 * // In your Fragment
 * override fun onViewCreated(...) {
 *     composeView.setContent {
 *         var showCompanySheet by remember { mutableStateOf(false) }
 *
 *         // Observe events
 *         LaunchedEffect(Unit) {
 *             viewModel.events.collect { event ->
 *                 when (event) {
 *                     is LoginEvent.ShowCompanySelectionDialog -> {
 *                         showCompanySheet = true
 *                     }
 *                 }
 *             }
 *         }
 *
 *         // Show sheet
 *         CompanySelectionSheet(
 *             visible = showCompanySheet,
 *             companies = viewModel.companiesList,
 *             selectedId = viewModel.selectedCompanyId,
 *             onDismiss = { showCompanySheet = false },
 *             onCompanySelected = { viewModel.selectCompany(it) }
 *         )
 *     }
 * }
 * ```
 */
