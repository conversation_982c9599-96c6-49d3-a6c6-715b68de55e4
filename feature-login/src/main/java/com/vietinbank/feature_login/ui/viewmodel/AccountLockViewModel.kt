package com.vietinbank.feature_login.ui.viewmodel

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.AccountLockDomain
import com.vietinbank.core_domain.models.login.AccountLockFaceParams
import com.vietinbank.core_domain.models.login.AccountLockOTPDomain
import com.vietinbank.core_domain.models.login.AccountLockOTPParams
import com.vietinbank.core_domain.models.login.AccountLockParams
import com.vietinbank.core_domain.models.login.MethodDomain
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.usecase.login.LoginUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_login.R
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class AccountLockViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {
    companion object {
        const val NONE = ""
        const val RULE_APPROVE = "RULE_APPROVE"
        const val RULE_METHOD = "RULE_METHOD"
        const val SMS = "SMS"
        const val EMAIL = "E-mail"
        const val SHARE = "SHARE"
    }

    fun onAction(action: LockAction) {
        when (action) {
            is LockAction.OnBackPressed -> sendEvent(LockActionEvent.NavigateBack)
            is LockAction.OnNextClick -> sendEvent(LockActionEvent.NextClick)
            is LockAction.OnRuleClick -> sendEvent(LockActionEvent.RuleClick(ruleClick = action.ruleClick))
            is LockAction.OnToggleRuleClick -> {
                when (action.tongRule) {
                    is ApproverDomains -> {
                        setApproveUser(action.tongRule)
                    }

                    is MethodDomain -> {
                        setMethodProvide(action.tongRule)
                    }

                    else -> {
                    }
                }
            }
            is LockAction.OnMethodDismissed -> {
                val cur = _lockUIState.value as? LockUIState.InitState
                val needApprover = cur?.approveUser == null && true != cur?.accountDomain?.isFace
                if (needApprover) {
                    setRuleBottomEvent(RULE_APPROVE) // đóng Method xong thì mở Approver
                } else {
                    setRuleBottomEvent(NONE) // chỉ đóng nếu không cần Approver
                }
            }

            is LockAction.OnApproverDismissed -> {
                setRuleBottomEvent(NONE) // đóng Approver khi người dùng vuốt xuống
            }
            is LockAction.OnCompanyAccount -> {
                _initUiState.update {
                    it.copy(companyAccount = action.companyAccount?.trim().orEmpty())
                        .withSubmitEnabled()
                }
            }
            is LockAction.OnCompanyCif -> {
                _initUiState.update {
                    it.copy(companyCif = action.companyCif?.trim().orEmpty())
                        .withSubmitEnabled()
                }
            }
            is LockAction.OnCompanyId -> {
                _initUiState.update {
                    it.copy(companyId = action.companyId?.trim().orEmpty())
                        .withSubmitEnabled()
                }
            }
            is LockAction.OnCompanyUser -> {
                _initUiState.update {
                    it.copy(companyUser = action.companyUser?.trim().orEmpty())
                        .withSubmitEnabled()
                }
            }
        }
    }

    // Giữ logic enable ở 1 chỗ, dễ test & tái sử dụng (KHÔNG đổi)
    private fun LockInitUiState.withSubmitEnabled(): LockInitUiState {
        val enabled = companyCif.isNotEmpty() &&
            companyUser.isNotEmpty() &&
            companyId.isNotEmpty() &&
            companyAccount.isNotEmpty()
        return copy(isSubmitEnabled = enabled)
    }

    // Giữ lại type reset làm biến riêng (tham số gọi API)
    private val _typeReset = MutableStateFlow<String?>(null)

    // UI state tổng hợp cho màn hình Init
    data class LockInitUiState(
        val typeScreen: String = "",
        val companyCif: String = "",
        val companyUser: String = "",
        val companyId: String = "",
        val companyAccount: String = "",
        val isSubmitEnabled: Boolean = false,
    )

    // Một StateFlow duy nhất cho Compose thu
    private val _initUiState = MutableStateFlow(LockInitUiState())
    val initUiState: StateFlow<LockInitUiState> = _initUiState.asStateFlow()

    fun setTitleType(type: String?) {
        _typeReset.value = type
        val title = when (_typeReset.value) {
            Tags.TYPE_GROUP_RESET_PASSWORD -> resourceProvider.getString(R.string.prelogin_reissue_password)
            Tags.TYPE_GROUP_UNLOCK_USER -> resourceProvider.getString(R.string.prelogin_unlock_user)
            else -> ""
        }
        _initUiState.update { it.copy(typeScreen = title) }
    }

    private val lstChecker = mutableListOf<ApproverDomains>()
    private val lstMethod = mutableListOf<MethodDomain>()
    private val _lockUIState = MutableStateFlow<LockUIState>(LockUIState.NONE)
    val lockUIState = _lockUIState.asStateFlow()
    fun setLockUIState(state: LockUIState) {
        _lockUIState.value = state
        // man confirm -> state Init
        if (state is LockUIState.InitState) {
            lstChecker.clear()
            lstMethod.clear()
            // checker
            lstChecker.addAll(state.accountDomain.listChecker ?: emptyList())
            // them phuong thuc thu cong
            if (!state.accountDomain.phoneNumber.isNullOrEmpty()) {
                lstMethod.add(
                    MethodDomain(
                        SMS,
                        resourceProvider.getString(R.string.lock_confirm_method_sms, state.accountDomain.phoneNumber.toString()),
                        "",
                    ),
                )
            }
            if (!state.accountDomain.email.isNullOrEmpty()) {
                lstMethod.add(
                    MethodDomain(
                        EMAIL,
                        resourceProvider.getString(R.string.lock_confirm_method_email, state.accountDomain.email.toString()),
                        "",
                    ),
                )
            }
        }
    }

    fun setRuleBottomEvent(ruleType: String) {
        if (_lockUIState.value is LockUIState.InitState) {
            val cur = _lockUIState.value as LockUIState.InitState
            val event = when (ruleType) {
                RULE_APPROVE -> RuleBottomEvent.Approved(lstChecker, cur.approveUser)
                RULE_METHOD -> RuleBottomEvent.Method(lstMethod, cur.methodResetPassword)
                else -> RuleBottomEvent.None
            }
            _lockUIState.update { cur.copy(ruleBottomEvent = event) }
        }
    }

    // phuong thuc cap lai mk
    fun setMethodProvide(rule: MethodDomain?) {
        if (_lockUIState.value is LockUIState.InitState) {
            _lockUIState.update { (it as LockUIState.InitState).copy(methodResetPassword = rule) }
        }
    }

    // nguoi phe duyet
    fun setApproveUser(rule: ApproverDomains?) {
        if (_lockUIState.value is LockUIState.InitState) {
            _lockUIState.update { (it as LockUIState.InitState).copy(approveUser = rule) }
        }
    }

    fun validateInputField(initScreen: Boolean = true) {
        val cur = _lockUIState.value as? LockUIState.InitState
        val isFace = cur?.accountDomain?.isFace == true

        val errorMsg: String? = when {
            initScreen && (
                _initUiState.value.companyCif.isEmpty() ||
                    _initUiState.value.companyUser.isEmpty() ||
                    _initUiState.value.companyId.isEmpty() ||
                    _initUiState.value.companyAccount.isEmpty()
                ) -> resourceProvider.getString(R.string.lock_init_validate_empty)

            !initScreen && (
                cur?.methodResetPassword == null ||
                    (cur?.approveUser == null && !isFace)
                ) -> resourceProvider.getString(R.string.lock_init_validate_empty)

            else -> null
        }

        if (errorMsg != null) {
            sendEvent(LockActionEvent.ShowValidationError(errorMsg))
        } else {
            if (!initScreen) {
                sendOTPLockAccount()
            } else {
                checkInformation()
            }
        }
    }

    fun validateSelectorField(ruleType: String?) {
        val errorMsg: String? = when {
            ruleType == RULE_METHOD && lstMethod.isEmpty() ->
                resourceProvider.getString(R.string.lock_confirm_error_sms_email)

            ruleType == RULE_APPROVE && lstChecker.isEmpty() ->
                resourceProvider.getString(R.string.lock_confirm_error_approve)

            else -> null
        }

        if (errorMsg != null) {
            sendEvent(LockActionEvent.ShowValidationError(errorMsg))
        } else {
            setRuleBottomEvent(ruleType.toString())
        }
    }

    // Event một-lần (không replay)
    private val _eventsLockDomain = MutableSharedFlow<LockInitEvent>(replay = 0, extraBufferCapacity = 64)
    val eventsLockDomain = _eventsLockDomain.asSharedFlow()
    private var lockDomain: AccountLockDomain? = null
    private val _lockDomainState = MutableSharedFlow<AccountLockDomain>()
    val lockDomainState = _lockDomainState.asSharedFlow()
    fun checkInformation() = launchJob {
        val params = AccountLockParams(
            accountPayment = _initUiState.value.companyAccount,
            cifNo = _initUiState.value.companyCif,
            idCard = _initUiState.value.companyId,
            type = _typeReset.value?.lowercase(),
            username = _initUiState.value.companyUser,
        )
        val result = loginUseCase.checkInformation(params)
        handleResource(result) {
            it.apply {
                username = _initUiState.value.companyUser
                cifNo = _initUiState.value.companyCif
                idCard = _initUiState.value.companyId
                accountPayment = _initUiState.value.companyAccount
                typeReset = _typeReset.value?.lowercase()
            }
            lockDomain = it
            _lockDomainState.emit(it)

            //     maker - k admin -> khong face gi ca
            //     checker - k admin - k dai dien -> khong face gi ca
            //                - dai dien + da thu thap sinh tra hoc -> face
            //                  - dai dien + chua thu thạp -> "Yêu cầu Mở khóa người dùng không thành công, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
            //     checker - admin- k dai dien -> tb "“Yêu cầu Mở khóa người dùng không thành công do Quý khách được cấp quyền người dùng quản trị. Vui lòng liên hệ với Chi nhánh/Phòng giao dịch VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi]."
            //                    - dai dien + da thu thap -> face
            //                    - dai dien + chua thu thap -> tb "Yêu cầu Mở khóa người dùng không thành công do chưa thu thập Sinh trắc học, Quý khách vui lòng liên hệ với CN/PGD VietinBank gần nhất để được hỗ trợ.” + [Mã lỗi].
            // message lỗi ==> anh Quang trả -> chỉ còn 3 case
            // đại diện + đã thu thâp -> faceId
            // admin -> di luồng bình thường
            val needLiveness = (
                Tags.TYPE_SAY_YES == it.present &&
                    Tags.TYPE_FLAG_YES == it.ekycFlag
                )

            if (needLiveness) {
                _eventsLockDomain.emit(LockInitEvent.StartLiveness)
            } else {
                _eventsLockDomain.emit(LockInitEvent.GoToConfirm)
            }
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        when (exception) {
            is AppException.ApiException -> {
                if (exception.requestPath == Constants.MB_ACCOUNT_LOCK &&
                    exception.subCode == Tags.ERROR_CODE_MB_ACCOUNT_LOCK
                ) {
                    sendEvent(LockActionEvent.ShowErrorAccountLock(exception.message.toString()))
                } else {
                    super.onDisplayErrorMessage(exception)
                }
            }

            else -> super.onDisplayErrorMessage(exception)
        }
    }

    private val _otpLockDomain = MutableSharedFlow<AccountLockOTPDomain>()
    val otpLockDomain = _otpLockDomain.asSharedFlow()
    fun sendOTPLockAccount() = launchJob {
        when {
            _lockUIState.value is LockUIState.InitState -> {
                val params = AccountLockOTPParams(
                    type = when ((_lockUIState.value as? LockUIState.InitState)?.methodResetPassword?.code) {
                        SMS -> Tags.TYPE_FLAG_NO
                        EMAIL -> Tags.TYPE_FLAG_YES
                        else -> ""
                    },
                    typeReset = (_lockUIState.value as LockUIState.InitState).accountDomain.typeReset,
                    cifNo = (_lockUIState.value as LockUIState.InitState).accountDomain.cifNo,
                    username = (_lockUIState.value as LockUIState.InitState).accountDomain.username,
                    accountPayment = (_lockUIState.value as LockUIState.InitState).accountDomain.accountPayment,
                    idCard = (_lockUIState.value as LockUIState.InitState).accountDomain.idCard,
                    approver = (_lockUIState.value as LockUIState.InitState).approveUser?.username ?: (_lockUIState.value as LockUIState.InitState).accountDomain.username,
                    roleChecker = (_lockUIState.value as LockUIState.InitState).approveUser?.approverlevel,
                    token = when ((_lockUIState.value as? LockUIState.InitState)?.methodResetPassword?.code) {
                        SMS -> (_lockUIState.value as LockUIState.InitState).accountDomain.phoneNumber
                        EMAIL -> (_lockUIState.value as LockUIState.InitState).accountDomain.email
                        else -> ""
                    },
                )
                val result = loginUseCase.sendOTPLockAccount(params)
                handleResource(result) {
                    _otpLockDomain.emit(it)
                }
            }

            else -> {
            }
        }
    }

    fun getResultLockAccount(domain: AccountLockOTPDomain): Pair<String, List<ItemResult>>? {
        if (_lockUIState.value is LockUIState.InitState) {
            (_lockUIState.value as LockUIState.InitState).accountDomain.let {
                val description = when (it.typeReset) {
                    Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> {
                        resourceProvider.getString(R.string.prelogin_unlock_user)
                    }

                    else -> {
                        resourceProvider.getString(R.string.prelogin_reissue_password)
                    }
                }

                val lstDisplay = mutableListOf<ItemResult>(
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.transaction_number_label),
                        value = domain.mtId ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_cif_mst),
                        value = it.cifNo ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_name),
                        value = it.username ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_cccd),
                        value = it.idCard ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_account),
                        value = it.accountPayment ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(R.string.lock_confirm_approve),
                        value = (
                            (_lockUIState.value as LockUIState.InitState).approveUser?.username + "–" +
                                (_lockUIState.value as LockUIState.InitState).approveUser?.title
                            ) ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_confirm_method_pass),
                        value = (_lockUIState.value as LockUIState.InitState).methodResetPassword?.value ?: "",
                    ),
                    ItemResult(
                        title = resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_checker_process_time),
                        value = domain.createdDate ?: "",
                    ),
                )
                return Pair(description, lstDisplay)
            }
        }
        return null
    }

    private val _eventsFaceLockAccount = MutableSharedFlow<LockInitEvent>(replay = 0, extraBufferCapacity = 64)
    val eventsFaceLockAccount = _eventsFaceLockAccount.asSharedFlow()
    private val _faceLockAccount = MutableSharedFlow<AccountLockOTPDomain>()
    val faceLockAccount = _faceLockAccount.asSharedFlow()
    fun compareFaceLockAccount(image: String?) = launchJob {
        val params = AccountLockFaceParams(
            cifNo = lockDomain?.cifNo,
            username = lockDomain?.username,
            image = image,
        )
        val result = loginUseCase.compareFaceLockAccount(params)
        handleResource(result) {
            lockDomain?.isFace = true
            _faceLockAccount.emit(it)

            if (Tags.TYPE_SAY_YES == it.match) {
                _eventsFaceLockAccount.emit(FaceLockInitEvent.GoToConfirmFace)
            } else {
                _eventsFaceLockAccount.emit(FaceLockInitEvent.ShowNotice(it.status.message.toString()))
            }
        }
    }

    fun getBundleLockAccount() =
        lockDomain?.let { Utils.g().provideGson().toJson(it) }

    fun validateOTPLockAccount(accountLockDomain: AccountLockOTPDomain) {
        if (_lockUIState.value is LockUIState.InitState && (_lockUIState.value as LockUIState.InitState).accountDomain.isFace == true) {
            sendEvent(LockActionEvent.ShowSuccessFace(accountLockDomain.status.message.toString()))
        } else {
            sendEvent(LockActionEvent.LockResult(accountLockDomain))
        }
    }
}

sealed class RuleBottomEvent {
    data object None : RuleBottomEvent()
    data class Method(val lstMethod: List<MethodDomain>?, val selector: MethodDomain?) :
        RuleBottomEvent()

    data class Approved(val lstApprover: List<ApproverDomains>?, val approved: ApproverDomains?) :
        RuleBottomEvent()
}

sealed class LockUIState {
    data object NONE : LockUIState()
    data class ResultState(val result: Pair<String, List<ItemResult>>) : LockUIState()
    data class InitState(
        val accountDomain: AccountLockDomain,
        val methodResetPassword: MethodDomain? = null,
        val approveUser: ApproverDomains? = null,
        val ruleBottomEvent: RuleBottomEvent = RuleBottomEvent.None,
    ) : LockUIState()
}

sealed interface LockInitEvent {
    data object StartLiveness : LockInitEvent
    data object GoToConfirm : LockInitEvent
}

sealed interface FaceLockInitEvent {
    data object GoToConfirmFace : LockInitEvent
    data class ShowNotice(val message: String) : LockInitEvent
}

sealed interface LockAction {
    data object OnBackPressed : LockAction
    data object OnNextClick : LockAction
    data class OnRuleClick(val ruleClick: String) : LockAction
    data class OnToggleRuleClick(val tongRule: Any) : LockAction
    data class OnCompanyCif(val companyCif: String) : LockAction
    data class OnCompanyUser(val companyUser: String) : LockAction
    data class OnCompanyId(val companyId: String) : LockAction
    data class OnCompanyAccount(val companyAccount: String) : LockAction
    data object OnMethodDismissed : LockAction
    data object OnApproverDismissed : LockAction
}

sealed class LockActionEvent : OneTimeEvent {
    data object NavigateBack : LockActionEvent()
    data object NextClick : LockActionEvent()
    data class RuleClick(val ruleClick: String) : LockActionEvent()
    data class ShowValidationError(val message: String) : LockActionEvent()
    data class ShowErrorAccountLock(val message: String) : LockActionEvent()
    data class ShowSuccessFace(val message: String) : LockActionEvent()
    data class LockResult(val accountLockDomain: AccountLockOTPDomain) : LockActionEvent()
}