package com.vietinbank.feature_login.ui.state

import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.feature_login.ui.screen.newui.QuickActionItemModel
import com.vietinbank.feature_login.ui.viewmodel.DialogState

/**
 * UI State for Login screen
 * Uses StateFlow for reactive UI updates
 * Separates UI state from one-time events
 */
data class LoginUiState(
    // User Input
    val username: String = "",
    val password: String = "",
    val passwordVisible: Boolean = false,
    val showUsernameTooltip: Boolean = false,
    val userRole: String = "",

    val listQuickAction: List<QuickActionItemModel> = listOf(),

    // User Status
    val isActive: Boolean = false,
    val fullName: String = "",
    val savedUsername: String = "",

    // Selected user info from SwitchUserDialog (for display only)
    // These fields are populated when user selects a different user from switch dialog
    val selectedUserFullName: String? = null,
    val selectedUserCorpName: String? = null,
    val selectedUserRoleId: String? = null,
    val isShowingSelectedUser: Boolean = false, // Flag to indicate we're showing selected user, not current user

    // Multi-CIF
    val selectedCompany: CifSharedDomain? = null,

    // Validation Errors
    val usernameError: String? = null,
    val passwordError: String? = null,

    // Loading States
    val isLoading: Boolean = false,
    val loginState: Resource<LoginDomain>? = null,

    // Feature Flags
    val isBiometricEnabled: Boolean = false,
    val isForceUpdateRequired: Boolean = false,

    // Dialog Management
    val dialog: DialogState = DialogState.None,

    // Focus Management
    val passwordFocusTrigger: Int = 0, // Increment to trigger password focus
) {

    /**
     * Helper function to check if login button should be enabled
     */
    fun isLoginEnabled(): Boolean {
        return when {
            isLoading -> false
            isActive -> password.isNotBlank()
            else -> username.isNotBlank() && password.isNotBlank()
        }
    }
}