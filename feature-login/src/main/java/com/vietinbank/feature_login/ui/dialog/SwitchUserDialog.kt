package com.vietinbank.feature_login.ui.dialog

import android.os.Bundle
import android.os.Parcelable
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_common.recent.RecentUserEntry
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.dialog.DialogMotion
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.ui.sheet.DeleteUserAction
import com.vietinbank.feature_login.ui.sheet.DeleteUserConfirmationBottomSheet
import kotlinx.parcelize.Parcelize
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS
import com.vietinbank.feature_login.R as LoginR

/**
 * Dialog constants for non-design values
 */
private object SwitchUserDialogConstants {
    const val ENTER_DURATION_MS = 300
    const val EXIT_DURATION_MS = 200
    const val SPRING_STIFFNESS = 400f
    const val MAX_WIDTH_DP = 600
    const val AVATAR_SIZE_DP = 56
}

/**
 * Result data for user selection
 */
@Parcelize
data class SwitchUserResult(
    val selectedUsername: String,
    val hasBiometric: Boolean,
) : Parcelable

/**
 * Parcelable wrapper for RecentUserEntry to pass via arguments
 */
@Parcelize
data class RecentUserParcelable(
    val username: String,
    val fullName: String? = null,
    val corpName: String? = null,
    val roleId: String? = null,
    val biometricToken: String? = null,
) : Parcelable {
    fun toRecentUserEntry() = RecentUserEntry(
        username = username,
        fullName = fullName,
        corpName = corpName,
        roleId = roleId,
        biometricToken = biometricToken,
    )

    companion object {
        fun fromRecentUserEntry(entry: RecentUserEntry) = RecentUserParcelable(
            username = entry.username,
            fullName = entry.fullName,
            corpName = entry.corpName,
            roleId = entry.roleId,
            biometricToken = entry.biometricToken,
        )
    }
}

/**
 * Switch User Dialog for account selection
 *
 * Features:
 * - Shows up to 5 recent users
 * - Displays user avatar with initials
 * - Shows biometric icon if user has biometric enabled
 * - Allows removal of users from recent list
 * - Follows Foundation Design System
 *
 * Example usage:
 * ```kotlin
 * // Show dialog
 * SwitchUserDialog.show(
 *     fragmentManager = childFragmentManager,
 *     recentUsers = viewModel.getRecentUsers(),
 *     currentUsername = viewModel.getUserName(),
 *     onRemoveUser = { username -> viewModel.removeRecentUser(username) }
 * )
 *
 * // Listen for result
 * childFragmentManager.setFragmentResultListener(
 *     "switch_user_result",
 *     viewLifecycleOwner
 * ) { _, bundle ->
 *     val isCancelled = bundle.getBoolean("key_cancelled", false)
 *     if (!isCancelled) {
 *         val result = bundle.getParcelable<SwitchUserResult>("key_result")
 *         result?.let {
 *             viewModel.setSelectedLoginUsername(it.selectedUsername)
 *             if (it.hasBiometric) {
 *                 // Trigger biometric authentication
 *                 viewModel.performBiometricLogin()
 *             } else {
 *                 // Show login form for password entry
 *                 viewModel.showLoginForUser(it.selectedUsername)
 *             }
 *         }
 *     }
 * }
 * ```
 */
class SwitchUserDialog : BaseDialog<SwitchUserResult>() {

    companion object {
        private const val ARG_RECENT_USERS = "arg_recent_users"
        private const val ARG_CURRENT_USERNAME = "arg_current_username"
        private const val TAG = "SwitchUserDialog"

        /**
         * Show dialog only if not already showing
         * This prevents duplicate dialogs when navigating back or configuration changes
         */

        fun show(
            fragmentManager: FragmentManager,
            recentUsers: List<RecentUserEntry>,
            currentUsername: String? = null,
            onRemoveUser: ((String) -> Unit)? = null,
        ) {
            (fragmentManager.findFragmentByTag(TAG) as? DialogFragment)
                ?.dismissAllowingStateLoss()
            fragmentManager.executePendingTransactions()

            val dialog = SwitchUserDialog().apply {
                arguments = Bundle().apply {
                    putParcelableArrayList(
                        ARG_RECENT_USERS,
                        ArrayList(recentUsers.map { RecentUserParcelable.fromRecentUserEntry(it) }),
                    )
                    putString(ARG_CURRENT_USERNAME, currentUsername)
                }
                this.onRemoveUserCallback = onRemoveUser
            }

            dialog.show(fragmentManager, TAG)
        }
    }

    // Callback for removing users (not using Result API since this is immediate action)
    private var onRemoveUserCallback: ((String) -> Unit)? = null

    // Dialog configuration
    override val resultKey: String = "switch_user_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet
    override val motion: DialogMotion = DialogMotion(
        enterDurationMs = SwitchUserDialogConstants.ENTER_DURATION_MS,
        exitDurationMs = SwitchUserDialogConstants.EXIT_DURATION_MS,
        springStiffness = SwitchUserDialogConstants.SPRING_STIFFNESS,
    )
    override val allowTouchDismiss: Boolean = true
    override val requiresSecureFlag: Boolean = true // Banking app security
    override val maxWidthDp: Int = SwitchUserDialogConstants.MAX_WIDTH_DP

    override val resultFragmentManager: FragmentManager
        get() = parentFragmentManager

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (SwitchUserResult) -> Unit,
    ) {
        val recentUsers = remember {
            arguments?.getParcelableArrayList<RecentUserParcelable>(ARG_RECENT_USERS)
                ?.map { it.toRecentUserEntry() }
                ?: emptyList()
        }

        val currentUsername = remember {
            arguments?.getString(ARG_CURRENT_USERNAME)
        }

        // Local state for removed users (to update UI immediately)
        var localUsers by remember { mutableStateOf(recentUsers) }

        // Edit mode state management
        var isEditMode by remember { mutableStateOf(false) }
        var selectedUsers by remember { mutableStateOf<Set<String>>(emptySet()) }

        // Delete confirmation state
        var showDeleteConfirmation by remember { mutableStateOf(false) }

        Column(
            modifier = Modifier
                .fillMaxWidth(),
        ) {
            // Main container with white background
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = FDS.Colors.backgroundBgContainer,
                        shape = RoundedCornerShape(FDS.Sizer.Radius.radius16),
                    ),
            ) {
                // Header
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FDS.Sizer.Padding.padding24,
                            vertical = FDS.Sizer.Padding.padding24,
                        ),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // Hidden icon for balance
                    Icon(
                        painter = painterResource(id = R.drawable.ic_edit),
                        contentDescription = null,
                        tint = FDS.Colors.characterHighlighted,
                        modifier = Modifier
                            .size(FDS.Sizer.Icon.icon24)
                            .alpha(0f), // Hidden for balance
                    )

                    // Title - conditional based on edit mode
                    FoundationText(
                        text = if (isEditMode) {
                            stringResource(R.string.switch_user_delete_title)
                        } else {
                            stringResource(R.string.switch_user_title)
                        },
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    // Edit icon - clickable to toggle edit mode
                    Icon(
                        painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_edit_24),
                        contentDescription = stringResource(R.string.switch_user_edit),
                        tint = FDS.Colors.characterHighlighted,
                        modifier = Modifier
                            .size(FDS.Sizer.Icon.icon24)
                            .safeClickable {
                                isEditMode = !isEditMode
                                selectedUsers = emptySet() // Reset selection when toggling
                            },
                    )
                }

                // Divider
                HorizontalDivider(
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke05,
                )

                // User list
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FDS.Sizer.Padding.padding24,
                            vertical = FDS.Sizer.Padding.padding8,
                        ),
                ) {
                    localUsers.forEachIndexed { index, user ->
                        UserItem(
                            user = user,
                            isCurrentUser = user.username == currentUsername,
                            isEditMode = isEditMode,
                            isSelected = selectedUsers.contains(user.username),
                            onSelect = {
                                if (isEditMode) {
                                    // Toggle selection in edit mode
                                    selectedUsers = if (selectedUsers.contains(user.username)) {
                                        selectedUsers - user.username
                                    } else {
                                        selectedUsers + user.username
                                    }
                                } else {
                                    // Normal mode - select user and return result
                                    onResult(
                                        SwitchUserResult(
                                            selectedUsername = user.username,
                                            hasBiometric = !user.biometricToken.isNullOrEmpty(),
                                        ),
                                    )
                                    // No need to call onDismissRequest - BaseDialog auto-dismisses after result
                                }
                            },
                        )

                        // Divider between items (not after last item and not before "Add new account")
                        if (index < localUsers.size - 1) {
                            HorizontalDivider(
                                color = FDS.Colors.strokeDivider,
                                thickness = FDS.Sizer.Stroke.stroke05,
                                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
                            )
                        }
                    }

                    // Divider before "Add new account"
                    if (localUsers.isNotEmpty()) {
                        HorizontalDivider(
                            color = FDS.Colors.strokeDivider,
                            thickness = FDS.Sizer.Stroke.stroke05,
                            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
                        )
                    }

                    // Add new account item
                    AddNewAccountItem(
                        onClick = {
                            onResult(SwitchUserResult(selectedUsername = "", hasBiometric = false))
                            // No need to call onDismissRequest - BaseDialog auto-dismisses after result
                        },
                    )
                }
            }

            // Space between container and button

            // Button area - animated visibility based on edit mode
            AnimatedVisibility(
                visible = isEditMode,
                enter = expandVertically(
                    animationSpec = tween(300, easing = FastOutSlowInEasing),
                    expandFrom = Alignment.Top,
                ) + fadeIn(animationSpec = tween(300)),
                exit = shrinkVertically(
                    animationSpec = tween(250, easing = FastOutSlowInEasing),
                    shrinkTowards = Alignment.Top,
                ) + fadeOut(animationSpec = tween(250)),
            ) {
                Column {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                    // Edit mode: 2 buttons (Quay lại + Xóa)
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding16),
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    ) {
                        // Quay lại (Back) - dark button
                        FoundationButton(
                            text = stringResource(R.string.common_back),
                            onClick = {
                                isEditMode = false
                                selectedUsers = emptySet()
                            },
                            modifier = Modifier.weight(1f),
                            isLightButton = false, // Dark button
                        )

                        // Xóa (Delete) - light button
                        FoundationButton(
                            text = stringResource(R.string.common_delete),
                            onClick = {
                                // Show delete confirmation bottom sheet
                                showDeleteConfirmation = true
                            },
                            modifier = Modifier.weight(1f),
                            isLightButton = true, // Light button
                            enabled = selectedUsers.isNotEmpty(),
                        )
                    }
                }
            }
        }

        // Delete Confirmation Bottom Sheet
        DeleteUserConfirmationBottomSheet(
            visible = showDeleteConfirmation,
            selectedUsers = localUsers.filter { selectedUsers.contains(it.username) },
            onAction = { action ->
                when (action) {
                    DeleteUserAction.OnDismiss -> {
                        showDeleteConfirmation = false
                    }
                    DeleteUserAction.OnConfirmDelete -> {
                        // Handle actual deletion
                        val deleted = selectedUsers.toList()
                        onRemoveUserCallback?.let { callback ->
                            deleted.forEach { username ->
                                callback(username)
                            }
                        }
                        // Update local users list
                        localUsers = localUsers.filter { !deleted.contains(it.username) }
                        // Reset states
                        showDeleteConfirmation = false
                        isEditMode = false
                        selectedUsers = emptySet()
                        // Notify parent fragment that deletion occurred (for immediate UI refresh)
                        resultFragmentManager.setFragmentResult(
                            "switch_user_deleted",
                            Bundle().apply {
                                putStringArrayList("deleted_usernames", ArrayList(deleted))
                            },
                        )
                    }
                }
            },
        )
    }
}

/**
 * Individual user item in the list
 */
@Composable
private fun UserItem(
    user: RecentUserEntry,
    isCurrentUser: Boolean,
    isEditMode: Boolean = false,
    isSelected: Boolean = false,
    onSelect: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable(onSafeClick = onSelect)
            .padding(vertical = FDS.Sizer.Padding.padding8),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Avatar with initials
        Box(
            modifier = Modifier
                .size(SwitchUserDialogConstants.AVATAR_SIZE_DP.dp)
                .clip(CircleShape)
                .background(FDS.Colors.blue100),
            contentAlignment = Alignment.Center,
        ) {
            val initials = user.fullName?.getInitials()
                ?: user.username.take(2).uppercase()

            FoundationText(
                text = initials,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
        }

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))

        // User info
        Column(
            modifier = Modifier.weight(1f),
        ) {
            // Fullname (top position)
            FoundationText(
                text = user.fullName ?: user.username,
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterHighlighted,
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            // Role + Company (bottom position)
            val roleText = if (user.roleId == Tags.CHECKER) {
                stringResource(com.vietinbank.feature_login.R.string.login_role_checker)
            } else {
                stringResource(com.vietinbank.feature_login.R.string.login_role_maker)
            }

            val displayText = user.corpName?.let { corp ->
                stringResource(com.vietinbank.feature_login.R.string.login_role_with_company, roleText, corp)
            } ?: user.username

            FoundationText(
                text = displayText,
                style = FDS.Typography.captionCaptionMBold,
                color = FDS.Colors.characterSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        if (isEditMode) {
            // Checkbox in edit mode
            Checkbox(
                checked = isSelected,
                onCheckedChange = { onSelect() },
                colors = CheckboxDefaults.colors(
                    checkedColor = FDS.Colors.characterHighlightSoft, // #2eb2f7
                    uncheckedColor = FDS.Colors.characterSecondary, // #7c8b95
                    checkmarkColor = FDS.Colors.white,
                ),
                modifier = Modifier.size(20.dp),
            )
        } else {
            // Normal mode - show current user indicator and biometric icon
            // Current user indicator
            if (isCurrentUser) {
                Box(
                    modifier = Modifier
                        .background(
                            color = FDS.Colors.blue50,
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        )
                        .border(
                            width = FDS.Sizer.Stroke.stroke1,
                            color = FDS.Colors.blue700,
                            shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        )
                        .padding(
                            horizontal = FDS.Sizer.Gap.gap8,
                            vertical = FDS.Sizer.Gap.gap4,
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    FoundationText(
                        text = stringResource(R.string.switch_user_current),
                        style = FDS.Typography.captionMBold,
                        color = FDS.Colors.blue700,
                    )
                }
            }

            // Biometric icon (if has biometric)
            if (!user.biometricToken.isNullOrEmpty()) {
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                Icon(
                    painter = painterResource(id = R.drawable.ic_fingerprint),
                    contentDescription = stringResource(R.string.switch_user_biometric_enabled),
                    tint = FDS.Colors.primary,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                )
            }
        }
    }
}

/**
 * Add new account item
 */
@Composable
private fun AddNewAccountItem(
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable(onSafeClick = onClick)
            .padding(vertical = FDS.Sizer.Padding.padding16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Add person icon
        Image(
            painter = painterResource(id = LoginR.drawable.ic_login_add_person_48),
            contentDescription = stringResource(R.string.switch_user_add_new),
            modifier = Modifier.size(SwitchUserDialogConstants.AVATAR_SIZE_DP.dp),
        )

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap16))

        // Text
        FoundationText(
            text = stringResource(R.string.switch_user_add_new),
            style = FDS.Typography.bodyB2Emphasized,
            color = FDS.Colors.characterSecondary,
        )
    }
}