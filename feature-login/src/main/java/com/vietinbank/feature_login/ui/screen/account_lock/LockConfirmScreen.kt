package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.login.MethodDomain
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationInfoVertical
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockAction
import com.vietinbank.feature_login.ui.viewmodel.LockUIState
import com.vietinbank.feature_login.ui.viewmodel.RuleBottomEvent

@Composable
fun LockConfirmScreen(
    uiState: LockUIState,
    onAction: (LockAction) -> Unit,
) {
    val cur = uiState as? LockUIState.InitState
    val methodResetPassword = cur?.methodResetPassword
    val approveUser = cur?.approveUser
    val ruleBottomEvent = cur?.ruleBottomEvent ?: RuleBottomEvent.None

    // 1) Sheet chọn phương thức cấp lại mật khẩu
    MethodRuleBottomSheet(
        visible = ruleBottomEvent is RuleBottomEvent.Method,
        lstMethod = (ruleBottomEvent as? RuleBottomEvent.Method)?.lstMethod.orEmpty(),
        selector = (ruleBottomEvent as? RuleBottomEvent.Method)?.selector,
        onSelect = { chosen: MethodDomain ->
            onAction(LockAction.OnToggleRuleClick(chosen))
        },
        onDismiss = {
            onAction(LockAction.OnMethodDismissed)
        },
    )

    // 2) Sheet chọn người phê duyệt
    ApproverRuleBottomSheet(
        visible = ruleBottomEvent is RuleBottomEvent.Approved,
        lstApprover = (ruleBottomEvent as? RuleBottomEvent.Approved)?.lstApprover.orEmpty(),
        selector = (ruleBottomEvent as? RuleBottomEvent.Approved)?.approved,
        onSelect = { chosen: ApproverDomains ->
            onAction(LockAction.OnToggleRuleClick(chosen))
        },
        onDismiss = {
            onAction(LockAction.OnApproverDismissed)
        },
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding()
            .dismissKeyboardOnClickOutside(),
    ) {
        FoundationAppBar(
            title = when ((uiState as LockUIState.InitState).accountDomain.typeReset?.uppercase()) {
                Tags.TYPE_GROUP_RESET_PASSWORD -> stringResource(R.string.prelogin_reissue_password)
                Tags.TYPE_GROUP_UNLOCK_USER -> stringResource(R.string.prelogin_unlock_user)
                else -> stringResource(com.vietinbank.core_ui.R.string.common_confirm)
            },
            isLightIcon = false,
            navigationIcon = rememberVectorPainter(Icons.AutoMirrored.Filled.ArrowBack),
            onNavigationClick = {
                onAction(LockAction.OnBackPressed)
            },
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FoundationDesignSystem.Sizer.Gap.gap8, FoundationDesignSystem.Sizer.Gap.gap8)
                .weight(1f)
                .verticalScroll(rememberScrollState()),
        ) {
            when {
                uiState is LockUIState.InitState -> {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FoundationDesignSystem.Sizer.Gap.gap12)
                            .background(
                                FoundationDesignSystem.Colors.white,
                                RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                            )
                            .padding(
                                horizontal = FoundationDesignSystem.Sizer.Gap.gap16,
                                vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                            ),
                    ) {
                        FoundationText(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
                            text = stringResource(R.string.lock_confirm_title),
                            style = FoundationDesignSystem.Typography.headingH4,
                            color = FoundationDesignSystem.Colors.characterPrimary,
                            textAlign = TextAlign.Center,
                        )

                        FoundationDivider(modifier = Modifier.padding(vertical = FoundationDesignSystem.Sizer.Gap.gap20))

                        FoundationInfoHorizontal(
                            title = stringResource(com.vietinbank.core_ui.R.string.lock_init_cif_mst),
                            value = (uiState as LockUIState.InitState).accountDomain.cifNo ?: "",
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                        )

                        FoundationInfoHorizontal(
                            modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
                            title = stringResource(com.vietinbank.core_ui.R.string.lock_init_name),
                            value = (uiState as LockUIState.InitState).accountDomain.username ?: "",
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                        )

                        FoundationInfoHorizontal(
                            modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
                            title = stringResource(com.vietinbank.core_ui.R.string.lock_init_cccd),
                            value = (uiState as LockUIState.InitState).accountDomain.idCard ?: "",
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                        )

                        FoundationInfoHorizontal(
                            modifier = Modifier.padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap16,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                            ),
                            title = stringResource(com.vietinbank.core_ui.R.string.lock_init_account),
                            value = (uiState as LockUIState.InitState).accountDomain.accountPayment
                                ?: "",
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                        )
                    }

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap8,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap8,
                            )
                            .background(
                                FoundationDesignSystem.Colors.white,
                                RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                            )
                            .padding(
                                horizontal = FoundationDesignSystem.Sizer.Gap.gap16,
                                vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                            ),
                    ) {
                        FoundationInfoVertical(
                            modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
                            title = stringResource(com.vietinbank.core_ui.R.string.lock_confirm_method_pass),
                            value = methodResetPassword?.value ?: "",
                            titleStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB1,
                            valueColor = FoundationDesignSystem.Colors.characterHighlighted,
                            icon = com.vietinbank.core_ui.R.drawable.ic_drop_down,
                        ) {
                            onAction(LockAction.OnRuleClick(AccountLockViewModel.RULE_METHOD))
                        }

                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap12))

                        if (true != (uiState as LockUIState.InitState).accountDomain.isFace) {
                            FoundationDivider(
                                modifier = Modifier.padding(
                                    top = FoundationDesignSystem.Sizer.Gap.gap2,
                                    bottom = FoundationDesignSystem.Sizer.Gap.gap12,
                                ),
                            )

                            FoundationInfoVertical(
                                title = stringResource(R.string.lock_confirm_approve),
                                value = listOfNotNull(
                                    approveUser?.username?.takeIf { it.isNotBlank() },
                                    approveUser?.title?.takeIf { it.isNotBlank() },
                                ).joinToString(" – "),
                                modifier = Modifier.padding(bottom = FoundationDesignSystem.Sizer.Gap.gap16),
                                titleStyle = FoundationDesignSystem.Typography.bodyB2Emphasized,
                                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                                valueStyle = FoundationDesignSystem.Typography.bodyB1,
                                valueColor = FoundationDesignSystem.Colors.characterHighlighted,
                                icon = com.vietinbank.core_ui.R.drawable.ic_drop_down,
                            ) {
                                onAction(LockAction.OnRuleClick(AccountLockViewModel.RULE_APPROVE))
                            }
                        }
                    }
                }

                else -> {
                }
            }
        }

        FoundationButton(
            isLightButton = methodResetPassword?.value?.isNotEmpty() == true &&
                ((uiState as? LockUIState.InitState)?.accountDomain?.isFace == true || approveUser?.username?.isNotEmpty() == true),
            text = stringResource(id = R.string.lock_confirm_transfer_control),
            onClick = { onAction(LockAction.OnNextClick) },
            modifier = Modifier
                .widthIn(max = FoundationDesignSystem.Sizer.ScreenBreakpoint.tabletMinWidth)
                .fillMaxWidth()
                .padding(start = FoundationDesignSystem.Sizer.Gap.gap24, end = FoundationDesignSystem.Sizer.Gap.gap24),
            size = ButtonSize.Large,
            enabled = methodResetPassword?.value?.isNotEmpty() == true &&
                ((uiState as? LockUIState.InitState)?.accountDomain?.isFace == true || approveUser?.username?.isNotEmpty() == true),
        )
    }
}