package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.login.LockResultActions
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.ScreenshotUtil
import com.vietinbank.core_ui.utils.ScreenshotUtil.trackBounds
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel.Companion.SHARE
import com.vietinbank.feature_login.ui.viewmodel.LockUIState
import kotlinx.coroutines.launch

@Composable
fun LockResultScreen(
    viewModel: AccountLockViewModel,
    actions: LockResultActions?,
) {
    val uiState by viewModel.lockUIState.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val rootView = LocalView.current
    val context = LocalContext.current

    // State to track the bounds of the content area for screenshot
    var contentBounds by remember { mutableStateOf<Rect?>(null) }

    val errorShare = stringResource(R.string.maker_transfer_account_result_error_share_picture)
    val shareAction: () -> Unit = {
        coroutineScope.launch {
            contentBounds?.let { bounds ->
                val bitmap = ScreenshotUtil.captureScreenshot(rootView, bounds, context)
                bitmap?.let { capturedBitmap ->
                    ScreenshotUtil.shareBitmap(context, capturedBitmap)
                } ?: actions?.onDownloadClick?.invoke(errorShare)
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding()
            .dismissKeyboardOnClickOutside(),
    ) {
        FoundationAppBar(
            isLightIcon = false,
            title = when {
                uiState is LockUIState.ResultState ->
                    (uiState as LockUIState.ResultState).result.first.toString()
                else -> ""
            },
            onNavigationClick = {},
            actions = listOf(
                AppBarAction(
                    icon = R.drawable.ic_share,
                    contentDescription = SHARE,
                    onClick = { shareAction() },
                ),
            ),
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
        ) {
            val scrollState = rememberScrollState()

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
                    .padding(FoundationDesignSystem.Sizer.Gap.gap8),
            ) {
                val resultState = uiState as? LockUIState.ResultState
                val items = resultState?.result?.second ?: emptyList()

                FoundationTransfer(
                    // Bọc trackBounds vào chính card để share đúng vùng hiển thị
                    modifier = Modifier
                        .fillMaxWidth()
                        .then(
                            Modifier.trackBounds { coordinates ->
                                contentBounds = coordinates.boundsInRoot()
                            },
                        ),
                    lstContent = items,
                    // Header theo ảnh: "Giao dịch số <code>" + chip trạng thái
                    contentTop = {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            // Chỉ để chip trạng thái và nằm sát trái
                            FoundationStatus(statusCode = Status.MakerTransferSuccess)
                        }
                    },
                )
            }
        }
        // -------------------------------------------------------------------

        FoundationButton(
            isLightButton = true,
            text = stringResource(id = com.vietinbank.feature_login.R.string.lock_result_back_login),
            onClick = { actions?.onLoginClick?.invoke() },
            modifier = Modifier
                .widthIn(max = FoundationDesignSystem.Sizer.ScreenBreakpoint.tabletMinWidth)
                .fillMaxWidth()
                .padding(
                    start = FoundationDesignSystem.Sizer.Gap.gap24,
                    end = FoundationDesignSystem.Sizer.Gap.gap24,
                ),
            size = ButtonSize.Large,
            enabled = true,
        )
    }
}
