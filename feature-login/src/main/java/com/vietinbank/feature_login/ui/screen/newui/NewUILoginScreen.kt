package com.vietinbank.feature_login.ui.screen.newui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.sheet.CompanySelectionSheet
import com.vietinbank.feature_login.ui.viewmodel.DialogState
import com.vietinbank.feature_login.ui.viewmodel.ValidationConstants
import kotlinx.coroutines.delay
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

// Animation constants for non-design values
private object LoginScreenAnimationConstants {
    const val INITIAL_FOCUS_DELAY = 350L // milliseconds
    const val KEYBOARD_SHOW_DELAY = 100L // milliseconds
    const val FADE_IN_DURATION = 400 // milliseconds
    const val FADE_IN_DELAY = 100 // milliseconds
    const val FADE_OUT_DURATION = 200 // milliseconds

    // Marquee animation for long text
    const val MARQUEE_REPEAT_DELAY = 2000 // milliseconds between iterations
    const val MARQUEE_INITIAL_DELAY = 1000 // milliseconds before starting
    val MARQUEE_VELOCITY = 30.dp // scroll speed
}

/**
 * Login screen with NewUI design
 * Supports CIF selection for multi-CIF users
 * Features smooth keyboard animation with auto-focus
 */
@Composable
fun NewUILoginScreen(
    isActive: Boolean,
    username: String,
    password: String,
    passwordVisible: Boolean,
    showUsernameTooltip: Boolean, // Keep for backward compatibility
    selectedCompany: CifSharedDomain?,
    fullName: String? = null,
    usernameError: String? = null,
    passwordError: String? = null,
    showCompanyDialog: Boolean = false,
    dialogState: DialogState = DialogState.None,
    passwordFocusTrigger: Int = 0,
    onAction: (NewUILoginAction) -> Unit,
    onDismissDialog: () -> Unit = {},
) {
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    // Focus requesters for input fields
    val usernameFocusRequester = remember { FocusRequester() }
    val passwordFocusRequester = remember { FocusRequester() }

    // Track if this is the initial focus request
    var hasInitialFocused by remember { mutableStateOf(false) }
    // Track last processed trigger to avoid re-processing
    var lastProcessedTrigger by remember { mutableStateOf(0) }

    // Combined focus logic to avoid conflicts
    LaunchedEffect(isActive, passwordFocusTrigger) {
        // Handle password focus trigger from dialog (highest priority)
        if (passwordFocusTrigger > lastProcessedTrigger && isActive) {
            // Mark as processed immediately to avoid double processing
            lastProcessedTrigger = passwordFocusTrigger
            hasInitialFocused = true // Also mark initial as done

            // Small delay to ensure UI and dialog are ready
            delay(LoginScreenAnimationConstants.INITIAL_FOCUS_DELAY)
            try {
                passwordFocusRequester.requestFocus()
                delay(LoginScreenAnimationConstants.KEYBOARD_SHOW_DELAY)
                keyboardController?.show()
            } catch (e: Exception) {
                // Handle case where focus requester is not attached yet
            }
        }
        // Handle initial focus when screen opens (lower priority)
        else if (!hasInitialFocused) {
            // Small delay for smooth screen transition
            delay(LoginScreenAnimationConstants.INITIAL_FOCUS_DELAY)

            if (isActive) {
                // Returning user - focus password field
                try {
                    passwordFocusRequester.requestFocus()
                    delay(LoginScreenAnimationConstants.KEYBOARD_SHOW_DELAY)
                    keyboardController?.show()
                } catch (e: Exception) {
                    // Handle case where focus requester is not attached yet
                }
            } else {
                // New user - focus username field
                try {
                    usernameFocusRequester.requestFocus()
                    delay(LoginScreenAnimationConstants.KEYBOARD_SHOW_DELAY)
                    keyboardController?.show()
                } catch (e: Exception) {
                    // Handle case where focus requester is not attached yet
                }
            }
            hasInitialFocused = true
        }
    }

    // Track sheet visibility based on DialogState
    val showCompanySheet = dialogState is DialogState.CompanySelection
    val companiesList = (dialogState as? DialogState.CompanySelection)?.companies ?: emptyList()

    // Hide keyboard when dialog/sheet opens
    LaunchedEffect(showCompanyDialog, showCompanySheet) {
        if (showCompanyDialog || showCompanySheet) {
            keyboardController?.hide()
            focusManager.clearFocus()
        }
    }

    // Auto scroll state for keyboard handling
    val scrollState = rememberScrollState()
    val ime = WindowInsets.ime
    val imeHeight = ime.getBottom(LocalDensity.current)

    // Auto scroll to bottom when keyboard appears
    LaunchedEffect(imeHeight) {
        if (imeHeight > 0) {
            // Scroll to show login button when keyboard appears
            scrollState.animateScrollTo(scrollState.maxValue)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding(),
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.TopCenter,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .widthIn(max = FDS.Sizer.ScreenBreakpoint.tabletMinWidth)
                    .padding(
                        start = FDS.Sizer.Padding.padding8,
                        end = FDS.Sizer.Padding.padding8,
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .verticalScroll(scrollState)
                        .dismissKeyboardOnClickOutside(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    FoundationAppBar(
                        isLightIcon = false,
                        title = stringResource(id = com.vietinbank.feature_login.R.string.login_title),
                        onNavigationClick = { onAction(NewUILoginAction.OnBackClick) },
                        modifier = Modifier.padding(top = FDS.Sizer.Padding.padding8),
                    )

                    if (isActive && fullName != null) {
                        ReturningUserLoginCard(
                            fullName = fullName,
                            password = password,
                            passwordVisible = passwordVisible,
                            passwordError = passwordError,
                            onAction = onAction,
                            focusManager = focusManager,
                            passwordFocusRequester = passwordFocusRequester,
                        )
                    } else {
                        NewUserLoginCard(
                            username = username,
                            password = password,
                            passwordVisible = passwordVisible,
                            showUsernameTooltip = showUsernameTooltip,
                            usernameError = usernameError,
                            passwordError = passwordError,
                            selectedCompany = selectedCompany,
                            onAction = onAction,
                            focusManager = focusManager,
                            usernameFocusRequester = usernameFocusRequester,
                            passwordFocusRequester = passwordFocusRequester,
                        )
                    }

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                    // Bottom action buttons with unlock user and forgot password
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(
                            FDS.Sizer.Gap.gap16,
                            Alignment.CenterHorizontally,
                        ),
                    ) {
                        ActionButton(
                            icon = com.vietinbank.feature_login.R.drawable.ic_login_unlock_user_24,
                            label = stringResource(com.vietinbank.feature_login.R.string.prelogin_unlock_user),
                            onClick = { onAction(NewUILoginAction.OnUnlockUserClick) },
                        )

//                        ActionButton(
//                            icon = com.vietinbank.feature_login.R.drawable.ic_login_unlock_user_24,
//                            label = stringResource(com.vietinbank.feature_login.R.string.prelogin_unlock_user),
//                            onClick = { onAction(NewUILoginAction.OnUnlockUserClick) },
//                        )

                        ActionButton(
                            icon = com.vietinbank.feature_login.R.drawable.ic_login_password_recovery_24,
                            label = stringResource(com.vietinbank.feature_login.R.string.login_restore_password),
                            onClick = { onAction(NewUILoginAction.OnForgotPasswordClick) },
                        )
                    }
                }

                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                FoundationButton(
                    isLightButton = true,
                    text = stringResource(id = com.vietinbank.feature_login.R.string.prelogin_login_action),
                    onClick = { onAction(NewUILoginAction.OnLoginClick) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = FDS.Sizer.Padding.padding24,
                            vertical = FDS.Sizer.Padding.padding16,
                        ),
                    size = ButtonSize.Large,
                    enabled = if (isActive) {
                        // For returning users: only need password to be non-empty
                        password.isNotEmpty()
                    } else {
                        // For new users: both username and password must be non-empty
                        username.isNotEmpty() && password.isNotEmpty()
                    },
                )
            }
        }

        CompanySelectionSheet(
            visible = showCompanySheet,
            companies = companiesList,
            selectedId = selectedCompany?.enterpriseid,
            onDismiss = { onAction(NewUILoginAction.OnDismissCompanyDialog) },
            onCompanySelected = { company ->
                onAction(NewUILoginAction.OnCompanySelect(company))
                onAction(NewUILoginAction.OnConfirmCompanySelection)
            },
        )
    }
}

@Composable
private fun ReturningUserLoginCard(
    fullName: String,
    password: String,
    passwordVisible: Boolean,
    passwordError: String? = null,
    onAction: (NewUILoginAction) -> Unit,
    focusManager: FocusManager,
    passwordFocusRequester: FocusRequester,
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Padding.padding16),
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.white,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = FDS.Effects.elevationSm,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        ) {
            // User info section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(top = FDS.Sizer.Padding.padding8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Avatar with initials
                Box(
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon48)
                        .background(
                            color = FDS.Colors.blue100,
                            shape = CircleShape,
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    val initials = fullName.getInitials()

                    FoundationText(
                        text = initials,
                        style = FDS.Typography.bodyB1Emphasized,
                        color = FDS.Colors.characterHighlighted,
                    )
                }

                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

                // Name and role
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.Center,
                ) {
                    // Apply marquee for long names (similar to pre-login screen)
                    FoundationText(
                        text = fullName,
                        style = FDS.Typography.bodyB2Emphasized,
                        color = FDS.Colors.characterHighlighted,
                        maxLines = 1,
                        overflow = androidx.compose.ui.text.style.TextOverflow.Clip,
                        softWrap = false, // Important for marquee
                        modifier = Modifier
                            .fillMaxWidth()
                            .basicMarquee(
                                iterations = Int.MAX_VALUE,
                                repeatDelayMillis = LoginScreenAnimationConstants.MARQUEE_REPEAT_DELAY,
                                initialDelayMillis = LoginScreenAnimationConstants.MARQUEE_INITIAL_DELAY,
                                velocity = LoginScreenAnimationConstants.MARQUEE_VELOCITY,
                            ),
                    )
                    FoundationText(
                        text = stringResource(id = com.vietinbank.feature_login.R.string.login_action),
                        style = FDS.Typography.interactionSmallButton,
                        color = FDS.Colors.characterSecondary,
                    )
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // Divider before switch/refresh icon
                    VerticalDivider(
                        modifier = Modifier
                            .height(FDS.Sizer.Icon.icon24)
                            .padding(
                                start = FDS.Sizer.Padding.padding8,
                                end = FDS.Sizer.Padding.padding8,
                            ),
                        thickness = FDS.Sizer.Stroke.stroke1,
                        color = FDS.Colors.strokeDivider,
                    )

                    // Switch/refresh icon
                    IconButton(
                        onClick = { onAction(NewUILoginAction.OnBackClick) },
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_common_swich_horizontal_24),
                            contentDescription = stringResource(id = com.vietinbank.feature_login.R.string.login_change_account),
                            tint = FDS.Colors.characterSecondary,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    }
                }
            }

            // Divider - FULL WIDTH without padding
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding16),
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            // Password field section with more spacing from divider
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                FoundationEditText(
                    value = password,
                    onValueChange = { onAction(NewUILoginAction.OnPasswordChange(it)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(passwordFocusRequester),
                    placeholder = stringResource(id = com.vietinbank.feature_login.R.string.login_password_hint),
                    inputType = InputType.PASSWORD,
                    maxLength = ValidationConstants.PASSWORD_MAX_LENGTH, // Use constant from ViewModel
                    isError = passwordError != null,
                    errorMessage = passwordError,
                    showErrorMessage = false, // Disable inline validation
                    showCharacterCounter = false,
                    keyboardActions = KeyboardActions(
                        onDone = { focusManager.clearFocus() },
                    ),
                )
            }
        }
    }
}

@Composable
private fun NewUserLoginCard(
    username: String,
    password: String,
    passwordVisible: Boolean,
    showUsernameTooltip: Boolean,
    usernameError: String? = null,
    passwordError: String? = null,
    selectedCompany: CifSharedDomain?,
    onAction: (NewUILoginAction) -> Unit,
    focusManager: FocusManager,
    usernameFocusRequester: FocusRequester,
    passwordFocusRequester: FocusRequester,
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Padding.padding16),
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.white,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = FDS.Effects.elevationSm,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding16),
        ) {
            // Username field with switch icon
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(top = FDS.Sizer.Padding.padding8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    modifier = Modifier.weight(1f),
                ) {
                    UsernameField(
                        username = username,
                        usernameError = usernameError,
                        onUsernameChange = { onAction(NewUILoginAction.OnUsernameChange(it)) },
                        onNext = { focusManager.moveFocus(FocusDirection.Down) },
                        focusRequester = usernameFocusRequester,
                    )
                }

                // username info icon
                IconButton(
                    onClick = { onAction(NewUILoginAction.OnUsernameTooltipToggle) },
                    modifier = Modifier.size(FDS.Sizer.Icon.icon32),
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.feature_login.R.drawable.ic_login_info_24),
                        tint = FDS.Colors.characterSecondary,
                        contentDescription = stringResource(id = com.vietinbank.feature_login.R.string.login_show_tooltip),
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    )
                }
            }

            // Username help text - expandable section
            AnimatedVisibility(
                visible = showUsernameTooltip,
                enter = expandVertically() + fadeIn(),
                exit = shrinkVertically() + fadeOut(),
            ) {
                FoundationText(
                    text = stringResource(id = com.vietinbank.core_ui.R.string.login_username_help),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterSecondary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding16)
                        .padding(vertical = FDS.Sizer.Padding.padding8),
                )
            }

            // Divider between fields - FULL WIDTH without padding
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding16),
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
            )

            // Password field with more spacing from divider
            PasswordField(
                password = password,
                passwordVisible = passwordVisible,
                passwordError = passwordError,
                onPasswordChange = { onAction(NewUILoginAction.OnPasswordChange(it)) },
                onPasswordVisibilityToggle = { onAction(NewUILoginAction.OnPasswordVisibilityToggle) },
                onDone = {
                    focusManager.clearFocus()
                    onAction(NewUILoginAction.OnLoginClick)
                },
                focusRequester = passwordFocusRequester,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            )
        }
    }
}

@Composable
private fun UsernameField(
    username: String,
    usernameError: String? = null,
    onUsernameChange: (String) -> Unit,
    onNext: () -> Unit,
    focusRequester: FocusRequester,
) {
    FoundationEditText(
        value = username,
        onValueChange = onUsernameChange,
        modifier = Modifier
            .fillMaxWidth()
            .focusRequester(focusRequester),
        placeholder = stringResource(id = com.vietinbank.feature_login.R.string.login_username_hint),
        inputType = InputType.TEXT,
        maxLength = ValidationConstants.USERNAME_MAX_LENGTH, // Set max length for username
        isError = usernameError != null,
        errorMessage = usernameError,
        showErrorMessage = false, // Disable inline validation
        showCharacterCounter = false,
        keyboardActions = KeyboardActions(
            onNext = { onNext() },
        ),
    )
}

@Composable
private fun PasswordField(
    modifier: Modifier = Modifier,
    password: String,
    passwordVisible: Boolean,
    passwordError: String? = null,
    onPasswordChange: (String) -> Unit,
    onPasswordVisibilityToggle: () -> Unit,
    onDone: () -> Unit,
    focusRequester: FocusRequester,
) {
    FoundationEditText(
        value = password,
        onValueChange = onPasswordChange,
        modifier = modifier
            .focusRequester(focusRequester),
        placeholder = stringResource(id = com.vietinbank.feature_login.R.string.login_password_hint),
        inputType = InputType.PASSWORD,
        maxLength = ValidationConstants.PASSWORD_MAX_LENGTH, // Use constant from ViewModel
        isError = passwordError != null,
        errorMessage = passwordError,
        showErrorMessage = false, // Disable inline validation
        showCharacterCounter = false,
        keyboardActions = KeyboardActions(
            onDone = { onDone() },
        ),
    )
}

/**
 * Single action button with icon and label
 * Reusable component for action buttons with circular icon
 */
@Composable
fun ActionButton(
    icon: Int,
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLightIcon: Boolean = false,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        CircularIconButton(
            isLightIcon = isLightIcon,
            icon = painterResource(id = icon),
            onClick = onClick,
            size = CircularIconButtonSize.Large,
        )
        FoundationText(
            text = label,
            style = FDS.Typography.interactionSmallButton,
            color = FDS.Colors.white,
            textAlign = TextAlign.Center,
        )
    }
}

/**
 * Actions for Login screen
 */
sealed class NewUILoginAction {
    object OnBackClick : NewUILoginAction()
    data class OnUsernameChange(val username: String) : NewUILoginAction()
    data class OnPasswordChange(val password: String) : NewUILoginAction()
    object OnPasswordVisibilityToggle : NewUILoginAction()
    object OnUsernameTooltipToggle : NewUILoginAction() // Keep for backward compatibility
    data class OnCompanySelect(val company: CifSharedDomain) : NewUILoginAction()
    object OnConfirmCompanySelection : NewUILoginAction()
    object OnDismissCompanyDialog : NewUILoginAction()
    object OnLoginClick : NewUILoginAction()
    object OnBiometricClick : NewUILoginAction()
    object OnForgotPasswordClick : NewUILoginAction()
    object OnUnlockUserClick : NewUILoginAction() // New action for unlock user
    object OnHotlineClick : NewUILoginAction()
    object OnGuideClick : NewUILoginAction()
}