package com.vietinbank.feature_login.ui.dialog.otp
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.withFrameNanos
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.foundation.pin.PinHiddenComponent
import com.vietinbank.core_ui.components.foundation.pin.PinShowComponent
import com.vietinbank.core_ui.components.foundation.pin.PinShowState
import com.vietinbank.core_ui.components.foundation.pin.PinState
import com.vietinbank.core_ui.components.foundation.pin.StealthOtpInputField
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import kotlinx.coroutines.delay
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Visual-only content for PIN Hidden Input step (no IME/input handling)
 * Used when input is hoisted to parent level
 *
 * @param step Current HiddenInput state
 * @param onToggleVisibility Callback to toggle PIN visibility
 * @param onContinue Callback when user taps continue button
 * @param onForgotPin Callback when user taps forgot PIN
 * @param onRowTap Callback when user taps the PIN row
 */
@Composable
fun HiddenStepVisual(
    step: OtpDialogStep.HiddenInput,
    onToggleVisibility: () -> Unit,
    onContinue: () -> Unit,
    onForgotPin: () -> Unit = {},
    onRowTap: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // Safe digits handling
    val safeDigits = step.digits ?: ""

    // Cache string resources from core-ui
    val enterPinText = stringResource(com.vietinbank.core_ui.R.string.enter_your_pin)
    val pinVerifiedText = stringResource(com.vietinbank.core_ui.R.string.pin_verified)
    val incorrectPinText = stringResource(com.vietinbank.core_ui.R.string.incorrect_pin)
    val attemptsRemainingText = stringResource(com.vietinbank.core_ui.R.string.attempts_remaining)
    val continueButtonText = stringResource(com.vietinbank.core_ui.R.string.continue_button)
    val verifyingText = stringResource(com.vietinbank.core_ui.R.string.verifying)
    val forgotPinText = stringResource(com.vietinbank.core_ui.R.string.forgot_pin)

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Title
        FoundationText(
            text = when (step.state) {
                PinState.INPUT -> enterPinText
                PinState.SUCCESS -> pinVerifiedText
                PinState.ERROR -> incorrectPinText
                PinState.EXPIRED -> stringResource(com.vietinbank.feature_login.R.string.expired_pin_state)
            },
            style = FDS.Typography.headingH3,
            color = when (step.state) {
                PinState.ERROR -> FDS.Colors.stateError
                else -> FDS.Colors.characterPrimary
            },
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        // Subtitle with attempts
        if (step.attemptsLeft != null && step.attemptsLeft < 3) {
            FoundationText(
                text = "$attemptsRemainingText${step.attemptsLeft}",
                style = FDS.Typography.bodyB2,
                color = if (step.attemptsLeft == 1) {
                    FDS.Colors.stateWarning
                } else {
                    FDS.Colors.characterSecondary
                },
                textAlign = TextAlign.Center,
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }

        // Visual PIN component only (no input field)
        PinHiddenComponent(
            state = step.state,
            showNumbers = step.showNumbers,
            digits = safeDigits,
            length = step.length,
            onToggleVisibility = onToggleVisibility,
            hideToggleIcon = true,
            onRowTap = onRowTap,
            modifier = Modifier.fillMaxWidth(),
        )

        // Eye icon for visibility toggle
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

        IconButton(
            onClick = onToggleVisibility,
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon24)
                .semantics {
                    contentDescription = if (step.showNumbers) {
                        "Ẩn mã PIN"
                    } else {
                        "Hiện mã PIN"
                    }
                },
        ) {
            Icon(
                painter = painterResource(
                    id = if (step.showNumbers) {
                        R.drawable.ic_dangnhap_eye_open
                    } else {
                        R.drawable.ic_dangnhap_eye_close
                    },
                ),
                contentDescription = null,
                tint = FDS.Colors.characterHighlighted,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }

        // Error message
        if (step.errorMessage != null) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            FoundationText(
                text = step.errorMessage,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.stateError,
                textAlign = TextAlign.Center,
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Continue button
        FoundationButton(
            text = if (step.isVerifying) {
                verifyingText
            } else {
                continueButtonText
            },
            onClick = onContinue,
            enabled = safeDigits.length == step.length && !step.isVerifying,
            modifier = Modifier.fillMaxWidth(),
            isLightButton = true,
        )

        // Loading indicator
        if (step.isVerifying) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                color = FDS.Colors.primary,
                strokeWidth = 2.dp,
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Forgot PIN link
        FoundationClickableText(
            text = forgotPinText,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterHighlighted,
            onClick = onForgotPin,
        )
    }
}

/**
 * Content for PIN Hidden Input step
 *
 * @param step Current HiddenInput state
 * @param onDigitChange Callback when digits change
 * @param onToggleVisibility Callback to toggle PIN visibility
 * @param onContinue Callback when user taps continue button
 */
@Composable
fun HiddenStepContent(
    step: OtpDialogStep.HiddenInput,
    onDigitChange: (String) -> Unit,
    onToggleVisibility: () -> Unit,
    onContinue: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusRequester = remember { FocusRequester() }

    // Guard input
    val safeDigits = remember(step.digits, step.length) {
        step.digits.take(step.length)
    }

    // Cache string resources to avoid repeated loading
    val enterPinText = stringResource(R.string.enter_your_pin)
    val pinVerifiedText = stringResource(R.string.pin_verified)
    val incorrectPinText = stringResource(R.string.incorrect_pin)
    val attemptsRemainingText = stringResource(R.string.attempts_remaining, 0).substringBefore("0")
    val verifyingText = stringResource(R.string.verifying)
    val continueButtonText = stringResource(R.string.continue_button)
    val forgotPinText = stringResource(R.string.forgot_pin)

    // NOTE: IME activation is handled at dialog level - do not duplicate here

    // Auto-continue with proper frame timing
    LaunchedEffect(safeDigits) {
        if (step.state != PinState.ERROR &&
            safeDigits.length == step.length &&
            !step.isVerifying
        ) {
            // Wait for UI to render the last digit
            withFrameNanos { /* wait one frame */ }
            delay(150) // Small delay so user sees the last digit
            onContinue()
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Title
        FoundationText(
            text = when (step.state) {
                PinState.INPUT -> enterPinText
                PinState.SUCCESS -> pinVerifiedText
                PinState.ERROR -> incorrectPinText
                PinState.EXPIRED -> stringResource(com.vietinbank.feature_login.R.string.expired_pin_state)
            },
            style = FDS.Typography.headingH3,
            color = when (step.state) {
                PinState.ERROR -> FDS.Colors.stateError
                else -> FDS.Colors.characterPrimary
            },
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        // Subtitle with attempts
        if (step.attemptsLeft != null && step.attemptsLeft < 3) {
            FoundationText(
                text = "$attemptsRemainingText${step.attemptsLeft}",
                style = FDS.Typography.bodyB2,
                color = if (step.attemptsLeft == 1) {
                    FDS.Colors.stateWarning
                } else {
                    FDS.Colors.characterSecondary
                },
                textAlign = TextAlign.Center,
            )

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }

        // PIN Component with stealth input overlay
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center,
        ) {
            // Visual PIN component (displays the PIN boxes)
            PinHiddenComponent(
                state = step.state,
                showNumbers = step.showNumbers,
                digits = safeDigits,
                length = step.length,
                onToggleVisibility = onToggleVisibility,
                hideToggleIcon = true, // Hide icon inside component
                onRowTap = {
                    // Only request focus, don't show keyboard
                    // Compose will maintain IME visibility if input is focused
                    focusRequester.requestFocus()
                },
                modifier = Modifier.fillMaxWidth(),
            )

            // Stealth input field overlay
            StealthOtpInputField(
                value = safeDigits,
                length = step.length,
                onValueChange = onDigitChange,
                focusRequester = focusRequester,
                enabled = !step.isVerifying && step.state != PinState.SUCCESS,
                onDone = {
                    if (safeDigits.length == step.length) {
                        onContinue()
                    }
                },
            )
        }

        // Eye icon for visibility toggle (outside the Box)
        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

        IconButton(
            onClick = onToggleVisibility,
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon24)
                .semantics {
                    contentDescription = if (step.showNumbers) {
                        "Ẩn mã PIN"
                    } else {
                        "Hiện mã PIN"
                    }
                },
        ) {
            Icon(
                painter = painterResource(
                    id = if (step.showNumbers) {
                        R.drawable.ic_dangnhap_eye_open
                    } else {
                        R.drawable.ic_dangnhap_eye_close
                    },
                ),
                contentDescription = null,
                tint = FDS.Colors.characterHighlighted,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }

        // Error message
        if (step.errorMessage != null) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            FoundationText(
                text = step.errorMessage,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.stateError,
                textAlign = TextAlign.Center,
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Continue button
        FoundationButton(
            text = if (step.isVerifying) {
                verifyingText
            } else {
                continueButtonText
            },
            onClick = onContinue,
            enabled = safeDigits.length == step.length && !step.isVerifying,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // Forgot PIN link
        FoundationClickableText(
            text = forgotPinText,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.primary,
            onClick = {
                // Handle forgot PIN
            },
        )
    }
}

/**
 * Visual-only content for OTP Show step (no IME/input handling)
 * Used when input is hoisted to parent level
 *
 * @param step Current Show state
 * @param onResend Callback to resend OTP
 * @param onRowTap Callback when user taps the PIN row
 */
@Composable
fun ShowStepVisual(
    step: OtpDialogStep.Show,
    onResend: () -> Unit,
    onRowTap: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // Calculate current OTP string from pinValues
    val otpString = remember(step.pinValues) {
        step.pinValues.filter { it != "_" }.joinToString("")
    }

    // Cache string resources from core-ui
    val enterOtpText = stringResource(com.vietinbank.core_ui.R.string.enter_otp_code)
    val verifyingOtpText = stringResource(com.vietinbank.core_ui.R.string.verifying_otp)
    val otpCanResendText = stringResource(com.vietinbank.core_ui.R.string.otp_can_resend)
    val otpExpiredText = stringResource(com.vietinbank.core_ui.R.string.otp_expired)
    val otpValidForText = stringResource(com.vietinbank.core_ui.R.string.otp_valid_for, "").substringBefore(":")
    val otpResendAvailableText = stringResource(com.vietinbank.core_ui.R.string.otp_resend_available)
    val otpExpiredMessageText = stringResource(com.vietinbank.core_ui.R.string.otp_expired_message)
    val resendOtpInText = stringResource(com.vietinbank.core_ui.R.string.resend_otp_in, 0).substringBefore("0")
    val sendingText = stringResource(com.vietinbank.core_ui.R.string.sending)
    val resendOtpText = stringResource(com.vietinbank.core_ui.R.string.resend_otp)

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Title
        FoundationText(
            text = when (step.state) {
                PinShowState.TYPING -> enterOtpText
                PinShowState.TYPED -> verifyingOtpText
                PinShowState.RESEND -> otpCanResendText
                PinShowState.EXPIRED -> otpExpiredText
                PinShowState.VERIFYING -> verifyingOtpText
            },
            style = FDS.Typography.headingH3,
            color = when (step.state) {
                PinShowState.EXPIRED -> FDS.Colors.stateWarning
                else -> FDS.Colors.characterPrimary
            },
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        // Timer or status message
        when (step.state) {
            PinShowState.TYPING, PinShowState.TYPED, PinShowState.VERIFYING -> {
                FoundationText(
                    text = "$otpValidForText: ${step.currentValidityRemaining.toTimeString()}",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )
            }
            PinShowState.RESEND -> {
                FoundationText(
                    text = otpResendAvailableText,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )
            }
            PinShowState.EXPIRED -> {
                FoundationText(
                    text = otpExpiredMessageText,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.stateWarning,
                    textAlign = TextAlign.Center,
                )
            }
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Visual PIN component only (no input field)
        PinShowComponent(
            pinValues = step.pinValues,
            state = step.state,
            onRowTap = onRowTap,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Resend section
        when {
            step.state == PinShowState.EXPIRED ||
                (step.state == PinShowState.RESEND && step.currentResendRemaining == 0) -> {
                FoundationButton(
                    text = if (step.isResending) sendingText else resendOtpText,
                    onClick = onResend,
                    enabled = !step.isResending,
                    modifier = Modifier.fillMaxWidth(),
                    isLightButton = false,
                )
            }
            step.currentResendRemaining > 0 -> {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = "$resendOtpInText${step.currentResendRemaining.toTimeString()}",
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }
        }

        // Loading indicator
        if (step.state == PinShowState.TYPED) {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                color = FDS.Colors.primary,
                strokeWidth = 2.dp,
            )
        }
    }
}

/**
 * Content for OTP Show step
 *
 * @param step Current Show state
 * @param onResend Callback to resend OTP
 */
@Composable
fun ShowStepContent(
    step: OtpDialogStep.Show,
    onResend: () -> Unit,
    onDigitChange: (String) -> Unit,
    onVerify: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusRequester = remember { FocusRequester() }

    // Calculate current OTP string from pinValues
    val otpString = remember(step.pinValues) {
        step.pinValues.filter { it != "_" }.joinToString("")
    }

    // Cache string resources
    val enterOtpText = stringResource(R.string.enter_otp_code)
    val verifyingOtpText = stringResource(R.string.verifying_otp)
    val otpCanResendText = stringResource(R.string.otp_can_resend)
    val otpExpiredText = stringResource(R.string.otp_expired)
    val otpValidForText = stringResource(R.string.otp_valid_for, "").substringBefore(":")
    val otpResendAvailableText = stringResource(R.string.otp_resend_available)
    val otpExpiredMessageText = stringResource(R.string.otp_expired_message)
    val resendOtpInText = stringResource(R.string.resend_otp_in, 0).substringBefore("0")
    val sendingText = stringResource(R.string.sending)
    val resendOtpText = stringResource(R.string.resend_otp)

    // NOTE: IME activation is handled at dialog level - do not duplicate here

    // Auto-verify with proper frame timing
    LaunchedEffect(otpString) {
        if (otpString.length == step.pinValues.size &&
            step.state == PinShowState.TYPING
        ) {
            // Wait for UI to render the last digit
            withFrameNanos { /* wait one frame */ }
            delay(150) // Small delay so user sees the last digit
            onVerify()
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Title
        FoundationText(
            text = when (step.state) {
                PinShowState.TYPING -> enterOtpText
                PinShowState.TYPED -> verifyingOtpText
                PinShowState.RESEND -> otpCanResendText
                PinShowState.EXPIRED -> otpExpiredText
                PinShowState.VERIFYING -> verifyingOtpText
            },
            style = FDS.Typography.headingH3,
            color = when (step.state) {
                PinShowState.EXPIRED -> FDS.Colors.stateWarning
                else -> FDS.Colors.characterPrimary
            },
            textAlign = TextAlign.Center,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

        // Timer or status message
        when (step.state) {
            PinShowState.TYPING, PinShowState.TYPED, PinShowState.VERIFYING -> {
                FoundationText(
                    text = "$otpValidForText: ${step.currentValidityRemaining.toTimeString()}",
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )
            }
            PinShowState.RESEND -> {
                FoundationText(
                    text = otpResendAvailableText,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )
            }
            PinShowState.EXPIRED -> {
                FoundationText(
                    text = otpExpiredMessageText,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.stateWarning,
                    textAlign = TextAlign.Center,
                )
            }
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        // OTP Component with stealth input overlay
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp), // Give explicit height for the input area
            contentAlignment = Alignment.Center,
        ) {
            // Visual OTP component (displays the OTP boxes)
            PinShowComponent(
                state = step.state,
                pinValues = step.pinValues,
                onRowTap = {
                    // Only request focus, don't show keyboard
                    // Compose will maintain IME visibility if input is focused
                    focusRequester.requestFocus()
                },
                modifier = Modifier.fillMaxWidth(),
            )

            // Stealth input field overlay
            StealthOtpInputField(
                value = otpString,
                length = step.pinValues.size,
                onValueChange = onDigitChange,
                focusRequester = focusRequester,
                enabled = step.state != PinShowState.EXPIRED && !step.isResending,
                onDone = {
                    if (otpString.length == step.pinValues.size) {
                        onVerify()
                    }
                },
            )
        }

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

        // Action buttons
        when (step.state) {
            PinShowState.TYPING -> {
                // Resend countdown
                if (step.currentResendRemaining > 0) {
                    FoundationText(
                        text = "$resendOtpInText${step.currentResendRemaining}",
                        style = FDS.Typography.captionCaptionL,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }
            PinShowState.TYPED, PinShowState.VERIFYING -> {
                // Show verifying indicator
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    CircularProgressIndicator(
                        color = FDS.Colors.primary,
                        modifier = Modifier.padding(FDS.Sizer.Padding.padding8),
                    )
                    FoundationText(
                        text = verifyingOtpText,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterSecondary,
                    )
                }
            }
            PinShowState.RESEND, PinShowState.EXPIRED -> {
                // Resend button
                FoundationButton(
                    text = if (step.isResending) {
                        sendingText
                    } else {
                        resendOtpText
                    },
                    onClick = onResend,
                    enabled = !step.isResending,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

/**
 * Timer component for countdown display
 */
@Composable
fun OtpTimerDisplay(
    seconds: Int,
    label: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        FoundationText(
            text = label,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterSecondary,
        )

        FoundationText(
            text = seconds.toTimeString(),
            style = FDS.Typography.bodyB1,
            color = if (seconds < 30) {
                FDS.Colors.stateWarning
            } else {
                FDS.Colors.characterPrimary
            },
            modifier = Modifier.padding(start = FDS.Sizer.Padding.padding4),
        )
    }
}