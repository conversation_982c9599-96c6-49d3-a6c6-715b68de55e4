package com.vietinbank.feature_login.ui.fragment

// Dialog imports no longer needed - using CompanySelectionSheet in Compose
// CompanySelectionDialog and CompanySelectionResult are replaced by the Sheet
// import com.vietinbank.feature_login.ui.dialog.CompanySelectionDialog
// import com.vietinbank.feature_login.ui.dialog.CompanySelectionResult
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.VerifyPasswordDialog
import com.vietinbank.core_ui.components.dialog.DialogType
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.dialog.Active2FAStep1Result
import com.vietinbank.feature_login.ui.dialog.Active2FAStep2Result
import com.vietinbank.feature_login.ui.dialog.NewUIActive2FAStep1Dialog
import com.vietinbank.feature_login.ui.dialog.NewUIActive2FAStep2Dialog
import com.vietinbank.feature_login.ui.events.LoginEvent
import com.vietinbank.feature_login.ui.screen.newui.NewUILoginAction
import com.vietinbank.feature_login.ui.screen.newui.NewUILoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

// Bundle key constants for fragment arguments
private object BundleKeys {
    const val SESSION_EXPIRED_FLAG = "session_expired_flag"
}

// Animation timing constants
private object AnimationConstants {
    const val SESSION_EXPIRED_DIALOG_DELAY = 450L // milliseconds (wait for initial focus)
}

/**
 * NewUI Login Fragment - Handles all login business logic for NewUI
 * This fragment can display either Welcome screen (not active) or Login screen (active)
 */
@AndroidEntryPoint
class NewUILoginFragment : BaseFragment<LoginViewModel>() {

    // region Properties
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    // Permission launcher - declared once as property
    private val notificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        printLog(
            if (isGranted) {
                getString(R.string.login_notification_permission_granted)
            } else {
                getString(R.string.login_notification_permission_denied)
            },
        )
    }
    // endregion

    // region Lifecycle
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestNotificationPermissionIfNeeded()
        setupEventCollector() // NEW: Collect Channel events
        setupAllObservers() // Keep for backward compatibility

        // Check if this is for new account (clear form)
        val shouldClearForm = arguments?.getBoolean("clear_form", false) ?: false

        if (!shouldClearForm) {
            // Normal flow: auto-fill from saved users
            viewModel.updateLoginStatus()

            // Handle selected user from bundle if exists
            handleSelectedUserFromBundle()

            viewModel.forceUpdate()
        } else {
            // New account flow: clear form for fresh login
            viewModel.clearUsername()
            viewModel.updatePassword("")

            // KHÔNG gọi handleSelectedUserFromBundle() và forceUpdate()
            // để tránh auto-fill lại từ saved users
        }

        // Check if navigated here due to session expiry
        checkAndShowSessionExpiredDialog()
    }

    override fun onPause() {
        super.onPause()
        // Note: We should NOT clear selected user info here because:
        // 1. onPause can be called temporarily when dialogs/keyboards show
        // 2. This causes loss of isShowingSelectedUser flag during normal interaction
        // 3. The state should only be cleared when login succeeds or user explicitly navigates back

        // The clearSelectedUserInfo() is already called in:
        // - LoginResult event handler (after successful login)
        // - When user selects "Add new account" in PreLoginFragment
    }
    // endregion

    // region Selected User Handling
    private fun handleSelectedUserFromBundle() {
        arguments?.let { bundle ->
            val selectedUsername = bundle.getString("selected_username")
            val selectedFullname = bundle.getString("selected_fullname")
            val selectedCorpname = bundle.getString("selected_corpname")

            if (!selectedUsername.isNullOrEmpty()) {
                // Update ViewModel with selected user info
                viewModel.updateUsername(selectedUsername)
                // Set selected user info directly in UI state
                viewModel.setSelectedUserInfo(
                    username = selectedUsername,
                    fullName = selectedFullname,
                    corpName = selectedCorpname,
                )

                // Clear arguments to prevent re-processing
                arguments?.clear()
            }
        }
    }
    // endregion

    // region Compose UI
    @Composable
    override fun ComposeScreen() {
        AppTheme {
            LoginScreenContent()
        }
    }

    @Composable
    private fun LoginScreenContent() {
        // Single source of truth - unified state
        val uiState by viewModel.uiState.collectAsState()

        // Note: Company dialog is now handled via Channel events in handleLoginEvent()
        // This prevents the dialog from auto-showing on back navigation

        // Determine which full name to display
        // If we're showing a selected user (from switch dialog), use that info
        // Otherwise use the current logged-in user info
        val displayFullName = if (uiState.isShowingSelectedUser) {
            uiState.selectedUserFullName ?: uiState.fullName
        } else {
            uiState.fullName
        }

        NewUILoginScreen(
            isActive = uiState.isActive,
            username = uiState.username,
            password = uiState.password,
            passwordVisible = uiState.passwordVisible,
            showUsernameTooltip = uiState.showUsernameTooltip,
            selectedCompany = uiState.selectedCompany,
            fullName = displayFullName,
            usernameError = uiState.usernameError,
            passwordError = uiState.passwordError,
            showCompanyDialog = false, // Dialog is shown via one-time events
            dialogState = uiState.dialog,
            passwordFocusTrigger = uiState.passwordFocusTrigger,
            onAction = { action ->
                handleScreenAction(action)
            },
            onDismissDialog = {
                viewModel.dismissDialog()
            },
        )
    }

    /**
     * Show Company Selection Dialog using the new BaseDialog architecture
     * Maps CifSharedDomain to Company and handles result
     */
    // No longer needed - CompanySelectionSheet is shown directly in Compose
    // The dialog state is managed in LoginViewModel and rendered in NewUILoginScreen
    /*
    private fun showCompanySelectionDialog(
        companies: List<CifSharedDomain>,
        selectedId: String?,
    ) {
        // Map CifSharedDomain to Company for dialog
        val companyList = companies.map { cif ->
            Company(
                id = cif.enterpriseid ?: "",
                name = cif.enterprisename ?: "",
                taxCode = cif.enterpriseid ?: "", // Use enterpriseid as tax code display
            )
        }

        // Show the dialog
        CompanySelectionDialog.show(
            fragmentManager = childFragmentManager,
            companies = companyList,
            selectedId = selectedId,
        )
    }
     */

    // No longer needed - CompanySelectionSheet handles everything in Compose
    // Results are handled via direct callbacks in NewUILoginScreen
    /*
    // Setup persistent listener for company selection dialog results
    private fun setupCompanySelectionListener() {
        childFragmentManager.setFragmentResultListener(
            "company_selection_result",
            viewLifecycleOwner,
        ) { requestKey, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<CompanySelectionResult>("key_result")

                result?.let { selectionResult ->
                    // Get companies from ViewModel using proper encapsulation
                    val companies = viewModel.getCompaniesForSelection()

                    // Find the selected CifSharedDomain from result
                    val selectedCif = companies?.find { it.enterpriseid == selectionResult.companyId }

                    selectedCif?.let {
                        viewModel.selectCompany(it)
                        // Dialog auto-dismisses on result
                        // Now call login with the selected company
                        val username = viewModel.username.value.trim()
                        val password = viewModel.password.value

                        // Call login based on user status
                        if (viewModel.isActive()) {
                            viewModel.login(viewModel.getUserName().toString(), password)
                        } else {
                            viewModel.login(username, password)
                        }
                    }
                }
            } else {
                // User dismissed dialog without selecting
                // Clear multi-cif to allow re-triggering
                viewModel.clearMultiCifState()
            }
        }
    }
     */
    // endregion

    // region Event Collector (NEW - Channel-based one-time events)
    private fun setupEventCollector() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.events.collect { event ->
                    handleLoginEvent(event)
                }
            }
        }
    }

    private fun handleLoginEvent(event: LoginEvent) {
        when (event) {
            is LoginEvent.NavigateTo2FA -> {
                showActive2FAStep1Dialog(Tags.SMS_OTP)
            }
            is LoginEvent.GenOTPResult -> {
                Toast.makeText(requireContext(), event.data.status?.message.toString(), Toast.LENGTH_LONG).show()

                val validitySecs = Tags.TIME_DIALOG_ACTIVE_STEP2_VALIDITY
                val resendSecs = Tags.TIME_DIALOG_ACTIVE_STEP2_RESEND

                // LƯU efastId để Step1 → Step2 về sau có thể resume KHÔNG gọi API
                viewModel.updateLastOtpInfo(event.data.efastId?.toString())

                // để startOtpTimers và notifyOtpStarted
                viewModel.onOtpGenerated(validitySecs, resendSecs)

                if (event.typePopup == 1) {
                    showActive2FAStep2Dialog(viewModel.typeOTP2FA.value, event.data.efastId.toString())
                }
            }
            is LoginEvent.VerifyOTPResult -> {
                moveWhenLoginSuccess(event.data.loginResponse)
                viewModel.stopOtpTicksAndClear()
            }
            is LoginEvent.NavigateToActive -> {
                viewModel.saveSession(sessionId = event.sessionId)
                appNavigator.goToActive()
            }
            is LoginEvent.NavigateToHome -> {
                navigateToHome()
            }
            is LoginEvent.NavigateToLockAccount -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, event.dataType)
                    },
                )
            }
            is LoginEvent.ShowErrorDialog -> {
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowNoticeDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = event.positiveAction ?: {},
                    positiveButtonText = event.positiveButtonText ?: getString(R.string.dialog_button_confirm),
                )
            }
            is LoginEvent.ShowConfirmDialog -> {
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = event.positiveButtonText,
                    negativeButtonText = event.negativeButtonText,
                    positiveAction = event.positiveAction,
                    negativeAction = event.negativeAction ?: {},
                    dismissAction = {},
                )
            }
            is LoginEvent.ShowPasswordExpiryWarning -> {
                handlePasswordExpiryWarning(event.data)
            }
            is LoginEvent.ShowPasswordExpiryNew -> {
                handlePasswordExpiryNew(event.data)
            }
            is LoginEvent.ShowPasswordExpiredDialog -> {
                handlePasswordExpired(event.data)
            }
            is LoginEvent.ShowPasswordExpired881Dialog -> {
                handlePasswordExpired881(event.data)
            }
            is LoginEvent.ShowFirstLoginDialog -> {
                handleFirstLoginResponse(event.data)
            }
            is LoginEvent.ShowForgotPasswordDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = {},
                    positiveButtonText = getString(R.string.login_forgot_password_button),
                )
            }
            is LoginEvent.ShowBiometricNotAvailable -> {
                // Backward compatibility - generic message
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowBiometricOsNotSupported -> {
                // OS does not support biometric (Android < 6.0)
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowBiometricDeviceNotEnrolled -> {
                // Device has no fingerprint/PIN/pattern enrolled
                // Show dialog with option to go to Settings
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = getString(R.string.button_settings),
                    negativeButtonText = getString(R.string.dialog_button_close),
                    positiveAction = {
                        // Open Security Settings
                        try {
                            val intent = Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog(
                                message = getString(R.string.error_cannot_open_settings),
                                type = DialogType.ERROR,
                            )
                        }
                    },
                    negativeAction = {},
                )
            }
            is LoginEvent.ShowVerifyPasswordForBiometric -> {
                showVerifyPasswordDialog()
            }
            is LoginEvent.ShowBiometricPrompt -> {
                showFingerPrintPopup()
            }
            is LoginEvent.ShowForceUpdateDialog -> {
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowUpdatableDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.HandleForceUpdate -> {
                viewModel.handleForceUpdateOrNavigate(event.functionId) {}
            }
            is LoginEvent.ShowCompanySelectionDialog -> {
                // Now handled directly in Compose via DialogState.CompanySelection
                // The CompanySelectionSheet is shown when dialogState changes
                // No Fragment-level handling needed anymore
            }
            is LoginEvent.LoginSuccessWithSetup -> {
                // DEPRECATED - Using LoginResult instead to avoid duplicate processing
                // This event is kept for backward compatibility but should not be used
            }
            is LoginEvent.LoginResult -> {
                // Handle login result - save data and navigate
                val data = event.data

                // Clear selected user info now that login is successful
                viewModel.clearSelectedUserInfo()

                // Save session
                viewModel.saveSession(sessionId = data.sessionId.toString())

                // Save user profile
                saveProfileAfterLoginSuccess(data)

                // Set timer config
                viewModel.setTimerConfig(true)

                // Update fingerID if present
                if (!TextUtils.isEmpty(data.addField3)) {
                    viewModel.updateFingerID(data.addField3!!)
                }

                // Navigate to home
                navigateToHome()

                // Enable eKYC check and update status
                viewModel.setEnableCheckEkyc()
                viewModel.updateLoginStatus()
            }
            is LoginEvent.ChangePasswordResult -> {
                // Handle change password result
                showNoticeDialog(
                    message = event.data.status.message ?: "Đổi mật khẩu thành công",
                    positiveAction = { /* Navigate to login or appropriate screen */ },
                )
            }
            is LoginEvent.ForceUpdateResult -> {
                // Handle force update result
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
            }
        }
    }
    // endregion

    // region Observers Setup
    private fun setupAllObservers() {
        setupUpdateObservers()
        setupErrorHandling() // Add error handling observers
    }

    private fun setupUpdateObservers() {
        // Force update handling - Migrated to Channel events
        // Now handled in handleLoginEvent() -> LoginEvent.ForceUpdateResult
        /*
        viewModel.forceUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
                }
                is Resource.Error -> {
                    // Error handled by BaseViewModel
                }
            }
        }
         */

        // Update dialogs now handled via Channel events
    }

    /**
     * Setup error handling observers for validation and biometric errors
     */
    private fun setupErrorHandling() {
        // All error handling migrated to Channel events
        // See handleLoginEvent() for:
        // - LoginEvent.ShowErrorDialog (validation errors)
        // - LoginEvent.ShowBiometricNotAvailable (biometric errors)
        /*
        viewModel.biometricNotAvailable.observe(viewLifecycleOwner) { message ->
            showNoticeDialog(message)
        }
         */
    }
    // endregion

    // region User Actions
    private fun handleScreenAction(action: NewUILoginAction) {
        when (action) {
            is NewUILoginAction.OnBackClick -> {
                // Clear selected user info when user explicitly navigates back
                viewModel.clearSelectedUserInfo()
                appNavigator.navigateUp()
            }
            is NewUILoginAction.OnUsernameChange -> {
                viewModel.updateUsername(action.username)
                viewModel.currentUsername.value = action.username
            }
            is NewUILoginAction.OnPasswordChange -> {
                viewModel.updatePassword(action.password)
            }
            is NewUILoginAction.OnPasswordVisibilityToggle -> {
                viewModel.togglePasswordVisibility()
            }
            is NewUILoginAction.OnUsernameTooltipToggle -> {
                viewModel.toggleUsernameTooltip()
            }
            is NewUILoginAction.OnCompanySelect -> {
                viewModel.selectCompany(action.company)
            }
            is NewUILoginAction.OnConfirmCompanySelection -> {
                // Close dialog and perform login with selected company
                val username = viewModel.username.value
                val password = viewModel.password.value
                val uiState = viewModel.uiState.value

                // IMPORTANT: When showing selected user (from switch dialog), use the selected username
                // not the current logged-in user's username from userProf
                if (uiState.isShowingSelectedUser) {
                    // User selected from switch dialog - use the selected username
                    viewModel.login(username.trim(), password)
                } else if (viewModel.isActive()) {
                    // Normal active user login - use current user's username from userProf
                    viewModel.login(viewModel.getUserName().toString(), password)
                } else {
                    // New user login - use username from input
                    viewModel.login(username.trim(), password)
                }
            }
            is NewUILoginAction.OnDismissCompanyDialog -> {
                // Back - Clear selection and dismiss
                viewModel.clearCompanySelection()
            }
            is NewUILoginAction.OnLoginClick -> {
                viewModel.onLoginClicked()
            }
            is NewUILoginAction.OnBiometricClick -> {
                handleBiometricLogin()
            }
            is NewUILoginAction.OnForgotPasswordClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUILoginAction.OnUnlockUserClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_UNLOCK_USER)
                    },
                )
            }
            is NewUILoginAction.OnHotlineClick -> {
                showNoticeDialog(getString(com.vietinbank.feature_login.R.string.login_hotline), type = DialogType.INFO)
            }
            is NewUILoginAction.OnGuideClick -> {
                showNoticeDialog(getString(R.string.feature_guide_under_construction), type = DialogType.INFO)
            }
        }
    }
    // endregion

    // region Biometric Handling
    private fun handleBiometricLogin() {
        if (viewModel.isActive()) {
            if (viewModel.enableTouchID()) {
                // Call checkBiometricForLogin which will send appropriate event
                // The event handler will then call showFingerPrintPopup() if ready
                viewModel.checkBiometricForLogin()
            } else {
                showVerifyPasswordDialog()
            }
        } else {
            showNoticeDialog(getString(R.string.login_biometric_activation_message), type = DialogType.INFO)
        }
    }

    private fun showVerifyPasswordDialog() {
        val dialog = VerifyPasswordDialog.newInstance(
            title = getString(R.string.login_verify_password_title),
            message = getString(R.string.login_verify_password_message),
        )
        dialog.setOnConfirmListener { password ->
            viewModel.login(
                username = viewModel.getUserName().toString(),
                password = password,
                regTouchId = "1",
            )
        }
        dialog.setOnDismissClickListener {
            // user dismissed
        }
        dialog.show(childFragmentManager, "VerifyPasswordDialog")
    }

    private fun showFingerPrintPopup() {
        // Don't call checkBiometricForLogin() here - it creates infinite loop!
        // This function is called FROM the event handler after check is done
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = getString(R.string.login_biometric_title),
            subtitle = getString(R.string.login_biometric_subtitle),
            onSuccess = {
                printLog("verify success")
                viewModel.login(
                    username = viewModel.getUserName().toString(),
                    password = "",
                    isActiveFinger = true,
                )
            },
            onError = { errorMsg ->
                showNoticeDialog(
                    message = errorMsg,
                    type = DialogType.ERROR,
                )
            },
        )
    }
    // endregion

    // region Helper Methods
    private fun handleFirstLoginResponse(data: LoginDomain) {
        printLog("data.status?.code: ${data.status?.code}")
        if (!TextUtils.isEmpty(data.corpUser?.status) && Tags.FIRST_LOGIN_SIGNAL == data.corpUser?.status) {
            if (viewModel.isActive()) {
                clearAllData()
            }
            showConfirmDialog(
                message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
                positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
                negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
                positiveAction = {
                    viewModel.saveSession(sessionId = data.sessionId.toString())
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
                negativeAction = {},
                dismissAction = {},
            )
        } else {
            showNoticeDialog(data.status?.message.toString(), type = DialogType.ERROR)
        }
    }

    private fun handlePasswordExpiryWarning(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {
                // Nếu sắp hết hạn thì khi ấn bỏ qua sẽ điều hướng vào Home. Làm như case LoginResult
                // Save session
                viewModel.saveSession(sessionId = data.sessionId.toString())

                // Save user profile
                saveProfileAfterLoginSuccess(data)

                // Set timer config
                viewModel.setTimerConfig(true)

                // Update fingerID if present
                if (!TextUtils.isEmpty(data.addField3)) {
                    viewModel.updateFingerID(data.addField3!!)
                }

                // Navigate to home
                navigateToHome()

                // Enable eKYC check and update status
                viewModel.setEnableCheckEkyc()
                viewModel.updateLoginStatus()
            },
            dismissAction = {},
        )
    }

    private fun handlePasswordExpiryNew(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showNoticeDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            type = DialogType.INFO,
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            cancelable = false,
        )
    }

    private fun handlePasswordExpired(data: LoginDomain) {
        // Generic password expired - navigate to Active screen to change password
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired),
            type = DialogType.WARNING,
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun handlePasswordExpired881(data: LoginDomain) {
        // Password expired with code 881 - specific handling
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired_881),
            type = DialogType.WARNING,
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val granted = ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.POST_NOTIFICATIONS,
            ) == PackageManager.PERMISSION_GRANTED

            if (!granted) {
                notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    private fun clearAllData() {
        viewModel.clearSession()
        viewModel.clearProfile()
        viewModel.clearAllConfig()
        viewModel.transferCacheManager.clearTransferALL()
        viewModel.ottRegistrationRepository.clearAllOttCache()
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
                status = data.corpUser?.status.toString(),
            ),
        )

        viewModel.setTimerConfig(true)
    }

    // region Session Expired Handling
    /**
     * Check if navigated here due to session expiry and show dialog
     * Security-first approach: Navigate immediately, then show dialog
     */
    private fun checkAndShowSessionExpiredDialog() {
        // Check if Bundle contains session expired flag
        val hasSessionExpired = arguments?.getBoolean(BundleKeys.SESSION_EXPIRED_FLAG, false) ?: false

        if (hasSessionExpired) {
            // Clear the flag to prevent showing dialog on rotation
            arguments?.remove(BundleKeys.SESSION_EXPIRED_FLAG)

            // Get message from string resource (auto handles locale)
            val sessionExpiredMessage = getString(R.string.server_expired_session)

            // Delay dialog to let initial focus complete first (avoid conflict)
            lifecycleScope.launch {
                // Wait for initial focus animation to complete
                delay(AnimationConstants.SESSION_EXPIRED_DIALOG_DELAY)

                // Show dialog with the session expired message
                // Dialog is shown AFTER navigation to Login, ensuring security
                showNoticeDialog(
                    message = sessionExpiredMessage,
                    positiveButtonText = getString(R.string.dialog_button_relogin),
                    cancelable = false,
                    positiveAction = {
                        // Request focus to password field when dialog is dismissed
                        viewModel.requestPasswordFocus()
                    },
                )
            }
        }
    }
    // endregion

    private fun navigateToHome() {
        if (viewModel.isNonFinancial()) {
            appNavigator.goToAccountFromLogin()
        } else {
            appNavigator.goToHome()
        }
    }

    private fun showActive2FAStep1Dialog(typeOTP2FA: String) {
        NewUIActive2FAStep1Dialog.show(childFragmentManager, typeOTP2FA)

        // Listen for dialog result using Fragment Result API
        childFragmentManager.setFragmentResultListener(
            "newui_active2fa_step1_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<Active2FAStep1Result>("key_result")
                result?.let {
                    // Khi user ấn Tiếp tục ở Step1
                    val resume = viewModel.getOtpTimerResume()
                    val lastEfastId = viewModel.getLastEfastId()

                    if (resume.resendLeft > 0 && lastEfastId != null) {
                        // CÒN HIỆU LỰC → chỉ mở lại Step2 và đảm bảo tick đang chạy
                        showActive2FAStep2Dialog(result.typeOTP2FA, lastEfastId)
                        viewModel.startOtpTicks() // ĐỌC prefs → đẩy phần còn lại vào otpUi
                    } else {
                        // HẾT HẠN hoặc chưa từng sinh → gen mới
                        viewModel.genOTP(result.typeOTP2FA, 1)
                    }
                }
            }
        }
    }

    private fun showActive2FAStep2Dialog(
        typeOTP2FA: String,
        efastId: String,
    ) {
        // 1) ĐĂNG KÝ LISTENER TRƯỚC KHI SHOW (tránh miss event)
        childFragmentManager.setFragmentResultListener(
            "newui_active2fa_step2_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (isCancelled) return@setFragmentResultListener

            val result = bundle.getParcelable<Active2FAStep2Result>("key_result")
                ?: return@setFragmentResultListener

            when (result.action) {
                Active2FAStep2Result.Action.BACK -> {
                    showActive2FAStep1Dialog(typeOTP2FA)
                }
                Active2FAStep2Result.Action.RESEND -> {
                    viewModel.genOTP(typeOTP2FA, 0)
                }
                Active2FAStep2Result.Action.CONTINUE -> {
                    viewModel.verifyOTP(result.otpInput.orEmpty(), efastId)
                }
            }
        }

        // 2) SHOW DIALOG (không gen OTP ở đây)
        NewUIActive2FAStep2Dialog.show(
            childFragmentManager,
            viewModel.phoneNo.value,
            viewModel.emailNo.value,
        )
        // Đảm bảo fragment đã được thêm trước khi notify xuống
        childFragmentManager.executePendingTransactions()

        // đảm bảo có tick khi vừa mở lại
        viewModel.startOtpTicks()
    }

    private fun moveWhenLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        saveProfileAfterLoginSuccess(data)
        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        navigateToHome()
    }
}