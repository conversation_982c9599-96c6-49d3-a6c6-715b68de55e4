package com.vietinbank.feature_login.ui.fragment.account_lock

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.login.AccountLockDomain
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.screen.account_lock.LockConfirmScreen
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockActionEvent
import com.vietinbank.feature_login.ui.viewmodel.LockUIState
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class AccountLockConfirmFragment : BaseFragment<AccountLockViewModel>() {
    override val viewModel: AccountLockViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getExtrasData()
        initObserver()
        handleSingleEvent { event ->
            when (event) {
                is LockActionEvent.NavigateBack -> {
                    appNavigator.popBackStack()
                }

                is LockActionEvent.NextClick -> {
                    viewModel.validateInputField(false)
                }

                is LockActionEvent.ShowValidationError -> {
                    showNoticeDialog(event.message)
                }

                is LockActionEvent.RuleClick -> {
                    viewModel.validateSelectorField(event.ruleClick)
                }

                is LockActionEvent.ShowSuccessFace -> {
                    showNoticeDialog(
                        event.message,
                        positiveButtonText = requireContext().getString(
                            R.string.lock_result_back_login,
                        ),
                    ) {
                        appNavigator.popToLogin(clearBackStack = true)
                    }
                }

                is LockActionEvent.LockResult -> {
                    viewModel.getResultLockAccount(event.accountLockDomain)?.let { result ->
                        appNavigator.goToLockResultAccount(
                            Utils.g().provideGson().toJson(LockUIState.ResultState(result)),
                        )
                    }
                }
            }
        }
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this@AccountLockConfirmFragment, viewModel.otpLockDomain) { model ->
            viewModel.validateOTPLockAccount(model)
        }
    }

    private fun getExtrasData() {
        try {
            val acc = Utils.g().provideGson().fromJson(
                arguments?.getString(Tags.TRANSACTION_BUNDLE),
                AccountLockDomain::class.java,
            )
            viewModel.setLockUIState(LockUIState.InitState(accountDomain = acc))
        } catch (e: Exception) {
            printLog("LockConfirm Parse bundle failed$e")
        }
    }

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            val uiState by viewModel.lockUIState.collectAsState()

            LockConfirmScreen(uiState, viewModel::onAction)
        }
    }
}