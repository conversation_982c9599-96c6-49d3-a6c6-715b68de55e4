package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.getInitials
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_login.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ApproverRuleBottomSheet(
    visible: Boolean,
    lstApprover: List<ApproverDomains>,
    selector: ApproverDomains?,
    onSelect: (ApproverDomains) -> Unit,
    onDismiss: () -> Unit,
) {
    BaseBottomSheet<ApproverDomains>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { chosen -> onSelect(chosen) },
        allowTouchDismiss = true,
        containerColor = FoundationDesignSystem.Colors.white,
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
    ) { onResult ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Padding.padding12),
        ) {
            // Header title
            FoundationText(
                text = stringResource(R.string.lock_confirm_approve_title),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding8),
                style = FoundationDesignSystem.Typography.headingH3,
                textAlign = TextAlign.Center,
                color = FDS.Colors.characterHighlighted,
            )

            FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap8))

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding16),
            ) {
                itemsIndexed(lstApprover) { index, approver ->
                    ApproverRow(
                        approver = approver,
                        isSelected = approver == selector,
                        onClick = { onResult(approver) },
                    )
                    if (index < lstApprover.lastIndex) {
                        FoundationDivider()
                    }
                }
            }
        }
    }
}

/** 1 item người phê duyệt */
@Composable
private fun ApproverRow(
    approver: ApproverDomains,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    val name = approver.fullname.orEmpty().ifBlank { "" }

    // “vai trò – username”
    val subtitle = "${approver.username} – ${approver.title}"

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable(onSafeClick = onClick)
            .padding(vertical = FDS.Sizer.Gap.gap16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Avatar with initials
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon48)
                .background(
                    color = FDS.Colors.blue100,
                    shape = CircleShape,
                ),
            contentAlignment = Alignment.Center,
        ) {
            val initials = name.getInitials()

            FoundationText(
                text = initials,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
        }

        Spacer(Modifier.width(FDS.Sizer.Gap.gap12))

        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.Center,
        ) {
            FoundationText(
                text = name,
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
            )

            Spacer(Modifier.height(FDS.Sizer.Gap.gap4))

            FoundationText(
                text = subtitle,
                style = FoundationDesignSystem.Typography.captionCaptionM,
                color = FoundationDesignSystem.Colors.characterSecondary,
            )
        }
    }
}
