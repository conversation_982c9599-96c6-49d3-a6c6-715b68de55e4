package com.vietinbank.feature_login.ui.events

import com.vietinbank.core_common.models.ForceUpdateDomain
import com.vietinbank.core_domain.models.login.ChangePasswordDomain
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_domain.models.login.GenOTPDomain
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.models.login.VerifyOTPDomain
import com.vietinbank.core_ui.components.dialog.DialogType

/**
 * Sealed class for one-time events in Login flow
 * Uses Channel to ensure events are consumed only once
 * Critical for banking apps to prevent duplicate navigation/dialogs
 */
sealed class LoginEvent {

    // Navigation Events
    object NavigateTo2FA : LoginEvent()

    data class NavigateToActive(
        val sessionId: String,
        val isFirstLogin: Boolean = false,
    ) : LoginEvent()

    object NavigateToHome : LoginEvent()

    // Login Success with full setup - replaces LiveData navigation
    data class LoginSuccessWithSetup(
        val loginData: LoginDomain,
        val sessionId: String,
        val fingerID: String? = null,
    ) : LoginEvent()

    data class NavigateToLockAccount(
        val dataType: String,
    ) : LoginEvent()

    // Dialog Events
    data class ShowErrorDialog(
        val message: String,
        val type: DialogType = DialogType.ERROR,
    ) : LoginEvent()

    data class ShowNoticeDialog(
        val message: String,
        val positiveAction: (() -> Unit)? = null,
        val positiveButtonText: String? = null,
    ) : LoginEvent()

    data class ShowConfirmDialog(
        val message: String,
        val positiveButtonText: String,
        val negativeButtonText: String,
        val positiveAction: () -> Unit,
        val negativeAction: (() -> Unit)? = null,
    ) : LoginEvent()

    data class ShowPasswordExpiryWarning(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ShowPasswordExpiryNew(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ShowPasswordExpiredDialog(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ShowPasswordExpired881Dialog(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ShowFirstLoginDialog(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ShowForgotPasswordDialog(
        val message: String,
    ) : LoginEvent()

    // Company Selection Event
    data class ShowCompanySelectionDialog(
        val companies: List<CifSharedDomain>,
        val selectedId: String? = null,
    ) : LoginEvent()

    // Biometric Events
    data class ShowBiometricNotAvailable(
        val message: String,
        val type: DialogType = DialogType.ERROR,
    ) : LoginEvent()

    /** Show dialog that OS does not support biometric (Android < 6.0) */
    data class ShowBiometricOsNotSupported(
        val message: String,
        val type: DialogType = DialogType.ERROR,
    ) : LoginEvent()

    /** Show dialog that device has no fingerprint/PIN/pattern enrolled, with option to go to Settings */
    data class ShowBiometricDeviceNotEnrolled(
        val message: String,
    ) : LoginEvent()

    object ShowVerifyPasswordForBiometric : LoginEvent()

    object ShowBiometricPrompt : LoginEvent()

    // Force Update Events
    data class ShowForceUpdateDialog(
        val message: String,
        val type: DialogType = DialogType.ERROR,
    ) : LoginEvent()

    data class ShowUpdatableDialog(
        val message: String,
    ) : LoginEvent()

    data class HandleForceUpdate(
        val functionId: String,
    ) : LoginEvent()

    // Result Events (replacing LiveData for one-time delivery)
    data class LoginResult(
        val data: LoginDomain,
    ) : LoginEvent()

    data class ChangePasswordResult(
        val data: ChangePasswordDomain,
    ) : LoginEvent()

    data class ForceUpdateResult(
        val data: ForceUpdateDomain,
    ) : LoginEvent()

    data class GenOTPResult(
        val data: GenOTPDomain,
        val typePopup: Int,
    ) : LoginEvent()

    data class VerifyOTPResult(
        val data: VerifyOTPDomain,
    ) : LoginEvent()
}