package com.vietinbank.feature_login.ui.screen.account_lock

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.ButtonSize
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_login.R
import com.vietinbank.feature_login.ui.viewmodel.AccountLockViewModel
import com.vietinbank.feature_login.ui.viewmodel.LockAction

@Composable
fun LockInitScreen(
    uiState: AccountLockViewModel.LockInitUiState,
    onAction: (LockAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .imePadding()
            .dismissKeyboardOnClickOutside(),
    ) {
        FoundationAppBar(
            title = uiState.typeScreen ?: "",
            isLightIcon = false,
            navigationIcon = rememberVectorPainter(Icons.AutoMirrored.Filled.ArrowBack),
            onNavigationClick = {
                onAction(LockAction.OnBackPressed)
            },
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FoundationDesignSystem.Sizer.Gap.gap8, FoundationDesignSystem.Sizer.Gap.gap8)
                .weight(1f)
                .verticalScroll(rememberScrollState()),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FoundationDesignSystem.Sizer.Gap.gap12)
                    .background(
                        FoundationDesignSystem.Colors.white,
                        RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                    )
                    .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap16, vertical = FoundationDesignSystem.Sizer.Gap.gap8),
            ) {
                FoundationEditText(
                    value = uiState.companyCif ?: "",
                    onValueChange = { onAction(LockAction.OnCompanyCif(it)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FoundationDesignSystem.Sizer.Gap.gap24),
                    showCharacterCounter = false,
                    placeholder = stringResource(id = com.vietinbank.core_ui.R.string.lock_init_cif_mst),
                    inputType = InputType.NUMBER,
                )

                FoundationDivider(
                    modifier = Modifier.padding(
                        top = FoundationDesignSystem.Sizer.Gap.gap8,
                        bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                    ),
                )

                FoundationEditText(
                    value = uiState.companyUser ?: "",
                    onValueChange = { onAction(LockAction.OnCompanyUser(it)) },
                    modifier = Modifier
                        .fillMaxWidth(),
                    showCharacterCounter = false,
                    placeholder = stringResource(id = com.vietinbank.core_ui.R.string.lock_init_name),
                    inputType = InputType.TEXT,
                )

                FoundationDivider(
                    modifier = Modifier.padding(
                        top = FoundationDesignSystem.Sizer.Gap.gap8,
                        bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                    ),
                )

                FoundationEditText(
                    value = uiState.companyId ?: "",
                    onValueChange = { onAction(LockAction.OnCompanyId(it)) },
                    modifier = Modifier
                        .fillMaxWidth(),
                    showCharacterCounter = false,
                    placeholder = stringResource(id = com.vietinbank.core_ui.R.string.lock_init_cccd),
                    inputType = InputType.NUMBER,
                )

                FoundationDivider(
                    modifier = Modifier.padding(
                        top = FoundationDesignSystem.Sizer.Gap.gap8,
                        bottom = FoundationDesignSystem.Sizer.Gap.gap16,
                    ),
                )

                FoundationEditText(
                    value = uiState.companyAccount ?: "",
                    onValueChange = { onAction(LockAction.OnCompanyAccount(it)) },
                    modifier = Modifier
                        .fillMaxWidth(),
                    showCharacterCounter = false,
                    placeholder = stringResource(id = com.vietinbank.core_ui.R.string.lock_init_account),
                    inputType = InputType.NUMBER,
                )

                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            }
        }

        FoundationButton(
            isLightButton = uiState.isSubmitEnabled,
            text = stringResource(id = com.vietinbank.core_ui.R.string.continue_button),
            onClick = { onAction(LockAction.OnNextClick) },
            modifier = Modifier
                .widthIn(max = FoundationDesignSystem.Sizer.ScreenBreakpoint.tabletMinWidth)
                .fillMaxWidth()
                .padding(start = FoundationDesignSystem.Sizer.Gap.gap24, end = FoundationDesignSystem.Sizer.Gap.gap24),
            size = ButtonSize.Large,
            enabled = uiState.isSubmitEnabled,
        )
    }
}