package com.vietinbank.feature_login.ui.fragment

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.openUrl
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.models.UserProfData
import com.vietinbank.core_common.multiuser.IMultiUserStore
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.recent.IRecentUsersStore
import com.vietinbank.core_common.recent.RecentUserEntry
import com.vietinbank.core_common.utils.BiometricHelper
import com.vietinbank.core_domain.models.login.CifSharedDomain
import com.vietinbank.core_domain.models.login.LoginDomain
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.components.dialog.DialogType
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_login.ui.dialog.Active2FAStep1Result
import com.vietinbank.feature_login.ui.dialog.Active2FAStep2Result
import com.vietinbank.feature_login.ui.dialog.BiometricVerifyPasswordDialog
import com.vietinbank.feature_login.ui.dialog.BiometricVerifyPasswordResult
import com.vietinbank.feature_login.ui.dialog.Company
import com.vietinbank.feature_login.ui.dialog.CompanySelectionDialog
import com.vietinbank.feature_login.ui.dialog.CompanySelectionResult
import com.vietinbank.feature_login.ui.dialog.NewUIActive2FAStep1Dialog
import com.vietinbank.feature_login.ui.dialog.NewUIActive2FAStep2Dialog
import com.vietinbank.feature_login.ui.dialog.SwitchUserDialog
import com.vietinbank.feature_login.ui.dialog.SwitchUserResult
import com.vietinbank.feature_login.ui.events.LoginEvent
import com.vietinbank.feature_login.ui.screen.newui.NewUIPreLoginAction
import com.vietinbank.feature_login.ui.screen.newui.NewUIPreLoginScreen
import com.vietinbank.feature_login.ui.viewmodel.LoginViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Pre-login fragment for NewUI
 * Handles both new users and returning users based on isActive state
 */
@AndroidEntryPoint
class NewUIPreLoginFragment : BaseFragment<LoginViewModel>() {

    // region Properties
    override val viewModel: LoginViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    @Inject
    lateinit var recentUsersStore: IRecentUsersStore

    @Inject
    lateinit var multiUserStore: IMultiUserStore

    /**
     * Override to prevent session timeout handling in pre-login screen
     * This prevents the infinite loop when navigating back from login after timeout
     */
    override fun shouldObserveSessionExpiry(): Boolean = false
    // endregion

    // region Lifecycle
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestNotificationPermissionIfNeeded()
        setupCompanySelectionListener() // Setup Fragment Result listener for company selection
        setupSwitchUserResultListener() // Setup Fragment Result listener for switch user dialog
        setupEventCollector() // Collect Channel events

        // Listen to deletion events from SwitchUserDialog to refresh UI immediately
        childFragmentManager.setFragmentResultListener(
            "switch_user_deleted",
            viewLifecycleOwner,
        ) { _, bundle ->
            val deletedUsernames = bundle.getStringArrayList("deleted_usernames") ?: arrayListOf()
            val currentUsername = viewModel.getUserName()

            // Check if current user was deleted
            val isCurrentUserDeleted = currentUsername != null &&
                deletedUsernames.contains(currentUsername)

            if (isCurrentUserDeleted) {
                // Current user was deleted - need special handling
                viewModel.handleCurrentUserDeleted()
            } else {
                // Other users deleted - normal refresh
                viewModel.refreshSavedUserFromStores()
            }
        }
        setupAllObservers() // Keep for backward compatibility
        viewModel.updateLoginStatus()
        viewModel.forceUpdate()

        // Load Soft OTP status for checker role users
        if (viewModel.isChecker()) {
            softManager.loadSoftStatus()
        }

        // Ensure PreLogin reflects current saved users when opening
        viewModel.refreshSavedUserFromStores()
    }
    // endregion

    override fun onResume() {
        super.onResume()
        viewModel.updateQuickAction()
    }

    // region Compose UI
    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        // Derive display info purely from uiState for reactive recomposition
        val hasSavedUser = !uiState.isActive && uiState.savedUsername.isNotEmpty()
        val displayFullName = if (uiState.isActive) uiState.fullName else if (hasSavedUser) uiState.fullName else null
        val displayUsername = if (uiState.isActive) uiState.savedUsername else if (hasSavedUser) uiState.savedUsername else null

        AppTheme {
            NewUIPreLoginScreen(
                userRole = uiState.userRole,
                isActive = uiState.isActive || !displayFullName.isNullOrEmpty(), // Show user UI if we have saved user info
                userName = displayUsername,
                fullName = displayFullName,
                listQuickAction = uiState.listQuickAction,
                onAction = { action -> handleScreenAction(action) },
            )
        }
    }
    // endregion

    // region User Actions
    private fun handleScreenAction(action: NewUIPreLoginAction) {
        when (action) {
            is NewUIPreLoginAction.OnLoginClick, NewUIPreLoginAction.OnMyRequestClick -> {
                // Navigate to login screen
                if (viewModel.isActive()) {
                    // Đang có session active -> vào login bình thường
                    appNavigator.goToLogin()
                } else {
                    // Không active: nếu có user đã lưu (đang hiển thị trên PreLogin), truyền theo để Login hiển thị đúng
                    val savedUser = multiUserStore.getAllSavedUsers().firstOrNull()
                    if (savedUser != null) {
                        appNavigator.goToLoginWithSelectedUser(
                            username = savedUser.username,
                            fullName = savedUser.fullName,
                            corpName = savedUser.corpName,
                        )
                    } else {
                        appNavigator.goToLogin()
                    }
                }
            }
            is NewUIPreLoginAction.OnRegisterClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnSyncTokenClick -> {
                activity?.openUrl(VSoftConstants.URL_SYNC)
            }
            is NewUIPreLoginAction.OnUnlockUserClick -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_UNLOCK_USER)
                    },
                )
            }
            is NewUIPreLoginAction.OnBranchLocationClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnGuideClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnContactClick -> {
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnSwitchUserClick -> {
                showSwitchUserDialog()
            }
            is NewUIPreLoginAction.OnFingerprintClick -> {
                handleBiometricLogin()
            }
            is NewUIPreLoginAction.OnSearchClick -> {
                showNoticeDialog(getString(R.string.prelogin_search_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnVoiceClick -> {
                showNoticeDialog(getString(R.string.prelogin_voice_search_under_construction), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnTransferClick -> {
                appNavigator.goToLogin()
            }
            is NewUIPreLoginAction.OnInternationalTransferClick -> {
                showNoticeDialog(getString(R.string.prelogin_login_required_for_international), type = DialogType.INFO)
            }
            is NewUIPreLoginAction.OnReissuePasswordClick -> {
                // Cấp lại mật khẩu for new users
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUIPreLoginAction.OnRestorePasswordClick -> {
                // Khôi phục mật khẩu for active users
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, Tags.TYPE_GROUP_RESET_PASSWORD)
                    },
                )
            }
            is NewUIPreLoginAction.OnSoftOtpClick -> {
                // Handle Soft OTP quick access
                if (softManager.isAppActive) {
                    // Navigate to PIN entry for OTP retrieval
                    appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.GET_OTP.name)
                } else {
                    // Show dialog that Soft OTP is not activated
                    showNoticeDialog(getString(R.string.prelogin_soft_otp_not_activated), type = DialogType.INFO)
                }
            }

            // Header notification/language actions
            is NewUIPreLoginAction.OnLanguageClick -> {
                // TODO: Implement language/region selector
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }

            is NewUIPreLoginAction.OnNotificationClick -> {
                // TODO: Navigate to notifications
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }

            is NewUIPreLoginAction.OnOttNotificationClick -> {
                // TODO: Navigate to OTT notification settings
                showNoticeDialog(getString(R.string.prelogin_feature_under_construction), type = DialogType.INFO)
            }
        }
    }

    /**
     * Setup listener for company selection dialog results
     * This is needed when login from biometric registration returns multi-CIF response
     */
    private fun setupCompanySelectionListener() {
        childFragmentManager.setFragmentResultListener(
            "company_selection_result",
            viewLifecycleOwner,
        ) { requestKey, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<CompanySelectionResult>("key_result")

                result?.let { selectionResult ->
                    // Get companies from ViewModel using proper encapsulation
                    val companies = viewModel.getCompaniesForSelection()

                    // Find the selected CifSharedDomain from result
                    val selectedCif = companies?.find { it.enterpriseid == selectionResult.companyId }

                    selectedCif?.let {
                        viewModel.selectCompany(it)
                        // Dialog auto-dismisses on result

                        // Now call login with the selected company
                        val username = viewModel.getUserName().toString()

                        // Check if we're coming from biometric password dialog or fingerprint login
                        val password = viewModel.getLastLoginPassword()

                        when {
                            !password.isNullOrEmpty() -> {
                                // Coming from biometric password dialog - need to register TouchID
                                viewModel.login(
                                    username = username,
                                    password = password,
                                    regTouchId = "1", // Register TouchID flag
                                )
                            }
                            viewModel.isUsingFingerprint() -> {
                                // Coming from fingerprint login - no password, use fingerprint auth
                                viewModel.login(
                                    username = username,
                                    password = "",
                                    isActiveFinger = true, // Use fingerprint authentication
                                )
                            }
                            else -> {
                                // Shouldn't happen, but handle gracefully
                                // Clear state and don't retry
                                viewModel.clearMultiCifState()
                                viewModel.clearBiometricPassword()
                            }
                        }
                    }
                }
            } else {
                // User dismissed dialog without selecting
                // Clear multi-cif to allow re-triggering
                viewModel.clearMultiCifState()
            }
        }
    }

    /**
     * Show Company Selection Dialog using the new BaseDialog architecture
     * Maps CifSharedDomain to Company and handles result
     */
    private fun showCompanySelectionDialog(
        companies: List<CifSharedDomain>,
        selectedId: String?,
    ) {
        // Map CifSharedDomain to Company for dialog
        val companyList = companies.map { cif ->
            Company(
                id = cif.enterpriseid ?: "",
                name = cif.enterprisename ?: "",
                taxCode = cif.enterpriseid ?: "", // Use enterpriseid as tax code display
            )
        }

        // Show the dialog
        CompanySelectionDialog.show(
            fragmentManager = childFragmentManager,
            companies = companyList,
            selectedId = selectedId,
        )
    }

    /**
     * Show Switch User Dialog
     * Shows users from both MultiUserStore and RecentUsersStore
     */
    private fun showSwitchUserDialog() {
        // Lấy dữ liệu từ cả 2 nguồn
        val multiUserList = multiUserStore.getAllSavedUsers()
        val recentUserList = recentUsersStore.getRecentUsers()

        // Kết hợp và loại bỏ trùng lặp, ưu tiên MultiUserStore
        val combinedMap = mutableMapOf<String, RecentUserEntry>()

        // Thêm từ RecentUsersStore trước
        recentUserList.forEach { user ->
            combinedMap[user.username] = user
        }

        // Thêm/cập nhật từ MultiUserStore (ưu tiên hơn)
        multiUserList.forEach { savedUser ->
            val existingEntry = combinedMap[savedUser.username]
            combinedMap[savedUser.username] = RecentUserEntry(
                username = savedUser.username,
                fullName = savedUser.fullName ?: existingEntry?.fullName,
                corpName = savedUser.corpName ?: existingEntry?.corpName,
                roleId = savedUser.roleId ?: existingEntry?.roleId,
                biometricToken = existingEntry?.biometricToken,
            )
        }

        // Sắp xếp theo thời gian login gần nhất (từ MultiUserStore) hoặc thứ tự ban đầu
        val sortedUsers = combinedMap.values.sortedByDescending { entry ->
            multiUserList.find { it.username == entry.username }?.lastLoginTime ?: 0L
        }.toMutableList()

        var currentUsername = viewModel.getUserName()
        val currentFullName = viewModel.getFullName()

        // Nếu chưa active, dùng user đầu tiên trong danh sách (đang hiển thị ở PreLogin) làm current
        if (!viewModel.isActive()) {
            currentUsername = sortedUsers.firstOrNull()?.username
        }

        // Nếu user đang đăng nhập, đảm bảo xuất hiện ở đầu danh sách (hoặc nếu đã chọn user mặc định ở PreLogin)
        if (!currentUsername.isNullOrEmpty()) {
            val existingCurrentUser = sortedUsers.find { it.username == currentUsername }

            if (existingCurrentUser == null) {
                // Tạo entry mới cho current user
                val currentUser = RecentUserEntry(
                    username = currentUsername,
                    fullName = currentFullName,
                    corpName = viewModel.getCorpName(),
                    roleId = viewModel.getRoleId(),
                    biometricToken = null, // Removed fingerprint storage for security
                )
                sortedUsers.add(0, currentUser)
            } else {
                // Đưa current user lên đầu nếu chưa phải
                sortedUsers.remove(existingCurrentUser)
                sortedUsers.add(0, existingCurrentUser)
            }
        }

        SwitchUserDialog.show(
            fragmentManager = childFragmentManager,
            recentUsers = sortedUsers,
            currentUsername = currentUsername,
            onRemoveUser = { username ->
                // Check nếu đang xóa user active → clear session
                if (viewModel.isActive() && viewModel.getUserName() == username) {
                    clearAllData()
                }

                // Xóa từ cả 2 store (with verification)
                try {
                    recentUsersStore.removeUser(username)
                } catch (e: Exception) {
                }
                try {
                    multiUserStore.removeUser(username)
                } catch (e: Exception) {
                }

                // Verify removal and retry once if needed
                val stillInRecent = try { recentUsersStore.getRecentUsers().any { it.username == username } } catch (_: Exception) { false }
                val stillInMulti = try { multiUserStore.getAllSavedUsers().any { it.username == username } } catch (_: Exception) { false }

                if (stillInRecent) {
                    try { recentUsersStore.removeUser(username) } catch (_: Exception) {}
                }
                if (stillInMulti) {
                    try { multiUserStore.removeUser(username) } catch (_: Exception) {}
                }

                // Update PreLogin state immediately after deletion
                viewModel.refreshSavedUserFromStores()
            },
        )
    }

    /**
     * Setup listener for switch user dialog results
     */
    private fun setupSwitchUserResultListener() {
        childFragmentManager.setFragmentResultListener(
            "switch_user_result",
            viewLifecycleOwner,
        ) { _, bundle ->

            val isCancelled = bundle.getBoolean("key_cancelled", false)

            if (!isCancelled) {
                val result = bundle.getParcelable<SwitchUserResult>("key_result")

                result?.let { switchResult ->
                    when {
                        switchResult.selectedUsername.isEmpty() -> {
                            // Add new account - navigate to login with clean UI (clear form)
                            appNavigator.goToLoginForNewAccount()
                        }
                        else -> {
                            // User selected (with or without biometric) - navigate to login with pre-filled info

                            // Get user info from MultiUserStore
                            val savedUser = multiUserStore.getUserByUsername(switchResult.selectedUsername)

                            // Navigate to login with selected user info
                            // The login screen will check if user has biometric and show appropriate UI
                            appNavigator.goToLoginWithSelectedUser(
                                username = switchResult.selectedUsername,
                                fullName = savedUser?.fullName,
                                corpName = savedUser?.corpName,
                            )
                        }
                    }
                }
            }
        }
    }
    // endregion

    // region Event Collector (NEW - Channel-based one-time events)
    private fun setupEventCollector() {
        // Collect Channel events for backward compatibility
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.events.collect { event ->
                    handleLoginEvent(event)
                }
            }
        }
    }

    private fun handleLoginEvent(event: LoginEvent) {
        when (event) {
            is LoginEvent.NavigateTo2FA -> {
                showActive2FAStep1Dialog(Tags.SMS_OTP)
            }
            is LoginEvent.GenOTPResult -> {
                Toast.makeText(requireContext(), event.data.status?.message.toString(), Toast.LENGTH_LONG).show()

                val validitySecs = Tags.TIME_DIALOG_ACTIVE_STEP2_VALIDITY
                val resendSecs = Tags.TIME_DIALOG_ACTIVE_STEP2_RESEND

                // LƯU efastId để Step1 → Step2 về sau có thể resume KHÔNG gọi API
                viewModel.updateLastOtpInfo(event.data.efastId?.toString())

                // để startOtpTimers và notifyOtpStarted
                viewModel.onOtpGenerated(validitySecs, resendSecs)

                if (event.typePopup == 1) {
                    showActive2FAStep2Dialog(viewModel.typeOTP2FA.value, event.data.efastId.toString())
                }
            }
            is LoginEvent.VerifyOTPResult -> {
                moveWhenLoginSuccess(event.data.loginResponse)
                viewModel.stopOtpTicksAndClear()
            }
            is LoginEvent.NavigateToActive -> {
                viewModel.saveSession(sessionId = event.sessionId)
                appNavigator.goToActive()
            }
            is LoginEvent.NavigateToHome -> {
                navigateToHome()
            }
            is LoginEvent.LoginSuccessWithSetup -> {
                // DEPRECATED - Using LoginResult instead to avoid duplicate processing
                // This event is kept for backward compatibility but should not be used
            }
            is LoginEvent.NavigateToLockAccount -> {
                appNavigator.goToLockAccount(
                    bundleOf().apply {
                        putString(Tags.DATA_TYPE, event.dataType)
                    },
                )
            }
            is LoginEvent.ShowErrorDialog -> {
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowNoticeDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = event.positiveAction ?: {},
                    positiveButtonText = event.positiveButtonText ?: getString(R.string.dialog_button_confirm),
                )
            }
            is LoginEvent.ShowConfirmDialog -> {
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = event.positiveButtonText,
                    negativeButtonText = event.negativeButtonText,
                    positiveAction = event.positiveAction,
                    negativeAction = event.negativeAction ?: {},
                    dismissAction = {},
                )
            }
            is LoginEvent.ShowPasswordExpiryWarning -> {
                handlePasswordExpiryWarning(event.data)
            }
            is LoginEvent.ShowPasswordExpiryNew -> {
                handlePasswordExpiryNew(event.data)
            }
            is LoginEvent.ShowPasswordExpiredDialog -> {
                // Handle generic password expired - navigate to Active to change password
                handlePasswordExpired(event.data)
            }
            is LoginEvent.ShowPasswordExpired881Dialog -> {
                // Handle password expired with code 881
                handlePasswordExpired881(event.data)
            }
            is LoginEvent.ShowFirstLoginDialog -> {
                handleFirstLoginResponse(event.data)
            }
            is LoginEvent.ShowForgotPasswordDialog -> {
                showNoticeDialog(
                    message = event.message,
                    positiveAction = {},
                    positiveButtonText = getString(R.string.login_forgot_password_button),
                )
            }
            is LoginEvent.ShowBiometricNotAvailable -> {
                // Backward compatibility - generic message
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowBiometricOsNotSupported -> {
                // OS does not support biometric (Android < 6.0)
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowBiometricDeviceNotEnrolled -> {
                // Device has no fingerprint/PIN/pattern enrolled
                // Show dialog with option to go to Settings
                showConfirmDialog(
                    message = event.message,
                    positiveButtonText = getString(R.string.button_settings),
                    negativeButtonText = getString(R.string.dialog_button_close),
                    positiveAction = {
                        // Open Security Settings
                        try {
                            val intent = Intent(android.provider.Settings.ACTION_SECURITY_SETTINGS)
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog(
                                message = getString(R.string.error_cannot_open_settings),
                                type = DialogType.ERROR,
                            )
                        }
                    },
                    negativeAction = {},
                )
            }
            is LoginEvent.ShowVerifyPasswordForBiometric -> {
                showVerifyPasswordDialog()
            }
            is LoginEvent.ShowBiometricPrompt -> {
                showFingerPrintPopup()
            }
            is LoginEvent.ShowForceUpdateDialog -> {
                showNoticeDialog(event.message, type = event.type)
            }
            is LoginEvent.ShowUpdatableDialog -> {
                showNoticeDialog(event.message)
            }
            is LoginEvent.HandleForceUpdate -> {
                viewModel.handleForceUpdateOrNavigate(event.functionId) {}
            }
            is LoginEvent.ShowCompanySelectionDialog -> {
                // Show company selection dialog when multi-CIF response is received
                // This can happen when login is triggered from biometric registration
                showCompanySelectionDialog(
                    companies = event.companies,
                    selectedId = event.selectedId,
                )
            }
            is LoginEvent.LoginResult -> {
                // Handle login result using existing logic
                handleLoginSuccess(event.data)
            }
            is LoginEvent.ChangePasswordResult -> {
                // Handle change password result
                showNoticeDialog(
                    message = event.data.status.message ?: "Đổi mật khẩu thành công",
                    positiveAction = { appNavigator.popBackStack() },
                )
            }
            is LoginEvent.ForceUpdateResult -> {
                // Handle force update result
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
            }
        }
    }
    // endregion

    // region Observers Setup
    private fun setupAllObservers() {
        setupLoginObservers()
        setupErrorObservers()
        setupUpdateObservers()
    }

    private fun setupLoginObservers() {
        // Migrated to Channel events - see LoginEvent.LoginResult
        // Now handled in handleLoginEvent()
        /*
        // Login success/error handling
        viewModel.loginState.observe(viewLifecycleOwner) { resource ->
            when (resource) {
                is Resource.Success -> {
                    handleLoginSuccess(resource.data)
                    viewModel.updateLoginStatus()
                }
                is Resource.Error -> {
                    // Error handled in BaseFragment
                }
            }
        }
         */
    }

    private fun setupErrorObservers() {
        // Multi-CIF handling
        viewModel.multiCifResponse.observe(viewLifecycleOwner) { response ->
            // Multi-CIF logic handled in login screen - this is UI state, not event
        }
    }

    private fun setupUpdateObservers() {
        // Migrated to Channel events - see LoginEvent.ForceUpdateResult
        // Now handled in handleLoginEvent()
        /*
        // Force update handling
        viewModel.forceUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is Resource.Success -> {
                    viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_ALL) {}
                }
                is Resource.Error -> {
                    // Error handled by BaseViewModel
                }
            }
        }
         */

        // Update dialogs now handled via Channel events
    }
    // endregion

    // region Helper Methods
    private fun handleFirstLoginResponse(data: LoginDomain) {
        printLog("data.status?.code: ${data.status?.code}")
        if (!TextUtils.isEmpty(data.corpUser?.status) && Tags.FIRST_LOGIN_SIGNAL == data.corpUser?.status) {
            if (viewModel.isActive()) {
                clearAllData()
            }
            showConfirmDialog(
                message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
                positiveButtonText = getString(R.string.login_change_password_button),
                negativeButtonText = getString(R.string.login_close_button),
                positiveAction = {
                    viewModel.saveSession(sessionId = data.sessionId.toString())
                    saveProfileAfterLoginSuccess(data)
                    viewModel.updateLoginStatus()
                    appNavigator.goToActive()
                },
                negativeAction = {},
                dismissAction = {},
            )
        } else {
            showNoticeDialog(data.status?.message.toString(), type = DialogType.ERROR)
        }
    }

    private fun handlePasswordExpiryWarning(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showConfirmDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            negativeButtonText = getString(com.vietinbank.feature_login.R.string.company_dialog_cancel_close),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            negativeAction = {
                // Nếu sắp hết hạn thì khi ấn bỏ qua sẽ điều hướng vào Home. Làm như case LoginResult
                // Handle login result using existing logic
                handleLoginSuccess(data)
            },
            dismissAction = {},
        )
    }

    private fun handlePasswordExpiryNew(data: LoginDomain) {
        if (viewModel.isActive()) {
            clearAllData()
        }
        showNoticeDialog(
            message = data.status?.message.toString() + "\n (" + data.status?.subCode.toString() + ")",
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                viewModel.saveProfile(
                    UserProfData(
                        userName = data.corpUser?.username ?: viewModel.userName.toString(),
                        cifNo = data.cifNo,
                        status = data.corpUser?.status,
                    ),
                )
                appNavigator.goToActive()
            },
            positiveButtonText = getString(com.vietinbank.feature_login.R.string.change_password_title),
            cancelable = false,
        )
    }

    private fun handlePasswordExpired(data: LoginDomain) {
        // Generic password expired - navigate to Active screen to change password
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }

    private fun handlePasswordExpired881(data: LoginDomain) {
        // Password expired with code 881 - specific handling
        showNoticeDialog(
            message = data.status?.message ?: getString(R.string.login_password_expired_881),
            positiveAction = {
                viewModel.saveSession(sessionId = data.sessionId.toString())
                saveProfileAfterLoginSuccess(data)
                viewModel.updateLoginStatus()
                appNavigator.goToActive()
            },
        )
    }
    // endregion

    // region Biometric Handling
    private fun handleBiometricLogin() {
        if (viewModel.isActive()) {
            // Fingerprint functionality disabled for security reasons
            val hasFingerprintRegistered = false

            if (hasFingerprintRegistered) {
                // Call checkBiometricForLogin which will send appropriate event
                // The event handler will then call showFingerPrintPopup() if ready
                viewModel.checkBiometricForLogin()
            } else {
                showVerifyPasswordDialog()
            }
        } else {
            showNoticeDialog(getString(R.string.login_biometric_activation_message), type = DialogType.INFO)
        }
    }

    /**
     * Shows the new BiometricVerifyPasswordDialog
     * Replaces the old XML-based VerifyPasswordDialog
     */
    private fun showVerifyPasswordDialog() {
        // Create and show dialog using BaseDialog pattern
        val dialog = BiometricVerifyPasswordDialog.newInstance(
            title = getString(R.string.login_verify_password_title),
            message = getString(R.string.login_verify_password_message),
        )
        dialog.show(childFragmentManager, "BiometricVerifyPasswordDialog")

        // Listen for dialog result using Fragment Result API
        childFragmentManager.setFragmentResultListener(
            "biometric_verify_password_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<BiometricVerifyPasswordResult>("key_result")
                result?.let {
                    // Store password in ViewModel for potential multi-CIF retry
                    // This is needed in case server returns multi-CIF response
                    viewModel.storeBiometricPassword(it.password)

                    // Perform login with TouchID registration
                    viewModel.login(
                        username = viewModel.getUserName().toString(),
                        password = it.password,
                        regTouchId = "1", // Register TouchID flag - critical for biometric registration
                    )
                }
            }
        }
    }

    private fun showFingerPrintPopup() {
        // Don't call checkBiometricForLogin() here - it creates infinite loop!
        // This function is called FROM the event handler after check is done
        BiometricHelper.showBiometricOrDeviceCredentialPrompt(
            fragment = this,
            title = getString(R.string.login_biometric_title),
            subtitle = getString(R.string.login_biometric_subtitle),
            onSuccess = {
                printLog("verify success - using fingerprint for user: ${viewModel.getUserName()}")
                viewModel.login(
                    username = viewModel.getUserName().toString(),
                    password = "",
                    isActiveFinger = true,
                )
            },
            onError = { errorMsg ->
                showNoticeDialog(
                    message = errorMsg,
                    type = DialogType.ERROR,
                )
            },
        )
    }
    // endregion

    // region Permission Handling
    private fun requestNotificationPermissionIfNeeded() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.POST_NOTIFICATIONS,
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                    if (isGranted) {
                        printLog(getString(R.string.login_notification_permission_granted))
                    } else {
                        printLog(getString(R.string.login_notification_permission_denied))
                    }
                }.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }
    // endregion

    private fun clearAllData() {
        viewModel.clearSession()
        viewModel.clearProfile()
        viewModel.clearAllConfig() // This calls clearAll() which should preserve PERSISTENT keys
        viewModel.transferCacheManager.clearTransferALL()
        viewModel.ottRegistrationRepository.clearAllOttCache()
    }

    private fun saveProfileAfterLoginSuccess(data: LoginDomain) {
        viewModel.saveProfile(
            UserProfData(
                userName = data.corpUser?.username.toString(),
                phone = data.corpUser?.phoneNo.toString(),
                fullName = data.corpUser?.fullname.toString(),
                cifNo = data.cifNo.toString(),
                roleId = data.corpUser?.roleId,
                keypassprofile = data.corpUser?.keypassprofile ?: "",
                keypasssoftotp = data.corpUser?.keypasssoftotp ?: "",
                email = data.corpUser!!.email,
                corpName = data.corpUser?.corpName ?: "",
                // keypass
                keypassCode = data.corpUser?.keypassid,
                keypassToken = data.corpUser?.keypassid,
                // next approver
                roleLevel = data.corpUser?.roleLevel,
                groupType = data.corpUser?.groupType,
                sessionID = data.sessionId,
                idNumber = data.corpUser?.idNumber,
                userId = data.userId,
                addField4 = data.addField4,
            ),
        )

        viewModel.setTimerConfig(true)
    }

    private fun handleLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // saveDataLogin
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        printLog("Save session success: ${data.sessionId}")
        saveProfileAfterLoginSuccess(data)

        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        navigateToHome()
        viewModel.setEnableCheckEkyc()
    }

    private fun navigateToHome() {
        if (viewModel.isNonFinancial()) {
            appNavigator.goToAccountFromLogin()
        } else {
            appNavigator.goToHome()
        }
    }

    private fun showActive2FAStep1Dialog(typeOTP2FA: String) {
        NewUIActive2FAStep1Dialog.show(childFragmentManager, typeOTP2FA)

        // Listen for dialog result using Fragment Result API
        childFragmentManager.setFragmentResultListener(
            "newui_active2fa_step1_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<Active2FAStep1Result>("key_result")
                result?.let {
                    // Khi user ấn Tiếp tục ở Step1
                    val resume = viewModel.getOtpTimerResume()
                    val lastEfastId = viewModel.getLastEfastId()

                    if (resume.validityLeft > 0 && lastEfastId != null) {
                        // CÒN HIỆU LỰC → chỉ mở lại Step2 và đảm bảo tick đang chạy
                        showActive2FAStep2Dialog(result.typeOTP2FA, lastEfastId)
                        viewModel.startOtpTicks() // ĐỌC prefs → đẩy phần còn lại vào otpUi
                    } else {
                        // HẾT HẠN hoặc chưa từng sinh → gen mới
                        viewModel.genOTP(result.typeOTP2FA, 1)
                    }
                }
            }
        }
    }

    private fun showActive2FAStep2Dialog(
        typeOTP2FA: String,
        efastId: String,
    ) {
        // 1) ĐĂNG KÝ LISTENER TRƯỚC KHI SHOW (tránh miss event)
        childFragmentManager.setFragmentResultListener(
            "newui_active2fa_step2_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (isCancelled) return@setFragmentResultListener

            val result = bundle.getParcelable<Active2FAStep2Result>("key_result")
                ?: return@setFragmentResultListener

            when (result.action) {
                Active2FAStep2Result.Action.BACK -> {
                    showActive2FAStep1Dialog(typeOTP2FA)
                }
                Active2FAStep2Result.Action.RESEND -> {
                    viewModel.genOTP(typeOTP2FA, 0)
                }
                Active2FAStep2Result.Action.CONTINUE -> {
                    viewModel.verifyOTP(result.otpInput.orEmpty(), efastId)
                }
            }
        }

        // 2) SHOW DIALOG (không gen OTP ở đây)
        NewUIActive2FAStep2Dialog.show(
            childFragmentManager,
            viewModel.phoneNo.value,
            viewModel.emailNo.value,
        )
        // Đảm bảo fragment đã được thêm trước khi notify xuống
        childFragmentManager.executePendingTransactions()

        // đảm bảo có tick khi vừa mở lại
        viewModel.startOtpTicks()
    }

    private fun moveWhenLoginSuccess(data: LoginDomain) {
        // case dang nhap lan sau (normal case)
        // dieu huong sang man hinh Home
        viewModel.saveSession(
            sessionId = data.sessionId.toString(),
        )
        saveProfileAfterLoginSuccess(data)
        if (!TextUtils.isEmpty(data.addField3)) {
            printLog("AddField3: ${data.addField3}")
            viewModel.updateFingerID(data.addField3!!)
        }
        navigateToHome()
    }
}