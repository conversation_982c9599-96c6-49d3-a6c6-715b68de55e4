package com.vietinbank.core_common.extensions

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.TextUtils
import android.util.Base64
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import java.text.Normalizer
import java.util.regex.Pattern

fun String?.maskPhoneNumber(): String {
    if (this == null) return ""
    if (this.length < 7) return this // Đảm bảo số điện thoại có ít nhất 7 chữ số
    val start = this.take(3) // Lấy 3 chữ số đầu
    val end = this.takeLast(4) // Lấy 4 chữ số cuối
    val maskedPart = "*".repeat(this.length - 7) // Tạo phần mask cho các chữ số giữa
    return "$start$maskedPart$end" // Kết hợp lại
}

fun String?.maskEmail(): String {
    if (this == null) return ""
    val lstMark = this.split("@")
    val startMask = if (lstMark[0].length < 3) {
        "*".repeat(lstMark[0].length)
    } else {
        "${lstMark[0].take(lstMark[0].length - 3)}***"
    }
    val endEmail = this.substring(lstMark[0].length)
    return "$startMask$endEmail"
}

// base 64 to bitmap
fun String?.toBitmap(): Bitmap? {
    try {
        val decodedString: ByteArray = Base64.decode(this, Base64.DEFAULT)
        return BitmapFactory.decodeByteArray(decodedString, 0, decodedString.size)
    } catch (_: Exception) {
    }
    return null
}

fun String?.removeVietNam(ignoreCase: Boolean = false): String {
    if (TextUtils.isEmpty(this)) {
        return ""
    }
    val temp = Normalizer.normalize(this, Normalizer.Form.NFD)
    val pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+")
    if (ignoreCase) return pattern.matcher(temp).replaceAll("").lowercase()
    return pattern.matcher(temp).replaceAll("")
}

fun String?.removeAllSpaces(): String {
    if (TextUtils.isEmpty(this)) {
        return ""
    }
    return this!!.replace("\\s".toRegex(), "")
}

fun String.getAmountServer(): String {
    return this.replace("[^\\d-.]".toRegex(), "")
}

fun String?.mapIconCcy() = when (this) {
    MoneyCurrency.VND.value -> com.vietinbank.core_common.R.drawable.ic_currency_vnd
    MoneyCurrency.USD.value -> com.vietinbank.core_common.R.drawable.ic_currency_usd
    MoneyCurrency.EUR.value -> com.vietinbank.core_common.R.drawable.ic_currency_eur
    MoneyCurrency.GBP.value -> com.vietinbank.core_common.R.drawable.ic_currency_gbp
    MoneyCurrency.AUD.value -> com.vietinbank.core_common.R.drawable.ic_currency_aud
    MoneyCurrency.JPY.value -> com.vietinbank.core_common.R.drawable.ic_currency_jpy
    MoneyCurrency.CAD.value -> com.vietinbank.core_common.R.drawable.ic_currency_cad
    MoneyCurrency.CHF.value -> com.vietinbank.core_common.R.drawable.ic_currency_chf
    MoneyCurrency.CNY.value -> com.vietinbank.core_common.R.drawable.ic_currency_cny
    MoneyCurrency.SGD.value -> com.vietinbank.core_common.R.drawable.ic_currency_sgd
    MoneyCurrency.INR.value -> com.vietinbank.core_common.R.drawable.ic_currency_inr
    MoneyCurrency.KRW.value -> com.vietinbank.core_common.R.drawable.ic_currency_krw
    MoneyCurrency.IDR.value -> com.vietinbank.core_common.R.drawable.ic_currency_idr
    MoneyCurrency.ILS.value -> com.vietinbank.core_common.R.drawable.ic_currency_ils
    MoneyCurrency.UAH.value -> com.vietinbank.core_common.R.drawable.ic_currency_uah
    MoneyCurrency.AMD.value -> com.vietinbank.core_common.R.drawable.ic_currency_amd
    MoneyCurrency.GEL.value -> com.vietinbank.core_common.R.drawable.ic_currency_gel
    MoneyCurrency.MNT.value -> com.vietinbank.core_common.R.drawable.ic_currency_mnt
    MoneyCurrency.PHP.value -> com.vietinbank.core_common.R.drawable.ic_currency_php
    MoneyCurrency.AZN.value -> com.vietinbank.core_common.R.drawable.ic_currency_azn
    MoneyCurrency.TRY.value -> com.vietinbank.core_common.R.drawable.ic_currency_try
    MoneyCurrency.KZT.value -> com.vietinbank.core_common.R.drawable.ic_currency_kzt
    MoneyCurrency.LAK.value -> com.vietinbank.core_common.R.drawable.ic_currency_lak
    MoneyCurrency.ZAR.value -> com.vietinbank.core_common.R.drawable.ic_currency_zar
    MoneyCurrency.RUB.value -> com.vietinbank.core_common.R.drawable.ic_currency_rub
    MoneyCurrency.THB.value -> com.vietinbank.core_common.R.drawable.ic_currency_thb
//    MoneyCurrency.DKK.value -> com.vietinbank.core_common.R.drawable.ic_currency_dkk // dan mach
//    MoneyCurrency.HKD.value -> com.vietinbank.core_common.R.drawable.ic_currency_hkd // hong kong
//    MoneyCurrency.KWD.value -> com.vietinbank.core_common.R.drawable.ic_currency_kwd
//    MoneyCurrency.MYR.value -> com.vietinbank.core_common.R.drawable.ic_currency_myr
//    MoneyCurrency.NOK.value -> com.vietinbank.core_common.R.drawable.ic_currency_nok // na uy
//    MoneyCurrency.SAR.value -> com.vietinbank.core_common.R.drawable.ic_currency_sar // ả rập
//    MoneyCurrency.SEK.value -> com.vietinbank.core_common.R.drawable.ic_currency_sek // thụy điển
    else -> com.vietinbank.core_common.R.drawable.ic_currency_default
}

fun String?.containsNotVietNam(key: String?, ignoreCase: Boolean = true): Boolean {
    return this.removeVietNam().contains(key.removeVietNam(), ignoreCase)
}

/**
 * Generate initials from a full name according to rules:
 * - 1 word: Show 1 initial (e.g., "Ngọc" → "N")
 * - 2 words: Show 2 initials (e.g., "Minh Ngọc" → "MN")
 * - 3+ words: Show last 2 initials (e.g., "Nguyễn Văn A" → "VA")
 */
fun String.getInitials(): String {
    val words = this.trim().split("\\s+".toRegex()).filter { it.isNotEmpty() }
    return when (words.size) {
        0 -> ""
        1 -> words[0].firstOrNull()?.uppercase() ?: ""
        2 -> words.mapNotNull { it.firstOrNull()?.uppercase() }.joinToString("")
        else -> {
            // For 3+ words, take the last 2 initials
            words.takeLast(2).mapNotNull { it.firstOrNull()?.uppercase() }.joinToString("")
        }
    }
}

fun String?.getOnlyText(): String {
    return this?.replace(Regex("[^A-Za-z]"), "") ?: ""
}