package com.vietinbank.core_common.extensions

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Base64
import android.util.Log
import android.widget.Toast
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.core.graphics.scale
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.VTBLogger
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import kotlin.math.max
import kotlin.math.round
import kotlin.math.roundToInt
import kotlin.random.Random

/**
 * Created by vandz on 19/12/24.
 */

fun <T> T.toJsonString(signature: String, requestPath: String, requestId: String): String {
    val gson = Gson()
    val json = gson.toJson(this)
    return json
}

/**
 * Main logging function - delegate to VTBLogger
 */
fun printLog(message: Any?, prefix: String = "") {
    VTBLogger.log(message, prefix)
}

// Error logging
fun printLogError(message: String, prefix: String = "", throwable: Throwable? = null) {
    if (throwable != null) {
        VTBLogger.log(throwable, prefix, Log.ERROR)
    } else {
        VTBLogger.log(message, prefix, Log.ERROR)
    }
}

// Info logging
fun printLogInfo(message: String, prefix: String = "") {
    VTBLogger.log(message, prefix, Log.INFO)
}

// Warning logging
fun printLogWarn(message: String, prefix: String = "") {
    VTBLogger.log(message, prefix, Log.WARN)
}

/**
 * Convert the request to a string before sending it to the server
 */
inline fun <reified T> T.toJsonString(): String {
    return try {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        val type = object : TypeToken<T>() {}.type
        gson.toJson(this, type)
    } catch (e: Exception) {
        e.printStackTrace()
        ""
    }
}

fun Context.toast(
    message: String,
    duration: Int = Toast.LENGTH_SHORT,
) {
    Toast.makeText(this, message, duration).show()
}

fun generateRequestID(): String {
    val charPool: List<Char> = ('A'..'Z') + ('0'..'9')

    return (1..8).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")
}

fun Context.openUrl(url: String) {
    val syncIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    startActivity(syncIntent)
}

fun resizeAndConvertUriToBase64(
    context: Context,
    uri: Uri,
    targetWidth: Int = 1920,
    targetHeight: Int = 1080,
): String {
    val contentResolver = context.contentResolver

    // Load the bitmap from URI
    val inputStream = contentResolver.openInputStream(uri)
    val originalBitmap = BitmapFactory.decodeStream(inputStream)
    inputStream?.close()

    // Calculate dimensions while maintaining aspect ratio
    val originalWidth = originalBitmap.width
    val originalHeight = originalBitmap.height

    val aspectRatio = originalWidth.toFloat() / originalHeight.toFloat()

    val width: Int
    val height: Int

    if (aspectRatio > (targetWidth.toFloat() / targetHeight.toFloat())) {
        // Image is wider than target aspect ratio
        width = targetWidth
        height = (targetWidth / aspectRatio).toInt()
    } else {
        // Image is taller than target aspect ratio
        height = targetHeight
        width = (targetHeight * aspectRatio).toInt()
    }

    // Resize the bitmap
    val resizedBitmap = originalBitmap.scale(width, height)

    // Convert the resized bitmap to Base64
    val outputStream = ByteArrayOutputStream()
    resizedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)

    val byteArray = outputStream.toByteArray()
    return Base64.encodeToString(byteArray, Base64.DEFAULT)
}

fun getFileNameFromUri(context: Context, uri: Uri): String? {
    var result: String? = null
    if (uri.scheme == "content") {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            if (it.moveToFirst()) {
                val displayNameIndex = it.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    result = it.getString(displayNameIndex)
                }
            }
        }
    }
    if (result == null) {
        // If the query didn't work, try using the path
        result = uri.path
        val cut = result?.lastIndexOf('/')
        if (cut != -1) {
            result = result?.substring(cut!! + 1)
        }
    }
    return result
}

fun isFileSizeValid(context: Context, uri: Uri, sizeMB: Int = 5): Boolean {
    val contentResolver = context.contentResolver

    return try {
        contentResolver.openFileDescriptor(uri, "r")?.use { fileDescriptor ->
            val fileSize = fileDescriptor.statSize
            // Check if file size < 10MB (10 * 1024 * 1024 bytes)
            fileSize < sizeMB * 1024 * 1024
        } ?: false
    } catch (e: IOException) {
        e.printStackTrace()
        false
    }
}

fun convertFileToBase64(context: Context, uri: Uri): String? {
    val contentResolver = context.contentResolver

    return try {
        contentResolver.openInputStream(uri)?.use { inputStream ->
            val bytes = inputStream.readBytes()
            Base64.encodeToString(bytes, Base64.DEFAULT)
        }
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun getFileType(context: Context, uri: Uri): String? {
    val contentResolver = context.contentResolver
    return contentResolver.getType(uri)
}

fun Uri?.toBitmap(context: Context?): Bitmap? {
    if (this == null) return null
    return try {
        val inputStream: InputStream? = context?.contentResolver?.openInputStream(this)
        BitmapFactory.decodeStream(inputStream)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun Bitmap?.toUri(context: Context): Uri? {
    if (this == null) return null
    return try {
        val bytes = ByteArrayOutputStream()
        this.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        val path = MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            null,
        )
        Uri.parse(path)
    } catch (e: Exception) {
        null
    }
}

fun Bitmap?.addImageToGallery(context: Context) {
    try {
        val bytes = ByteArrayOutputStream()
        this?.compress(Bitmap.CompressFormat.JPEG, 100, bytes)
        MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            "",
        )
    } catch (e: Exception) {
        MediaStore.Images.Media.insertImage(
            context.contentResolver,
            this,
            "Vietin_QR" + System.currentTimeMillis(),
            "",
        )
    }

//    if (isShowToast) VtbToastView(
//        context,
//        VtbToastStatus.SUCCESS,
//        context.getString(R.string.vtb_ra_save_success),
//    )
}

fun Activity?.isInstalledApp(uri: String): Boolean {
    var installedApp = false
    try {
        this?.packageManager?.getPackageInfo(uri, 1)
        installedApp = true
    } catch (_: Exception) {
    }
    return installedApp
}

fun saveBase64File(context: Context, base64Data: String, fileName: String): Result<File> {
    return try {
        val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)

        val directory = context.getExternalFilesDir(null)
            ?: throw NullPointerException("Không tìm được thư mục lưu file")

        val file = File(directory, fileName)

        FileOutputStream(file).use { it.write(decodedBytes) }

        Result.success(file)
    } catch (e: IllegalArgumentException) {
        return Result.failure(IllegalArgumentException("Dữ liệu base64 không hợp lệ", e))
    } catch (e: NullPointerException) {
        return Result.failure(e)
    } catch (e: IOException) {
        return Result.failure(IOException("Không thể ghi dữ liệu vào file", e))
    } catch (e: Exception) {
        return Result.failure(RuntimeException("Lỗi không xác định khi lưu file", e))
    }
}

/**
 * Chuyển Bitmap -> Base64 (có resize + nén) và trả về Result<String>
 */
fun bitmapToBase64Result(
    bitmap: Bitmap,
    maxWidth: Int = 300,
    maxHeight: Int = 300,
    quality: Int = 70,
    format: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG,
): Result<String> {
    try {
        // 1) Validate: KHÔNG throw, trả Result.failure ngay
        if (maxWidth <= 0 || maxHeight <= 0) {
            return Result.failure(
                IllegalArgumentException("Kích thước tối đa phải > 0 (maxWidth=$maxWidth, maxHeight=$maxHeight)"),
            )
        }
        if (quality !in 0..100) {
            return Result.failure(
                IllegalArgumentException("quality phải nằm trong [0..100], current=$quality"),
            )
        }
        if (bitmap.width <= 0 || bitmap.height <= 0) {
            return Result.failure(
                IllegalArgumentException("Bitmap không hợp lệ: width=${bitmap.width}, height=${bitmap.height}"),
            )
        }

        // 2) Resize (propagate lỗi từ Result của bạn)
        val resized = resizeBitmapResult(bitmap, maxWidth, maxHeight)
            .getOrElse { err -> return Result.failure(err) }

        var recycleCandidate: Bitmap? = null
        var recycleError: Throwable? = null

        // 3) Nén + encode trong try/finally; KHÔNG throw trong flow chính
        val result: Result<String> = try {
            // chỉ đánh dấu để recycle nếu là bitmap tạm và không phải HARDWARE
            val shouldRecycle = resized !== bitmap &&
                !(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && resized.config == Bitmap.Config.HARDWARE)
            recycleCandidate = if (shouldRecycle) resized else null

            ByteArrayOutputStream().use { os ->
                val ok = resized.compress(format, quality, os)
                if (!ok) {
                    Result.failure(IOException("Không thể nén bitmap với format=$format và quality=$quality"))
                } else {
                    val base64 = Base64.encodeToString(os.toByteArray(), Base64.NO_WRAP)
                    Result.success(base64)
                }
            }
        } finally {
            // 4) Recycle bitmap tạm; nếu lỗi → ghi lại để fail sau
            val rc = recycleCandidate
            if (rc != null && !rc.isRecycled) {
                try {
                    rc.recycle()
                } catch (t: Throwable) {
                    recycleError = t
                }
            }
        }

        // 5) Nếu recycle lỗi → fail luôn (giữ nguyên behavior nghiêm ngặt)
        if (recycleError != null) {
            val rc = recycleCandidate
            val details = if (rc != null) "size=${rc.width}x${rc.height}, config=${rc.config}" else "unknown"
            return Result.failure(
                IllegalStateException("Recycle bitmap tạm thất bại ($details).", recycleError),
            )
        }

        return result
    } catch (e: IllegalArgumentException) {
        return Result.failure(IllegalArgumentException("Tham số không hợp lệ: ${e.message}", e))
    } catch (e: NullPointerException) {
        return Result.failure(NullPointerException("Dữ liệu null không hợp lệ: ${e.message}"))
    } catch (e: IOException) {
        return Result.failure(IOException("Lỗi IO khi nén/ghi dữ liệu ảnh: ${e.message}", e))
    } catch (e: Exception) {
        return Result.failure(RuntimeException("Lỗi không xác định khi chuyển Bitmap sang Base64", e))
    }
}

/**
 * Resize bitmap theo tỉ lệ, trả về Result<Bitmap> để báo lỗi rõ ràng.
 * Nếu kích thước tính được trùng với ảnh gốc thì trả luôn ảnh gốc (không tạo bitmap mới).
 */
private fun resizeBitmapResult(
    bitmap: Bitmap,
    maxWidth: Int,
    maxHeight: Int,
): Result<Bitmap> {
    try {
        // 1) Validate: KHÔNG throw
        if (maxWidth <= 0 || maxHeight <= 0) {
            return Result.failure(IllegalArgumentException("maxWidth/maxHeight phải > 0"))
        }
        val w = bitmap.width
        val h = bitmap.height
        if (w <= 0 || h <= 0) {
            return Result.failure(IllegalArgumentException("Bitmap không hợp lệ: width=$w, height=$h"))
        }

        // 2) Nếu là HARDWARE => copy sang ARGB_8888 (immutable) để tránh sự cố khi xử lý
        val source: Bitmap = if (
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.O &&
            bitmap.config == Bitmap.Config.HARDWARE
        ) {
            val copied = bitmap.copy(Bitmap.Config.ARGB_8888, false)
            if (copied == null) {
                return Result.failure(IllegalStateException("Không thể copy bitmap HARDWARE sang ARGB_8888"))
            }
            copied
        } else {
            bitmap
        }

        // 3) Tính kích thước mới
        val ratio = w.toFloat() / h.toFloat()
        val (newW, newH) = if (ratio > 1f) {
            // Landscape: khống chế theo maxWidth
            max(1, maxWidth) to max(1, (maxWidth / ratio).roundToInt())
        } else {
            // Portrait/square: khống chế theo maxHeight
            max(1, (maxHeight * ratio).roundToInt()) to max(1, maxHeight)
        }

        // 4) Không cần scale nếu kích thước giữ nguyên
        if (newW == w && newH == h) {
            // Lưu ý: nếu vừa copy từ HARDWARE, 'source' là instance mới (điều này là mong muốn)
            return Result.success(source)
        }

        // 5) Tạo bitmap đã scale
        val scaled = Bitmap.createScaledBitmap(source, newW, newH, true)
        return Result.success(scaled)
    } catch (e: IllegalArgumentException) {
        return Result.failure(IllegalArgumentException("Tham số resize không hợp lệ: ${e.message}", e))
    } catch (e: NullPointerException) {
        return Result.failure(NullPointerException("Bitmap null hoặc dữ liệu không hợp lệ: ${e.message}"))
    } catch (e: Exception) {
        return Result.failure(RuntimeException("Lỗi không xác định khi resize bitmap", e))
    }
}

fun String.buildBoldText(
    boldWords: List<String>,
    colorHighlighted: Color? = null,
): AnnotatedString {
    return buildAnnotatedString {
        append(this@buildBoldText)
        boldWords.forEach { word ->
            // Dùng Regex để tìm tất cả vị trí xuất hiện (nếu có lặp lại)
            Regex(Regex.escape(word)).findAll(this@buildBoldText).forEach { match ->
                addStyle(
                    style = colorHighlighted?.let {
                        SpanStyle(fontWeight = FontWeight.Bold, color = it)
                    } ?: SpanStyle(fontWeight = FontWeight.Bold),
                    start = match.range.first,
                    end = match.range.last + 1,
                )
            }
        }
    }
}

@Suppress("DEPRECATION")
inline fun <reified T : Parcelable> Bundle?.getParcelableArrayListCustom(key: String): List<T>? {
    return try {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            this?.getParcelableArrayList(key, T::class.java)
        } else {
            this?.getParcelableArrayList(key)
        }
    } catch (_: Exception) {
        null
    }
}

@Suppress("DEPRECATION")
inline fun <reified T : Parcelable> Bundle?.getParcelableCustom(key: String): T? {
    return try {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            this?.getParcelable(key, T::class.java)
        } else {
            this?.getParcelable(key)
        }
    } catch (_: Exception) {
        null
    }
}

fun String.formatAsMoney(): String {
    val decimalIndex = this.indexOf(".")
    val trimmed = if (decimalIndex != -1 && decimalIndex + 3 <= this.length) {
        this.substring(0, decimalIndex + 3) // lấy phần nguyên + . + 2 số
    } else {
        this
    }
    val number = trimmed.toDoubleOrNull() ?: return this
    val rounded = round(number * 100) / 100
    val formatter = DecimalFormat(Tags.HOME_MONEY_PATTERN, DecimalFormatSymbols(Locale.US))
    return formatter.format(rounded)
}

fun Int.toMMSS(): String {
    val minutes = this / 60
    val seconds = this % 60
    return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
}

fun getRemainingSeconds(minute: Int, initialTime: Long): Int {
    val totalSeconds = minute * 60 // 5 phút = 300 giây
    val elapsedSeconds = ((System.currentTimeMillis() - initialTime) / 1000).toInt()
    val remaining = totalSeconds - elapsedSeconds
    return remaining.coerceAtLeast(0) // tránh âm
}

fun String.removeVietnameseDiacritics(): String {
    val toneMap = mapOf(
        'á' to "as", 'à' to "af", 'ả' to "ar", 'ã' to "ax", 'ạ' to "aj",
        'ấ' to "aas", 'ầ' to "aaf", 'ẩ' to "aar", 'ẫ' to "aax", 'ậ' to "aaj",
        'ắ' to "aws", 'ằ' to "awf", 'ẳ' to "awr", 'ẵ' to "awx", 'ặ' to "awj",
        'é' to "es", 'è' to "ef", 'ẻ' to "er", 'ẽ' to "ex", 'ẹ' to "ej",
        'ế' to "ees", 'ề' to "eef", 'ể' to "eer", 'ễ' to "eex", 'ệ' to "eej",
        'í' to "is", 'ì' to "if", 'ỉ' to "ir", 'ĩ' to "ix", 'ị' to "ij",
        'ó' to "os", 'ò' to "of", 'ỏ' to "or", 'õ' to "ox", 'ọ' to "oj",
        'ố' to "oos", 'ồ' to "oof", 'ổ' to "oor", 'ỗ' to "oox", 'ộ' to "ooj",
        'ớ' to "ows", 'ờ' to "owf", 'ở' to "owr", 'ỡ' to "owx", 'ợ' to "owj",
        'ú' to "us", 'ù' to "uf", 'ủ' to "ur", 'ũ' to "ux", 'ụ' to "uj",
        'ứ' to "uws", 'ừ' to "uwf", 'ử' to "uwr", 'ữ' to "uwx", 'ự' to "uwj",
        'ý' to "ys", 'ỳ' to "yf", 'ỷ' to "yr", 'ỹ' to "yx", 'ỵ' to "yj",
    )

    return this.map { ch -> toneMap[ch] ?: ch.toString() }
        .joinToString("")
}
