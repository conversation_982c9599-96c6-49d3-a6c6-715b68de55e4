package com.vietinbank.core_common.nav

import android.os.Bundle

/**
 * Created by vand<PERSON> on 27/2/25.
 */
interface IAppNavigator {
    fun goToHome()
    fun popToHomeFromAccount()
    fun goToHomeTest()
    fun goToEditShortcut()
    fun goToAccountFromLogin()
    fun goToAccountNoAnm()
    fun goToLogin()
    fun goToLoginForNewAccount()
    fun goToLoginWithSelectedUser(username: String, fullName: String?, corpName: String?)
    fun goToLoginAndPopAll(sessionExpiredMessage: String? = null)
    fun goToLoginFromSplash()
    fun goToHomePreLogin()
    fun goToHomePreLoginFromSplash()
    fun goToHomeFrom2FA()
    fun goToHomePreLoginAndPopAll()
    fun goToActive()
    fun goToApprovalList(
        transaction: String,
        tranType: String,
        groupType: String,
        serviceType: String,
        isBatchFileMode: Boolean,
        countTransaction: String? = null,
    )

    fun popToApprovalList(clearBackStack: Boolean = true)

    fun goToInitTransaction(transaction: String, multipleType: String?)
    fun goToConfirmChecker(
        transaction: String,
        paymentMethod: String,
        confirmType: String,
        dataType: String,
    )

    fun goToConfirmOTP(
        transaction: String,
        transactionID: String,
        originalTransaction: String,
        token: String,
        step: Int,
        nextApprover: String,
        confirmType: String,
        mtId: String,
        ekycId: String = "",
    )

    fun goToConfirmKeypass(
        challengeCode: String,
        originalTransaction: String,
        confirmType: String,
        transaction: String,
        nextApprover: String,
        mtId: String,
        ekycId: String = "",
    )

    fun goToConfirmKeypassDialog(
        challengeCode: String,
    )

    fun gotoSuccessChecker(successList: String, confirmRes: String, confirmType: String, message: String)
    fun navigateUp() // optional
    fun popBackStack() // optional
    fun goToMakerTransfer(bundle: Bundle)
    fun goToMakerPaymentOrder(bundle: Bundle)
    fun goToMakerTransferBankChoosed(typeTransfer: String, itemStringObj: String)
    fun goToMakerTransferCard(typeTransfer: String)
    fun goToMakerTransferConfirm(typeTransfer: String, validateItem: String)
    fun goToMakerTransferResult(typeTransfer: String, confirmItem: String, resultItem: String)
    fun goToPaymentOrderFragment() // lệnh chi
    fun goToMakerResultTransferFragment(bundle: Bundle)
    fun goToActive2FAStep1Fragment(bundle: Bundle)
    fun goToActive2FAStep2Fragment(bundle: Bundle)

    fun setFragmentResult(requestKey: String, result: Bundle)

    fun gotoKeypassMain()
    fun gotoActivSoft()
    fun goToForgotKeypass()
    fun gotToForgotSoftOTP()
    fun goToEnterPIN(flowPin: String) // nhap pin
    fun goToLockSoftOTPDialog()
    fun gotoHomeSoft()

    // quan ly giao dich
    fun gotoTransactionManager(bundle: Bundle)
    fun gotoFilterTransaction()
    fun goToinquiryApproverListFragment(bundle: Bundle) // tra soát

    // van tin tai khoan
    fun goToInquiryAccount(inquiryAccount: String)

    // tra soat
    fun goToTracePaymentFragment(bundle: Bundle)
    fun goToTracePaymentDetailFragment(bundle: Bundle)
    fun goToTracePaymentComfirmFragment(bundle: Bundle)

    // bao cao giao dich
    fun goToDashBoardReportFragment(bundle: Bundle)
    fun gotoListRejectTransferDetailFragment(bundle: Bundle)

    // dang ky smart CA
    fun goToListSmartCA()
    fun goToDetailSmartCA(mtID: String)
    fun goToRegisterSmartCA()
    fun goToConfirmSmartCA(transactionBundle: String, dataType: String)
    fun goToResultSmartCA(transactionBundle: String)

    // giai ngan bao lanh
    fun goToDisbursement()

    // Quan ly ott
    fun goToOttDashboard()
    fun goToDashboardFeatureOTT(flowSetting: String)
    fun goToOttDashboard(messageId: String? = null)
    fun goToRegisterOTT(tag: String, flowSetting: String)
    fun goToValidateOTT(tag: String, alertType: String? = null)
    fun goToConfirmOTT()
    fun goToConfirmOTTLoan()
    fun goToConfirmSmsTransform(alertType: String, tagRoute: String)
    fun goToTransactionStatus()

    // ekyc
    fun gotoUpdateEkyc()
    fun gotoUpdateEkycUserInfo()
    fun goToConfirmUpdateEkycScreen()
    fun goToRegisterEkycScreen()

    fun goToSetting()
    fun goToAccountBalanceSetting(accountType: String)
    fun goToOttServiceManage()

    // scan qr transfer
    fun goToMakerQRTransferFragment()

    // lock user
    fun goToLockAccount(bundle: Bundle?)
    fun goToLockConfirmAccount(lockTransaction: String)
    fun goToLockResultAccount(bundle: String?)
    fun popToLogin(clearBackStack: Boolean)

    // duyet nhieu giao dich
    fun goToMultipleApprovalList(bundle: Bundle?)
    fun goToMultipleConfirmApproval(bundle: Bundle?)
    fun goToMultipleResultApproval(bundle: Bundle?)
}
