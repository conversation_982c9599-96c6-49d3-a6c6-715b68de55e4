package com.vietinbank.core_common.utils

/**
 * Created by van<PERSON><PERSON> on 19/12/24.
 */
import android.content.ActivityNotFoundException
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.widget.Toast
import androidx.core.content.FileProvider
import androidx.core.content.res.ResourcesCompat
import androidx.core.net.toUri
import com.google.gson.Gson
import com.vietinbank.core_common.R
import java.io.File
import java.io.FileOutputStream
import java.text.Normalizer
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object Utils {
    private val gson: Gson by lazy { Gson() }

    fun g(): Utils {
        return this
    }

    fun provideGson(): Gson {
        return gson
    }

    fun getDPtoPX(context: Context, dp: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            context.resources.displayMetrics,
        ).toInt()
    }

    fun removeAccent(str: String): String {
        var str = str
        if (TextUtils.isEmpty(str)) return ""
        str = str.replace("Đ", "D")
        str = str.replace("[ỲÝỴỶỸ]".toRegex(), "Y")
        str = str.replace("[ÙÚỤỦŨƯỪỨỰỬỮ]".toRegex(), "U")
        str = str.replace("[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]".toRegex(), "O")
        str = str.replace("[ÌÍỊỈĨ]".toRegex(), "I")
        str = str.replace("[ÈÉẸẺẼÊỀẾỆỂỄ]".toRegex(), "E")
        str = str.replace("[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]".toRegex(), "A")
        str = str.replace("[àáạảãâầấậẩẫăằắặẳẵ]".toRegex(), "a")
        str = str.replace("[éèẽẹêẻềếệểễ]".toRegex(), "e")
        str = str.replace("[ìíịỉĩ]".toRegex(), "i")
        str = str.replace("[òóọỏõôồốộổỗơờớợởỡ]".toRegex(), "o")
        str = str.replace("[ùúụủũưừứựửữ]".toRegex(), "u")
        str = str.replace("[ỳýỵỷỹ]".toRegex(), "y")
        str = str.replace("đ", "d")
        return str
    }

    private var FONT_REGULAR: Typeface? = null
    private var FONT_SEMI_BOLD: Typeface? = null
    private var FONT_SEMI_BOLD_ITALIC: Typeface? = null
    private var FONT_HEAVY_ITALIC: Typeface? = null
    private var FONT_HEAVY_NORMAL: Typeface? = null
    private var FONT_MEDIUM: Typeface? = null
    private var FONT_REGULAR_BOLD: Typeface? = null
    private var FONT_X_BOLD: Typeface? = null

    fun getFont(type: Int?, context: Context?): Typeface? {
        if (null == context) return null
        try {
            when (type) {
                2 -> {
                    if (FONT_SEMI_BOLD == null) {
                        FONT_SEMI_BOLD = ResourcesCompat.getFont(context, R.font.semibold)
                    }
                    return FONT_SEMI_BOLD
                }

                3 -> {
                    if (FONT_HEAVY_ITALIC == null) {
                        FONT_HEAVY_ITALIC = ResourcesCompat.getFont(context, R.font.heavyitalic)
                    }
                    return FONT_HEAVY_ITALIC
                }

                4 -> {
                    if (FONT_MEDIUM == null) {
                        FONT_MEDIUM = ResourcesCompat.getFont(context, R.font.medium)
                    }
                    return FONT_MEDIUM
                }

                5 -> {
                    if (FONT_REGULAR_BOLD == null) {
                        FONT_REGULAR_BOLD = ResourcesCompat.getFont(context, R.font.bold)
                    }
                    return FONT_REGULAR_BOLD
                }

                6 -> {
                    if (FONT_X_BOLD == null) {
                        FONT_X_BOLD = ResourcesCompat.getFont(context, R.font.xbold)
                    }
                    return FONT_X_BOLD
                }

                7 -> {
                    if (FONT_SEMI_BOLD_ITALIC == null) {
                        FONT_SEMI_BOLD_ITALIC =
                            ResourcesCompat.getFont(context, R.font.semibolditalic)
                    }
                    return FONT_SEMI_BOLD_ITALIC
                }

                8 -> {
                    if (FONT_HEAVY_NORMAL == null) {
                        FONT_HEAVY_NORMAL = ResourcesCompat.getFont(context, R.font.heavy)
                    }
                    return FONT_HEAVY_NORMAL
                }

                else -> {
                    if (FONT_REGULAR == null) {
                        FONT_REGULAR = ResourcesCompat.getFont(context, R.font.regular)
                    }
                    return FONT_REGULAR
                }
            }
        } catch (_: Exception) {
        }
        return null
    }

    fun getDotMoneyHasCcy(str: String, ccy: String): String {
        return if (!TextUtils.isEmpty(ccy)) {
            getDotMoney(str, ",") + " " + ccy
        } else {
            getDotMoney(
                str,
                ",",
            )
        }
    }

    fun getDotMoney(str: String?): String {
        return getDotMoney(str, ",") // sẽ dùng . phân cách hàng thập phân
    }

    fun getDotMoney(str: String?, dot: String): String {
        val str = str ?: ""
        var rs = str
        try {
            val roundStr = str.split(".")
            if (roundStr.size > 1 && roundStr[1].toLong() == 0L) {
                rs = roundStr[0]
            }
            val splitCcy: String
            val temp: Array<String>
            if ("." == dot) {
                splitCcy = ","
                temp = rs.split(",".toRegex()).toTypedArray()
            } else {
                splitCcy = "."
                temp = rs.split("\\.".toRegex()).toTypedArray()
            }
            return if (temp.size < 2) {
                rs.replace("[^\\d-]".toRegex(), "")
                    .replace("\\B(?=(\\d{3})+(?!\\d))".toRegex(), dot)
            } else {
                temp[0].replace("[^\\d-]".toRegex(), "")
                    .replace("\\B(?=(\\d{3})+(?!\\d))".toRegex(), dot) + splitCcy + temp[1]
            }
        } catch (e: java.lang.Exception) {
            Log.wtf("EX", e)
        }
        return rs
    }

    fun saveLayoutToGallery(view: View, context: Context) {
        // Create bitmap and canvas (same as before)
        val originalWidth = view.width
        val originalHeight = view.height
        val maxWidth = 1920
        val maxHeight = 1080
        var scaledWidth = originalWidth
        var scaledHeight = originalHeight
        if (originalWidth > maxWidth || originalHeight > maxHeight) {
            val widthScale = maxWidth.toFloat() / originalWidth.toFloat()
            val heightScale = maxHeight.toFloat() / originalHeight.toFloat()
            val scaleFactor = Math.min(widthScale, heightScale)
            scaledWidth = (originalWidth * scaleFactor).toInt()
            scaledHeight = (originalHeight * scaleFactor).toInt()
        }
        val bitmap = Bitmap.createBitmap(scaledWidth, scaledHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        if (scaledWidth != originalWidth || scaledHeight != originalHeight) {
            val scaleX = scaledWidth.toFloat() / originalWidth.toFloat()
            val scaleY = scaledHeight.toFloat() / originalHeight.toFloat()
            val matrix = Matrix()
            matrix.setScale(scaleX, scaleY)
            canvas.setMatrix(matrix)
        }
        view.draw(canvas)

        // Get current time in milliseconds
        val currentTimeMillis = System.currentTimeMillis()

        // Create formatted date string for filename
        val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
        val formattedDate = dateFormat.format(Date(currentTimeMillis))
        val fileName = "Screenshot_$formattedDate.jpg"

        // Save to gallery with proper date metadata
        val resolver = context.contentResolver
        val contentValues = ContentValues().apply {
            put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
            // Add date metadata
            put(MediaStore.Images.Media.DATE_ADDED, currentTimeMillis / 1000)
            put(MediaStore.Images.Media.DATE_TAKEN, currentTimeMillis)
            put(MediaStore.Images.Media.DATE_MODIFIED, currentTimeMillis / 1000)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
            }
        }
        val imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

        try {
            imageUri?.let { uri ->
                resolver.openOutputStream(uri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
                Toast.makeText(context, "Đã lưu xuống thư viện", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(context, "Lưu không thành công: ${e.message}", Toast.LENGTH_SHORT).show()
            e.printStackTrace()
        }
    }

    fun shareLayoutAsImage(view: View, context: Context) {
        val originalWidth = view.width
        val originalHeight = view.height
        val maxWidth = 1920
        val maxHeight = 1080
        var scaledWidth = originalWidth
        var scaledHeight = originalHeight
        if (originalWidth > maxWidth || originalHeight > maxHeight) {
            val widthScale = maxWidth.toFloat() / originalWidth.toFloat()
            val heightScale = maxHeight.toFloat() / originalHeight.toFloat()
            val scaleFactor = Math.min(widthScale, heightScale)
            scaledWidth = (originalWidth * scaleFactor).toInt()
            scaledHeight = (originalHeight * scaleFactor).toInt()
        }
        val bitmap = Bitmap.createBitmap(scaledWidth, scaledHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        if (scaledWidth != originalWidth || scaledHeight != originalHeight) {
            val scaleX = scaledWidth.toFloat() / originalWidth.toFloat()
            val scaleY = scaledHeight.toFloat() / originalHeight.toFloat()
            val matrix = Matrix()
            matrix.setScale(scaleX, scaleY)
            canvas.setMatrix(matrix)
        }
        view.draw(canvas)
        try {
            val cachePath = File(context.cacheDir, "images")
            cachePath.mkdirs()
            val file = File(cachePath, "shared_image.jpg")
            val outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
            outputStream.flush()
            outputStream.close()
            val fileUri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.provider",
                file,
            )
            val intent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, fileUri)
                type = "image/jpeg"
                flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            }
            context.startActivity(Intent.createChooser(intent, "Share Image"))
        } catch (e: Exception) {
            Toast.makeText(context, "Sẩy ra lỗi khi chia sẻ ảnh: ${e.message}", Toast.LENGTH_SHORT)
                .show()
            e.printStackTrace()
        }
    }

    fun asciiOnlyFilter(): InputFilter {
        return InputFilter { source, _, _, _, _, _ ->
            val normalized = Normalizer.normalize(source, Normalizer.Form.NFKD)
            val noAccents = normalized.replace(Regex("\\p{InCombiningDiacriticalMarks}+"), "")
            return@InputFilter noAccents.filter { it.code in 32..126 }
        }
    }

    fun openAppInPlayStore(context: Context, packageName: String) {
        try {
            // Cố gắng mở bằng app CH Play (nếu có cài trong máy)
            val intent = Intent(Intent.ACTION_VIEW, "market://details?id=$packageName".toUri())
            intent.setPackage("com.android.vending")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            // Nếu khong có CH Play thì mở bằng trình duyệt
            val intent = Intent(
                Intent.ACTION_VIEW,
                "https://play.google.com/store/apps/details?id=$packageName".toUri(),
            )
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        }
    }

    private val MIME_TYPE_MAP = mapOf(
        "pdf" to "application/pdf",
        "xlsx" to "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "xls" to "application/vnd.ms-excel",
        "docx" to "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "doc" to "application/msword",
        "ppt" to "application/vnd.ms-powerpoint",
        "pptx" to "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "txt" to "text/plain",
        "csv" to "text/csv",
        "jpg" to "image/jpeg",
        "jpeg" to "image/jpeg",
        "png" to "image/png",
        "gif" to "image/gif",
    )

    fun getMimeType(file: File): String {
        val extension = file.extension.lowercase()
        return MIME_TYPE_MAP[extension] ?: "*/*"
    }

    fun getFileUri(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            file,
        )
    }

//    // dung khi muon lay duong dan file cu the
//    fun getUriForFile(context: Context, file: File?): Uri? {
//        if (file == null) return null
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            val packageName = context.applicationContext.packageName
//            return FileProvider.getUriForFile(context, "$packageName.provider", file)
//        } else {
//            return Uri.fromFile(file)
//        }
//    }
//
//    // tao file anh
//    fun createImageFile(context: Context): File? {
//        try {
//            val timeStamp: String = todayAsString("yyyyMMdd_HHmmss")
//            val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
//            return File.createTempFile(
//                "JPEG_${timeStamp}_", /* prefix */
//                ".jpg", /* suffix */
//                storageDir, /* directory */
//            ).apply {
//                // Save a file: path for use with ACTION_VIEW intents
//              currentPhotoPath = absolutePath
//            }
//        } catch (_: Exception) {
//        } // Create an image file name
//        return null
//    }
}
