package com.vietinbank.core_domain.models.ott_feature

class ListRegRequestParams(
    val alertMethod: String,
    val isVacct: String,
    val mobileNumber: String,
    val roleId: String,
    val tranId: String,
    val typeCheck: String,
    val username: String,
    val type: String = "",
    val cifno: String = "",
)

data class ListRegDomains(
    val encryptKey: String?,
    val encryptType: String?,
    val fees: List<FeeItemDomains>?,
    val results: List<ResultItemDomains>?,
)

data class FeeItemDomains(
    val alertType: String,
    val alertMethod: String,
    val feeType: String,
    val custType: String,
    val feeAmount: String,
    val currCode: String,
    val feeName: String,
)
data class ResultItemDomains(
    val position: String? = "",
    val customerNumber: String?,
    val accountNumber: String?,
    val currency: String?,
    val alertAddr: String?,
    val alertType: String?,
    val alertMethod: String?,
    val feeAccountNumber: String?,
    val feeAmount: String?,
    val feeType: String?,
    val startDate: String?,
    val action: String?,
    val accountName: String?,
    val username: String?,
    val isVacct: String?,
    val phonenumber: String?,
    val status: String?,
    val accountType: String?,
    var isSelected: Boolean = false,
    var branchId: String?,
)
