plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.hilt.android)
    alias(libs.plugins.compose.compiler)
    id('kotlin-kapt')
}

android {
    namespace 'com.vietinbank.core_ott'
    compileSdk libs.versions.compileSdk.get().toInteger()

    defaultConfig {
        minSdkVersion libs.versions.minSdk.get().toInteger()
        targetSdkVersion libs.versions.targetSdk.get().toInteger()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildFeatures {
        viewBinding true // Bật ViewBinding
        compose true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {
    implementation project(':core-common')
    implementation project(':core-data')
    implementation project(':core-domain')
    implementation project(':core-ui')

    implementation libs.androidx.runtime.livedata

    implementation(libs.coroutines.stdlib)
    implementation(libs.gson)

    // Sử dụng BOM cho Compose
    def composeBom = platform(libs.compose.bom)
    implementation composeBom
    androidTestImplementation composeBom

    // Thư viện Compose cơ bản
    implementation libs.compose.ui
    implementation libs.compose.ui.tooling.preview
    debugImplementation libs.compose.ui.tooling
    implementation libs.androidx.material3

    // Coroutines
    implementation(libs.coroutines.core)
    implementation(libs.coroutines.android)

    // UI Testing
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.test.manifest

    // Tích hợp với AndroidX
    implementation libs.androidx.activity.compose
    implementation libs.androidx.lifecycle.viewmodel.compose

    // AndroidX
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)

    // Lifecycle
    implementation(libs.lifecycle.livedata)
    implementation(libs.lifecycle.viewmodel)

    // Hilt
    implementation(libs.hilt.android)
    implementation libs.androidx.legacy.support.v4
    implementation libs.androidx.fragment.ktx
    kapt(libs.hilt.compiler)

    // Material Design
    implementation(libs.material)

    // Navigation Component
    implementation(libs.navigation.fragment)
    implementation(libs.navigation.ui)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.messaging)

    //okhttp
    implementation(libs.okhttp)
    implementation(libs.logging.interceptor)

    //socket io
    implementation 'io.socket:socket.io-client:2.1.0'

    // Room
    implementation 'androidx.room:room-runtime:2.5.2'
    implementation 'androidx.room:room-ktx:2.5.2'
    kapt 'androidx.room:room-compiler:2.5.2'


}