# Module OTT

Module OTT là thành phần quản lý giao tiếp thời gian thực và thông báo push cho ứng dụng eFast của
Vietinbank. Module này được phát triển theo nguyên tắc Clean Architecture với mục tiêu tăng cường
khả năng bảo trì và mở rộng.

## <PERSON><PERSON><PERSON> lụ<PERSON>

- [Tổng quan](#tổng-quan)
- [Kiến trúc](#kiến-trúc)
- [Tính năng chính](#tính-năng-chính)
- [Cài đặt](#cài-đặt)
- [Sử dụng](#sử-dụng)
- [C<PERSON><PERSON> hình](#cấu-hình)
- [Quyền sử dụng](#quyền-sử-dụng)
- [Phát triển](#phát-triển)

## Tổng quan

Module OTT (Over-The-Top) được thiết kế để:

- Nhận và xử lý thông báo từ Firebase Cloud Messaging (FCM)
- <PERSON><PERSON><PERSON> thị thông báo cho người dùng
- Kết nối Socket.IO để giao tiếp thời gian thực
- Đồng bộ hóa trạng thái tin nhắn giữa các thiết bị
- Quản lý đăng ký device token với server

## Kiến trúc

Module được thiết kế theo nguyên tắc Clean Architecture với 3 layer chính:

### Domain Layer (core-domain)

- **Interfaces**: Các giao diện định nghĩa chức năng của module
- **Models**: Các đối tượng miền được sử dụng trong toàn bộ ứng dụng
- **Use Cases**: Các trường hợp sử dụng nghiệp vụ
- **Repository Interfaces**: Các giao diện repository để giao tiếp với tầng dữ liệu

### Data Layer (core-data)

- **Repository Implementations**: Triển khai các giao diện repository
- **API Services**: Giao tiếp với API server
- **Mappers**: Chuyển đổi giữa DTO và domain models
- **Database**: Lưu trữ cục bộ cho tin nhắn và tokens

### Presentation Layer (core-ott)

- **Firebase Integration**: Xử lý FCM, token, và thông báo
- **WebSocket**: Kết nối Socket.IO và xử lý sự kiện
- **Notifications**: Hiển thị thông báo và quản lý quyền
- **Manager**: Triển khai IOttManager để quản lý toàn bộ module

## Tính năng chính

- **Nhận thông báo FCM**: Xử lý thông báo từ Firebase Cloud Messaging
- **Hiển thị thông báo**: Hỗ trợ hiển thị thông báo dạng heads-up với nội dung phong phú
- **Kết nối Socket.IO**: Thiết lập và duy trì kết nối WebSocket
- **Đồng bộ trạng thái**: Đồng bộ trạng thái tin nhắn qua WebSocket
- **Đăng ký device token**: Đăng ký token thiết bị với server
- **Xử lý quyền thông báo**: Hỗ trợ kiểm tra và yêu cầu quyền POST_NOTIFICATIONS (Android 13+)

## Cài đặt

Thêm module vào project:

```kotlin
// settings.gradle.kts
include(":core-ott")
```

Thêm dependency vào các module khác:

```kotlin
// build.gradle.kts
dependencies {
    implementation(project(":core-ott"))
}
```

## Sử dụng

### Khởi tạo

Khởi tạo OttBuilder trong MainApplication:

```kotlin
@HiltAndroidApp
class MainApplication : Application() {
    @Inject
    lateinit var ottBuilder: OttBuilder
    
    override fun onCreate() {
        super.onCreate()
        // Khởi tạo module OTT
        ottBuilder.install().setCallback(MyOTTCallback())
    }
}
```

### Triển khai Callback

```kotlin
class MyOTTCallback : IOttMessageListener {
    override fun onMessageReceived(message: OttMessage) {
        // Xử lý khi nhận được tin nhắn OTT
    }

    override fun onError(error: Throwable) {
        // Xử lý khi có lỗi
    }
}
```

### Sử dụng OttManager trong ViewModel

```kotlin
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val ottManager: IOttManager
) : ViewModel() {

    fun setupOtt(user: User) {
        viewModelScope.launch {
            val params = IOttManager.OttSetupParams(
                userId = user.id,
                cifNo = user.cifNo,
                phoneNumber = user.phoneNumber,
                username = user.userName,
                channelId = "efast"
            )
            
            when (val result = ottManager.setup(params)) {
                is Resource.Success -> {
                    // Thiết lập thành công
                }
                is Resource.Error -> {
                    // Xử lý lỗi
                }
            }
        }
        
        // Theo dõi trạng thái OTT
        viewModelScope.launch {
            ottManager.ottState.collect { state ->
                when (state) {
                    is IOttManager.OttState.NeedsNotificationPermission -> {
                        // Yêu cầu quyền thông báo
                        ottManager.requestNotificationPermission(activity)
                    }
                    // Xử lý các trạng thái khác
                }
            }
        }
    }
    
    // Đồng bộ trạng thái tin nhắn
    fun markMessageAsRead(messageId: String) {
        viewModelScope.launch {
            ottManager.syncMessageStatus(messageId, OttMessage.STATE_READ)
        }
    }
}
```

## Cấu hình

### Cấu hình Firebase

Thêm Firebase vào project:

1. Thiết lập tài khoản Firebase và tạo project
2. Tải file `google-services.json` và đặt vào thư mục `app/`
3. Thêm Firebase SDK:

```kotlin
// build.gradle.kts (project)
plugins {
    id("com.google.gms.google-services") version "4.4.0" apply false
}

// build.gradle.kts (app)
plugins {
    id("com.google.gms.google-services")
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:33.9.0"))
    implementation("com.google.firebase:firebase-messaging-ktx")
}
```

### Cấu hình Socket.IO

Cấu hình mặc định:

- Base URL: https://webdemo.vietinbank.vn
- Socket.IO path: /socket-decode/socket.io

Để thay đổi cấu hình, gọi:

```kotlin
val configRepository = // Inject ConfigRepository
configRepository.updateConfigFromServer(
    socketBaseUrl = "https://newserver.example.com", 
    socketPath = "/socket.io"
)
```

## Quyền sử dụng

Module yêu cầu các quyền sau trong AndroidManifest.xml:

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

## Phát triển

### Cấu trúc project

```
core-ott/
├── src/main/
│   ├── java/com/vietinbank/core_ott/
│   │   ├── di/                       # Dependency Injection
│   │   ├── firebase/                 # Firebase integration
│   │   ├── initialization/           # Initialization logic
│   │   ├── manager/                  # Main manager implementation
│   │   ├── notification/             # Notification handling
│   │   ├── token/                    # Token management
│   │   ├── utils/                    # Utilities
│   │   └── websocket/                # Socket.IO client
│   └── AndroidManifest.xml
└── build.gradle.kts

core-domain/
└── com/vietinbank/core_domain/
    ├── models/ott/                   # Domain models
    ├── ott/                          # Interfaces
    ├── repository/                   # Repository interfaces
    └── usecase/ott/                  # Use cases

core-data/
└── com/vietinbank/core_data/
    ├── database/ott/                 # Local storage
    ├── di/                           # Data layer DI
    ├── mapper/ott/                   # DTO to domain mappers
    ├── models/                       # Request/response models
    ├── network/api/ott/              # API services
    └── repository/ott/               # Repository implementations
```

### Hiệu suất

- Sử dụng Kotlin Coroutines để xử lý bất đồng bộ
- StateFlow và SharedFlow để expose các trạng thái và sự kiện
- Reconnection tự động cho WebSocket

```