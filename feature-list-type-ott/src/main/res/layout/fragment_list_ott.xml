<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_common_level2"
    tools:context=".list_ott.ListOttFragment">

    <com.vietinbank.feature_list_type_ott.components.FoundationAppBarView
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.vietinbank.feature_list_type_ott.components.InnerShadowWithBorderLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp8"
        app:shadowColor="#8FFFFFFF"
        app:shadowBlur="4dp"
        app:shadowOffsetY="-2dp"
        app:borderWidth="@dimen/dp1"
        app:cornerRadius="32dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvTabSelector"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.vietinbank.feature_list_type_ott.components.InnerShadowWithBorderLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list_ott"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:id="@+id/empty_list_ott_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp60"
            android:contentDescription=""
            android:src="@drawable/ic_ott_no_alert" />

        <com.vietinbank.core_ui.base.views.BaseTextView
            android:id="@+id/tv_empty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/empty_list_ott_text"
            android:textColor="@color/text_gray_07"
            android:textSize="@dimen/sp14" />
    </LinearLayout>
</LinearLayout>