<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_common_level2"
    android:orientation="vertical"
    tools:context=".list_ott.ListOttFragment">

    <com.vietinbank.feature_list_type_ott.components.FoundationAppBarView
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.vietinbank.feature_list_type_ott.components.InnerShadowWithBorderLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp24"
        android:layout_marginVertical="@dimen/dp16"
        android:padding="@dimen/dp8"
        app:enableDualShadow="true">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvTabSelector"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.vietinbank.feature_list_type_ott.components.InnerShadowWithBorderLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_corner_card"
        android:orientation="vertical"
        android:padding="@dimen/dp24">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.vietinbank.core_ui.base.views.BaseEditText
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/rounded_neutral_50"
                android:lineHeight="@dimen/dp24"
                android:paddingHorizontal="@dimen/dp16"
                android:paddingVertical="@dimen/dp8"
                android:textSize="@dimen/sp16"
                app:fontCus="semi_bold"
                app:left="@drawable/ic_ott_search"
                tools:ignore="UnusedAttribute" />

            <Space
                android:layout_width="@dimen/dp16"
                android:layout_height="wrap_content" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Bo loc" />

            <Space
                android:layout_width="@dimen/dp16"
                android:layout_height="wrap_content" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_filter_modify"
                tools:ignore="ContentDescription" />

            <Space
                android:layout_width="@dimen/dp16"
                android:layout_height="wrap_content" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_trash"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list_ott"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/empty_list_ott_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/dp60"
                android:layout_height="@dimen/dp60"
                android:contentDescription=""
                android:src="@drawable/ic_ott_no_alert" />

            <com.vietinbank.core_ui.base.views.BaseTextView
                android:id="@+id/tv_empty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/empty_list_ott_text"
                android:textColor="@color/text_gray_07"
                android:textSize="@dimen/sp14" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>