<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp12"
    android:background="@drawable/rounded_tab_item">

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:paddingHorizontal="@dimen/dp16"
        android:paddingVertical="@dimen/dp8"
        android:id="@+id/tab_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/item_title_menu"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:lineHeight="@dimen/dp16"
        app:fontCus="semi_bold"
        tools:targetApi="28" />

    <com.vietinbank.core_ui.base.views.BaseTextView
        android:id="@+id/tab_badge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="99+"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp10"
        android:lineHeight="@dimen/dp14"
        android:background="@drawable/tab_badge_counter"
        app:fontCus="semi_bold"
        tools:targetApi="28" />
</LinearLayout>
