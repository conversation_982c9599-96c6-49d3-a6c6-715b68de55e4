<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Newcore" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <declare-styleable name="InnerShadowWithBorderLayout">
        <attr name="shadowColor" format="color" />
        <attr name="shadowBlur" format="dimension" />
        <attr name="shadowOffsetX" format="dimension" />
        <attr name="shadowOffsetY" format="dimension" />
        <attr name="cornerRadius" format="dimension" />
        <attr name="borderWidth" format="dimension" />
        <attr name="borderColor" format="color" />
        <attr name="enableDualShadow" format="boolean" />
        <attr name="secondaryShadowColor" format="color" />
        <attr name="secondaryShadowBlur" format="dimension" />
        <attr name="secondaryShadowOffsetX" format="dimension" />
        <attr name="secondaryShadowOffsetY" format="dimension" />
        <attr name="secondaryShadowSpread" format="color" />
    </declare-styleable>
</resources>