package com.vietinbank.feature_list_type_ott.screen.register_ott_feature

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.ott_feature.CheckRegDomains
import com.vietinbank.core_domain.models.ott_feature.CheckRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ItemRegistedInfoDomain
import com.vietinbank.core_domain.models.ott_feature.ListRegDomains
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.OttRegisterDomains
import com.vietinbank.core_domain.models.ott_feature.OttRegisterRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestDomain
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RegisterOTTViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val ottUseCase: OttFeatureUseCase,
    private val ottRegistrationRepository: IOttRegistrationRepository,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    private val _cifNoState = MutableStateFlow("")
    val cifNoState = _cifNoState.asStateFlow()

    private val _phoneNoState = MutableStateFlow("")
    val phoneNoState = _phoneNoState.asStateFlow()

    private val _enableEditCifNoState = MutableStateFlow("N")
    val enableEditCifNoState = _enableEditCifNoState.asStateFlow()

    private val _totalSelectedAmount = MutableStateFlow("0 VND")
    val totalSelectedAmount: StateFlow<String> = _totalSelectedAmount.asStateFlow()

    private val _checkPolicy = MutableStateFlow(false)
    var checkPolicy: StateFlow<Boolean> = _checkPolicy.asStateFlow()

    private val _phoneNoHiddenState = MutableStateFlow<String?>(null)
    val phoneNoHiddenState: StateFlow<String?> = _phoneNoHiddenState.asStateFlow()

    private val _corpName = MutableStateFlow<String?>(null)
    val corpName: StateFlow<String?> = _corpName.asStateFlow()

    private val _checkRegState = SingleLiveEvent<CheckRegDomains>()
    val checkRegState: SingleLiveEvent<CheckRegDomains> get() = _checkRegState

    private val _listRegState = SingleLiveEvent<ListRegDomains>()
    val listRegState: SingleLiveEvent<ListRegDomains> get() = _listRegState

    var listFee = mutableListOf<ResultItemDomains>()
    private val _listRegister = MutableStateFlow<List<ResultItemDomains>>(emptyList())
    val listRegister: StateFlow<List<ResultItemDomains>> = _listRegister.asStateFlow()

    private val _verifySMSState = SingleLiveEvent<SmsOtpVerifyRequestDomain>()
    val verifySMSState: SingleLiveEvent<SmsOtpVerifyRequestDomain> get() = _verifySMSState

    private val _handleErrorOtpCreatetState = SingleLiveEvent<AppException>()
    val handleErrorOtpCreatetState: SingleLiveEvent<AppException> get() = _handleErrorOtpCreatetState

    private val _ottRegResponseState = SingleLiveEvent<OttRegisterDomains>()
    val ottRegResponseState: SingleLiveEvent<OttRegisterDomains> get() = _ottRegResponseState

    private val _otp = MutableStateFlow("")
    val otp = _otp.asStateFlow()

    var tagRoute: String = ""
    var alertType: String = ""
    var settingFlow = ""

    private val _hasItemRegisted = MutableStateFlow(false)
    val hasItemRegisted = _hasItemRegisted.asStateFlow()

    init {
        _cifNoState.value = userProf.getCifNo() ?: ""
        _phoneNoState.value = userProf.getPhoneNo() ?: ""
        _phoneNoHiddenState.value = maskPhoneNumber(userProf.getPhoneNo() ?: "")
        _corpName.value = userProf.getCorpName() ?: ""
    }

    fun checkReg() {
        launchJob(showLoading = true) {
            val listRequest = mutableListOf<ItemRegistedInfoDomain>()
            listRequest.add(
                ItemRegistedInfoDomain(
                    cifno = cifNoState.value,
                    phoneNumber = userProf.getPhoneNo() ?: "",
                    username = userProf.getUserName() ?: "",
                    accountName = userProf.getFullName() ?: "",
                ),
            )
            val params = CheckRegRequestParams(
                registedInfoList = listRequest,
                tranId = "",
                username = userProf.getUserName() ?: "",
                isVacct = "",
            )
            val res = ottUseCase.checkRegister(
                params,
            )
            handleResource(res) { data ->
                _checkRegState.postValue(data)
            }
        }
    }

    fun smsOtpCreate() {
        val isVacct = if (tagRoute == Tags.IDENTITY_ACCOUNT_TYPE_REG) "Y" else ""
        launchJob(showLoading = true) {
            val params = SmsOtpCreateRequestParams(
                accountNumber = "",
                isVacct = isVacct,
                phoneNo = userProf.getPhoneNo() ?: "",
                roleId = "",
                type = "",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.smsOtpCreate(
                params,
            )
            handleResource(res) { data ->
            }
        }
    }

    fun smsOtpVerify() {
        launchJob(showLoading = true) {
            val params = SmsOtpVerifyRequestParams(
                isVacct = "",
                otpNumber = otp.value,
                phoneNo = userProf.getPhoneNo() ?: "",
                roleId = "",
                typeCheck = "Y",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.smsOtpVerify(
                params,
            )
            handleResource(res) { data ->
                _verifySMSState.postValue(data)
            }
        }
    }

    fun listReg() {
        val cachedRegistrations = ottRegistrationRepository.getCachedRegistrations()
        if (cachedRegistrations?.results?.isNotEmpty() == true) {
            updateListWithPositions(cachedRegistrations.results ?: listOf())
            return
        }

        launchJob(showLoading = false) {
            val params = createListRegParams()
            val res = ottUseCase.listReg(params)

            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    viewModelScope.launch(Dispatchers.Default) {
                        _listRegState.postValue(data)
                        updateListWithPositions(data.results ?: listOf())
                    }
                },
            )
        }
    }

    private fun updateListWithPositions(results: List<ResultItemDomains>) {
        listFee = results.mapIndexed { index, item ->
            item.copy(position = index.toString())
        }.toMutableList()
        _listRegister.value = listFee
        hasRegisterItem()
    }

    private fun createListRegParams() = ListRegRequestParams(
        alertMethod = "ALL",
        isVacct = "",
        mobileNumber = userProf.getPhoneNo().orEmpty(),
        roleId = "",
        tranId = "",
        typeCheck = "Y",
        username = userProf.getUserName().orEmpty(),
    )

    data class RegistrationConfig(
        val ottAlertType: String,
        val smsAlertType: String,
    )

    private val STANDARD_REGISTRATION_CONFIG = RegistrationConfig(
        ottAlertType = "159",
        smsAlertType = "89",
    )

    private val LOAN_REGISTRATION_CONFIG = RegistrationConfig(
        ottAlertType = "110",
        smsAlertType = "10",
    )

    fun registerOTT() {
        performRegistration(STANDARD_REGISTRATION_CONFIG)
    }

    fun registerOTTLoan() {
        performRegistration(LOAN_REGISTRATION_CONFIG)
    }

    private fun performRegistration(config: RegistrationConfig) {
        launchJob(showLoading = true) {
            val selectedItems = getSelectedItems()
            val registrationItems = createRegistrationItems(selectedItems, config)
            val unregistrationItems = createUnregistrationItems(selectedItems, config)

            val allItems = (registrationItems + unregistrationItems).toMutableList()

            val params = createOttRegisterParams(allItems)
            val res = ottUseCase.registerOTT(params)

            handleResource(res) { data ->
                clearOttRegistrationCache()
                _ottRegResponseState.postValue(data)
            }
        }
    }

    private fun getSelectedItems() = _listRegister.value.filter { it.isSelected }

    private fun createRegistrationItems(
        selectedItems: List<ResultItemDomains>,
        config: RegistrationConfig,
    ): List<ResultItemDomains> {
        return selectedItems.map { item ->
            item.copy(
                alertType = config.ottAlertType,
                alertMethod = "OTT",
                action = "REG",
                isSelected = false,
            )
        }
    }

    private fun createUnregistrationItems(
        selectedItems: List<ResultItemDomains>,
        config: RegistrationConfig,
    ): List<ResultItemDomains> {
        return selectedItems.map { item ->
            item.copy(
                alertType = config.smsAlertType,
                alertMethod = "SMS",
                action = "UNREG",
                isSelected = false,
            )
        }
    }

    private fun createOttRegisterParams(items: MutableList<ResultItemDomains>) =
        OttRegisterRequestParams(
            isVacct = "",
            mobileNumber = userProf.getPhoneNo().orEmpty(),
            roleId = "",
            tranId = "",
            username = userProf.getUserName().orEmpty(),
            recordsParams = items,
        )

    fun togglePolicyCheck() {
        _checkPolicy.value = !_checkPolicy.value
    }

    fun isPolicyAccepted(): Boolean {
        return _checkPolicy.value
    }

    fun hasRegisterItem() {
        if (listFee.isEmpty()) {
            _hasItemRegisted.value = false
        } else if (listFee.any { it.alertType == "159" }) {
            _hasItemRegisted.value = true
        }
    }

    fun isHasRegisterItem(): Boolean {
        return _hasItemRegisted.value
    }

    fun toggleFeeItemSelection(position: Int) {
        _listRegister.value = _listRegister.value.map { item ->
            if (item.position == position.toString()) {
                item.copy(isSelected = !item.isSelected)
            } else {
                item
            }
        }
        updateTotalSelectedAmount()
    }

    fun clearOttRegistrationCache() {
        // Không cần launch vì clearRegistrations() không còn là suspend
        ottRegistrationRepository.clearRegistrations()
    }

    private fun updateTotalSelectedAmount() {
        val total = _listRegister.value
            .filter { it.isSelected }
            .sumOf { it.feeAmount?.toDoubleOrNull() ?: 0.0 }.toBigDecimal()
        val totalMess = Utils.g().getDotMoneyHasCcy(
            total.toString(),
            "VND",
        )
        _totalSelectedAmount.value = totalMess
    }

    fun updateOTP(newUsername: String) {
        _otp.value = newUsername
    }

    fun updateCifNo(cifNo: String) {
        _cifNoState.value = cifNo
    }

    fun updatePhoneNo(phone: String) {
        _phoneNoState.value = phone
    }

    fun enableEditCifNo() {
        _enableEditCifNoState.value = settingFlow
    }

    fun maskPhoneNumber(phone: String): String {
        val cleanPhone = phone.replace(Regex("[\\s-]"), "")
        return when {
            cleanPhone.length < 4 -> phone
            cleanPhone.length <= 7 -> {
                val start = cleanPhone.take(2)
                val end = cleanPhone.takeLast(2)
                val middle = "x".repeat(cleanPhone.length - 4)
                "$start$middle$end"
            }

            else -> {
                val start = cleanPhone.take(4)
                val end = cleanPhone.takeLast(3)
                val middle = "x".repeat(cleanPhone.length - 7)
                "$start$middle$end"
            }
        }
    }

    fun validateFieldsCheckREG(): String? {
        return when {
            cifNoState.value.isBlank() -> "Số CIF không được bỏ trống"
            _phoneNoState.value?.isBlank() == true -> "Số điện thoại không được bỏ trống. Quý khách vui lòng kiểm tra lại"
            !isValidPhoneNumber(
                _phoneNoState.value ?: "",
            ) -> "Số điện thoại không đúng định dạng. Quý khách vui lòng kiểm tra lại"

            else -> null
        }
    }

    private fun isValidPhoneNumber(phone: String): Boolean {
        val regex = Regex("^0[0-9]{9,10}$")
        return regex.matches(phone)
    }

    fun validateFieldsOTP(): String? {
        return when {
            otp.value.isBlank() -> "Mã kích hoạt không được bỏ trống.  Quý khách vui lòng kiểm tra lại"
            otp.value.length < 6 -> "Mã kích hoạt phải bao gồm 6 ký tự số. Quý khách vui lòng kiểm tra lại"
            else -> null
        }
    }

    fun validateFieldsConfirm(): String? {
        return when {
            !isPolicyAccepted() -> "Bạn chưa đồng ý điều khoản"
            else -> null
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_OTT_SMS_CREATE) {
            if ("0" == exception.code) {
                _handleErrorOtpCreatetState.postValue(exception)
            }
        }
        super.onDisplayErrorMessage(exception)
    }
}