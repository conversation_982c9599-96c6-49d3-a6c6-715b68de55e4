package com.vietinbank.feature_list_type_ott.components

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.TextStyle
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationAppBarScrollState
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class FoundationAppBarView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    private val composeView = ComposeView(context)

    init {
        // Gắn ComposeView vào custom view
        addView(
            composeView,
            LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        )
    }

    fun setContent(
        title: String,
        onNavigationClick: () -> Unit,
        modifier: Modifier = Modifier,
        navigationIcon: Painter? = null,
        isBackHome: Boolean = false,
        actions: List<AppBarAction> = emptyList(),
        customActions: @Composable (() -> Unit)? = null,
        isLightIcon: Boolean = true,
        titleStyle: TextStyle = FDS.Typography.headingH2,
        isSingleLineAppBar: Boolean = false,
        isCustomActionUseSafeClick: Boolean = true,
        scrollState: FoundationAppBarScrollState? = null,
        collapsedTitleStyle: TextStyle = FDS.Typography.headingH3,
        showLogo: Boolean = false,
        logoIcon: Painter? = null
    ) {
        composeView.setContent {
            FoundationAppBar(
                title = title,
                onNavigationClick = onNavigationClick,
                modifier = modifier,
                navigationIcon = navigationIcon,
                isBackHome = isBackHome,
                actions = actions,
                customActions = customActions,
                isLightIcon = isLightIcon,
                titleStyle = titleStyle,
                isSingleLineAppBar = isSingleLineAppBar,
                isCustomActionUseSafeClick = isCustomActionUseSafeClick,
                scrollState = scrollState,
                collapsedTitleStyle = collapsedTitleStyle,
                showLogo = showLogo,
                logoIcon = logoIcon
            )
        }
    }
}
