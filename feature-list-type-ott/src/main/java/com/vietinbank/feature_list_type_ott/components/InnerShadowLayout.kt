package com.vietinbank.feature_list_type_ott.components

import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.FrameLayout
import androidx.core.graphics.toColorInt
import com.vietinbank.feature_list_type_ott.R

class InnerShadowWithBorderLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
): FrameLayout(context, attrs, defStyleAttr) {

    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val secondaryShadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var cornerRadius = 32f.dpToPx()
    private var borderWidth = 1f.dpToPx()
    private var borderColor = "#8FFFFFFF".toColorInt()
    private var shadowColor = "#8FFFFFFF".toColorInt()
    private var shadowBlur = 2f.dpToPx()
    private var shadowOffsetX = (-1f).dpToPx()
    private var shadowOffsetY = (-1f).dpToPx()
    private var shadowSpread = 0f.dpToPx()
    private var enableDualShadow = false
    private var secondaryShadowColor = shadowColor
    private var secondaryShadowBlur = shadowBlur
    private var secondaryShadowOffsetX = -shadowOffsetX
    private var secondaryShadowOffsetY = -shadowOffsetY
    private var secondaryShadowSpread = shadowSpread

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        setWillNotDraw(false)
        setupAttributes(attrs)
        setupPaints()
    }

    private fun setupAttributes(attrs: AttributeSet?) {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(
                it,
                R.styleable.InnerShadowWithBorderLayout
            )
            try {
                shadowColor = typedArray.getColor(
                    R.styleable.InnerShadowWithBorderLayout_shadowColor,
                    shadowColor
                )
                shadowBlur = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_shadowBlur,
                    shadowBlur
                )
                shadowOffsetX = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_shadowOffsetX,
                    shadowOffsetX
                )
                shadowOffsetY = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_shadowOffsetY,
                    shadowOffsetY
                )
                cornerRadius = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_cornerRadius,
                    cornerRadius
                )
                borderWidth = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_borderWidth,
                    borderWidth
                )
                borderColor = typedArray.getColor(
                    R.styleable.InnerShadowWithBorderLayout_borderColor,
                    borderColor
                )

                // Dual shadow attributes (if available)
                enableDualShadow = typedArray.getBoolean(
                    R.styleable.InnerShadowWithBorderLayout_enableDualShadow,
                    enableDualShadow
                )
                secondaryShadowColor = typedArray.getColor(
                    R.styleable.InnerShadowWithBorderLayout_secondaryShadowColor,
                    secondaryShadowColor
                )
                secondaryShadowBlur = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_secondaryShadowBlur,
                    secondaryShadowBlur
                )
                secondaryShadowOffsetX = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_secondaryShadowOffsetX,
                    secondaryShadowOffsetX
                )
                secondaryShadowOffsetY = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_secondaryShadowOffsetY,
                    secondaryShadowOffsetY
                )
                secondaryShadowSpread = typedArray.getDimension(
                    R.styleable.InnerShadowWithBorderLayout_secondaryShadowSpread,
                    secondaryShadowSpread
                )
            } finally {
                typedArray.recycle()
            }
        }
    }

    private fun setupPaints() {
        shadowPaint.apply {
            color = shadowColor
            isAntiAlias = true
        }

        secondaryShadowPaint.apply {
            color = secondaryShadowColor
            isAntiAlias = true
        }

        borderPaint.apply {
            color = borderColor
            style = Paint.Style.STROKE
            strokeWidth = borderWidth
            isAntiAlias = true
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        val rect = RectF(
            borderWidth / 2,
            borderWidth / 2,
            width - borderWidth / 2,
            height - borderWidth / 2
        )

        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, borderPaint)

        drawInnerShadow(
            canvas = canvas,
            bounds = rect,
            shadowSpread = shadowSpread,
            shadowPain = shadowPaint,
            shadowBlur = shadowBlur,
            offsetX = shadowOffsetX,
            offsetY = shadowOffsetY
        )

        if (enableDualShadow) {
            drawInnerShadow(
                canvas = canvas,
                bounds = rect,
                shadowSpread = secondaryShadowSpread,
                shadowPain = secondaryShadowPaint,
                shadowBlur = secondaryShadowBlur,
                offsetX = secondaryShadowOffsetX,
                offsetY = secondaryShadowOffsetY
            )
        }
        super.dispatchDraw(canvas)
    }

    private fun drawInnerShadow(
        canvas: Canvas,
        bounds: RectF,
        shadowSpread: Float = 0f,
        shadowPain: Paint,
        shadowBlur: Float = 0f,
        offsetX: Float = 0f,
        offsetY: Float = 0f,
    ) {
        val innerRect = RectF(
            bounds.left + borderWidth / 2,
            bounds.top + borderWidth / 2,
            bounds.right - borderWidth / 2,
            bounds.bottom - borderWidth / 2
        )

        val innerCornerRadius = cornerRadius

        val layerPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        val saveLayer = canvas.saveLayer(bounds, layerPaint)

        val shadowRect = RectF(
            innerRect.left - shadowSpread,
            innerRect.top - shadowSpread,
            innerRect.right + shadowSpread,
            innerRect.bottom + shadowSpread
        )

        canvas.drawRoundRect(shadowRect, innerCornerRadius, innerCornerRadius, shadowPain)

        layerPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
            color = android.graphics.Color.BLACK
            if (shadowBlur > 0) {
                maskFilter = BlurMaskFilter(shadowBlur, BlurMaskFilter.Blur.NORMAL)
            }
        }

        canvas.translate(offsetX, offsetY)

        canvas.drawRoundRect(shadowRect, innerCornerRadius, innerCornerRadius, layerPaint)

        canvas.restoreToCount(saveLayer)
    }

    // Public config methods
    fun setBorderColor(color: Int) {
        borderColor = color
        setupPaints()
        invalidate()
    }

    fun setBorderWidth(width: Float) {
        borderWidth = width.dpToPx()
        setupPaints()
        invalidate()
    }

    fun setShadowColor(color: Int) {
        shadowColor = color
        setupPaints()
        invalidate()
    }

    fun setShadowBlur(blur: Float) {
        shadowBlur = blur
        setupPaints()
        invalidate()
    }

    fun setShadowOffset(
        x: Float,
        y: Float
    ) {
        shadowOffsetX = x
        shadowOffsetY = y
        invalidate()
    }

    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }

    // Dual shadow configuration methods
    fun setDualShadowEnabled(enabled: Boolean) {
        enableDualShadow = enabled
        invalidate()
    }

    fun setSecondaryShadowColor(color: Int) {
        secondaryShadowColor = color
        setupPaints()
        invalidate()
    }

    fun setSecondaryShadowBlur(blur: Float) {
        secondaryShadowBlur = blur
        invalidate()
    }

    fun setSecondaryShadowOffset(
        x: Float,
        y: Float
    ) {
        secondaryShadowOffsetX = x
        secondaryShadowOffsetY = y
        invalidate()
    }

    fun setSecondaryShadowSpread(spread: Float) {
        secondaryShadowSpread = spread
        invalidate()
    }

    private fun Float.dpToPx() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, this, resources.displayMetrics
    )
}