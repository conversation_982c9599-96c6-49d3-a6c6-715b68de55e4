package com.vietinbank.feature_list_type_ott.components

import android.content.Context
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.FrameLayout
import com.vietinbank.feature_list_type_ott.R
import androidx.core.graphics.toColorInt

class InnerShadowWithBorderLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private var cornerRadius = 32f.dpToPx()
    private var borderWidth = 1f.dpToPx()
    private var borderColor = Color.WHITE

    // Inner shadow config
    private var shadowColor = "#********".toColorInt()
    private var shadowBlur = 8f
    private var shadowOffsetX = 2f
    private var shadowOffsetY = 4f
    private var shadowSpread = 0f

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        setWillNotDraw(false)
        setupAttributes(attrs)
        setupPaints()
    }

    private fun setupAttributes(attrs: AttributeSet?) {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.InnerShadowWithBorderLayout)
            try {
                shadowColor = typedArray.getColor(R.styleable.InnerShadowWithBorderLayout_shadowColor, shadowColor)
                shadowBlur = typedArray.getDimension(R.styleable.InnerShadowWithBorderLayout_shadowBlur, shadowBlur)
                shadowOffsetX = typedArray.getDimension(R.styleable.InnerShadowWithBorderLayout_shadowOffsetX, shadowOffsetX)
                shadowOffsetY = typedArray.getDimension(R.styleable.InnerShadowWithBorderLayout_shadowOffsetY, shadowOffsetY)
                cornerRadius = typedArray.getDimension(R.styleable.InnerShadowWithBorderLayout_cornerRadius, cornerRadius)
                borderWidth = typedArray.getDimension(R.styleable.InnerShadowWithBorderLayout_borderWidth, borderWidth)
                borderColor = typedArray.getColor(R.styleable.InnerShadowWithBorderLayout_borderColor, borderColor)
            } finally {
                typedArray.recycle()
            }
        }
    }

    private fun setupPaints() {
        shadowPaint.apply {
            color = shadowColor
            isAntiAlias = true
            // Không set maskFilter ở đây, sẽ set trong drawInnerShadow
        }

        borderPaint.apply {
            color = borderColor
            style = Paint.Style.STROKE
            strokeWidth = borderWidth
            isAntiAlias = true
        }
    }

    override fun dispatchDraw(canvas: Canvas) {
        val rect = RectF(
            borderWidth / 2,
            borderWidth / 2,
            width - borderWidth / 2,
            height - borderWidth / 2
        )

        // 1. Vẽ border trước
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, borderPaint)

        // 2. Vẽ inner shadow
        drawInnerShadow(canvas, rect)

        // 3. Vẽ children
        super.dispatchDraw(canvas)
    }

    private fun drawInnerShadow(canvas: Canvas, bounds: RectF) {
        // Tạo inner bounds để shadow không bị border che
        val innerRect = RectF(
            bounds.left + borderWidth,
            bounds.top + borderWidth,
            bounds.right - borderWidth,
            bounds.bottom - borderWidth
        )

        val innerCornerRadius = maxOf(0f, cornerRadius - borderWidth)

        // Tạo shadow size với spread
        val shadowWidth = innerRect.width() + shadowSpread * 2
        val shadowHeight = innerRect.height() + shadowSpread * 2

        // Save layer để tạo inner shadow effect
        val layerPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        val saveLayer = canvas.saveLayer(innerRect, layerPaint)

        // Vẽ shadow shape với spread (không có offset ở đây)
        val shadowRect = RectF(
            innerRect.left - shadowSpread,
            innerRect.top - shadowSpread,
            innerRect.right + shadowSpread,
            innerRect.bottom + shadowSpread
        )

        // Vẽ shadow với màu gốc
        canvas.drawRoundRect(shadowRect, innerCornerRadius, innerCornerRadius, shadowPaint)

        // Thiết lập paint để tạo hiệu ứng DST_OUT với blur
        layerPaint.apply {
            xfermode = PorterDuffXfermode(PorterDuff.Mode.DST_OUT)
            color = android.graphics.Color.BLACK
            if (shadowBlur > 0) {
                maskFilter = BlurMaskFilter(shadowBlur, BlurMaskFilter.Blur.NORMAL)
            }
        }

        // Translate canvas để tạo offset cho shadow
        canvas.translate(shadowOffsetX, shadowOffsetY)

        // Vẽ shape để "cắt" tạo inner shadow
        canvas.drawRoundRect(shadowRect, innerCornerRadius, innerCornerRadius, layerPaint)

        canvas.restoreToCount(saveLayer)
    }

    // Public config methods
    fun setBorderColor(color: Int) {
        borderColor = color
        setupPaints()
        invalidate()
    }

    fun setBorderWidth(width: Float) {
        borderWidth = width.dpToPx()
        setupPaints()
        invalidate()
    }

    fun setShadowColor(color: Int) {
        shadowColor = color
        setupPaints()
        invalidate()
    }

    fun setShadowBlur(blur: Float) {
        shadowBlur = blur
        setupPaints()
        invalidate()
    }

    fun setShadowOffset(x: Float, y: Float) {
        shadowOffsetX = x
        shadowOffsetY = y
        invalidate()
    }

    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        invalidate()
    }

    private fun Float.dpToPx() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, this, resources.displayMetrics
    )
}