package com.vietinbank.feature_list_type_ott.list_ott

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_ui.R
import com.vietinbank.feature_list_type_ott.R as OTR
import com.vietinbank.feature_list_type_ott.databinding.ItemMenuBinding
import kotlin.properties.Delegates

class TabMenuAdapter(
    private val onClick: (Int, String) -> Unit,
) : RecyclerView.Adapter<TabMenuAdapter.TabViewHolder>() {
    private val tabItems = mutableListOf(
        TabItem("Tất cả", true),
        TabItem("Biến động số dư", false),
        TabItem("Tin cá nhân", false),
        TabItem("Tin được chia sẻ", false),
    )

    private var currentSelectedItemIndex by Delegates.observable(0) { _, old, new ->
        if (old != new) {
            tabItems[old] = tabItems[old].copy(isChecked = false)
            tabItems[new] = tabItems[new].copy(isChecked = true)
            notifyItemChanged(old)
            notifyItemChanged(new)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return super.getItemViewType(position)
    }

    inner class TabViewHolder(private val binding: ItemMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: TabItem, position: Int) {
            binding.tabTitle.text = item.tabTitle
            binding.tabTitle.setTextColor(
                binding.root.context.getColor(
                    if (item.isChecked) R.color.foundation_character_tertiary
                    else R.color.foundation_neutrals_white
                )
            )
            binding.root.setBackgroundResource(
                if (item.isChecked) OTR.drawable.rounded_tab_item
                else R.color.transparent
            )
            binding.root.setOnClickListener {
                currentSelectedItemIndex = position
                onClick(position, tabItems[position].tabTitle)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        return TabViewHolder(
            ItemMenuBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false,
            ),
        )
    }

    override fun getItemCount(): Int = tabItems.size

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        holder.bind(tabItems[position], position)
    }

    data class TabItem(
        val tabTitle: String,
        val isChecked: Boolean,
    )
}