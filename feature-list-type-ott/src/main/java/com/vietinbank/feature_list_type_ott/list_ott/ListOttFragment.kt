package com.vietinbank.feature_list_type_ott.list_ott

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.hilt.navigation.fragment.hiltNavGraphViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_list_type_ott.R
import com.vietinbank.feature_list_type_ott.databinding.FragmentListOttBinding
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ListOttFragment: BaseFragment<OttViewModel>() {
    override val viewModel: OttViewModel by hiltNavGraphViewModels(R.id.list_ott_nav)

    @Inject
    override lateinit var appNavigator: IAppNavigator

    private val viewBinding: FragmentListOttBinding by lazy {
        binding as FragmentListOttBinding
    }

    private val ottAdapter: OttSessionAdapter by lazy {
        OttSessionAdapter(
            onItemClick = {
                viewModel.ottBottomSheetAction(it)
                findNavController().navigate(R.id.action_ListOttFragment_to_BottomSheetMenuFragment)
            },
        )
    }

    private val tabMenuAdapter: TabMenuAdapter by lazy {
        TabMenuAdapter() { selected, tabTitle ->
            viewModel.onAction(OttScreenAction.SelectTabIndexed(selected, tabTitle))
            viewBinding.rcvTabSelector.scrollToPosition(0)
        }
    }

    override fun inflateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
    ): FragmentListOttBinding {
        return FragmentListOttBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val statusBar = insets.getInsets(WindowInsetsCompat.Type.statusBars())
            val navBar = insets.getInsets(WindowInsetsCompat.Type.navigationBars())
            v.setPadding(
                statusBar.left,
                statusBar.top,
                statusBar.right,
                navBar.bottom
            )
            insets
        }
        handleDeepLinkParams()
        with(viewBinding) {
            appBar.setContent("Hehehehe", onNavigationClick = {})
            with(rcvTabSelector) {
                layoutManager =
                    LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
                adapter = tabMenuAdapter
            }

            with(rvListOtt) {
                layoutManager =
                    LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
                adapter = ottAdapter
                setHasFixedSize(true)
                addOnScrollListener(object: RecyclerView.OnScrollListener() {
                    override fun onScrolled(
                        recyclerView: RecyclerView,
                        dx: Int,
                        dy: Int
                    ) {
                        super.onScrolled(recyclerView, dx, dy)
                        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
                            ?: return
                        val totalItemCount = layoutManager.itemCount
                        val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                        if (lastVisibleItemPosition >= totalItemCount - 1 && dy > 0) {
                            viewModel.onAction(OttScreenAction.LoadMore)
                        }
                    }
                })
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.sectionOttItem.collectLatest {
                        if (it.isEmpty()) {
                            viewBinding.rvListOtt.visibility = View.GONE
                            viewBinding.emptyListOttLayout.visibility = View.VISIBLE
                        } else {
                            viewBinding.rvListOtt.visibility = View.VISIBLE
                            viewBinding.emptyListOttLayout.visibility = View.GONE
                            ottAdapter.submitList(it)
                        }
                    }
                }
                launch {
                    viewModel.uiState.collectLatest {
                        viewBinding.tvEmpty.text = getString(
                            R.string.empty_list_ott_text,
                            it.selectedTabTitle
                        )
                        if (it.isLoadingMore) {
                            ottAdapter.submitList(ottAdapter.currentList + SectionItem.Loading)
                        }
                    }
                }
                launch {
                    viewModel.oneTimeEvent.collect { event ->
                        when (event) {
                            is OttScreenEvent.ScrollToPosition -> {
                                viewBinding.rvListOtt.scrollToPosition(event.position)
                            }

                            else -> {} // Handle other events if needed
                        }
                    }
                }
            }
        }

        // Observe saved state for navigation updates
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>("messageId")
            ?.observe(viewLifecycleOwner) { messageId ->
                if (!messageId.isNullOrEmpty()) {
                    printLog("ListOttFragment: Received messageId from savedState: $messageId")
                    viewLifecycleOwner.lifecycleScope.launch {
                        // Wait for messages to load
                        kotlinx.coroutines.delay(500)
                        viewModel.onAction(OttScreenAction.ScrollToMessage(messageId))
                    }
                    // Clear the value to avoid re-triggering
                    findNavController().currentBackStackEntry?.savedStateHandle?.remove<String>("messageId")
                }
            }
    }

    private fun handleDeepLinkParams() {
        // Lấy query parameters từ deep link
        val messageId = arguments?.getString("messageId")
            ?: activity?.intent?.data?.getQueryParameter("messageId")

        val openFromNotification = arguments?.getString("openFromNotification")?.toBoolean()
            ?: activity?.intent?.data?.getQueryParameter("openFromNotification")?.toBoolean()
            ?: false

        val ottData = arguments?.getString("ottData")
            ?: activity?.intent?.data?.getQueryParameter("ottData")

        if (openFromNotification && !messageId.isNullOrEmpty()) {
            printLog("OTT opened from notification: $messageId")

            // MainActivity already saved the message when processing notification
            // We don't need to save it again here - this prevents duplicate saves
            // and avoids reloading messages which causes screen flicker

            // Mark as read
//            viewModel.markMessageAsRead(messageId)

            // Scroll to the message after a delay to ensure the list is loaded
            viewLifecycleOwner.lifecycleScope.launch {
                // Wait for messages to load
                kotlinx.coroutines.delay(500)
                viewModel.onAction(OttScreenAction.ScrollToMessage(messageId))
            }
        }
    }
}