package com.vietinbank.feature_checker.ui.component.success

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.NextApproversListDomain
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType

/**
 * Helper class để tạo danh sách thông tin cho màn hình success
 * Có thể tùy chỉnh theo từng loại giao dịch (tranType)
 */
object SuccessHelper {

    /**
     * Tạo danh sách thông tin cho màn hình thành công dựa trên loại giao dịch
     * @param tranType Loại giao dịch
     * @param mtId Mã giao dịch
     * @param items Danh sách các item từ màn hình confirm
     * @param isBatch Có phải là giao dịch batch không
     * @param tranDate Thời gian giao dịch từ API response
     * @param hostMtId Số giao dịch từ core banking (nếu có)
     * @return Danh sách các item hiển thị
     */
    fun createSuccessList(
        tranType: String,
        mtId: String,
        items: List<ConfirmItem>?,
        isBatch: Boolean = false,
        tranDate: String? = null,
        fileAttachments: List<FileTransactionDomain>? = null,
        nextApprovers: ArrayList<NextApproversListDomain>? = null,
        hostMtId: String? = null,
    ): List<SuccessItemDomain> {
        // Nếu là giao dịch batch, xử lý riêng. Duyệt theo lô vào đây
        if (isBatch) {
            return createBatchSuccessList(tranType, mtId, items, tranDate, hostMtId)
        }

        // Xử lý giao dịch đơn lẻ
        val successList = when (tranType) {
            Tags.TransferType.TYPE_IN,
            Tags.TransferType.TYPE_OUT,
            Tags.TransferType.TYPE_NAPAS,
            -> createTransferInSuccessList(mtId, items, fileAttachments, hostMtId)

            Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER -> createPaymentInSuccessList(mtId, items, hostMtId)
            "sl", "slo", "sx" -> createSalaryInSuccessList(mtId, tranType, items, fileAttachments)
            "do" -> createDisbursementSuccessList(items)
            "tr" -> createTraceSuccessList(items)
            "tx", "if" -> createCustomsInlandSuccessList(mtId, tranType, items, fileAttachments)
            "go", "goc", "gor" -> createGuaranteeSuccessList(items, tranType, fileAttachments)
            "btx" -> createFileSuccessList(mtId, items, tranDate, hostMtId)
            "fx", "fxr" -> createTransferForeignSuccessList(mtId, tranType, items, fileAttachments)
            Tags.TYPE_GROUP_RESET_PASSWORD.lowercase(), Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> createUnLockUserSuccessList(items)
            // Thêm các loại giao dịch khác khi cần
            else -> createDefaultSuccessList(mtId, items, hostMtId)
        }

        val mutableList = successList.toMutableList()

        if (!tranDate.isNullOrEmpty()) {
            mutableList.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = tranDate,
                    stableKey = ConfirmFieldKey.TRANSACTION_TIME,
                ),
            )
        }

        nextApprovers?.let { approvers ->
            if (approvers.isNotEmpty()) {
                approvers.forEachIndexed { _, approver ->
                    val title = buildString {
                        append("Người phê duyệt tiếp theo")
                        if (approvers.size > 1) {
                            approver.approverlevel?.let { append(" - Cấp $it") }
                        }
                    }
                    val value = buildString {
                        approver.username?.let { append(it) }
                        approver.fullname?.let { append("_$it") }
                    }
                    if (value.isNotEmpty()) {
                        mutableList.add(SuccessItemDomain(title, value))
                    }
                }
            }
        }

        return mutableList
    }

    /**
     * Tạo danh sách cho giao dịch chuyển khoản nội bộ (TYPE_IN) và ngoài (TYPE_OUT)
     * Thứ tự hiển thị theo yêu cầu: Nội dung, Phí, Hình thức thu phí, Hình thức chuyển, Thời gian giao dịch, Mã tham chiếu
     */
    private fun createTransferInSuccessList(
        mtId: String,
        confirmItems: List<ConfirmItem>?,
        fileAttachments: List<FileTransactionDomain>? = null,
        hostMtId: String? = null, // Thêm hostMtId parameter cho Số giao dịch
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        // Chỉ thêm Số giao dịch nếu có hostMtId từ API
        if (!hostMtId.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = hostMtId,
                    stableKey = ConfirmFieldKey.HOST_MTID,
                ),
            )
        }

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            // Dùng stableKey 100%, không còn fallback theo nhãn
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thứ tự các trường theo yêu cầu cho in/ou
            val fieldOrder = listOf(
                ConfirmFieldKey.FROM_ACCOUNT,
                ConfirmFieldKey.TO_ACCOUNT,
                ConfirmFieldKey.BANK_NAME,
                ConfirmFieldKey.AMOUNT,
                ConfirmFieldKey.CONTENT,
                ConfirmFieldKey.FEE,
                ConfirmFieldKey.FEE_METHOD,
                ConfirmFieldKey.TRANSFER_METHOD,
                ConfirmFieldKey.SCHEDULE_TIME,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]
                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }

        // Thêm Mã tham chiếu sau Số giao dịch
        if (mtId.isNotEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = mtId,
                    stableKey = ConfirmFieldKey.REFERENCE_NO,
                ),
            )
        }

        return successItems
    }

    private fun createPaymentInSuccessList(
        mtId: String,
        confirmItems: List<ConfirmItem>?,
        hostMtId: String? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        // Chỉ thêm Số giao dịch nếu có hostMtId từ API
        if (!hostMtId.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = hostMtId,
                    stableKey = ConfirmFieldKey.HOST_MTID,
                ),
            )
        }

        if (!confirmItems.isNullOrEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên
            val fieldOrder = listOf(
                ConfirmFieldKey.FROM_ACCOUNT,
                ConfirmFieldKey.TO_ACCOUNT,
                ConfirmFieldKey.BANK_NAME,
                ConfirmFieldKey.MTID,
                ConfirmFieldKey.FEE_ACCOUNT_NO,
                ConfirmFieldKey.BRANCH_PROCESSING,
                ConfirmFieldKey.CONTENT,
                ConfirmFieldKey.FEE,
                ConfirmFieldKey.FEE_METHOD,
                ConfirmFieldKey.ATTACHMENT_FILE,
                ConfirmFieldKey.TRANSFER_METHOD,
                ConfirmFieldKey.SCHEDULE_TIME,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]
                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }

        return successItems
    }

    private fun createSalaryInSuccessList( // sl, slo, sx
        mtId: String,
        tranType: String,
        confirmItems: List<ConfirmItem>?,
        fileAttachments: List<FileTransactionDomain>? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()
        val fileMap = fileAttachments?.associateBy { it.fileName } ?: emptyMap()

        if (!confirmItems.isNullOrEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên
            val fieldOrder = listOf(
                ConfirmFieldKey.FROM_ACCOUNT,
                ConfirmFieldKey.FEE,
                ConfirmFieldKey.CONTENT,
                ConfirmFieldKey.ATTACHMENT_FILE,
                ConfirmFieldKey.ATTACHMENT_PROFILE,
                ConfirmFieldKey.TRANSFER_METHOD,
                ConfirmFieldKey.SCHEDULE_TIME,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    val extraValue = when (key) {
                        ConfirmFieldKey.ATTACHMENT_FILE, ConfirmFieldKey.ATTACHMENT_PROFILE -> {
                            val file = fileMap[it.value]
                            file?.let { Utils.g().provideGson().toJson(it) }
                        }
                        else -> null
                    }
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            extraValue = extraValue,
                            tranType = tranType,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }

        return successItems
    }

    /**
     * Tạo danh sách cho giao dịch batch (xử lý nhiều giao dịch cùng lúc)
     */
    private fun createBatchSuccessList(
        tranType: String,
        mtId: String,
        confirmItems: List<ConfirmItem>?,
        tranDate: String? = null,
        hostMtId: String? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            // Ánh xạ nhãn với các item để dễ dàng truy xuất
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            val fieldOrder = when (tranType) {
                "fx", "fxr" -> listOf(
                    ConfirmFieldKey.EXCHANGE_RATE,
                    ConfirmFieldKey.TOTAL_TRANSFER_AMOUNT,
                    ConfirmFieldKey.EXCHANGE_RATE,
                    ConfirmFieldKey.TOTAL_TRANSFER_AMOUNT,
                )
                else -> listOf(
                    ConfirmFieldKey.TOTAL_TRANSACTIONS,
                    ConfirmFieldKey.TOTAL_TRANSFER_AMOUNT,
                    ConfirmFieldKey.TOTAL_AMOUNT,
                    ConfirmFieldKey.TOTAL_FEE,
                )
            }

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
// Không chuẩn hóa tiêu đề theo label tiếng Việt nữa; UI sẽ quyết định nhãn theo stableKey
                    val displayTitle = it.label
                    successItems.add(
                        SuccessItemDomain(
                            title = displayTitle,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }

        // Thêm hostMtId nếu có
        if (!hostMtId.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = hostMtId,
                    stableKey = ConfirmFieldKey.HOST_MTID,
                ),
            )
        }

        // Thêm Mã tham chiếu
        if (mtId.isNotEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = mtId,
                    stableKey = ConfirmFieldKey.REFERENCE_NO,
                ),
            )
        }

        // Thêm tranDate vào cuối danh sách nếu có
        if (!tranDate.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = tranDate,
                    stableKey = ConfirmFieldKey.TRANSACTION_TIME,
                ),
            )
        }

        return successItems
    }

    /**
     * Tạo danh sách cho giao dịch xử lý theo file
     */
    private fun createFileSuccessList(
        mtId: String,
        confirmItems: List<ConfirmItem>?,
        tranDate: String? = null,
        hostMtId: String? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            // Ánh xạ nhãn với các item để dễ dàng truy xuất
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên cho batch
            val fieldOrder = listOf(
                ConfirmFieldKey.TOTAL_TRANSACTIONS,
                ConfirmFieldKey.TOTAL_AMOUNT,
                ConfirmFieldKey.TOTAL_FEE,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }

        // Thêm hostMtId nếu có
        if (!hostMtId.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = hostMtId,
                    stableKey = ConfirmFieldKey.HOST_MTID,
                ),
            )
        }

        // Thêm Mã tham chiếu
        if (mtId.isNotEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = mtId,
                    stableKey = ConfirmFieldKey.REFERENCE_NO,
                ),
            )
        }

        // Thêm tranDate vào cuối danh sách nếu có
        if (!tranDate.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = tranDate,
                    stableKey = ConfirmFieldKey.TRANSACTION_TIME,
                ),
            )
        }

        return successItems
    }

    /**
     * Tạo danh sách mặc định cho các loại giao dịch chưa có xử lý riêng
     */
    private fun createTraceSuccessList(
        confirmItems: List<ConfirmItem>?,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Chỉ sử dụng các item KEY_VALUE
            confirmItems
                .filter { it.type == ConfirmItemType.KEY_VALUE }
                .forEach { item ->
                    successItems.add(
                        SuccessItemDomain(
                            title = item.label,
                            mainValue = item.value,
                            subValue = item.subValue,
                        ),
                    )
                }
        }

        return successItems
    }

    /**
     * Tạo danh sách mặc định cho các loại giao dịch chưa có xử lý riêng
     */
    private fun createDefaultSuccessList(
        mtId: String,
        confirmItems: List<ConfirmItem>?,
        hostMtId: String? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        // Thêm Số giao dịch nếu có hostMtId
        if (!hostMtId.isNullOrEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = hostMtId,
                    stableKey = ConfirmFieldKey.HOST_MTID,
                ),
            )
        }

        // Thêm Mã tham chiếu
        if (mtId.isNotEmpty()) {
            successItems.add(
                SuccessItemDomain(
                    title = "",
                    mainValue = mtId,
                    stableKey = ConfirmFieldKey.REFERENCE_NO,
                ),
            )
        }

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Chỉ sử dụng các item KEY_VALUE
            confirmItems
                .filter { it.type == ConfirmItemType.KEY_VALUE }
                .forEach { item ->
                    successItems.add(
                        SuccessItemDomain(
                            title = item.label,
                            mainValue = item.value,
                            subValue = item.subValue,
                            stableKey = item.stableKey,
                        ),
                    )
                }
        }

        return successItems
    }

    /** giải ngân online */
    private fun createDisbursementSuccessList(
        confirmItems: List<ConfirmItem>?,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()
        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            // Ánh xạ nhãn với các item để dễ dàng truy xuất
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên cho batch
            val fieldOrder = listOf(
                ConfirmFieldKey.DISBURSEMENT_TYPE,
                ConfirmFieldKey.DISBURSEMENT_PURPOSE,
                ConfirmFieldKey.DISBURSEMENT_APPLY_DATE,
                ConfirmFieldKey.INTEREST_RATE,
                ConfirmFieldKey.TERM,
                ConfirmFieldKey.REPAYMENT_DATE,
                ConfirmFieldKey.AMOUNT,
                ConfirmFieldKey.EXCHANGE_RATE,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }
        return successItems
    }

    /** bao lanh online */
    private fun createGuaranteeSuccessList(
        confirmItems: List<ConfirmItem>?,
        tranType: String,
        fileAttachments: List<FileTransactionDomain>? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()
        val fileMap = fileAttachments?.associateBy { it.fileName } ?: emptyMap()

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter {
                it.type == ConfirmItemType.KEY_VALUE || it.type == ConfirmItemType.KEY_VALUE_RESULT
            }

            // Ánh xạ nhãn với các item để dễ dàng truy xuất
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            val fieldOrder = listOf(
                ConfirmFieldKey.HOST_MTID,
                ConfirmFieldKey.GUARANTEE_TYPE,
                ConfirmFieldKey.RELEASE_TYPE,
                ConfirmFieldKey.AMEND_TYPE,
                ConfirmFieldKey.COMPANY_NAME,
                ConfirmFieldKey.BENEFICIARY,
                ConfirmFieldKey.GUARANTEE_ISSUE_AMOUNT,
                ConfirmFieldKey.FEE,
                ConfirmFieldKey.FEE_LEVEL,
                ConfirmFieldKey.GUARANTEE_RELEASE_REQUEST,
                ConfirmFieldKey.GUARANTEE_AMEND_REQUEST,
            )
            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    val extraValue = when (key) {
                        ConfirmFieldKey.GUARANTEE_RELEASE_REQUEST, ConfirmFieldKey.GUARANTEE_AMEND_REQUEST -> {
                            val file = fileMap[it.value]
                            file?.let { Utils.g().provideGson().toJson(it) }
                        }
                        else -> null
                    }
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            extraValue = extraValue,
                            tranType = tranType,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }
        return successItems
    }

    /** Nộp NSNN */
    private fun createCustomsInlandSuccessList(
        mtId: String,
        tranType: String,
        confirmItems: List<ConfirmItem>?,
        fileAttachments: List<FileTransactionDomain>? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (!confirmItems.isNullOrEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên
            val fieldOrder = listOf(
                ConfirmFieldKey.METHOD,
                ConfirmFieldKey.PAYER_NAME,
                ConfirmFieldKey.TAX_CODE,
                ConfirmFieldKey.ADDRESS,
                ConfirmFieldKey.FROM_ACCOUNT,
                ConfirmFieldKey.BANK_NAME,
                ConfirmFieldKey.FEE,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                            stableKey = it.stableKey,
                        ),
                    )
                }
            }
        }
        return successItems
    }

    /** Chuyen tien ngoai te */
    private fun createTransferForeignSuccessList(
        mtId: String,
        tranType: String,
        confirmItems: List<ConfirmItem>?,
        fileAttachments: List<FileTransactionDomain>? = null,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (!confirmItems.isNullOrEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên
            val fieldOrder = listOf(
                ConfirmFieldKey.DEBIT_ACCOUNT_1,
                ConfirmFieldKey.DEBIT_RATE_1,
                ConfirmFieldKey.DEBIT_AMOUNT_1,
                ConfirmFieldKey.DEBIT_ACCOUNT_2,
                ConfirmFieldKey.DEBIT_RATE_2,
                ConfirmFieldKey.DEBIT_AMOUNT_2,
                ConfirmFieldKey.BENEFICIARY_ACCOUNT_IBAN,
                ConfirmFieldKey.BENEFICIARY_NAME,
                ConfirmFieldKey.BENEFICIARY_BIC,
                ConfirmFieldKey.BENEFICIARY_ADDRESS,
                ConfirmFieldKey.TOTAL_TRANSFER_AMOUNT,
                ConfirmFieldKey.FEE,
                ConfirmFieldKey.VALUE_DATE,
                ConfirmFieldKey.CMNDSDNT_PROFILE,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                        ),
                    )
                }
            }
        }
        return successItems
    }

    /** cap lai mk */
    private fun createUnLockUserSuccessList(
        confirmItems: List<ConfirmItem>?,
    ): List<SuccessItemDomain> {
        val successItems = mutableListOf<SuccessItemDomain>()

        if (confirmItems != null && confirmItems.isNotEmpty()) {
            // Lọc các item type KEY_VALUE
            val keyValueItems = confirmItems.filter { it.type == ConfirmItemType.KEY_VALUE }

            // Ánh xạ nhãn với các item để dễ dàng truy xuất
            val itemMapByKey = keyValueItems.filter { it.stableKey != null }.associateBy { it.stableKey }

            // Thêm các trường theo thứ tự ưu tiên cho batch
            val fieldOrder = listOf(
                ConfirmFieldKey.MTID,
                ConfirmFieldKey.CIFNO,
                ConfirmFieldKey.CREATOR,
                ConfirmFieldKey.IDCARD,
                ConfirmFieldKey.PAYMENT_ACCOUNT,
                ConfirmFieldKey.TYPE_SEND,
                ConfirmFieldKey.CREATED_DATE,
            )

            // Thêm các trường theo thứ tự đã định nghĩa
            fieldOrder.forEach { key ->
                val item = itemMapByKey[key]

                item?.let {
                    successItems.add(
                        SuccessItemDomain(
                            title = it.label,
                            mainValue = it.value,
                            subValue = it.subValue,
                        ),
                    )
                }
            }
        }
        return successItems
    }
}