package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ItemSubTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

class CustomsInlandDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Thông tin chung section
        val isCollapse = remember { mutableStateOf(true) }
        SectionHeader(title = "Thông tin chung", true, isCollapse.value) {
            isCollapse.value = !isCollapse.value
        }
        TransactionDetailsCard {
            KeyValueRow(label = "Loại giao dịch", value = transaction.tranTypeName ?: "")
            KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
            KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")
            if (!isCollapse.value) {
                transaction.activityLogs?.let {
                    it.createdBy?.let { user ->
                        KeyValueRow(
                            label = "Người khởi tạo",
                            value = "${user.username} - ${user.processDate}",
                        )
                    }

                    var verifyUser = ""
                    it.verifiedBy?.forEachIndexed { index, user ->
                        verifyUser += "${user.username ?: ""} - ${user.processDate}"
                        if (index < (it.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        KeyValueRow(label = "Người phê duyệt", value = verifyUser)
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin người nộp thuế
        SectionHeader(title = "Thông tin người nộp thuế")
        TransactionDetailsCard {
            if ("0" == transaction.payMethod) {
                KeyValueRow(label = "Hình thức", value = "Nộp cho chính đơn vị")
            } else if ("1" == transaction.payMethod) {
                KeyValueRow(label = "Hình thức", value = "Nộp thay cho đơn vị khác")
            }
            KeyValueRow(label = "Tên đơn vị nộp", value = transaction.payname ?: "")
            KeyValueRow(label = "Mã số thuế", value = transaction.payCode ?: "")
            if (!transaction.idNumber.isNullOrEmpty()) {
                KeyValueRow(label = "Số CMND/Hộ chiếu/CCCD", value = transaction.idNumber ?: "")
            }
            KeyValueRow(label = "Địa chỉ", value = transaction.payAddress ?: "")
            KeyValueRow(
                label = "Từ tài khoản",
                value = transaction.fromAccountNo ?: "",

            )
            KeyValueRow(label = "Ngân hàng", value = transaction.branchName ?: "")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin cơ quan thu
        SectionHeader(title = "Thông tin cơ quan thu")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Tỉnh/Thành phố",
                value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
            )
            KeyValueRow(
                label = "Địa bàn hành chính",
                value = "${transaction.areaCode ?: ""} - ${transaction.areaName ?: ""}",
            )
            KeyValueRow(
                label = "Mã kho bạc nhà nước",
                value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
            )
            KeyValueRow(
                label = "Tài khoản ghi thu",
                value = "${transaction.collectingAccount ?: ""} - ${transaction.collectAcctName ?: ""}",
            )
            KeyValueRow(
                label = "Mã cơ quan quản lý thu",
                value = "${transaction.collectionAgencyCode ?: ""} - ${transaction.collectionAgencyName ?: ""}",
            )
            if ("04" == transaction.taxType) {
                KeyValueRow(
                    label = "Mã chi cục HQ",
                    value = "${transaction.bucode ?: ""} - ${transaction.buName ?: ""}",
                )
                KeyValueRow(
                    label = "Mã HQ phát hành",
                    value = "${transaction.oficode ?: ""} - ${transaction.ofiName ?: ""}",
                )
            }

            KeyValueRow(
                label = "Mã chương",
                value = "${transaction.chapterCode ?: ""} - ${transaction.chapterName ?: ""}",
            )
            if (!transaction.declareNumber.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Số tờ khai QĐ/thông báo",
                    value = transaction.declareNumber ?: "",
                )
            }

            if ("04" == transaction.taxType) {
                KeyValueRow(label = "Ngày tờ khai", value = transaction.declareDate ?: "")
                KeyValueRow(
                    label = "Loại hình XNK",
                    value = "${transaction.ieType ?: ""} - ${transaction.ieName ?: ""}",
                )
                if (!transaction.uid.isNullOrEmpty()) {
                    KeyValueRow(label = "UID", value = transaction.uid ?: "")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin khác
        val additionalInfoItems = listOfNotNull(
            transaction.assetCode?.takeIf { it.isNotEmpty() }?.let {
                "Số khung/ Số tài sản" to it
            },
            transaction.machineNo?.takeIf { it.isNotEmpty() }?.let {
                "Số máy" to it
            },
            transaction.machineFeatures?.takeIf { it.isNotEmpty() }?.let {
                "Đặc điểm phương tiện" to it
            },
            transaction.propertyAdd?.takeIf { it.isNotEmpty() }?.let {
                "Địa chỉ tài sản" to it
            },
        )

        if (additionalInfoItems.isNotEmpty()) {
            SectionHeader(title = "Thông tin khác")
            TransactionDetailsCard {
                additionalInfoItems.forEach { (label, value) ->
                    KeyValueRow(label = label, value = value)
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Thông tin chi tiết nộp NSNN
        SectionHeader(title = "Thông tin chi tiết nộp NSNN")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Tổng số tiền thực nộp",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords ?: "",
            )
            KeyValueRow(
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            )

            if (!transaction.items.isNullOrEmpty()) {
                if (transaction.items?.size!! > 1) {
                    Spacer(modifier = Modifier.height(16.dp))
                    DashedDividerDetail()
                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Duyệt danh sách động từ API
                // Loop qua danh sách chi tiết
                transaction.items?.forEachIndexed { index, item ->
                    TaxDetailSection(item)

                    // Chỉ hiển thị Divider giữa các item nếu có nhiều hơn 1 item
                    if (transaction.items?.size!! > 1 && index < transaction.items?.lastIndex!!) {
                        Spacer(modifier = Modifier.height(16.dp))
                        DashedDividerDetail()
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
    }

    @Composable
    fun TaxDetailSection(file: ItemSubTransactionDetailDomain) {
        KeyValueRow(label = "Nội dung nộp thuế", value = file.content ?: "")
        KeyValueRow(label = "Mã NDKT (TM)", value = file.businessCode ?: "")
        KeyValueRow(label = "Kỳ thuế", value = file.taxPeriod ?: "")
        KeyValueRow(
            label = "Số tiền",
            value = Utils.g().getDotMoneyHasCcy(
                file.amount ?: "",
                file.currency ?: "",
            ),
        )
    }

    @Composable
    fun DashedDividerDetail(
        color: Color = Color.Gray,
        thickness: Dp = 1.dp,
        dashWidth: Dp = 6.dp,
        gapWidth: Dp = 4.dp,
    ) {
        // Lấy Density từ Composable scope
        val density = LocalDensity.current

        // Dùng remember để cache giá trị px
        val dashPx = remember(dashWidth, density) { with(density) { dashWidth.toPx() } }
        val gapPx = remember(gapWidth, density) { with(density) { gapWidth.toPx() } }
        val thicknessPx = remember(thickness, density) { with(density) { thickness.toPx() } }

        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(thickness),
        ) {
            val totalWidth = size.width
            var currentX = 0f

            while (currentX < totalWidth) {
                drawLine(
                    color = color,
                    start = Offset(currentX, 0f),
                    end = Offset(currentX + dashPx, 0f),
                    strokeWidth = thicknessPx,
                )
                currentX += dashPx + gapPx
            }
        }
    }
}