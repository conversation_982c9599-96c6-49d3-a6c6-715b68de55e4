package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

class UnLockUserDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(
                    vertical = FoundationDesignSystem.Sizer.Padding.padding16,
                    horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            Row(
                modifier = modifier
                    .fillMaxWidth(),
            ) {
                FoundationStatus(
                    statusMessage = transaction.statusName
                        ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
                    statusCode = when (transaction.status?.uppercase()) {
                        Tags.QUERY_TYPE_APPROVED -> Status.Success
                        Tags.QUERY_TYPE_REJECTED -> Status.Fail
                        else -> Status.Pending
                    },
                )
                Spacer(modifier = Modifier.weight(1f))
            }

            FoundationDivider(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.account_transaction_no),
                value = transaction.mtId ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.lock_init_cif_mst),
                value = transaction.cifno ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.lock_init_name),
                value = transaction.creator ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.lock_init_cccd),
                value = transaction.idCard ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.lock_init_account),
                value = transaction.paymentAccount ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.lock_confirm_method_pass),
                value = transaction.typeSend ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = transaction.statusCode.getColorStatus(),
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding8),
            )
        }
    }
}