package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.validator.cfkeypass.KeypassValidator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.screen.ConfirmKeypassScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmKeypassFragment : BaseFragment<ConfirmKeypassViewModel>() {

    override val viewModel: ConfirmKeypassViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        val keypassValue by viewModel.keypassValue.collectAsState()
        val validationState by viewModel.keypassValidationState.observeAsState()

        val validationError = when (val state = validationState) {
            is KeypassValidator.ValidationResult.Error -> state.message
            else -> null
        }

        AppTheme {
            ConfirmKeypassScreen(
                challengeCode = viewModel.challengeCode,
                onBackClick = { appNavigator.navigateUp() },
                onCancelClick = { appNavigator.navigateUp() },
                onConfirmClick = { keypassCode ->
                    printLog("keypasscode: $keypassCode")
                    if (viewModel.validateKeypass()) {
                        viewModel.onConfirmClick()
                    }
                },
                keypassValue = keypassValue,
                onKeypassValueChange = { newValue ->
                    viewModel.updateKeypassValue(newValue)
                },
                validationError = validationError,
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // Lấy dữ liệu từ Bundle
        arguments?.let {
            viewModel.challengeCode = it.getString(Tags.CHALLENGE_CODE_BUNDLE, "")
            viewModel.confirmType = it.getString(Tags.CONFIRM_TYPE_BUNDLE, "")
            viewModel.transactionsMtID = it.getString(Tags.MT_ID, "")
            viewModel.registerEkycID = it.getString(Tags.REGISTER_EKYC_ID, "")
        }
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                printLog("current trans init: $jsonData")

                // Kiểm tra xem dữ liệu có chứa "items" không (đặc trưng của ConfirmUiModel)
                if (jsonData.contains("\"items\"")) {
                    // Parse dữ liệu là ConfirmUiModel
                    val uiModel =
                        Utils.g().provideGson().fromJson(jsonData, ConfirmUiModel::class.java)
                    viewModel.processConfirmUiModel(uiModel)
                    printLog("Đã xử lý dữ liệu theo ConfirmUiModel")
                } else {
                    // Parse dữ liệu là TransactionListDomain (cách cũ)
                    val transaction = Utils.g().provideGson()
                        .fromJson(jsonData, TransactionListDomain::class.java)
                    viewModel.processTransaction(transaction)
                    printLog("Đã xử lý dữ liệu theo TransactionListDomain")
                }
            } catch (e: Exception) {
                printLog("Lỗi parse JSON: ${e.message}")
                e.printStackTrace()
            }
        } ?: run {
            printLog("Không có dữ liệu transaction trong bundle")
        }
        printLog("viewModel.confirmType ${viewModel.confirmType}")

        arguments?.getString(Tags.NEXT_APPROVER_BUNDLE)?.let { jsonData ->
            try {
                val typeToken = object : TypeToken<List<ApproverDomains>>() {}.type
                viewModel.selectedNextApprover =
                    Utils.g().provideGson().fromJson<List<ApproverDomains>>(jsonData, typeToken)
            } catch (e: Exception) {
                printLog("Lỗi parse transaction JSON: ${e.message}")
                e.printStackTrace()
            }
        }

        setupObserver()
        // Lấy dữ liệu transaction từ Bundle
    }

    private fun setupObserver() {
        viewModel.apply {
            doApproveResponse.observe(viewLifecycleOwner) { data ->
                when (data) {
                    is Resource.Success -> {
                        // chuyen man hinh
                        val successList = Utils.g().provideGson().toJson(viewModel.getSuccessList())
                        val successTitle = Utils.g().provideGson().toJson(
                            viewModel.getSuccessTitle(
                                data.data.transactions?.firstOrNull(),
                                data.data.status?.message.toString(),
                            ),
                        )
//                        appNavigator.gotoSuccessChecker(successList, successTitle, viewModel.confirmType)
                    }

                    else -> {
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.apiEvent.collect {
                        when (it) {
                            is ApiEvent.Success -> {
                                appNavigator.goToConfirmUpdateEkycScreen()
                            }
                            is ApiEvent.ShowError -> {}
                        }
                    }
                }
            }
        }
    }
}