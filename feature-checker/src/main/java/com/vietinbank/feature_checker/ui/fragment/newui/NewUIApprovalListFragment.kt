package com.vietinbank.feature_checker.ui.fragment.newui

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.components.date.FoundationDateRangePicker
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.newui.ApprovalListAction
import com.vietinbank.feature_checker.ui.component.newui.NewUIApprovalListScreenWithActions
import com.vietinbank.feature_checker.ui.compose.FilterApprovalBottomSheet
import com.vietinbank.feature_checker.ui.compose.FilterLockApprovalBottomSheet
import com.vietinbank.feature_checker.ui.compose.SortTransactionSheet
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel
import com.vietinbank.feature_checker.ui.fragment.FilterApprovalAction
import com.vietinbank.feature_checker.ui.fragment.SuccessFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * New UI implementation of Approval List using Foundation Design System
 * Reuses the existing ApprovalListViewModel for business logic
 */
@AndroidEntryPoint
class NewUIApprovalListFragment : BaseFragment<ApprovalListViewModel>() {

    override val viewModel: ApprovalListViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    lateinit var transactionRendererFactory: TransactionRendererFactory

    @Composable
    override fun ComposeScreen() {
        // Collect UI state
        val uiState by viewModel.uiState.collectAsState()
        val isChecker by viewModel.isChecker.collectAsState()
        val isSelectionMode by viewModel.isSelectionMode.collectAsState()
        val selectedTransactions by viewModel.selectedTransactions.collectAsState()
        val totalTransactionsCount by viewModel.totalTransactionsCount.collectAsState()
        val isGlobalMaxMode by viewModel.isGlobalMaxMode.collectAsState()
        val isShowApprove by viewModel.isShowApprove.collectAsState()
        val isLoadingMore by viewModel.isLoadingMore.collectAsState()
        val canLoadMore by viewModel.canLoadMore.collectAsState()
        val transactionSheetState by viewModel.transactionSheet.collectAsState()

        // Collect new UI states from ViewModel
//        val shouldShowChipFilter by viewModel.shouldShowChipFilter.collectAsState()
//        val chipFilterOptions by viewModel.chipFilterOptions.collectAsState()
        val transactionTypesForDialog by viewModel.transactionTypesForDialog.collectAsState()
        val selectedTransactionTypeCode by viewModel.selectedChipCode.collectAsState()
        val selectedTransactionItem by viewModel.selectedTransactionItem.collectAsState()
        val headerTitle by viewModel.headerTitle.collectAsState() // Get header title from ViewModel

        // filter option
        val filterTransaction by viewModel.filterTransaction.collectAsState()

        // Handle actions using sealed class
        val onAction: (ApprovalListAction) -> Unit = { action ->
            when (action) {
                is ApprovalListAction.BackClick -> {
                    appNavigator.popBackStack()
                }

                is ApprovalListAction.FilterChipSelected -> {
                    // Handle chip filter selection
                    viewModel.selectChipFilter(action.tranTypeCode)
                    viewModel.onChipFilterSelected(action.tranTypeCode)
                }

                is ApprovalListAction.TransactionTypeSelected -> {
                    // Find the selected item from countTransactionList
                    viewModel.selectTransactionType(action.functionId)
                }

                is ApprovalListAction.SortClick -> {
                    viewModel.onShowFilterSheet(true)
                }

                is ApprovalListAction.MultiSelectClick -> {
                    appNavigator.goToMultipleApprovalList(
                        bundleOf().apply {
                            putString(Tags.TYPE_BUNDLE, headerTitle)
                            putParcelable(Tags.TRANSACTION_BUNDLE, filterTransaction.originFilter)
                            putString(
                                Tags.DATA_TYPE,
                                Utils.g().provideGson()
                                    .toJson(selectedTransactionItem?.subTranTypeList ?: ""),
                            )
                            putBoolean(Tags.IS_REJECT_MODE_TRANSACTION, action.isRejectMode)
                        },
                    )
                }

                is ApprovalListAction.TabSelected -> {
                    // Use new method that handles tab selection with queryType
                    viewModel.onTabSelected(action.tabIndex)
                }

                is ApprovalListAction.TransactionClick -> {
                    if (isSelectionMode) {
                        viewModel.toggleTransactionSelection(action.transactionId)
                    } else {
                        // Navigate to detail
                        navigateToTransactionDetail(action.transactionId)
                    }
                }

                is ApprovalListAction.TransactionLongClick -> {
                    viewModel.toggleTransactionSelection(action.transaction.mtId ?: "")
                    viewModel.showSheetFor(action.transaction)
                }

                is ApprovalListAction.ApproveClick -> {
                    handleApprove()
                }

                is ApprovalListAction.RejectClick -> {
                    handleReject()
                }

                is ApprovalListAction.SelectAllClick -> {
                    viewModel.toggleMaxSelectAll()
                }

                is ApprovalListAction.LoadMoreClick -> {
                    viewModel.loadMoreTransactions()
                }

                is ApprovalListAction.RetryLoadMoreClick -> {
                    viewModel.retryLoadMore()
                }

                is ApprovalListAction.DismissClick -> {
                    viewModel.dismissSheet()
                }
                // xoa nhieu giao dich
                is ApprovalListAction.DeleteSelectClick -> {
//                    appNavigator.goToMultipleApprovalList(
//                        bundleOf().apply {
//                            putString(Tags.TYPE_BUNDLE, headerTitle)
//                            putParcelable(Tags.TRANSACTION_BUNDLE, filterTransaction.originFilter)
//                        },
//                    )
                }
            }
        }

        // Render the new UI screen with sealed class actions
        NewUIApprovalListScreenWithActions(
            uiState = uiState,
            headerTitle = headerTitle,
            isSelectionMode = isSelectionMode,
            selectedTransactions = selectedTransactions,
            totalTransactionsCount = totalTransactionsCount,
            isGlobalMaxMode = isGlobalMaxMode,
            isShowApprove = isShowApprove,
            isLoadingMore = isLoadingMore,
            canLoadMore = canLoadMore,
            onAction = onAction,
            transactionSheetState = transactionSheetState,
            transactionRendererFactory = transactionRendererFactory,
            isChecker = isChecker,
            // Pass new UI state from ViewModel
//            shouldShowChipFilter = shouldShowChipFilter,
            chipFilterOptions = selectedTransactionItem?.subTranTypeList ?: emptyList(),
            transactionTypesForDialog = transactionTypesForDialog,
            selectedTransactionTypeCode = selectedTransactionTypeCode,
            selectedTransactionItemId = selectedTransactionItem?.functionId
                ?: selectedTransactionItem?.tranType,
            isAdvanceFilter = filterTransaction.isFilterAdvance,
        )

        // Handle one-time events
        LaunchedEffect(Unit) {
            launch {
                viewModel.preApprove.collect { preApprove ->
                    preApprove?.let {
                        // Navigate to batch approval
                        navigateToBatchApproval(it)
                    }
                }
            }

            launch {
                viewModel.preApproveSingle.collect { preApprove ->
                    preApprove?.let {
                        // Navigate to single approval
                        navigateToSingleApproval(it)
                    }
                }
            }

            launch {
                viewModel.errorEvent.collect { error ->
                    error?.let {
                        showNoticeDialog(it)
                    }
                }
            }

            launch {
                viewModel.loadMoreError.collect { error ->
                    showNoticeDialog(error)
                }
            }
        }

        // filter transaction
        val actionFilter: (FilterApprovalAction) -> Unit = { action ->
            when (action) {
                is FilterApprovalAction.TapShowFilter -> {
                    viewModel.onShowFilterSheet(action.isShow)
                }

                is FilterApprovalAction.TapShowSort -> {
                    viewModel.onShowSortSheet(action.isShow)
                }

                is FilterApprovalAction.TapShowPicker -> {
                    viewModel.onShowPickerSheet(action.isShow)
                }

                is FilterApprovalAction.TapSelectSort -> {
                    viewModel.onChangeSortField(action.sortOption)
                }

                is FilterApprovalAction.TapReset -> {
                    viewModel.onResetFilter()
                }

                is FilterApprovalAction.TapApply -> {
                    viewModel.onApplyFilter()
                }

                is FilterApprovalAction.TapChip -> {
                    viewModel.onChangeChipFilter(action.chipType, action.chipValue)
                }

                is FilterApprovalAction.TapInput -> {
                    viewModel.onChangeInputFilter(action.inputType, action.inputValue)
                }

                else -> {}
            }
        }

        if (viewModel.isLockTransactionType(filterTransaction.changedFilter?.tranType)) {
            FilterLockApprovalBottomSheet(
                visible = filterTransaction.isShowFilter &&
                    !filterTransaction.isShowSort &&
                    !filterTransaction.isShowPicker,
                filterModel = filterTransaction.changedFilter,
                sortLst = viewModel.sortLstLock,
                selectedItem = filterTransaction.changedFilter?.sortField,
                onAction = actionFilter,
            )
        } else {
            FilterApprovalBottomSheet(
                visible = filterTransaction.isShowFilter &&
                    !filterTransaction.isShowSort &&
                    !filterTransaction.isShowPicker,
                filterModel = filterTransaction.changedFilter,
                onAction = actionFilter,
            )
        }

        // show selector data sheet
        SortTransactionSheet(
            visible = filterTransaction.isShowSort,
            sortLst = viewModel.sortLst,
            selectedItem = filterTransaction.changedFilter?.sortField,
            onAction = actionFilter,
        )

        // date picker
        if (filterTransaction.isShowPicker) {
            FoundationDateRangePicker(
                startDate = filterTransaction.changedFilter?.startDate,
                endDate = filterTransaction.changedFilter?.endDate,
                onDismiss = { viewModel.onShowPickerSheet(false) },
                onSelected = { startDate, endDate ->
                    viewModel.onChangeDate(startDate, endDate)
                },
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get bundle data first
        getBundleData()

        // Initialize observers
        initObserver()

        // Setup fragment result listener for refresh after approval
        setupFragmentResultListener()
    }

    private fun getBundleData() {
        // Store current types from bundle
        viewModel.initTransaction(arguments)
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    // Observe pre-approve result for batch
                    launch {
                        preApprove.collect { resource ->
                            resource?.let { data ->
                                handleTransactionResult(
                                    data,
                                    if (data.isApprove) Tags.APPROVE else Tags.REJECT,
                                )
                            }
                        }
                    }

                    // Observe pre-approve result for single
                    launch {
                        preApproveSingle.collect { resource ->
                            resource?.let { data ->
                                handleSingleTransactionResult(
                                    data,
                                    if (data.isApprove) Tags.APPROVE else Tags.REJECT,
                                    transaction = data.transactionProcessor!!,
                                )
                            }
                        }
                    }

                    // Observe refresh transaction list
                    launch {
                        refreshTransactionList.collect { data ->
                            viewModel.processTransactions(
                                transactions = data.transactions ?: listOf(),
                                countTransaction = data.countTransaction,
                            )
                            // Set flag to scroll to top after refresh
                            if (view != null && isAdded) {
                                viewModel.needScrollToTop = true
                            }
                        }
                    }

                    // Observe load more errors
                    launch {
                        loadMoreError.collect { errorMessage ->
                            printLog("Load more error: $errorMessage")
                        }
                    }
                }
            }
        }
    }

    private fun setupFragmentResultListener() {
        viewLifecycleOwner.lifecycleScope.launch {
            setFragmentResultListener(SuccessFragment.APPROVAL_FLOW_RESULT_KEY) { _, bundle ->
                val status = bundle.getString("status")
                val needRefresh = bundle.getBoolean("needRefresh", false)

                if (status == "SUCCESS") {
                    // Handle successful transaction
                    if (needRefresh && viewModel.originalTransactions.isNotEmpty()) {
                        // Refresh transaction list
                        val firstTransaction = viewModel.originalTransactions.firstOrNull()
                        firstTransaction?.let {
                            viewModel.refreshTransactionList(
                                groupType = it.groupType ?: "",
                                serviceType = it.serviceType ?: "",
                                tranType = it.tranType ?: "",
                            )
                        }
                    }
                }
            }
        }
    }

    private fun handleApprove() {
        // Validate selection
        val validationError = viewModel.validateTotalTransactions()
        if (validationError != null) {
            showNoticeDialog(validationError)
            return
        }

        // Proceed with approval
        viewModel.preApprove()
    }

    private fun handleReject() {
        // Validate selection
        val validationError = viewModel.validateTotalTransactions()
        if (validationError != null) {
            showNoticeDialog(validationError)
            return
        }

        // Proceed with rejection
        viewModel.preReject()
    }

    private fun navigateToTransactionDetail(transactionId: String) {
        // Get original transaction by ID and navigate to detail
        val originalTransaction = viewModel.getOriginalTransactionByMTId(transactionId)
        originalTransaction?.let {
            printLog(
                "Navigating to detail for transaction: ${
                    Utils.g().provideGson().toJson(it)
                }",
            )
            val jsonData = Utils.g().provideGson().toJson(it)
            appNavigator.goToInitTransaction(jsonData, null)
        }
    }

    private fun handleTransactionResult(
        preDomain: PreApproveDomain,
        confirmType: String,
    ) {
        // Create ConfirmUiModel for batch approval
        val uiModel = ConfirmUiModel(
            screenTitle = "Xác nhận",
            items = viewModel.buildConfirmItems(preDomain),
            transactionIds = viewModel.getSelectedTransactionsAsList(),
            serviceType = viewModel.getSelectedServiceTypeFromList(),
            tranType = viewModel.getSelectedTranTypeFromList(),
            groupType = viewModel.getSelectedGroupTypeFromList(),
        )

        val paymentMethod = confirmLauncher.getPaymentMethod(preDomain)
        viewModel.toggleSelectionMode() // Clear selection mode

        confirmLauncher.launchWithBatchConfirmModel(
            confirmUiModel = uiModel,
            confirmType = confirmType,
            paymentMethod = paymentMethod,
        )
    }

    private fun navigateToBatchApproval(preApprove: PreApproveDomain) {
        handleTransactionResult(
            preApprove,
            if (preApprove.isApprove) Tags.APPROVE else Tags.REJECT,
        )
    }

    private fun handleSingleTransactionResult(
        success: PreApproveDomain,
        confirmType: String,
        transaction: TransactionDomain,
    ) {
        // Get payment method
        val paymentMethod = confirmLauncher.getPaymentMethod(success)

        // Clear selection mode if enabled
        if (viewModel.isSelectionMode.value) {
            viewModel.toggleSelectionMode()
        }

        // Navigate to single transaction confirmation
        confirmLauncher.launchWithSingleTransaction(
            transaction = transaction,
            confirmType = confirmType,
            paymentMethod = paymentMethod,
        )
    }

    private fun navigateToSingleApproval(preApprove: PreApproveDomain) {
        preApprove.transactionProcessor?.let { transaction ->
            handleSingleTransactionResult(
                preApprove,
                if (preApprove.isApprove) Tags.APPROVE else Tags.REJECT,
                transaction,
            )
        }
    }
}
