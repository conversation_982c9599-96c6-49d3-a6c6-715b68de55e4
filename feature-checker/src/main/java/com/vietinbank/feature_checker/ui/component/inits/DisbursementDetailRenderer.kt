package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.layout
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_data.processor.isBeneficiaryOnly
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class DisbursementDetailRenderer
@Inject constructor(
    private val resourceProvider: IResourceProvider,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        val sections = TransactionDetailMapper.map(
            transaction,
            resourceProvider,
            isForeignDisbursement = transaction.subType == Tags.TYPE_DISBURSEMENT_FOREIGN,
        )

        val expandedStates = remember { mutableStateMapOf<Int, Boolean>() }

        LaunchedEffect(viewModel?.isExpanded) {
            viewModel?.isExpanded?.let {
                expandedStates.keys.toList().asReversed().forEach { key ->
                    expandedStates[key] = it
                }
            }
        }

        Column(
            modifier = modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
        ) {
            sections.forEachIndexed { index, section ->
                if (expandedStates[index] == null) {
                    expandedStates[index] = false
                }
                ExpandableSection(
                    title = section.title,
                    isFirstSection = section.isFirst,
                    isLastSection = section.isLast,
                    expandable = section.expandable,
                    expanded = expandedStates[index],
                    onToggle = {
                        expandedStates[index] = !(expandedStates[index] ?: false)
                    },
                ) {
                    if (section.isFirst) {
                        FoundationStatus(
                            statusMessage = transaction.statusName
                                ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
                            statusCode = when (transaction.status) {
                                Tags.STATUS_SUCCESS -> Status.Success
                                Tags.STATUS_USER_REJECT -> Status.Fail
                                Tags.STATUS_AWAITING_APPROVAL -> Status.Pending
                                else -> Status.Pending
                            },
                        )
                    }

                    section.items.forEach { item ->
                        FoundationInfoHorizontal(
                            title = item.title,
                            value = item.value.orEmpty(),
                            titleExtra = item.titleExtra,
                            titleStyle = FDS.Typography.bodyB2,
                            titleExtraColor = if (item.underline) {
                                FDS.Colors.characterHighlighted
                            } else {
                                null
                            },
                            valueStyle = FDS.Typography.bodyB2.copy(
                                textDecoration = if (item.underline) {
                                    TextDecoration.Underline
                                } else {
                                    TextDecoration.None
                                },
                            ),
                        )
                    }
                }
            }
            val parentHorizontalPadding = FDS.Sizer.Padding.padding24
            FoundationDivider(
                modifier = Modifier
                    .layout { measurable, constrain ->
                        val placeable = measurable.measure(
                            constrain.copy(
                                maxWidth = constrain.maxWidth +
                                    (parentHorizontalPadding * 2).roundToPx(),
                            ),
                        )
                        layout(placeable.width, placeable.height) {
                            placeable.place(0, 0)
                        }
                    }
                    .padding(vertical = FDS.Sizer.Padding.padding16),
            )

            Row(
                modifier = Modifier.safeClickable {
                    onFieldClick(TransactionFieldEvent.ViewAttachmentDocument)
                },
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = stringResource(CoreR.string.disbursement_view_attachment),
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterHighlighted,
                )
                Image(
                    painter = painterResource(id = CoreR.drawable.ic_right),
                    contentDescription = null,
                )
            }
        }
    }
}

@Composable
fun ExpandableSection(
    modifier: Modifier = Modifier,
    title: String? = null,
    isFirstSection: Boolean = false,
    isLastSection: Boolean = false,
    expandable: Boolean = true,
    expanded: Boolean? = null,
    paddingValues: PaddingValues = PaddingValues(),
    onToggle: (() -> Unit)? = null,
    content: @Composable () -> Unit,
) {
    var internalExpanded by remember { mutableStateOf(!expandable) }

    val isExpanded = expanded ?: internalExpanded || !expandable
    val toggle = {
        if (onToggle != null) {
            onToggle()
        } else {
            internalExpanded = !internalExpanded
        }
    }

    val parentHorizontalPadding = FDS.Sizer.Padding.padding24
    Column(modifier = modifier) {
        if (!isFirstSection) {
            FoundationDivider(
                modifier = Modifier.layout { measurable, constrain ->
                    val placeable = measurable.measure(
                        constrain.copy(
                            maxWidth = constrain.maxWidth +
                                (parentHorizontalPadding * 2).roundToPx(),
                        ),
                    )
                    layout(placeable.width, placeable.height) {
                        placeable.place(0, 0)
                    }
                },
            )
        }
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxWidth(),
        ) {
            // Header
            title?.let {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .then(
                            if (expandable) {
                                Modifier.clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = null,
                                    onClick = { toggle.invoke() },
                                )
                            } else {
                                Modifier
                            },
                        )
                        .padding(
                            bottom = FDS.Sizer.Padding.padding8,
                            top = FDS.Sizer.Padding.padding16,
                        ),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = it,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterHighlighted,
                    )

                    if (expandable) {
                        Image(
                            painter = painterResource(
                                if (isExpanded) {
                                    R.drawable.icon_button_chevron_up
                                } else {
                                    R.drawable.icon_button_chevron_down
                                },
                            ),
                            contentDescription = null,
                        )
                    }
                }
            }

            AnimatedVisibility(visible = isExpanded) {
                Column(
                    modifier = Modifier
                        .padding(top = FDS.Sizer.Padding.padding16)
                        .fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                ) {
                    content()
                }
            }

            if (!isLastSection) {
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
            }
        }
    }
}

object TransactionDetailMapper {
    fun map(
        transaction: TransactionDomain,
        resourceProvider: IResourceProvider,
        isForeignDisbursement: Boolean = false,
    ): List<InfoSection> {
        return buildList {
            // Chứng từ
            add(
                InfoSection(
                    isFirst = true,
                    expandable = false,
                    items = buildList {
                        add(
                            InfoItem(
                                title = resourceProvider.getString(CoreR.string.disbursement_debt_doc),
                                value = transaction.fileList?.firstOrNull()?.fileName,
                                underline = true,
                            ),
                        )
                        if (!isForeignDisbursement) {
                            add(
                                InfoItem(
                                    title = resourceProvider.getString(CoreR.string.disbursement_proposal_doc),
                                    value = transaction.fileList?.getOrNull(1)?.fileName,
                                    underline = true,
                                ),
                            )
                        }
                    },
                ),
            )

            // Thông tin chung
            add(
                InfoSection(
                    title = resourceProvider.getString(CoreR.string.disbursement_section_general),
                    expandable = false,
                    items = listOf(
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_transaction_id),
                            transaction.mtId,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_purpose),
                            transaction.disbursementPurposeName,
                        ),
                    ),
                ),
            )

            // Khách hàng
            add(
                InfoSection(
                    title = resourceProvider.getString(CoreR.string.disbursement_section_customer),
                    items = listOf(
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_company_name),
                            transaction.companyName,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_company_code),
                            transaction.companyCode,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_representative),
                            transaction.representativeName,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_authority),
                            transaction.authority,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_authority_info),
                            transaction.authorityNumber,
                        ),
                    ),
                ),
            )

            // Hợp đồng vay
            add(
                InfoSection(
                    title = resourceProvider.getString(CoreR.string.disbursement_section_contract),
                    items = listOf(
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_contract_no),
                            transaction.contractNo,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_contract_date),
                            transaction.contractDate,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_loan_limit),
                            transaction.loanLimit,
                            titleExtra = transaction.loanLimitInWord,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_disbursed_amount),
                            transaction.disbursedAmount,
                            titleExtra = transaction.disbursedAmountInWord,
                        ),
                    ),
                ),
            )

            // Đề nghị vay vốn
            add(
                InfoSection(
                    title = resourceProvider.getString(CoreR.string.disbursement_section_loan_proposal),
                    items = listOf(
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_branch),
                            transaction.disbursedBranchCode,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_purpose_of_debt),
                            transaction.purpose,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_loan_rate),
                            transaction.loanRate,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_disbursement_date),
                            transaction.disbursementDate,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_due_date),
                            transaction.dueDate,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_loan_term),
                            transaction.loanTerm,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_auto_repayment),
                            transaction.autoRepayment,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_account_id),
                            transaction.accountId,
                        ),
                        InfoItem(
                            resourceProvider.getString(CoreR.string.disbursement_first_payment_date),
                            transaction.firstPaymentDate,
                        ),
                    ),
                ),
            )

            // Ngoại tệ
            if (isForeignDisbursement) {
                add(
                    InfoSection(
                        title = resourceProvider.getString(CoreR.string.disbursement_section_foreign_transfer),
                        items = listOf(
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_transfer_amount),
                                transaction.transferAmount,
                            ),
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_transfer_loan_amount),
                                transaction.transferLoanAmount,
                            ),
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_transfer_cur_type),
                                transaction.transferLoanCurType,
                            ),
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_exchange_rate),
                                transaction.exchangeRate,
                            ),
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_charge_method),
                                transaction.chargeMethod,
                            ),
                            InfoItem(
                                resourceProvider.getString(CoreR.string.disbursement_lc_return_date),
                                transaction.lcReturnDate,
                            ),
                        ),
                    ),
                )
            }

            // Thụ hưởng
            if (isForeignDisbursement) {
                add(
                    InfoSection(
                        title = resourceProvider.getString(CoreR.string.disbursement_section_beneficiary),
                        isLast = true,
                        items = buildList {
                            add(
                                InfoItem(
                                    resourceProvider.getString(CoreR.string.disbursement_beneficiary),
                                    if (transaction.isBeneficiaryOnly) {
                                        resourceProvider.getString(CoreR.string.disbursement_one_beneficiary)
                                    } else {
                                        resourceProvider.getString(CoreR.string.disbursement_many_beneficiary)
                                    },
                                ),
                            )
                            if (transaction.isBeneficiaryOnly) {
                                add(
                                    InfoItem(
                                        resourceProvider.getString(CoreR.string.disbursement_receive_account),
                                        transaction.receiveAccount,
                                    ),
                                )
                                add(
                                    InfoItem(
                                        resourceProvider.getString(CoreR.string.disbursement_beneficiary_address),
                                        buildString {
                                            val firstPart = listOfNotNull(
                                                transaction.benName,
                                                transaction.benCountry,
                                            ).filter { it.isNotBlank() }
                                                .joinToString("/")

                                            if (firstPart.isNotEmpty()) append(firstPart)

                                            val secondPart = listOfNotNull(
                                                transaction.benDistrict,
                                                transaction.benWard,
                                            ).filter { it.isNotBlank() }
                                                .joinToString(",")

                                            if (secondPart.isNotEmpty()) {
                                                if (isNotEmpty()) append(",")
                                                append(secondPart)
                                            }
                                        },
                                    ),
                                )
                                add(
                                    InfoItem(
                                        resourceProvider.getString(CoreR.string.disbursement_receive_bank),
                                        transaction.receiveBank,
                                    ),
                                )
                                add(
                                    InfoItem(
                                        resourceProvider.getString(CoreR.string.disbursement_receive_bank_name),
                                        transaction.receiveBankName,
                                    ),
                                )
                            }
                        },
                    ),
                )
            }
        }
    }
}

data class InfoItem(
    val title: String,
    val value: String?,
    val titleExtra: String? = null,
    val underline: Boolean = false,
)

data class InfoSection(
    val title: String? = null,
    val expandable: Boolean = true,
    val isFirst: Boolean = false,
    val isLast: Boolean = false,
    val items: List<InfoItem>,
)