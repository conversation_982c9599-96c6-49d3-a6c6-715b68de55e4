package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.TransactionListDomain

/**
 * Created by vandz on 9/4/25.
 */
interface ITransactionContentRenderer {
    @Composable
    fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: <PERSON>olean,
        onChangeClick: () -> Unit,
    )
}