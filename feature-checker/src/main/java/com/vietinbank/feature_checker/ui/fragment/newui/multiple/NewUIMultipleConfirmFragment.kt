package com.vietinbank.feature_checker.ui.fragment.newui.multiple

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.MethodEntityDomain
import com.vietinbank.core_domain.models.checker.MultipleConfirmActions
import com.vietinbank.core_domain.models.checker.maping.confirm.MultipleConfirmUIModel
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.AuthenticationMethodDialog
import com.vietinbank.core_ui.base.dialog.AuthenticationMethodResult
import com.vietinbank.core_ui.base.dialog.KeypassAuthDialog
import com.vietinbank.core_ui.components.foundation.pin.PinAuthDialog
import com.vietinbank.feature_checker.ui.component.newui.multiple.NewUIMultipleConfirmScreen
import com.vietinbank.feature_checker.ui.fragment.ConfirmCheckerViewModel
import com.vietinbank.feature_checker.ui.fragment.KeypassAuthEvent
import com.vietinbank.feature_checker.ui.fragment.MultipleApprovalConfirmViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.setupSoftOTPDialogListener
import com.vietinbank.feature_soft.common.simpleHandleSoftOTPEvent
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPAuthEvent
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPAuthViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.Boolean
import kotlin.getValue

@AndroidEntryPoint
class NewUIMultipleConfirmFragment : BaseFragment<MultipleApprovalConfirmViewModel>() {

    override val viewModel: MultipleApprovalConfirmViewModel by viewModels()
    private val confirmViewModel: ConfirmCheckerViewModel by viewModels()
    private val softOTPAuthViewModel: SoftOTPAuthViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    lateinit var softManager: ISoftManager

    @Composable
    override fun ComposeScreen() {
        // Collect UI state
        val uiState by viewModel.uiConfirmState.collectAsState()
        // Render the new UI screen
        NewUIMultipleConfirmScreen(
            uiState = uiState,
            actions = { action ->
                when (action) {
                    is MultipleConfirmActions.BackClick -> {
                        appNavigator.popBackStack()
                    }

                    is MultipleConfirmActions.RejectReason -> {
                        viewModel.onChangeRejectSheet(action.isShow)
                    }

                    is MultipleConfirmActions.RejectClick -> {
//                        showNoticeDialog("Từ chối: ${action.reason}")
                        viewModel.doReject(action.reason)
                    }

                    is MultipleConfirmActions.ApproveClick -> {
                        handleTransactionResult(uiState.confirmModel?.methodList)
                    }

                    // thao tác với nội dung bên trong
                    else -> {
                    }
                }
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get bundle data first
        getBundleData()

        // Initialize observers
        initObserver()

        // Set up result listener for authentication method dialog
        // IMPORTANT: Use childFragmentManager because dialog is shown with childFragmentManager
        childFragmentManager.setFragmentResultListener(
            "authentication_method_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            printLog("setFragmentResultListener: Received result from AuthenticationMethodDialog")
            // IMPORTANT: BaseDialog uses "key_result" as the key, not "result"
            val result = bundle.getParcelable<AuthenticationMethodResult>("key_result")
            printLog("setFragmentResultListener: Parsed result = $result")
            result?.let {
                printLog("setFragmentResultListener: Selected method = ${it.selectedMethod}")
                // Continue with the selected authentication method
                handleAuthenticationMethodSelected(it.selectedMethod)
            } ?: printLog("setFragmentResultListener: Result is null")
        }

        // Set up result listener for PinAuthDialog
        setupSoftOTPDialogListener(
            onVerifyPin = {
                softOTPAuthViewModel.verifyPin(it)
            },
            onApprove = {
                viewModel.doApprove(
                    otpCode = it,
                    transactionSoftId = softOTPAuthViewModel.getTransactionSoftId(),
                )
            },
        )

        parentFragmentManager.setFragmentResultListener(
            KeypassAuthDialog.KEYPASS_AUTH_DIALOG_RESULT_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            bundle.getString(KeypassAuthDialog.RESULT_KEYPASS_VALUE)?.let { keypassValue ->
                confirmViewModel.verifyKeypass(keypassValue)
            }
        }
    }

    private fun getBundleData() {
        try {
            viewModel.updateConfirmState(
                Utils.g().provideGson().fromJson(
                    arguments?.getString(Tags.TRANSACTION_BUNDLE),
                    MultipleConfirmUIModel::class.java,
                ),
            )
        } catch (_: Exception) {
        }
    }

    private fun initObserver() {
        observeViewModel(confirmViewModel)
        observeViewModel(softOTPAuthViewModel)
        // Observe genKeyPassChallengeCode result
        FlowUtils.collectFlow(this, viewModel.genKeyPassChallengeCode) { data ->
            printLog("Keypass challenge code generated: ${data.challengeCode}")
//            val confirmDataJson = Utils.g().provideGson().toJson(confirmViewModel.uiModel.value)
            // Navigate to Keypass confirmation screen
            appNavigator.goToConfirmKeypassDialog(
                challengeCode = data.challengeCode ?: "",
            )
        }

        FlowUtils.collectFlow(this, softOTPAuthViewModel.oneTimeEvent) {
            simpleHandleSoftOTPEvent(
                event = it as SoftOTPAuthEvent,
                onGetTransactionIdSuccess = { transactionId ->
                    confirmViewModel.transactionID = transactionId
                },
            )
        }

        // Observe PIN verification result from ViewModel
        FlowUtils.collectFlow(this, viewModel.pinVerificationResult) { resource ->
            when (resource) {
                is Resource.Success -> {
                    printLog("PIN verification: Success!")
                    // PIN verified successfully, keep loading while OTP generation starts
                    // Don't hide loading here as OTP generation is next
                }

                is Resource.Error -> {
                    printLog("PIN verification: Error - ${resource.message}")
                    // Don't hide loading - viewmodel's launchJob handles it
                    showNoticeDialog(resource.message ?: "Mã PIN không đúng")
                }

                null -> {
                    // Initial state or loading
                    printLog("PIN verification: Loading or initial state")
                }
            }
        }

        // Observe doApprove result
        FlowUtils.collectFlow(this, viewModel.doApproveResponse) { data ->
            val existingDialog = childFragmentManager.findFragmentByTag("PinAuthDialog")
            (existingDialog as? PinAuthDialog)?.dismiss()
            navigateToSuccessScreen(data)
        }

        FlowUtils.collectFlow(this, confirmViewModel.oneTimeEvent) {
            (it as? KeypassAuthEvent)?.let { keypassEvent ->
                when (keypassEvent) {
                    KeypassAuthEvent.OnReachMaxFail -> showNoticeDialog(
                        resources.getString(com.vietinbank.feature_soft.R.string.keypass_auth_blocked_message),
                        icon = R.drawable.ic_commom_fail_24,
                    )

                    is KeypassAuthEvent.OnVerifyKeypassSuccess -> {
                        dismissAllDialogs()
                        navigateToSuccessScreen(keypassEvent.approveData)
                    }

                    is KeypassAuthEvent.OnVerifyFail -> {
                        showNoticeDialog(keypassEvent.message)
                    }

                    is KeypassAuthEvent.OnVerifyingKeypass -> {
                        if (keypassEvent.isDone) {
                            viewModel.hideLoading()
                        } else {
                            viewModel.showLoading()
                        }
                    }
                }
            }
        }
    }

    private fun navigateToSuccessScreen(data: ApproveDomain) {
        appNavigator.goToMultipleResultApproval(
            bundleOf().apply {
                putString(Tags.TRAN_TYPE_BUNDLE, viewModel.getTranType())
                putString(Tags.SERVICE_TYPE_BUNDLE, viewModel.getServiceType())
                putString(Tags.GROUP_TYPE_BUNDLE, viewModel.getGroupType())
                putString(Tags.TRANSACTION_BUNDLE, Utils.g().provideGson().toJson(data))
            },
        )
    }

    // handle multiple method
    private fun handleTransactionResult(methodList: ArrayList<MethodEntityDomain>? = null) {
        methodList?.let { methods ->
            if (methods.isNotEmpty()) {
                // Process available methods
                val hasSoftOTP = methods.any { it.method?.lowercase() == "softotp" }
                val hasKeypass = methods.any { it.method?.lowercase() == "keypass" }
                val hasSmartCA = methods.any { it.method?.lowercase() == "vnptsmartca" }

                printLog("handleTransactionResult: Available methods - SoftOTP: $hasSoftOTP, Keypass: $hasKeypass, SmartCA: $hasSmartCA")
                printLog("handleTransactionResult: Checking if at least one method is available")

                // Validate at least one method is available
                if (!hasSoftOTP && !hasKeypass && !hasSmartCA) {
                    printLog("handleTransactionResult: ERROR - No methods available despite having methodList")
                    showNoticeDialog("Không có phương thức xác thực khả dụng")
                    return
                }

                // If only one supported method is available, skip dialog and continue
                val availableMethods = methods.mapNotNull { it.method?.lowercase() }
                    .filter { it == "softotp" || it == "keypass" || it == "vnptsmartca" }.distinct()

                if (availableMethods.size == 1) {
                    val onlyMethod = availableMethods.first()
                    printLog("handleTransactionResult: Only one method available ($onlyMethod). Skipping dialog.")
                    handleAuthenticationMethodSelected(onlyMethod)
                    return
                }

                // Show dialog for multi-method selection
                printLog("handleTransactionResult: Showing AuthenticationMethodDialog")
                try {
                    val dialog = AuthenticationMethodDialog.show(
                        fragmentManager = childFragmentManager,
                        hasSoftOTP = hasSoftOTP,
                        hasKeypass = hasKeypass,
                        hasSmartCA = hasSmartCA,
                        isApprove = true,
                    )
                    printLog("handleTransactionResult: Dialog show() returned, dialog=$dialog")

                    // Check if dialog is actually in fragment manager
                    childFragmentManager.executePendingTransactions()
                    val foundDialog =
                        childFragmentManager.findFragmentByTag("AuthenticationMethodDialog")
                    printLog("handleTransactionResult: Dialog in FragmentManager? ${foundDialog != null}")
                    if (foundDialog != null) {
                        printLog("handleTransactionResult: Dialog state - isAdded=${foundDialog.isAdded}, isVisible=${foundDialog.isVisible}, isResumed=${foundDialog.isResumed}")
                    }
                } catch (e: Exception) {
                    printLog("handleTransactionResult: Exception showing dialog: ${e.message}")
                    e.printStackTrace()
                    showNoticeDialog("Không thể hiển thị phương thức xác thực: ${e.message}")
                }
            } else {
                printLog("handleTransactionResult: No authentication methods available")
                showNoticeDialog("Không có phương thức xác thực khả dụng cho phê duyệt")
            }
        } ?: run {
            printLog("handleTransactionResult: methodList is NULL")
            showNoticeDialog("Không có phương thức xác thực khả dụng cho phê duyệt")
        }
    }

    // handle one method
    private fun handleAuthenticationMethodSelected(selectedMethod: String) {
        when (selectedMethod.lowercase()) {
            "softotp" -> {
                // Handle Soft OTP with all validations
                handleSoftOtpSelection()
            }

            "keypass" -> {
                // Generate Keypass challenge code
                viewModel.genKeyPassChallengeCode()
            }

            else -> {}
//            "vnptsmartca" -> {
//                // For now, navigate to old flow
//                val filteredMethods = ArrayList(
//                    methodList.filter {
//                        it.method?.lowercase() == selectedMethod.lowercase()
//                    },
//                )
//                navigateToConfirmChecker(
//                    transaction = transaction,
//                    confirmType = confirmType,
//                    paymentMethod = filteredMethods,
//                    isBatchOperation = isBatchOperation,
//                )
//            }
//
//            else -> {
//                // Fallback to old flow
//                val filteredMethods = ArrayList(
//                    methodList.filter {
//                        it.method?.lowercase() == selectedMethod.lowercase()
//                    },
//                )
//                navigateToConfirmChecker(
//                    transaction = transaction,
//                    confirmType = confirmType,
//                    paymentMethod = filteredMethods,
//                    isBatchOperation = isBatchOperation,
//                )
//            }
        }
    }

    // Handle Soft OTP selection with all validation checks
    // Following the exact flow from NewUIInitTransactionFragment
    private fun handleSoftOtpSelection() {
        printLog("handleSoftOtpSelection: Starting Soft OTP validation")

        // Check 1: Is user allowed to use Soft OTP?
        if (!softManager.isAllowSoft()) {
            printLog("handleSoftOtpSelection: User not registered for Soft OTP")
            showNoticeDialog("Quý khách chưa đăng ký phương thức xác thực Soft OTP")
            return
        }

        // Check 2: Is Soft OTP locked (due to multiple failed attempts)?
//        val lockStatus = softManager.getStatusLockSoft()
//        if (lockStatus != null) {
//            printLog("handleSoftOtpSelection: Soft OTP is locked - $lockStatus")
//            showNoticeDialog(lockStatus)
//            return
//        }
        if (softManager.getStatusLockSoft()) {
            appNavigator.goToLockSoftOTPDialog()
            return
        }

        // Check 3: Is user active on this device?
        if (softManager.isUserActive) {
            printLog("handleSoftOtpSelection: User is active, generating transaction code")
            // User is activated on this device - proceed with transaction code generation
            softOTPAuthViewModel.gentOTPTransCode(
                groupType = viewModel.uiConfirmState.value.confirmModel?.groupType,
                mtIds = viewModel.uiConfirmState.value.confirmModel?.transactionIds?.joinToString(",")
                    ?: "",
                tranType = viewModel.uiConfirmState.value.confirmModel?.tranType ?: "",
            )
        } else {
            printLog("handleSoftOtpSelection: User not active, checking app activation status")
            // User not activated on this device - need to activate
            showConfirmDialog(
                message = "Quý khách cần kích hoạt Soft OTP để thực hiện giao dịch này",
                positiveButtonText = getString(R.string.common_agree),
                negativeButtonText = getString(R.string.common_cancel),
                positiveAction = {
                    if (softManager.isAppActive) {
                        printLog("handleSoftOtpSelection: App is active, going to PIN entry for activation")
                        // App is active - second activation, need PIN
                        appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.ACTIVE.name)
                    } else {
                        printLog("handleSoftOtpSelection: App not active, going to activation screen")
                        // App not active - first activation
                        appNavigator.gotoActivSoft()
                    }
                },
                negativeAction = {
                    printLog("handleSoftOtpSelection: User cancelled activation")
                },
            )
        }
    }
}