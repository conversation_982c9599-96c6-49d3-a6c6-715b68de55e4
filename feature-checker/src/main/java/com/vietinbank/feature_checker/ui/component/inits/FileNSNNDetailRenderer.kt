package com.vietinbank.feature_checker.ui.component.inits

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

class FileNSNNDetailRenderer @Inject constructor(private val moneyHelper: MoneyHelper) :
    ITransactionDetailRenderer {
    @SuppressLint("UnrememberedMutableState")
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Access selected transactions state
        val selectedTransactions = viewModel?.batchFileState?.collectAsState()

        // Thông tin chung section
        SectionHeader(title = "Thông tin chung")
        TransactionDetailsCard {
            KeyValueRow(label = "Số file ID", value = transaction.mtId ?: "")
            KeyValueRow(
                label = "Người tạo",
                value = transaction.creator ?: "",
                subValue = transaction.createdDate ?: "",
            )
            KeyValueRow(
                label = "Tổng số tiền giao dịch",
                value = transaction.total ?: "",
                subValue = transaction.amountInWords ?: "",
            )

            KeyValueRow(
                label = "Tổng phí giao dịch",
                value = transaction.totalFee ?: "",
            )

            KeyValueRow(
                label = "File đính kèm",
                value = transaction.fileName ?: "",
                isHyperlink = true,
            ) {
                onFieldClick(TransactionFieldEvent.FileBaseAttachmentClick(transaction))
            }

//            transaction.listFile?.firstOrNull()?.let { file ->
//                KeyValueRow(
//                    label = "File đính kèm",
//                    value = file.fileName ?: "",
//                    isHyperlink = true,
//                ) {
//                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
//                }
//            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Display child transactions if they exist
        if (transaction.subTranItemList?.isNotEmpty() == true) {
            // Header with selection count and select all button
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    // Checkbox indicator for select all
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(
                                if (selectedTransactions?.value?.batchSelectedLst?.size == transaction.subTranItemList!!.size) {
                                    AppColors.primary
                                } else {
                                    Color.LightGray
                                },
                            )
                            .clickable {
                                // Toggle select all using new method
                                viewModel?.toggleSelectAllGeneric(transaction.subTranItemList!!) { it.mtId }
                            },
                        contentAlignment = Alignment.Center,
                    ) {
                        if (selectedTransactions?.value?.batchSelectedLst?.size == transaction.subTranItemList!!.size) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_check),
                                contentDescription = "All Selected",
                                tint = Color.White,
                                modifier = Modifier.size(16.dp),
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    BaseText(
                        text = "Chọn tất cả",
                        color = Color.White,
                        textSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                    )
                }

                BaseText(
                    text = "${selectedTransactions?.value?.batchSelectedLst?.size ?: ""}/${transaction.subTranItemList!!.size} giao dịch được chọn",
                    color = Color.White,
                    textSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                )
            }

            // Display each child transaction with circular indicator
            transaction.subTranItemList!!.forEachIndexed { index, childTransaction ->
                val mtId = childTransaction.mtId ?: ""
                val isSelected = true == selectedTransactions?.value?.batchSelectedLst?.contains(mtId)

                // Transaction card with selection
                TransactionDetailsCard {
                    // Header row with amount and selection circle
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        // Selection circle
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .clip(CircleShape)
                                .background(if (isSelected) AppColors.primary else Color.LightGray)
                                .clickable {
                                    viewModel?.toggleTransactionSelection(mtId)
                                },
                        )

                        Spacer(modifier = Modifier.padding(horizontal = 8.dp))
                    }

                    val amount = Utils.g().getDotMoneyHasCcy(
                        childTransaction.amount ?: "0",
                        transaction.currency ?: MoneyCurrency.VND.value,
                    )

                    val amountWords =
                        moneyHelper.convertAmountToWords(
                            childTransaction.amount,
                            transaction.currency ?: "",
                        )

                    val totalAmount = amount + "\n" + amountWords

                    KeyValueRow(label = "Số tiền giao dịch", value = totalAmount)
                    KeyValueRow(label = "Số giao dịch", value = childTransaction.mtId ?: "")
                    KeyValueRow(
                        label = "Loại giao dịch",
                        value = childTransaction.tranTypeName ?: "",
                    )
                    // Transaction details
                    KeyValueRow(
                        label = "Tài khoản chuyển",
                        value = childTransaction.fromAccountNo ?: "",
                        subValue = childTransaction.fromAccountName ?: "",
                    )

                    KeyValueRow(
                        label = "Đơn vị nộp thuế",
                        value = childTransaction.payCode ?: "",
                        subValue = childTransaction.payName ?: "",
                    )
                }

                if (index < transaction.subTranItemList!!.size - 1) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}