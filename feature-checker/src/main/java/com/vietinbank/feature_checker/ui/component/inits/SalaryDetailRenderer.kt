package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

/**
 * Created by vandz on 9/4/25.
 */
class SalaryDetailRenderer @Inject constructor() : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Thông tin chung section
        val isCollapse = remember { mutableStateOf(true) }
        SectionHeader(title = "Thông tin chung", hasMoreInfo = true, isCollapse.value) {
            isCollapse.value = !isCollapse.value
        }
        TransactionDetailsCard {
            KeyValueRow(label = "Loại giao dịch", value = transaction.tranTypeName ?: "")
            KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
            KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")
            if (!isCollapse.value) {
                KeyValueRow(
                    label = "Người tạo",
                    value = "${transaction.activityLogs?.createdBy?.username ?: ""} - ${transaction.activityLogs?.createdBy?.processDate ?: ""}",
                )

                var verifyUser = ""
                transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                    verifyUser += "${item.username ?: ""} - ${item.processDate}"
                    if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                        verifyUser += "\n"
                    }
                }
                if (!verifyUser.isEmpty()) {
                    KeyValueRow(label = "Người phê duyệt", value = verifyUser)
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin tài khoản chuyển
        SectionHeader(title = "Thông tin tài khoản chuyển")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: ""} - ${transaction.fromAccountName ?: ""}",
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin tài khoản thụ hưởng
        SectionHeader(title = "Thông tin chi tiết")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
                subValue = transaction.amountInWords,
            )

            // chi luong ngoai te
            if (transaction.tranType == "sx") {
                transaction.exchangeRate?.let {
                    KeyValueRow(
                        label = "Tỷ giá (quy đổi tham khảo)",
                        value = it,
                    )
                }
                transaction.debitAmount?.let {
                    KeyValueRow(
                        label = "Số tiền trích nợ (tham khảo)",
                        value = it,
                    )
                }
            }

            if (!transaction.feeAmount.isNullOrEmpty()) {
                val fee = if (transaction.feePayMethod == "3") {
                    transaction.feePayMethodDesc1 ?: ""
                } else {
                    transaction.feeAmount!!
                }
                KeyValueRow(
                    label = "Phí giao dịch",
                    value = fee,
                )
            }

            KeyValueRow(label = "Nội dung", value = transaction.remark ?: "")

            val canClickFile = shouldEnableClickForField(transaction.tranType)
            val file = transaction.listFile?.firstOrNull()

            KeyValueRow(
                label = "File đính kèm",
                value = file?.fileName ?: "Chưa có file lương",
                isHyperlink = file != null && canClickFile,
                onClick = if (file != null && canClickFile) {
                    { onFieldClick(TransactionFieldEvent.FileAttachmentClick(file)) }
                } else {
                    null
                },
            )

            transaction.listFile2?.firstOrNull()?.let {
                KeyValueRow(
                    label = "Hồ sơ đính kèm",
                    value = it.fileName ?: "",
                    isHyperlink = canClickFile,
                    onClick = if (canClickFile) {
                        {
                            onFieldClick(TransactionFieldEvent.ProfileAttachmentClick(it))
                        }
                    } else {
                        null
                    },
                )
            }

            KeyValueRow(
                label = "Thời gian chuyển",
                value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
            )
            if (!transaction.process_time.isNullOrEmpty()) {
                KeyValueRow(label = "Ngày đặt lịch", value = transaction.process_time ?: "")
            }
        }
    }
}

private fun shouldEnableClickForField(tranType: String?): Boolean {
    // Xử lý dựa vào tranType
    return when (tranType) {
        "sl" -> true
        "slo" -> true
        "sx" -> true
        else -> false
    }
}