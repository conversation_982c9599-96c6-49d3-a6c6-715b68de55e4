package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class Bulk300DetailRenderer @Inject constructor() : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Thông tin chung section
        SectionHeader(title = "Thông tin chung")
        TransactionDetailsCard {
            KeyValueRow(label = "Loại giao dịch", value = transaction.tranTypeName ?: "")
            KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
            KeyValueRow(label = "Số file ID", value = transaction.bulkID ?: "")
            KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin tài khoản thụ hưởng
        SectionHeader(title = "Thông tin giao dịch")
        TransactionDetailsCard {
            KeyValueRow(label = "File chuyển tiền", value = transaction.fileName ?: "")
            KeyValueRow(label = "Tổng số giao dịch", value = transaction.totalTrxNo ?: "")
            KeyValueRow(
                label = "Tổng số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
                subValue = transaction.amountInWords,
            )
            KeyValueRow(label = "Tổng phí giao dịch", value = transaction.feeAmount ?: "")
            KeyValueRow(label = "Hình thức thu phí", value = transaction.feePayMethodDesc ?: "")
            val label =
                if (transaction.process_time.isNullOrEmpty()) "Thời gian chuyển" else "Ngày đặt lịch"
            val value =
                if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else transaction.process_time!!
            KeyValueRow(label = label, value = value)
        }
    }

    override fun isNewRender(): Boolean {
        return true
    }

    @Composable
    override fun NewRenderTransactionDetails(
        modifier: Modifier,
        transaction: TransactionDomain,
        transactionHelper: TransactionHelperDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        Column(modifier = modifier.fillMaxWidth()) {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                // Status chip
                FoundationStatus(
                    statusCode = Status.Pending,
                    statusMessage = transaction.statusName,
                )

                FoundationInfoHorizontal(
                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                    title = stringResource(R.string.approve_file_id),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.bulkID ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_name),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.fileName ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterHighlighted,
                    isValueUnderline = true,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_fee_method),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.payFeeType ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                // hình thức chuyển - title - ngày chuyển
                val transferType: Triple<String, String, String> =
                    if (transaction.process_time.isNullOrEmpty()) {
                        Triple(
                            stringResource(R.string.transaction_transfer_immediate),
                            stringResource(R.string.transaction_label_transfer_time),
                            transaction.createdDate ?: "",
                        )
                    } else {
                        Triple(
                            stringResource(R.string.transaction_transfer_scheduled),
                            stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                            transaction.process_time ?: "",
                        )
                    }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_transfer_type),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transferType.first,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                FoundationInfoHorizontal(
                    title = transferType.second,
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transferType.third,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )
            }

            FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16))
        }
    }
}

@Composable
fun File300Item(
    isSelected: Boolean,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Gap.gap16),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationSelector(boxType = SelectorType.Checkbox)

            FoundationText(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = FDS.Colors.characterSecondary)) {
                        append(stringResource(R.string.transaction_number_label))
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            color = when {
                                isSelected -> FDS.Colors.characterHighlighted
                                else -> FDS.Colors.characterSecondary
                            },
                            fontWeight = FontWeight.SemiBold,
                        ),
                    ) {
                        append("transaction.mtId")
                    }
                },
                style = FDS.Typography.captionL,
                modifier = Modifier.weight(1f),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(R.string.approve_file_amount),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = "100,000 vnd",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        FoundationInfoHorizontal(
            title = stringResource(R.string.manager_detail_transfer_to),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = "1234567",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_content),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = "12345678",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )
    }
}