package com.vietinbank.feature_checker.ui.component.newui.multiple

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.MultipleResultDomain
import com.vietinbank.core_domain.models.checker.MultipleResultUIState
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatView
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewUIMultipleResultScreen(
    uiState: MultipleResultUIState,
    isShowCSat: Boolean,
    actions: (MultipleResultActions) -> Unit,
) {
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
        containerColor = Color.Transparent,
        topBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8, bottom = FDS.Sizer.Gap.gap16),
            ) {
                FoundationAppBar(
                    modifier = Modifier.fillMaxWidth(),
                    title = uiState.resultDomain?.transactionSuccess?.groupTransactions?.groupTypeName
                        ?: uiState.resultDomain?.transactionFailed?.groupTransactions?.groupTypeName
                        ?: "",
                    onNavigationClick = { actions(MultipleResultActions.HomeClick) },
                    isLightIcon = false,
                    isBackHome = true,
                    actions = listOf(
                        AppBarAction(
                            icon = R.drawable.ic_common_share_24,
                            contentDescription = stringResource(R.string.content_desc_share),
                            onClick = { actions(MultipleResultActions.ShareClick) },
                        ),
                    ),
                )
            }
        },
    ) { paddingValues ->

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.BottomCenter,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = FDS.Sizer.Gap.gap8)
                    .verticalScroll(rememberScrollState()),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                        .background(FDS.Colors.white),
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.bg_common_water_mark),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.Center),
                        contentScale = ContentScale.FillWidth,
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(FDS.Sizer.Gap.gap24),
                    ) {
                        uiState.resultDomain?.transactionSuccess?.let {
                            Column(modifier = Modifier.fillMaxWidth()) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationStatus(
                                        statusMessage = uiState.messageLst?.first ?: "",
                                        statusCode = Status.Success,
                                    )

                                    FoundationIconText(
                                        modifier = Modifier.safeClickable {
                                            actions(MultipleResultActions.DetailClick(true, it))
                                        },
                                        text = stringResource(R.string.common_detail),
                                        style = FDS.Typography.interactionSmallButton,
                                        color = FDS.Colors.characterHighlighted,
                                        icons = mapOf(
                                            IconPosition.RIGHT to IconConfig(
                                                icon = R.drawable.ic_right,
                                                size = FDS.Sizer.Icon.icon16,
                                            ),
                                        ),
                                    )
                                }

                                FoundationInfoHorizontal(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    title = stringResource(R.string.checker_multiple_transaction),
                                    value = it.totalTrans,
                                    valueStyle = FDS.Typography.bodyB2,
                                )

                                FoundationInfoHorizontal(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                                    title = stringResource(R.string.feature_checker_process_time),
                                    value = uiState.resultDomain?.tranDate ?: "",
                                    valueStyle = FDS.Typography.bodyB2,
                                )
                            }
                        }

                        if (uiState.resultDomain?.transactionSuccess != null && uiState.resultDomain?.transactionFailed != null) {
                            FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24))
                        }

                        uiState.resultDomain?.transactionFailed?.let {
                            Column(modifier = Modifier.fillMaxWidth()) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    FoundationStatus(
                                        statusMessage = uiState.messageLst?.second ?: "",
                                        statusCode = Status.Fail,
                                    )

                                    FoundationIconText(
                                        modifier = Modifier.safeClickable {
                                            actions(MultipleResultActions.DetailClick(false, it))
                                        },
                                        text = stringResource(R.string.common_detail),
                                        style = FDS.Typography.interactionSmallButton,
                                        color = FDS.Colors.characterHighlighted,
                                        icons = mapOf(
                                            IconPosition.RIGHT to IconConfig(
                                                icon = R.drawable.ic_right,
                                                size = FDS.Sizer.Icon.icon16,
                                            ),
                                        ),
                                    )
                                }

                                FoundationInfoHorizontal(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    title = stringResource(R.string.checker_multiple_transaction),
                                    value = it.totalTrans,
                                    valueStyle = FDS.Typography.bodyB2,
                                )
                            }
                        }
                    }
                }

                if (isShowCSat) {
                    // CSAT section outside white card
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                    CsatView(onClick = {
                        actions(MultipleResultActions.EmojiSelected(0))
                    })
                }
            }

            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FDS.Sizer.Gap.gap24,
                        vertical = FDS.Sizer.Gap.gap16,
                    ),
                text = stringResource(R.string.manager_transaction),
                onClick = {
                    actions(MultipleResultActions.ManageClick)
                },
            )
        }
    }
}

sealed class MultipleResultActions {
    data object HomeClick : MultipleResultActions()
    data object ShareClick : MultipleResultActions()
    data object ManageClick : MultipleResultActions()
    data class DetailClick(
        val isConfirmSuccess: Boolean,
        val resultMultiple: MultipleResultDomain,
    ) : MultipleResultActions()

    data class EmojiSelected(val emojiIndex: Int) : MultipleResultActions()
}
