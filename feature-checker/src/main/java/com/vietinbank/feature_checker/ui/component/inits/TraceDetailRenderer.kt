package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ItemSubTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

/**
 * Created by vandz on 9/4/25.
 */
class TraceDetailRenderer
@Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(
                    vertical = FoundationDesignSystem.Sizer.Padding.padding16,
                    horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            Row(
                modifier = modifier
                    .fillMaxWidth(),
            ) {
                FoundationStatus(
                    statusMessage = transaction.statusName
                        ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
                    statusCode = when (transaction.status?.uppercase()) {
                        Tags.QUERY_TYPE_APPROVED -> Status.Success
                        Tags.QUERY_TYPE_REJECTED -> Status.Fail
                        else -> Status.Pending
                    },
                )
                Spacer(modifier = Modifier.weight(1f))
            }

            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_title),
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
                value = transaction.remark ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            transaction.feeAccountNo?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account_type_D),
                    value = transaction.feeAccountNo ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_feeAmount),
                value = transaction.feeAmount ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_title_trace),
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            // Thông tin tài khoản thụ hưởng
            transaction.pref?.let { pref ->
                when (pref.tranType) {
                    Tags.CHECKER_TRANTYPE_CT, Tags.CHECKER_TRANTYPE_HU,
                    Tags.CHECKER_TRANTYPE_BA,
                    -> TransferDetail(pref, onFieldClick)
                    Tags.CHECKER_TRANTYPE_SX, Tags.CHECKER_TRANTYPE_SL,
                    Tags.CHECKER_TRANTYPE_SLO,
                    -> SalaryDetail(pref, onFieldClick)
                    Tags.CHECKER_TRANTYPE_TX, Tags.CHECKER_TRANTYPE_BTX,
                    -> CustomsInlandDetail(pref, onFieldClick)
                    Tags.CHECKER_TRANTYPE_IF -> InfrastructureDetail(pref, onFieldClick)
                    Tags.CHECKER_TRANTYPE_FX, Tags.CHECKER_TRANTYPE_FXR,
                    -> TransferForeignDetail(pref, onFieldClick)
                    else -> DefaultDetail(pref, onFieldClick)
                }
            }
        }
    }

    @Composable
    private fun TransferDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
            value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency} - ${transaction.fromAccountName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_to_account),
            value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
            value = transaction.receiveBankName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
            value = transaction.feePayMethodDesc ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
            value = transaction.remark ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        transaction.listFile?.firstOrNull()?.let {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                value = it.fileName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                onClick = {
                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
            value = transaction.createdDate ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (!transaction.process_time.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                value = transaction.process_time ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }
    }

    // chi luong tu dong + chi luong qua ngan hang
    @Composable
    private fun SalaryDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (Tags.CHECKER_TRANTYPE_SX == transaction.tranType) {
            transaction.exchangeRate?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_salary_detail),
                    value = it,
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
            transaction.debitAmount?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_salary_detail_debit_amount),
                    value = it,
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
            value = transaction.remark ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        transaction.listFile?.firstOrNull()?.let {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                value = it.fileName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                onClick = {
                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
                },
            )
        }

        transaction.listFile2?.firstOrNull()?.let {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_file),
                value = it.fileName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                onClick = {
                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
                },
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
            value = transaction.createdDate ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_transfer_time),
            value = if (transaction.process_time.isNullOrEmpty()) {
                stringResource(com.vietinbank.core_ui.R.string.transaction_transfer_immediate)
            } else {
                stringResource(com.vietinbank.core_ui.R.string.transaction_transfer_scheduled)
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (!transaction.process_time.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_appointment_date),
                value = transaction.process_time ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }
    }

    // chuyen tien tach lenh
    @Composable
    private fun DefaultDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
            value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency} - ${transaction.fromAccountName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_to_account),
            value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
            value = transaction.receiveBankName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
            value = transaction.feePayMethodDesc ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
            value = transaction.remark ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        transaction.listFile?.firstOrNull()?.let {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                value = it.fileName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                onClick = {
                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(it))
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
            value = transaction.createdDate ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
    }

    // nộp NSNN thuế nội địa/hải quan
    // giao dịch con nộp thuế theo file
    @Composable
    private fun CustomsInlandDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax),
            value = transaction.taxMethodName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_name),
            value = transaction.payname ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_number),
            value = transaction.paycode ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_address),
            value = transaction.payadd ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
            value = transaction.fromAccountNo ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_bank),
            value = transaction.branchName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_province),
            value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_area),
            value = "${transaction.areaCode ?: ""} - ${transaction.areaName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_treasury),
            value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collection),
            value = "${transaction.collectionAccountNo ?: ""} - ${transaction.collectionAccountName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collect),
            value = "${transaction.collectAgencyCode ?: ""} - ${transaction.collectAgencyName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (Tags.NSNN_CHECKER_TRANTYPE_ND == transaction.taxType) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_bu),
                value = "${transaction.bucode ?: ""} - ${transaction.buname ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_ofi),
                value = "${transaction.oficode ?: ""} - ${transaction.ofiname ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_chap),
            value = "${transaction.chapCode ?: ""} - ${transaction.chapName ?: ""}",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        if (!transaction.customTableNo.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_custom),
                value = transaction.customTableNo ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        if (Tags.NSNN_CHECKER_TRANTYPE_ND == transaction.taxType) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_custom_date),
                value = transaction.customDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_ie),
                value = "${transaction.ieType ?: ""} - ${transaction.ieName ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            if (!transaction.uid.isNullOrEmpty()) {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_uid),
                    value = transaction.uid ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
        }

        // Thông tin khác
        val additionalInfoItems = listOfNotNull(
            transaction.assetCode?.takeIf { it.isNotEmpty() }?.let {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_asset) to it
            },
            transaction.machineNo?.takeIf { it.isNotEmpty() }?.let {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_machine) to it
            },
            transaction.machineFeatures?.takeIf { it.isNotEmpty() }?.let {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_machine_features) to it
            },
            transaction.propertyAdd?.takeIf { it.isNotEmpty() }?.let {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_address) to it
            },
        )

        if (additionalInfoItems.isNotEmpty()) {
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other),
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            transaction.assetCode?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_asset),
                    value = transaction.assetCode ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
            transaction.machineNo?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_machine),
                    value = transaction.machineNo ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
            transaction.machineFeatures?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_machine_features),
                    value = transaction.machineFeatures ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
            transaction.propertyAdd?.let {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_other_address),
                    value = transaction.propertyAdd ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
        }

        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail_total),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (!transaction.items.isNullOrEmpty()) {
            if (transaction.items?.size!! > 1) {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                TraceDashedDividerDetail()
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
            }

            // Duyệt danh sách động từ API
            // Loop qua danh sách chi tiết
            transaction.items?.forEachIndexed { index, item ->
                TaxDetailSection(item)

                // Chỉ hiển thị Divider giữa các item nếu có nhiều hơn 1 item
                if (transaction.items?.size!! > 1 && index < transaction.items?.lastIndex!!) {
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                    TraceDashedDividerDetail()
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                }
            }
        }
    }

    @Composable
    fun TaxDetailSection(file: ItemSubTransactionDetailDomain) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail_content),
            value = file.content ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail_sit),
            value = file.sitCode ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail_tax),
            value = file.taxPeriod ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
            value = Utils.g().getDotMoneyHasCcy(
                file.amount ?: "",
                file.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
    }

    @Composable
    fun TraceDashedDividerDetail(
        color: Color = Color.Gray,
        thickness: Dp = FoundationDesignSystem.Sizer.Gap.gap1,
        dashWidth: Dp = FoundationDesignSystem.Sizer.Gap.gap6,
        gapWidth: Dp = FoundationDesignSystem.Sizer.Gap.gap4,
    ) {
        // Lấy Density từ Composable scope
        val density = LocalDensity.current

        // Dùng remember để cache giá trị px
        val dashPx = remember(dashWidth, density) { with(density) { dashWidth.toPx() } }
        val gapPx = remember(gapWidth, density) { with(density) { gapWidth.toPx() } }
        val thicknessPx = remember(thickness, density) { with(density) { thickness.toPx() } }

        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(thickness),
        ) {
            val totalWidth = size.width
            var currentX = 0f

            while (currentX < totalWidth) {
                drawLine(
                    color = color,
                    start = Offset(currentX, 0f),
                    end = Offset(currentX + dashPx, 0f),
                    strokeWidth = thicknessPx,
                )
                currentX += dashPx + gapPx
            }
        }
    }

    // Hạ tầng
    @Composable
    private fun InfrastructureDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        // HCM
        if (Tags.NSNN_CHECKER_TRANTYPE_IN == transaction.providerCode) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure),
                value = (transaction.providerCode ?: "") + (transaction.providerName ?: ""),
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }
        if (Tags.NSNN_CHECKER_PAYMETHOD == transaction.payMethod) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax),
                value = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_method),
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        } else if (Tags.NSNN_CHECKER_PAYMETHOD_TAX == transaction.payMethod) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax),
                value = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_method_other),
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_name),
            value = transaction.payname ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_number),
            value = transaction.paycode ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_address),
            value = transaction.payAddress ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
            value = transaction.fromAccountNo ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_bank),
            value = transaction.branchName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (Tags.NSNN_CHECKER_TRANTYPE_IN != transaction.providerCode) {
            // Thông tin cơ quan thu
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland),
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_province),
                value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_area),
                value = "${transaction.areaCode ?: ""} - ${transaction.areaName ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_treasury),
                value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collection),
                value = transaction.collectingAccount ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collect),
                value = "${transaction.collectionAgencyCode ?: ""} - ${transaction.collectionAgencyName ?: ""}",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )

            if (!transaction.declareNumber.isNullOrEmpty()) {
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_custom),
                    value = transaction.declareNumber ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                )
            }
        }

        // Thông tin
        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_detail),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider),
            value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == transaction.providerCode) {
                transaction.invoiceId ?: ""
            } else {
                transaction.docId ?: ""
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider_doc),
            value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == transaction.providerCode) {
                transaction.voucherNumber ?: ""
            } else {
                transaction.docNum ?: ""
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider_syb),
            value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == transaction.providerCode) {
                transaction.voucherSymbol ?: ""
            } else {
                transaction.docSign ?: ""
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider_date),
            value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == transaction.providerCode) {
                transaction.voucherDate ?: ""
            } else {
                transaction.docDate ?: ""
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (Tags.NSNN_CHECKER_TRANTYPE_IN != transaction.providerCode) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_chap),
                value = transaction.chapterCode ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider_subsect),
                value = transaction.subsect ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_infrastructure_provider_amount),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
    }

    // Chuyển tiền ngoại tệ
    @Composable
    private fun TransferForeignDetail(
        transaction: TransactionDomain,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_name),
            value = transaction.fromAccountName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (transaction.remitterName?.isEmpty() == false) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_name_transfer),
                value = transaction.fromAccountName ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                value = transaction.remitterCountry ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                value = transaction.remitterDistrict ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                value = transaction.remitterWard ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                value = transaction.remitterStreet ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }

        // Thông tin
        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_introducer),
            value = transaction.introducer ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_branch),
            value = transaction.branchName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_total_amount),
            value = transaction.totalAmount ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.common_content),
            value = transaction.remark ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_note),
            value = transaction.note ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_date),
            value = transaction.valueDate ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_purpose),
            value = transaction.purposeTransferName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return),
            value = if (transaction.lcReturnDate?.isEmpty() == false) {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return_after)
            } else {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return_befor)
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (!transaction.lcReturnDate.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_date_return),
                value = transaction.lcReturnDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.bodyB2,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.bodyB2,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
            )
        }
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_total),
            value = Utils.g().getDotMoneyHasCcy(
                transaction.totalTrxNo ?: "",
                transaction.currency ?: "",
            ),
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_feetype),
            value = transaction.feeType ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_feeaccount),
            value = transaction.feeAccountNo ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_account_number),
            value = transaction.fromAccountNo ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debit),
            value = transaction.debitRate1 ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debitAmt),
            value = transaction.debitAmtByRate1 ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_no),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_account_number),
            value = transaction.fromAccount2 ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debit),
            value = transaction.debitRate2 ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debitAmt),
            value = transaction.debitAmtByRate2 ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_ficiary),
            value = transaction.beneficiaryAccount ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_name),
            value = transaction.benName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
            value = transaction.benCountry ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
            value = transaction.benDistrict ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
            value = transaction.benWard ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
            value = transaction.benStreet ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter),
            value = if (transaction.remitterName?.isEmpty() == false) {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter_yes)
            } else {
                stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter_no)
            },
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_final),
            value = transaction.finalBenName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
            value = transaction.finalBenCountry ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
            value = transaction.finalBenDistrict ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
            value = transaction.finalBenWard ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
            value = transaction.finalBenStreet ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_bic),
            value = transaction.receiveBank ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_address),
            value = transaction.benBankName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
            value = transaction.benBankCountry ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
            value = transaction.benBankDistrict ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
            value = transaction.benBankWard ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
            value = transaction.benBankStreet ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_banks),
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_bic),
            value = transaction.midBankCode ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_address),
            value = transaction.midBankName ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
            value = transaction.midBankCountry ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
            value = transaction.midBankDistrict ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
            value = transaction.midBankWard ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )
        FoundationInfoHorizontal(
            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
            value = transaction.midBankStreet ?: "",
            titleStyle = FoundationDesignSystem.Typography.bodyB2,
            titleColor = FoundationDesignSystem.Colors.characterSecondary,
            valueStyle = FoundationDesignSystem.Typography.bodyB2,
            valueColor = FoundationDesignSystem.Colors.characterPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
        )

        if (!transaction.listFile.isNullOrEmpty()) {
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))

            FoundationText(
                text = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                style = FoundationDesignSystem.Typography.bodyB2,
                color = FoundationDesignSystem.Colors.characterHighlighted,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding16),
            )

            transaction.listFile?.firstOrNull()?.let { file ->
                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_file),
                    value = file.fileName ?: "",
                    titleStyle = FoundationDesignSystem.Typography.bodyB2,
                    titleColor = FoundationDesignSystem.Colors.characterSecondary,
                    valueStyle = FoundationDesignSystem.Typography.bodyB2,
                    valueColor = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FoundationDesignSystem.Sizer.Padding.padding4),
                    onClick = {
                        onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                    },
                )
            }
        }
    }
}