package com.vietinbank.feature_checker.ui.fragment.newui.multiple

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.maping.confirm.MultipleConfirmUIModel
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.newui.multiple.NewUIMultipleApprovalScreen
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import com.vietinbank.feature_checker.ui.fragment.MultipleActions
import com.vietinbank.feature_checker.ui.fragment.MultipleApprovalListViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NewUIMultipleApprovalFragment : BaseFragment<MultipleApprovalListViewModel>() {

    override val viewModel: MultipleApprovalListViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    lateinit var transactionRendererFactory: TransactionRendererFactory

    @Composable
    override fun ComposeScreen() {
        // Collect UI state
        val uiState by viewModel.uiState.collectAsState()
        NewUIMultipleApprovalScreen(
            uiState = uiState,
            transactionRendererFactory = transactionRendererFactory,
            actions = { action ->
                when (action) {
                    is MultipleActions.BackClick -> {
                        appNavigator.popBackStack()
                    }

                    is MultipleActions.ContinueClick -> {
                        handleMultipleTransaction(action.isApprove)
                    }

                    is MultipleActions.SelectAllClick -> {
                        viewModel.toggleMaxSelectAll()
                    }

                    is MultipleActions.LoadMore -> {
                        viewModel.loadMoreTransactions()
                    }

                    is MultipleActions.RetryLoadMore -> {
                        viewModel.retryLoadMore()
                    }

                    is MultipleActions.TransactionClick -> {
                        // Navigate to detail
                        navigateToTransactionDetail(action.mtId)
                    }

                    is MultipleActions.TransactionLongClick -> {
                    }

                    is MultipleActions.TransactionSelectedClick -> {
                        viewModel.toggleTransactionSelection(action.mtId)
                    }

                    is MultipleActions.FilterSelectedClick -> {
                        viewModel.onChangeSubType(action.tranType)
                    }
                }
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get bundle data first
        getBundleData()

        // Initialize observers
        initObserver()
    }

    private fun getBundleData() {
        viewModel.initUiState(arguments)
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this, viewModel.preApprove) {
            it?.let { data ->
                val uiModel = MultipleConfirmUIModel(
                    titleScreen = if (data.isApprove) {
                        getString(R.string.checker_multiple_approve_title)
                    } else {
                        getString(R.string.checker_multiple_reject_title)
                    },
                    transactionIds = viewModel.getSelectedTransactionsAsList(),
                    serviceType = viewModel.getServiceType(),
                    tranType = viewModel.getTranTypeApprove(),
                    groupType = viewModel.getGroupType(),
                    groupTransactions = data.groupTransactions,
                    methodList = data.methodList,
                    isApprove = data.isApprove,
                    totalFee = data.totalFee.toString(),
                )

                appNavigator.goToMultipleConfirmApproval(
                    bundleOf().apply {
                        putString(Tags.TRANSACTION_BUNDLE, Utils.g().provideGson().toJson(uiModel))
                    },
                )
            }
        }

        FlowUtils.collectFlow(this, viewModel.loadMoreError) {
            printLog("Load more error: $it")
        }
    }

    private fun handleMultipleTransaction(isApprove: Boolean) {
        // Validate selection
        val validationError = viewModel.validateTotalTransactions()
        if (validationError != null) {
            showNoticeDialog(validationError)
            return
        }
        // neu chi chon 1 giao dich => dieu huong sang luong duyet don
        if (viewModel.getSelectedTransactionsAsList().size == 1) {
            navigateToTransactionDetail(
                viewModel.getSelectedTransactionsAsList().first(),
                if (isApprove) {
                    InitTransactionViewModel.MULTIPLE_APPROVE
                } else {
                    InitTransactionViewModel.MULTIPLE_REJECT
                },
            )
        } else if (isApprove) {
            // Proceed with rejection
            viewModel.preApprove()
        } else {
            viewModel.preReject()
        }
    }

    private fun navigateToTransactionDetail(transactionId: String, multipleType: String? = null) {
        val originalTransaction = viewModel.getOriginalTransactionByMTId(transactionId)
        originalTransaction?.let {
            printLog(
                "Navigating to detail for transaction: ${
                    Utils.g().provideGson().toJson(it)
                }",
            )
            val jsonData = Utils.g().provideGson().toJson(it)
            appNavigator.goToInitTransaction(jsonData, multipleType)
        }
    }
}