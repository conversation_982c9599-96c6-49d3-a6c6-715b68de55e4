package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListActions
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.screen.TransactionListScreen
import com.vietinbank.feature_checker.ui.sheet.SortApproveSheet
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ApprovalListFragment : BaseFragment<ApprovalListViewModel>() {
    override val viewModel: ApprovalListViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var transactionRendererFactory: TransactionRendererFactory

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    private var sortSheet: SortApproveSheet? = null

    @Composable
    override fun ComposeScreen() {
        // Tạo object cho TransactionListActions
        val actions =
            TransactionListActions(
                // Navigation actions
                onBackClick = { appNavigator.popBackStack() },
                onHomeClick = { appNavigator.goToHome() },
                // List actions
                onSortClick = {
                    // Xử lý sắp xếp
                    // show popup săp xep
//                    if (sortSheet == null) {
//                        sortSheet = SortApproveSheet(viewModel.currentServiceType)
//                    }
//                    sortSheet?.setOnApplyClick {
//                        viewModel.getTransactionList()
//                    }
//                    sortSheet?.show(childFragmentManager, SortApproveSheet::class.java.name)
                },
                onSelectClick = { viewModel.toggleSelectionMode() },
                onCloseSelectionClick = { viewModel.toggleSelectionMode() },
                // Transaction item actions
                onTransactionClick = { transaction ->
                    // Normal case - Mở chi tiết giao dịch
                    val originalTransaction =
                        viewModel.getOriginalTransactionByMTId(transaction.mtID)
                    originalTransaction?.let {
                        printLog(
                            "current trans: ${
                                Utils.g().provideGson().toJson(originalTransaction)
                            }",
                        )
                        val jsonData = Utils.g().provideGson().toJson(it)
                        appNavigator.goToInitTransaction(jsonData, null)
                    }
                },
                onTransactionMenuClick = { transaction ->
                    // Xử lý khi click vào menu của giao dịch
                },
                onTransactionSelectClick = { transaction ->
                    // Xử lý khi chọn/bỏ chọn giao dịch trong chế độ multi-select
                    if (viewModel.selectedSubTransactions.value.isNotEmpty()) {
                        Toast.makeText(context, "Đã chọn " + viewModel.selectedSubTransactions.value.size + " giao dịch", Toast.LENGTH_SHORT).show()
                    }
                    printLog("Transaction selected/deselected: ${transaction.mtID}")
                },
                onApproveClick = {
                    // Xử lý duyệt các giao dịch đã chọn
                    handleApproveTransactions()
                },
                onRejectClick = {
                    // Xử lý từ chối các giao dịch đã chọn
                    handleRejectTransactions()
                },
                // Error handling
                onRetryClick = {
                    // Xử lý khi click vào nút thử lại
                },
                onLoadMore = {
                    viewModel.loadMoreTransactions()
                },
            )
        AppTheme {
            TransactionListScreen(
                viewModel = viewModel,
                actions,
                factory = transactionRendererFactory,
            )
        }
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        // Lấy dữ liệu từ Bundle arguments
        getBundleData()
        initObserver()

        viewLifecycleOwner.lifecycleScope.launch {
            setFragmentResultListener(SuccessFragment.APPROVAL_FLOW_RESULT_KEY) { _, bundle ->
                val status = bundle.getString("status")
                val needRefresh = bundle.getBoolean("needRefresh", false)
                if (status == "SUCCESS") {
                    // Xử lý logic khi giao dịch thành công
                    if (needRefresh && viewModel.originalTransactions.isNotEmpty()) {
                        // Gọi API để làm mới danh sách giao dịch
                        val firstTransaction = viewModel.originalTransactions.firstOrNull()
                        firstTransaction?.let {
                            // Không cần thêm tag cho ComposeView nữa
                            viewModel.refreshTransactionList(
                                groupType = it.groupType ?: "",
                                serviceType = it.serviceType ?: "",
                                tranType = it.tranType ?: "",
                            )
                        }
                    }
                }
            }
        }
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        preApprove.collect { resource ->
                            resource?.let { data ->
                                handleTransactionResult(
                                    data,
                                    if (data.isApprove) {
                                        Tags.APPROVE
                                    } else {
                                        Tags.REJECT
                                    },
                                )
                            }
                        }
                    }

                    launch {
                        preApproveSingle.collect { resource ->
                            resource?.let { data ->
                                handleSingleTransactionResult(
                                    data,
                                    if (data.isApprove) {
                                        Tags.APPROVE
                                    } else {
                                        Tags.REJECT
                                    },
                                    transaction = data.transactionProcessor!!,
                                )
                            }
                        }
                    }

                    launch {
                        // refresh transaction list
                        refreshTransactionList.collect { data ->
                            viewModel.processTransactions(data.transactions ?: listOf())
                            // Scroll to top của danh sách sau khi refresh xong
                            if (view != null && isAdded) {
                                viewModel.needScrollToTop = true
                            }
                        }
                    }

                    launch {
                        loadMoreError.collect { errorMessage ->
                            printLog("Lỗi loadMore: $errorMessage")
                        }
                    }
                }
            }
        }
    }

    private fun getBundleData() {
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                // Chuyển đổi JSON thành danh sách TransactionListDomain
                val type = object : TypeToken<List<TransactionListDomain>>() {}.type
                val transactions: List<TransactionListDomain> =
                    Utils.g().provideGson().fromJson(jsonData, type)

                // Cập nhật ViewModel với dữ liệu đã nhận
                printLog(
                    "Frist trans item: ${
                        Utils.g().provideGson().toJson(transactions.firstOrNull())
                    }",
                )
                viewModel.processTransactions(transactions)
                printLog("Đã nhận ${transactions.size} giao dịch")
            } catch (e: Exception) {
                printLog("Lỗi parse JSON: ${e.message}")
                viewModel.showError("Không thể xử lý dữ liệu giao dịch.")
            }
        } ?: run {
            // Không có dữ liệu trong arguments
            printLog("Không có dữ liệu transactions trong bundle")
            viewModel.showError("Không tìm thấy dữ liệu giao dịch.")
        }

        viewModel.currentTransType = arguments?.getString(Tags.TRAN_TYPE_BUNDLE)
        viewModel.currentGroupType = arguments?.getString(Tags.GROUP_TYPE_BUNDLE)
        viewModel.currentServiceType = arguments?.getString(Tags.SERVICE_TYPE_BUNDLE)
    }

    /**
     * Xử lý duyệt các giao dịch đã chọn
     */
    private fun handleApproveTransactions() {
        viewModel.validateTotalTransactions()?.let { msg ->
            showNoticeDialog(msg)
            return
        }
        viewModel.preApprove()
    }

    /**
     * Xử lý từ chối các giao dịch đã chọn
     */
    private fun handleRejectTransactions() {
        viewModel.validateTotalTransactions()?.let { msg ->
            showNoticeDialog(msg)
            return
        }
        viewModel.preReject()
    }

    private fun handleTransactionResult(
        preDomain: PreApproveDomain,
        confirmType: String,
    ) {
        // Tạo ConfirmUiModel
        val uiModel =
            ConfirmUiModel(
                screenTitle = "Xác nhận",
                items = viewModel.buildConfirmItems(preDomain),
                transactionIds = viewModel.getSelectedTransactionsAsList(),
                serviceType = viewModel.getSelectedServiceTypeFromList(),
                tranType = viewModel.getSelectedTranTypeFromList(),
                groupType = viewModel.getSelectedGroupTypeFromList(),
            )

        val paymentMethod = confirmLauncher.getPaymentMethod(preDomain)
        viewModel.toggleSelectionMode() // Clear selection mode
        confirmLauncher.launchWithBatchConfirmModel(
            confirmUiModel = uiModel,
            confirmType = confirmType,
            paymentMethod = paymentMethod,
        )
    }

    /**
     * Xử lý kết quả từ API preApprove hoặc preReject và điều hướng đến màn hình xác nhận
     * @param success Resource chứa kết quả từ API
     * @param confirmType Loại xác nhận (APPROVE hoặc REJECT)
     * @param transaction Đối tượng giao dịch gốc
     */
    private fun handleSingleTransactionResult(
        success: PreApproveDomain,
        confirmType: String,
        transaction: TransactionDomain,
    ) {
        // Lấy thông tin phương thức thanh toán
        val paymentMethod = confirmLauncher.getPaymentMethod(success)

        // Chỉ clear selection mode nếu đang được enable
        if (viewModel.isSelectionMode.value) {
            viewModel.toggleSelectionMode()
        }

        // Điều hướng đến màn hình xác nhận
        confirmLauncher.launchWithSingleTransaction(
            transaction = transaction,
            confirmType = confirmType,
            paymentMethod = paymentMethod,
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        sortSheet = null
    }
}