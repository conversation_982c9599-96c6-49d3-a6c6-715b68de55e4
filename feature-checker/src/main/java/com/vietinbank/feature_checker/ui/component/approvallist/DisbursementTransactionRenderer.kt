package com.vietinbank.feature_checker.ui.component.approvallist

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import javax.inject.Inject
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class DisbursementTransactionRenderer
@Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        DisbursementTransactionInfo(
            modifier = Modifier.fillMaxWidth(),
            transaction = transaction,
            isSelectionMode = isSelectionMode,
            isSelected = isSelected,
            onChangeClick = onChangeClick,
        )
    }
}

@Composable
fun DisbursementTransactionInfo(
    transaction: TransactionListDomain,
    modifier: Modifier = Modifier,
    isSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onChangeClick: () -> Unit = {},
    allowCopy: Boolean = true,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        // 1. Transaction number with inline value and badge
        TransactionNumber(
            transactionNumber = transaction.mtId.orEmpty(),
            isSelectionMode = isSelectionMode,
            isSelected = isSelected,
            allowCopy = allowCopy,
            onChangeClick = onChangeClick,
        )

        // 2. Transaction type with icon and amount
        TransactionTypeAndAmount(
            transaction.amount.orEmpty(),
            transaction.currency.orEmpty(),
            transaction.tranTypeName.orEmpty(),
        )

        FoundationInfoHorizontal(
            title = stringResource(CoreR.string.feature_checker_disbursement_offer_time),
            value = transaction.disbursementDate,
            titleStyle = FDS.Typography.captionL,
            valueStyle = FDS.Typography.captionLSemibold,
        )

        FoundationInfoHorizontal(
            title = stringResource(CoreR.string.feature_checker_disbursement_loan_rate),
            value = stringResource(
                CoreR.string.feature_checker_disbursement_percent_per_year,
                transaction.loanRate.orEmpty(),
            ),
            titleStyle = FDS.Typography.captionL,
            valueStyle = FDS.Typography.captionLSemibold,
        )

        // 5. Creation time
        FoundationInfoHorizontal(
            title = stringResource(CoreR.string.feature_checker_disbursement_debt_repayment_term),
            value = transaction.loanTerm,
            titleStyle = FDS.Typography.captionL,
            valueStyle = FDS.Typography.captionLSemibold,
        )

        // 6. Transaction status
        FoundationInfoHorizontal(
            title = stringResource(CoreR.string.feature_checker_disbursement_create_time),
            value = transaction.createdDate,
            titleStyle = FDS.Typography.captionL,
            valueStyle = FDS.Typography.captionLSemibold,
        )

        FoundationInfoHorizontal(
            title = stringResource(CoreR.string.feature_checker_disbursement_status),
            value = transaction.statusName,
            titleStyle = FDS.Typography.captionL,
            valueStyle = FDS.Typography.captionLSemibold,
            valueColor = transaction.status.getColorStatus(),
        )
    }
}

@Composable
private fun TransactionNumber(
    transactionNumber: String,
    isSelectionMode: Boolean,
    isSelected: Boolean,
    allowCopy: Boolean = true,
    onChangeClick: (() -> Unit) = {},
) {
    val clipboardManager = LocalClipboardManager.current
    val context = LocalContext.current

    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (isSelectionMode) {
            FoundationSelector(
                modifier = Modifier.padding(end = FDS.Sizer.Gap.gap4),
                boxType = SelectorType.Checkbox,
                isSelected = isSelected,
                onClick = onChangeClick,
            )
        }

        FoundationText(
            text = buildAnnotatedString {
                withStyle(style = SpanStyle(color = FDS.Colors.characterSecondary)) {
                    append(stringResource(CoreR.string.transaction_number_label))
                }
                append(" ")
                withStyle(
                    style = SpanStyle(
                        color = FDS.Colors.characterHighlighted,
                        fontWeight = FontWeight.SemiBold,
                    ),
                ) {
                    append(transactionNumber)
                }
            },
            style = FDS.Typography.captionL,
            modifier = Modifier
                .safeClickable {
                    if (!allowCopy) return@safeClickable
                    clipboardManager.setText(AnnotatedString(transactionNumber))
                    Toast.makeText(
                        context,
                        context.getString(CoreR.string.manager_detail_copy) + " " + transactionNumber,
                        Toast.LENGTH_SHORT,
                    ).show()
                },
        )

        if (allowCopy) {
            Image(
                painter = painterResource(CoreR.drawable.ic_account_copy_24),
                contentDescription = null,
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon16)
                    .safeClickable {
                        clipboardManager.setText(AnnotatedString(transactionNumber))
                        Toast.makeText(
                            context,
                            context.getString(CoreR.string.manager_detail_copy) + " " + transactionNumber,
                            Toast.LENGTH_SHORT,
                        ).show()
                    },
            )
        }
    }
}

@Composable
private fun TransactionTypeAndAmount(
    amount: String,
    currency: String,
    tranTypeName: String,
) {
    // Cache formatted amount
    val formattedAmount = remember(amount, currency) {
        try {
            val cleanAmount = amount.getAmountServer()
            Utils.g().getDotMoneyHasCcy(cleanAmount, currency)
        } catch (_: Exception) {
            "$amount $currency"
        }
    }

    // Localized short labels
    val typeVND = stringResource(CoreR.string.feature_checker_transaction_type_disbursement_vnd)
    val typeForeign = stringResource(CoreR.string.feature_checker_transaction_type_disbursement_foreign)

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Left side: Icon + Transaction type name
        Row(
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_disbursement_transaction_type),
                contentDescription = null,
                tint = FDS.Colors.characterPrimary,
                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
            )

            // Shorten the transaction type name for better display
            val shortTypeName = tranTypeName.let {
                when {
                    it.contains(
                        "Giải ngân ngoại tệ",
                        ignoreCase = true,
                    ) -> typeForeign

                    else -> typeVND
                }
            }

            FoundationText(
                text = shortTypeName,
                style = FDS.Typography.captionLSemibold,
                color = FDS.Colors.characterPrimary,
            )
        }

        // Right side: Amount with currency (right aligned)
        FoundationText(
            text = formattedAmount,
            style = FDS.Typography.captionLSemibold,
            color = FDS.Colors.characterPrimary,
            textAlign = TextAlign.End,
        )
    }
}