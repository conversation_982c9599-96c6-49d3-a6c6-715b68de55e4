package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.feature_checker.R
import javax.inject.Inject

class NewUnLockUserTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (isSelectionMode) {
                    FoundationSelector(
                        modifier = Modifier.padding(end = FoundationDesignSystem.Sizer.Gap.gap4),
                        boxType = SelectorType.Checkbox,
                        isSelected = isSelected,
                        onClick = onChangeClick,
                    )
                }
                val label = stringResource(id = com.vietinbank.core_ui.R.string.transaction_number_label) + ": "
                FoundationText(
                    text = buildAnnotatedString {
                        withStyle(style = SpanStyle(color = FoundationDesignSystem.Colors.characterSecondary)) {
                            append(label)
                        }
                        withStyle(
                            style = SpanStyle(
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                fontWeight = FontWeight.Bold,
                            ),
                        ) {
                            append(transaction.mtId ?: "")
                        }
                    },
                    style = FoundationDesignSystem.Typography.captionCaptionL,
                    modifier = Modifier.weight(1f),
                )
            }

            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

            Row(
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap4),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_checker_sort_arrow_16),
                    contentDescription = null,
                    tint = FoundationDesignSystem.Colors.characterPrimary,
                    modifier = Modifier.size(FoundationDesignSystem.Sizer.Icon.icon16),
                )

                FoundationText(
                    text = transaction.tranTypeName ?: "",
                    style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                    color = FoundationDesignSystem.Colors.characterPrimary,
                )
            }

            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))

            transaction.creator?.let { detail ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.lock_init_name),
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterSecondary,
                        modifier = Modifier.width(FoundationDesignSystem.Sizer.Padding.padding100),
                    )
                    FoundationText(
                        text = detail,
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        textAlign = TextAlign.End,
                        modifier = Modifier.weight(1f),
                    )
                }
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            }

            transaction.createdDate?.let { detail ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.feature_checker_creation_time),
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterSecondary,
                    )
                    FoundationText(
                        text = detail,
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterPrimary,
                    )
                }
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            }

            transaction.statusName?.let { it ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.feature_checker_status),
                        style = FoundationDesignSystem.Typography.captionCaptionL,
                        color = FoundationDesignSystem.Colors.characterSecondary,
                    )
                    FoundationText(
                        text = it,
                        style = FoundationDesignSystem.Typography.captionCaptionLBold,
                        color = transaction.statusCode.getColorStatus(),
                    )
                }
            }
        }
    }
}