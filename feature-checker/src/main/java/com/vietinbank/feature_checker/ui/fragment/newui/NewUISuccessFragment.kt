package com.vietinbank.feature_checker.ui.fragment.newui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getParcelableCustom
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.AssessmentSuccessDialog
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatDialog
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatResult
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.component.newui.NewUISuccessScreen
import com.vietinbank.feature_checker.ui.component.success.renderer.TransactionSuccessRendererFactory
import com.vietinbank.feature_checker.ui.fragment.SuccessFragment
import com.vietinbank.feature_checker.ui.fragment.SuccessViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * New UI Success Fragment with Foundation Design System
 * Reuses SuccessViewModel for business logic
 */
@AndroidEntryPoint
class NewUISuccessFragment : BaseFragment<SuccessViewModel>() {

    override val viewModel: SuccessViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    @Inject
    lateinit var rendererFactory: TransactionSuccessRendererFactory

    private fun onBackToListClick() {
        // Set fragment result for approval list to refresh
        appNavigator.setFragmentResult(
            SuccessFragment.APPROVAL_FLOW_RESULT_KEY,
            bundleOf(
                "status" to "SUCCESS",
                "message" to "Giao dịch đã được xử lý thành công",
                "timestamp" to System.currentTimeMillis(),
                "needRefresh" to true,
            ),
        )
        // Navigate back to approval list
        appNavigator.popToApprovalList()
    }

    @Composable
    override fun ComposeScreen() {
        // Ensure bank list is loaded
        androidx.compose.runtime.LaunchedEffect(Unit) {
            viewModel.ensureBankListLoaded()
        }

        // Collect state from ViewModel
        val transaction by viewModel.transaction.collectAsState()
        val approveResult by viewModel.approveResult.collectAsState()
        val title by viewModel.title.collectAsState()
        val isShowCSatState by viewModel.isShowCSatState.collectAsState()
        val confirmType by viewModel.confirmType.collectAsState()
        val banksCache by viewModel.banksFlow.collectAsState(initial = null)
        val approvalHistory by viewModel.approvalHistory.collectAsState()
        val messageStatus by viewModel.messageStatus.collectAsState()

        // Determine tranType from arguments or use default
        val tranType = transaction.tranType ?: arguments?.getString("tranType") ?: "in" // Default to "in" if not specified

        AppTheme {
            NewUISuccessScreen(
                transaction = transaction, // Pass TransactionDomain directly
                approveResult = approveResult, // Pass ApproveResult
                successTitle = (title?.amountTitle ?: "Giao dịch") to (
                    title?.amountText
                        ?: "Thành công"
                    ),
                message = messageStatus,
                confirmType = confirmType ?: "",
                tranType = tranType, // Pass tranType
                rendererFactory = rendererFactory, // Pass the injected factory
                isRejected = confirmType == Tags.REJECT || title?.status == "Từ chối", // Check if this is a rejection
                isShowCSatState = isShowCSatState,
                imageLoader = imageLoader, // Pass imageLoader for loading bank icons
                getBankIconUrl = { bankName -> viewModel.getBankIconUrl(bankName) }, // Pass function for bank icon mapping
                activityLogs = approvalHistory,
                onHomeClick = {
                    // Navigate to home
                    appNavigator.goToHome()
                },
                onShareClick = {
                    // Capture and share screenshot using ScreenshotUtil
                    viewLifecycleOwner.lifecycleScope.launch {
                        try {
                            // Get the root view of the fragment
                            val rootView = view?.rootView ?: return@launch

                            // Create a full screen bounds since we want to capture the entire success screen
                            val screenBounds = androidx.compose.ui.geometry.Rect(
                                0f,
                                0f,
                                rootView.width.toFloat(),
                                rootView.height.toFloat(),
                            )

                            // Capture the screenshot
                            val bitmap =
                                com.vietinbank.core_ui.utils.ScreenshotUtil.captureScreenshot(
                                    rootView = rootView,
                                    contentBounds = screenBounds,
                                    context = requireContext(),
                                )

                            bitmap?.let {
                                // Share the captured screenshot
                                com.vietinbank.core_ui.utils.ScreenshotUtil.shareBitmap(
                                    context = requireContext(),
                                    bitmap = it,
                                )
                            } ?: run {
                                Toast.makeText(
                                    requireContext(),
                                    getString(com.vietinbank.core_ui.R.string.common_cannot_screenshot),
                                    Toast.LENGTH_SHORT,
                                ).show()
                            }
                        } catch (e: Exception) {
                            Toast.makeText(
                                requireContext(),
                                getString(
                                    com.vietinbank.core_ui.R.string.common_error_share,
                                    e.message,
                                ),
                                Toast.LENGTH_SHORT,
                            ).show()
                        }
                    }
                },
                onTransactionManageClick = {
                    // Navigate to transaction management
                    // 0 - tab yeu cau, 1 - tab quan lý
                    appNavigator.gotoTransactionManager(
                        bundleOf().apply { putInt(Tags.TRANSACTION_BUNDLE, 1) },
                    )
                },
                onEmojiSelected = { rating ->
                    // Show CSAT rating dialog
                    val dialog = CsatDialog.newInstance()
                    dialog.show(childFragmentManager, "CsatDialog")
                },
                onFileClick = { item ->
                    // Handle file download
                    viewModel.downloadFile(item)
                },
                onShowUpdateDialog = {},
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Hide keyboard with delay to ensure it happens after all transitions
        viewLifecycleOwner.lifecycleScope.launch {
            // Small delay for initial layout
            kotlinx.coroutines.delay(100)
            activity?.currentFocus?.clearFocus()
            activity?.window?.decorView?.clearFocus()
            hideKeyboard()

            // Double-check after navigation transition completes
            kotlinx.coroutines.delay(200)
            activity?.currentFocus?.clearFocus()
            hideKeyboard()
        }

        setupObserver()

        // Get data from bundle passed from NewUIInitTransactionFragment
        val successListJson = arguments?.getString(Tags.SUCCESS_LIST_BUNDLE)
        val successTitle = arguments?.getString(Tags.TRANSACTION_BUNDLE)
        val confirmType = arguments?.getString(Tags.CONFIRM_TYPE_BUNDLE)
        val messageStatus = arguments?.getString(Tags.MESSAGE_STATUS_BUNDLE)

        // Process data using existing ViewModel logic
        viewModel.processData(successListJson, successTitle, confirmType, messageStatus)

        // Configure CSAT if needed
        viewModel.configCSat(Tags.CSAT_FUNC_ID_KEY)

        // Force update account list based on transaction type
        // "DDA" - payment account, "LN" - loan account, "CD" - deposit account
        viewModel.forceUpdateAccountList("DDA")

        // Listen for dialog result
        childFragmentManager.setFragmentResultListener(
            "csat_dialog_result",
            this@NewUISuccessFragment,
        ) { _, bundle ->
            bundle.getParcelableCustom<CsatResult>("key_result")?.let {
                // Process the rating
                viewModel.rateCSat(Tags.CSAT_FUNC_ID_KEY, it.feedbackPoint, it.feedbackComment)
            }
        }
    }

    override fun onBackPressed(): Boolean {
        // Handle back press - navigate to approval list
        onBackToListClick()
        return true
    }

    private fun setupObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                // Collect error events
                launch {
                    viewModel.errorEvent.collect { errorMessage ->
                        showNoticeDialog(errorMessage)
                    }
                }

                // Collect toast events
                launch {
                    viewModel.toastEvent.collect { message ->
                        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                    }
                }

                // Collect CSAT rating status
                launch {
                    viewModel.rateCSATStatus.collect {
                        // Show success dialog for CSAT rating
                        AssessmentSuccessDialog.newInstance().show(
                            childFragmentManager,
                            AssessmentSuccessDialog::class.java.name,
                        )
                    }
                }

                // Collect file permission events
                launch {
                    viewModel.filePermissionEvent.collect { message ->
                        showNoticeDialog(message)
                    }
                }

                // Observer for file URL ready - open in browser
                launch {
                    viewModel.fileUrlReady.collect { url ->
                        try {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog("Không thể mở file")
                        }
                    }
                }

                // Collect download file response
                launch {
                    viewModel.getDownloadFileID.collect { resource ->
                        resource?.let {
                            // Handle download response
                            when (it) {
                                is com.vietinbank.core_common.utils.Resource.Success -> {
                                    // Success handled by fileUrlReady flow
                                }

                                is com.vietinbank.core_common.utils.Resource.Error -> {
                                    showNoticeDialog(it.message ?: "Không thể tải file")
                                }

                                else -> {
                                    // Loading or other states
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
