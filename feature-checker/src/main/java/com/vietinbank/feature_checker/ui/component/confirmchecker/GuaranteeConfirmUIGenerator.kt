package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class GuaranteeConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch",
            ),
        )

        when (transaction.tranType) {
            "go" -> {
                // phat hanh bao lanh
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại giao dịch",
                        value = transaction.tranTypeName ?: "",
                        stableKey = ConfirmFieldKey.GUARANTEE_TYPE,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại bảo lãnh",
                        value = transaction.documentTypeName ?: "",
                        stableKey = ConfirmFieldKey.GUARANTEE_TYPE,
                    ),
                )
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Tên công ty",
                        value = transaction.companyName ?: "",
                        stableKey = ConfirmFieldKey.COMPANY_NAME,
                    ),
                )
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Bên nhận bảo lãnh",
                        value = transaction.beneficiaryName ?: "",
                        stableKey = ConfirmFieldKey.BENEFICIARY,
                    ),
                )
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số tiền đề nghị phát hành BL",
                        value = transaction.amount ?: "",
                        stableKey = ConfirmFieldKey.GUARANTEE_ISSUE_AMOUNT,
                    ),
                )
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Phí giao dịch",
                        value = "Sẽ được NHCT tính toán và thu phí", // phat hanh bao lanh
                        stableKey = ConfirmFieldKey.FEE,
                    ),
                )
            }

            "gor" -> {
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại giao dịch",
                        value = transaction.trxTypeName ?: "",
                        stableKey = ConfirmFieldKey.GUARANTEE_TYPE,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số giao dịch",
                        value = transaction.mtId ?: "",
                        stableKey = ConfirmFieldKey.HOST_MTID,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại giải tỏa",
                        value = transaction.typeChangeDisplay ?: "",
                        stableKey = ConfirmFieldKey.RELEASE_TYPE,
                    ),
                )

                transaction.ibFile?.firstOrNull { "GDN" == it.attachmentType }?.let { file ->
                    items.add(
                        ConfirmItem(
                            type = ConfirmItemType.KEY_VALUE_RESULT,
                            label = "Đề nghị giải tỏa/giảm trừ bảo lãnh",
                            value = file.fileName ?: "",
                            isClickable = true,
                            stableKey = ConfirmFieldKey.GUARANTEE_RELEASE_REQUEST,
                        ),
                    )
                }

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Mức phí",
                        value = "Theo biểu phí của NHCT",
                        stableKey = ConfirmFieldKey.FEE_LEVEL,
                    ),
                )
            }

            "goc" -> {
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại giao dịch",
                        value = transaction.tranTypeName ?: "",
                    ),
                )
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Loại sửa đổi",
                        value = transaction.typeChangeDisplay ?: "",
                        stableKey = ConfirmFieldKey.AMEND_TYPE,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Mức phí",
                        value = "Theo biểu phí của NHCT",
                    ),
                )

                transaction.listFiles?.firstOrNull { "GDN" == it.attachmentType }?.let { file ->
                    items.add(
                        ConfirmItem(
                            type = ConfirmItemType.KEY_VALUE_RESULT,
                            label = "Đề nghị sửa đổi",
                            value = file.fileName ?: "",
                            isClickable = true,
                            stableKey = ConfirmFieldKey.GUARANTEE_AMEND_REQUEST,
                        ),
                    )
                }
            }
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền đề nghị giải ngân",
                value = totalAmount,
                subValue = Utils.g().getDotMoneyHasCcy(totalAmount, "VND"),
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
                subValue = Utils.g().getDotMoneyHasCcy(totalFee, "VND"),
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: "CT"
        val tranType = transactions.firstOrNull()?.tranType ?: "BATCH"

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
        )
    }
}