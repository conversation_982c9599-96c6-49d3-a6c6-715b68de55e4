package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.glassBottomGradient
import kotlinx.coroutines.delay
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for RejectReasonBottomSheet styling
 */
private object RejectReasonBottomSheetConstants {
    const val GRADIENT_BOTTOM_ALPHA = 0.675f
    const val GLASS_GRADIENT_ALPHA = 0.67f
}

/**
 * Actions for reject reason bottom sheet
 */
sealed interface RejectReasonAction {
    object OnDismiss : RejectReasonAction
    object OnBackClick : RejectReasonAction
    data class OnContinueClick(val reason: String) : RejectReasonAction
}

/**
 * Reject Reason Bottom Sheet
 * Follows Figma design for "Từ chối giao dịch" modal
 *
 * @param visible Whether the bottom sheet is visible
 * @param isLoading Whether the reject API is being called
 * @param onAction Callback for user actions
 */
@Composable
fun RejectReasonBottomSheet(
    visible: Boolean,
    isLoading: Boolean = false,
    onAction: (RejectReasonAction) -> Unit,
) {
    // State for input field
    var rejectionReason by remember { mutableStateOf("") }
    val maxLength = 146

    // Focus management for auto-showing keyboard
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    // Auto-focus and show keyboard when dialog becomes visible
    LaunchedEffect(visible) {
        if (visible) {
            // Small delay to ensure dialog is fully rendered
            delay(300)
            try {
                focusRequester.requestFocus()
                // Show keyboard smoothly
                keyboardController?.show()
            } catch (e: Exception) {
                // Ignore if focus request fails
            }
        }
    }

    BaseBottomSheet<RejectReasonAction>(
        visible = visible,
        onDismissRequest = {
            if (!isLoading) {
                // Hide keyboard before dismissing
                keyboardController?.hide()
                onAction(RejectReasonAction.OnDismiss)
                // Clear input for next time
                rejectionReason = ""
            }
        },
        onResult = { action ->
            // Hide keyboard when action is performed
            when (action) {
                is RejectReasonAction.OnBackClick -> {
                    keyboardController?.hide()
                }
                is RejectReasonAction.OnContinueClick -> {
                    keyboardController?.hide()
                }
                else -> {}
            }
            onAction(action)
        },
        allowTouchDismiss = !isLoading,
        secureFlag = true,
    ) { onSheetAction ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .imePadding(),
            verticalArrangement = Arrangement.Bottom,
        ) {
            // White card container with title, divider and input - ALL 4 corners rounded
            Surface(
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32), // All 4 corners rounded
                color = FDS.Colors.white,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap8),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding24),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title
                    FoundationText(
                        text = stringResource(R.string.reject_transaction),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))

                    // Full width divider
                    FoundationDivider()

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

                    // Input field using FoundationEditText with built-in counter and hint
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                    ) {
                        FoundationEditText(
                            value = rejectionReason,
                            onValueChange = { newValue ->
                                if (newValue.length <= maxLength) {
                                    rejectionReason = newValue
                                }
                            },
                            placeholder = stringResource(R.string.enter_reason),
                            hintText = stringResource(com.vietinbank.core_ui.R.string.reject_reason_example_hint),
                            enabled = !isLoading,
                            singleLine = false,
                            maxLength = maxLength,
                            showCharacterCounter = true,
                            showBottomBorder = true,
                            modifier = Modifier
                                .fillMaxWidth()
                                .focusRequester(focusRequester),
                        )
                    }
                }
            }

            // Bottom buttons section with gradient background (outside white container)
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .glassBottomGradient(
                        endColor = FDS.Colors.backgroundBgScreen,
                        alpha = RejectReasonBottomSheetConstants.GLASS_GRADIENT_ALPHA,
                        cornerRadius = FDS.Sizer.Radius.radius32,
                    )
                    .padding(
                        top = FDS.Sizer.Padding.padding16,
                        start = FDS.Sizer.Padding.padding16,
                        end = FDS.Sizer.Padding.padding16,
                    ),
            ) {
                // Loading overlay
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(FDS.Sizer.Button.largeButtonHeight),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(
                            color = FDS.Colors.primary,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon32),
                        )
                    }
                } else {
                    // Buttons row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    ) {
                        // Back button (Secondary style - DARK button as per Figma)
                        FoundationButton(
                            text = stringResource(R.string.button_back),
                            onClick = {
                                onSheetAction(RejectReasonAction.OnBackClick)
                            },
                            isLightButton = false, // DARK button
                            modifier = Modifier.weight(1f),
                        )

                        // Continue button (Primary style - LIGHT button as per Figma)
                        FoundationButton(
                            text = stringResource(R.string.button_continue),
                            onClick = {
                                if (rejectionReason.isNotEmpty()) {
                                    onSheetAction(RejectReasonAction.OnContinueClick(rejectionReason))
                                }
                            },
                            isLightButton = true, // LIGHT button
                            enabled = rejectionReason.isNotEmpty(),
                            modifier = Modifier.weight(1f),
                        )
                    }
                }
            }
        }
    }
}
