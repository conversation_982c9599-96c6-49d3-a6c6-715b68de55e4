package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.getParcelableCustom
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.processor.TransactionProcessorFactory
import com.vietinbank.core_domain.models.checker.FilterApproveDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MultipleTransactionUIState
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.PreRejectParams
import com.vietinbank.core_domain.models.checker.SubTranTypeDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.manage.FIELD_AMOUNT
import com.vietinbank.core_domain.models.manage.FIELD_TIME
import com.vietinbank.core_domain.models.manage.INCREASES
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.multiple_transaction.MultipleTransactionFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MultipleApprovalListViewModel
@Inject constructor(
    val useCase: CheckerUserCase,
    val homeUseCase: HomeUseCase,
    val factory: TransactionRendererFactory,
    val processorFactory: TransactionProcessorFactory,
    val renderMultipleFactory: MultipleTransactionFactory,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    savedStateHandle: SavedStateHandle,
) : BaseViewModel() {
    var originalTransactions: List<TransactionListDomain> = emptyList()

    // Cờ chỉ ra rằng cần scroll lên đầu danh sách sau khi làm mới
    var needScrollToTop: Boolean = false

    // UI State để hiển thị - sử dụng StateFlow
    private val _uiState =
        MutableStateFlow<MultipleTransactionUIState>(MultipleTransactionUIState())
    val uiState = _uiState.asStateFlow()

    // Selection mode state
    private val _isSelectionMode = MutableStateFlow(false)
    val isSelectionMode = _isSelectionMode.asStateFlow()

    private val _preApprove = MutableSharedFlow<PreApproveDomain?>()
    val preApprove = _preApprove.asSharedFlow()

    private val _errorEvent = MutableSharedFlow<String?>()
    val errorEvent = _errorEvent.asSharedFlow()

    private val _isLoadMoreRequest = MutableStateFlow(false) // Đánh dấu request load more

    private val _loadMoreError = MutableSharedFlow<String>()
    val loadMoreError = _loadMoreError.asSharedFlow()

    init {
        _uiState.update {
            it.copy(
                isRejectMode = runCatching {
                    savedStateHandle.get<Boolean>(Tags.IS_REJECT_MODE_TRANSACTION) ?: false
                }.getOrElse { false },
            )
        }
    }

    fun initUiState(bundle: Bundle?) {
        val filterModel = bundle?.getParcelableCustom<FilterApproveDomain>(Tags.TRANSACTION_BUNDLE)
        val groupName = bundle?.getString(Tags.TYPE_BUNDLE)
        val subTranTypeLst: List<SubTranTypeDomain>? =
            bundle?.getString(Tags.DATA_TYPE)?.let { countJson ->
                try {
                    Utils.g().provideGson()
                        .fromJson(countJson, object : TypeToken<List<SubTranTypeDomain>>() {}.type)
                } catch (e: Exception) {
                    null
                }
            }

        _uiState.value = _uiState.value.copy(
//            subTranLst = factory.getRenderSubType(filterModel?.serviceType ?: ""),
            filterApprove = filterModel?.copy(
                tranType = if (_uiState.value.isDisbursementTransaction) {
                    null
                } else {
                    filterModel.tranType
                },
            ),
            groupName = groupName ?: "",
            subTranLst = subTranTypeLst,
            isBatchFile = when (filterModel?.serviceType) {
                Tags.TYPE_GROUP_TRANSFER_CTTF300, Tags.TYPE_GROUP_TRANSFER_CTTF5000, Tags.TYPE_GROUP_FILENSNN -> true

                else -> false
            },
        )
        // call api
        if (!isKeepCallInit()) {
            getTransactionList()
            getTransactionList(isAll = true)
            setKeepCallInit()
        }
    }

    fun retryLoadMore() {
        if (_uiState.value.isLoadingMore) return
        loadMoreTransactions()
    }

    fun getTranTypeApprove() = if ("BTX" != getServiceType()) {
        _uiState.value.selectedTransactions.firstOrNull()?.let { trans ->
            getTransactionById(trans)
        }?.tranType.orEmpty()
    } else {
        ""
    }

    fun getTranType() = _uiState.value.filterApprove?.tranType.orEmpty()

    fun getGroupType() = _uiState.value.filterApprove?.groupType.orEmpty()

    fun getServiceType() = _uiState.value.filterApprove?.serviceType.orEmpty()

    fun toggleSelectionMode() {
        val newMode = !_isSelectionMode.value
        _isSelectionMode.value = newMode
        // Clear selections when exiting selection mode
        if (!newMode) {
            deselectAllTransactions()
        }
    }

    fun deselectAllTransactions() {
        // bo check trang thai phe duyet max giao dich
        _uiState.value = _uiState.value.copy(
            isGlobalMaximum = false,
            selectedTransactions = emptySet(),
            selectedSubTransactions = emptySet(),
            // button "Phê duyệt" khi chọn giao dich
            isShowApprove = true,
        )
    }

    /**
     * Toggle a transaction selection
     */
    fun toggleTransactionSelection(transactionId: String) {
        val currentSelection = _uiState.value.selectedTransactions.toMutableList()
        val currentSubSelection = _uiState.value.selectedSubTransactions.toMutableList()
        val subTransactionLst =
            originalTransactions.firstOrNull { it.mtId == transactionId }?.mtIdList ?: emptyList()
        if (currentSelection.contains(transactionId)) {
            // Deselect transaction
            currentSelection.remove(transactionId)
            currentSubSelection.removeAll(subTransactionLst)
        } else {
            // Select transaction
            currentSelection.add(transactionId)
            currentSubSelection.addAll(subTransactionLst)
        }
        // Update states
        // khi update 1 item -> se khong còn trạng thai chọn max giao dịch phe duyet nua
        _uiState.value = _uiState.value.copy(
            isGlobalMaximum = false,
            selectedTransactions = currentSelection.toSet(),
            selectedSubTransactions = currentSubSelection.toSet(),
            isShowApprove = currentSelection.size <= 1 || ("DO" != getServiceType() && "GO" != getServiceType()),
        )
    }

    /**
     * Get selected transaction IDs as ArrayList for API calls
     * Nếu ở chế độ chọn tất cả, trả về mtIds từ API
     * Nếu không, trả về danh sách ID đã chọn từ UI
     */
    fun getSelectedTransactionsAsList(): ArrayList<String> = if (isBatchFile()) {
        ArrayList(_uiState.value.selectedSubTransactions)
    } else {
        ArrayList(_uiState.value.selectedTransactions)
    }

    /**
     * Helper method để lấy transaction theo ID
     */
    private fun getTransactionById(id: String): TransactionListDomain? =
        originalTransactions.find { it.mtId == id }

    fun preApprove() {
        launchJob(showLoading = true) {
            val transactionSelected =
                _uiState.value.selectedTransactions.firstOrNull()?.let { trans ->
                    getTransactionById(trans)
                }
            val res = useCase.preApprove(
                PreApproveParams(
                    userName = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = "",
                    serviceType = transactionSelected?.serviceType ?: "",
                    tranType = transactionSelected?.tranType ?: "",
                    transactions = getSelectedTransactionsAsList(),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = true
                _preApprove.emit(data)
            }
        }
    }

    fun preReject() {
        launchJob(showLoading = true) {
            val transactionSelected =
                _uiState.value.selectedTransactions.firstOrNull()?.let { trans ->
                    getTransactionById(trans)
                }
            val res = useCase.preReject(
                PreRejectParams(
                    username = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = "",
                    serviceType = transactionSelected?.serviceType ?: "",
                    tranType = transactionSelected?.tranType ?: "",
                    transactions = getSelectedTransactionsAsList(),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = false
                _preApprove.emit(data)
            }
        }
    }

    fun getOriginalTransactionByMTId(mtID: String): TransactionListDomain? =
        originalTransactions.find {
            it.mtId == mtID
        }

    fun processTransactions(
        transactions: List<TransactionListDomain>,
        isLoadMore: Boolean = false,
    ) {
        // Nếu là loadMore, thì giữ lại originalTransactions cũ và thêm vào
        // Nếu không phải loadMore, thì thay thế hoàn toàn
        originalTransactions = if (isLoadMore) {
            val current = originalTransactions.toMutableList()
            current.addAll(transactions)
            current
        } else {
            transactions
        }
        try {
            // cap nhat tong so giao dich khi load more
            // Cập nhật UI state
            _uiState.value = _uiState.value.copy(transactions = originalTransactions)
        } catch (_: Exception) {
        }
    }

    fun getTransactionList(isAll: Boolean = false) {
        try {
            _uiState.value = _uiState.value.copy(isLoadingMore = true)
            val filterApply = _uiState.value.filterApprove
            launchJob(showLoading = true) {
                val res = homeUseCase.getTranList(
                    GetTransactionListParams(
                        all = if (isAll) "Y" else "N",
                        cifNo = userProf.getCifNo().toString(),
                        username = userProf.getUserName().toString(),
                        groupType = getGroupType(),
                        serviceType = getServiceType(),
                        tranType = getTranType(),
                        pageNum = "0",
                        pageSize = Tags.LIMIT_PAGE_SIZE,
                        orderByAmount = when {
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT && filterApply.sortField?.orderDirect == INCREASES -> "1"
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT -> "-1"
                            else -> "0"
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        orderByApproveDate = when {
                            filterApply?.sortField?.orderBy == FIELD_TIME && filterApply.sortField?.orderDirect == INCREASES -> "1"
                            filterApply?.sortField?.orderBy == FIELD_TIME -> "-1"
                            else -> "0"
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        fromDate = filterApply?.startDate ?: "",
                        toDate = filterApply?.endDate ?: "",
                        minAmount = filterApply?.startAmount ?: "",
                        maxAmount = filterApply?.endAmount ?: "",
                        isSchedule = filterApply?.dateTransfer ?: "",
                        tranSubType = filterApply?.tranSubType ?: "",
                    ),
                )
                handleResource(res) { data ->
                    printLog("transaction: ${data.transactions?.size}")
                    if (isAll) {
                        // Lấy danh sách mtIds từ API
                        // Lấy tổng giá trị từ API nếu có
                        // chon max so giao dich
                        // phần này trả về => có thể mang đi phê duyệt luôn => khong can xu ly gi ca
                        // Cập nhật tổng số lượng giao dịch theo mtIds tra ve
                        _uiState.value = _uiState.value.copy(
//                            isGlobalMaximum = true,
                            maxSelectedTransactions = data.mtIds?.toSet() ?: emptySet(),
                            maxSelectedSubTransactions = data.subMtIds?.toSet() ?: emptySet(),
                            totalTransactionsCount = data.countTransaction?.toIntOrNull() ?: 0,
                            totalSubTransactionsCount = data.countSubTransaction?.toIntOrNull()
                                ?: 0,
//                            isShowApprove = mtIds.size <= 1 || ("DO" != getServiceType() && "GO" != getServiceType()),
                        )
                    } else {
                        // Reset về trang đầu tiên khi lấy danh sách mới
                        _uiState.value = _uiState.value.copy(
                            currentPage = 1,
                            canLoadMore = (data.transactions?.size ?: 0) >= 15,
                            isLoadingMore = false,
                            totalTransactionsCount = data.countTransaction?.toIntOrNull() ?: 0,
                            totalSubTransactionsCount = data.countSubTransaction?.toIntOrNull()
                                ?: 0,
                        )
                        processTransactions(data.transactions ?: listOf())
                    }
                }
            }
        } catch (_: Exception) {
        } finally {
            _uiState.value = _uiState.value.copy(isLoadingMore = false)
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        when (exception) {
            is AppException.ApiException -> {
                if (_isLoadMoreRequest.value && exception.requestPath == Constants.MB_GET_TRANSACTION_LIST) {
                    // Lỗi khi loadMore - không hiển thị dialog lỗi mà chỉ hiển thị lỗi ở cuối danh sách
                    viewModelScope.launch {
                        _loadMoreError.emit(exception.message ?: "Không thể tải thêm dữ liệu")
                    }
                } else {
                    super.onDisplayErrorMessage(exception)
                }
            }

            else -> super.onDisplayErrorMessage(exception)
        }
    }

    fun loadMoreTransactions() {
        // Kiểm tra nếu đang tải hoặc không thể tải thêm thì không làm gì
        if (_uiState.value.isLoadingMore || !_uiState.value.canLoadMore) return
        _uiState.value = _uiState.value.copy(isLoadingMore = true)
        val filterApply = _uiState.value.filterApprove
        launchJob(showLoading = false) {
            try {
                val currentIds = originalTransactions.map { it.mtId }
                printLog("DEBUG: Before loadMore - Page: ${_uiState.value.currentPage}, Items count: ${currentIds.size}")

                val res = homeUseCase.getTranList(
                    GetTransactionListParams(
                        cifNo = userProf.getCifNo().toString(),
                        username = userProf.getUserName().toString(),
                        groupType = getGroupType(),
                        serviceType = getServiceType(),
                        tranType = getTranType(),
                        pageNum = _uiState.value.currentPage.toString(), // Trang tiếp theo
                        pageSize = Tags.LIMIT_PAGE_SIZE,
                        orderByAmount = when {
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT && filterApply.sortField?.orderDirect == INCREASES -> "1"
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT -> "-1"
                            else -> "0"
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        orderByApproveDate = when {
                            filterApply?.sortField?.orderBy == FIELD_TIME && filterApply.sortField?.orderDirect == INCREASES -> "1"
                            filterApply?.sortField?.orderBy == FIELD_TIME -> "-1"
                            else -> "0"
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        fromDate = filterApply?.startDate ?: "",
                        toDate = filterApply?.endDate ?: "",
                        minAmount = filterApply?.startAmount ?: "",
                        maxAmount = filterApply?.endAmount ?: "",
                        isSchedule = filterApply?.dateTransfer ?: "",
                        tranSubType = filterApply?.tranSubType ?: "",
                    ),
                )

                handleResource(res) { data ->
                    val newTransactions = data.transactions ?: emptyList()
                    // Cập nhật danh sách gốc và xử lý lại UI
                    processTransactions(newTransactions, true)
                    _uiState.value = _uiState.value.copy(
                        canLoadMore = newTransactions.size >= 15,
                        currentPage = _uiState.value.currentPage + 1,
                    )
                }
            } finally {
                _uiState.value = _uiState.value.copy(isLoadingMore = false)
            }
        }
    }

    // phe duyet toi da
    fun toggleMaxSelectAll() {
        // neu selected = total && dang load toan bo item -> bỏ chon tat ca
        if (_uiState.value.isGlobalMaximum) {
            // Nếu đang ở chế độ chọn tất cả -> hủy chọn tất cả
            deselectAllTransactions()
        } else {
            // Nếu chưa chọn tất cả, gọi API để lấy tổng số và chọn tất cả
            // neu chua co cache => call api
            val mtIds = _uiState.value.maxSelectedTransactions
            val subMtIds = _uiState.value.maxSelectedSubTransactions
            _uiState.value = _uiState.value.copy(
                isGlobalMaximum = true,
                selectedTransactions = mtIds,
                selectedSubTransactions = subMtIds,
                isShowApprove = mtIds.size <= 1 || ("DO" != getServiceType() && "GO" != getServiceType()),
            )
        }
    }

    fun validateTotalTransactions(): String? = when {
        _uiState.value.selectedTransactions.isEmpty() -> {
            resourceProvider.getString(com.vietinbank.core_ui.R.string.approve_transaction_empty_validate)
        }
        // dien duyet theo file -> co giao dich con
        isBatchFile() && _uiState.value.selectedTransactions.size > Tags.LIMIT_FILE_TRANSACTION -> {
            resourceProvider.getString(
                com.vietinbank.core_ui.R.string.approve_file_validate,
                Tags.LIMIT_FILE_TRANSACTION,
            )
        }

        isBatchFile() && _uiState.value.selectedSubTransactions.size > Tags.LIMIT_TRANSACTION -> {
            resourceProvider.getString(
                com.vietinbank.core_ui.R.string.approve_transaction_validate,
                Tags.LIMIT_TRANSACTION,
            )
        }

        _uiState.value.selectedTransactions.size > Tags.LIMIT_TRANSACTION -> {
            resourceProvider.getString(
                com.vietinbank.core_ui.R.string.approve_transaction_validate,
                Tags.LIMIT_TRANSACTION,
            )
        }

        else -> null
    }

    private fun isBatchFile() = _uiState.value.isBatchFile

    fun onChangeSubType(subType: String?) {
        _uiState.value.filterApprove?.copy(tranType = subType)?.let {
            _uiState.value = _uiState.value.copy(
                filterApprove = it.copy(
                    tranType = if (_uiState.value.isDisbursementTransaction) {
                        Tags.TYPE_DISBURSEMENT_ONLINE
                    } else {
                        it.tranType
                    },
                    tranSubType = if (_uiState.value.isDisbursementTransaction) {
                        it.tranType
                    } else {
                        it.tranSubType
                    },
                ),
                isGlobalMaximum = false,
                maxSelectedTransactions = emptySet(),
                maxSelectedSubTransactions = emptySet(),
                selectedTransactions = emptySet(),
                selectedSubTransactions = emptySet(),
                totalTransactionsCount = 0,
                totalSubTransactionsCount = 0,
            )
            getTransactionList()
            getTransactionList(isAll = true)
        }
    }
}

sealed class MultipleActions {
    data object BackClick : MultipleActions()
    data object SelectAllClick : MultipleActions()
    data object LoadMore : MultipleActions()
    data object RetryLoadMore : MultipleActions()
    data class TransactionClick(val mtId: String) : MultipleActions()
    data class TransactionLongClick(val mtId: String) : MultipleActions()
    data class TransactionSelectedClick(val mtId: String) : MultipleActions()
    data class ContinueClick(val isApprove: Boolean) : MultipleActions()
    data class FilterSelectedClick(val tranType: String?) : MultipleActions()
}