package com.vietinbank.feature_checker.ui.component.inits

import com.vietinbank.core_common.constants.Tags
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 9/4/25.
 */
@Singleton
class TransactionDetailRendererFactory
@Inject
constructor(
    private val transferDetailRenderer: TransferDetailRenderer,
    private val paymentDetailRenderer: PaymentDetailRenderer,
    private val salaryDetailRenderer: SalaryDetailRenderer,
    private val bulk5000DetailRenderer: Bulk5000DetailRenderer,
    private val bulk300DetailRenderer: Bulk300DetailRenderer,
    private val traceDetailRenderer: TraceDetailRenderer,
    private val disbursementOnlineRenderer: DisbursementDetailRenderer,
    private val guaranteeDetailRenderer: GuaranteeDetailRenderer,
    private val guaranteeAmendDetailRenderer: GuaranteeAmendDetailRenderer,
    private val guaranteeIssueDetailRenderer: GuaranteeIssueDetailRenderer,
    private val customsInlandDetailRenderer: CustomsInlandDetailRenderer,
    private val fileNSNNDetailRenderer: FileNSNNDetailRenderer,
    private val infrastructureDetailRenderer: InfrastructureDetailRenderer,
    private val transferForeignDetailRenderer: TransferForeignDetailRenderer,
    private val unLockUserDetailRenderer: UnLockUserDetailRenderer,
) {
    fun getRenderer(tranType: String, groupType: String? = null): ITransactionDetailRenderer {
        // Selection based on transaction type
        return when (tranType) {
            "in", "ou", "np", "sn" -> transferDetailRenderer
            "pm" -> paymentDetailRenderer
            "sl", "slo", "sx" -> salaryDetailRenderer
            "hu" -> bulk5000DetailRenderer
            "ba" -> bulk300DetailRenderer
            "tr" -> traceDetailRenderer
            "tx" -> customsInlandDetailRenderer
            "btx" -> fileNSNNDetailRenderer
            "if" -> infrastructureDetailRenderer
            "fx", "fxr" -> transferForeignDetailRenderer
            Tags.TYPE_DISBURSEMENT_ONLINE.lowercase() -> disbursementOnlineRenderer
            "gor" -> guaranteeDetailRenderer // giai toa
            "goc" -> guaranteeAmendDetailRenderer // sua doi
            "go" -> guaranteeIssueDetailRenderer // phat hanh
            Tags.TYPE_GROUP_RESET_PASSWORD.lowercase(), Tags.TYPE_GROUP_UNLOCK_USER.lowercase() -> unLockUserDetailRenderer // mo khoa & cap lai mat khau
            else -> transferDetailRenderer
        }
    }
}