package com.vietinbank.feature_checker.ui.component.newui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Bottom bar component displayed when in selection mode
 * Shows select all checkbox and action buttons (Approve/Reject)
 *
 * @param selectedCount Number of selected transactions
 * @param isAllSelected Whether all transactions are selected
 * @param isShowApprove Whether to show the approve button based on business logic
 * @param isChecker Whether the current user has Checker role
 * @param onSelectAllClick Callback when select all is clicked
 * @param onApproveClick Callback when approve is clicked
 * @param onRejectClick Callback when reject is clicked
 */
@Composable
fun SelectionBottomBar(
    selectedCount: Int,
    isAllSelected: Boolean,
    isShowApprove: Boolean,
    isChecker: Boolean = true,
    onSelectAllClick: () -> Unit,
    onApproveClick: () -> Unit,
    onRejectClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.BottomCenter,
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(
                topStart = FDS.Sizer.Radius.radius16,
                topEnd = FDS.Sizer.Radius.radius16,
                bottomStart = FDS.Sizer.Radius.radius0,
                bottomEnd = FDS.Sizer.Radius.radius0,
            ),
            colors = CardDefaults.cardColors(
                containerColor = FDS.Colors.white,
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = FDS.Effects.elevationNone,
            ),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(FDS.Sizer.Padding.padding16),
            ) {
                // Select all row
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = FDS.Sizer.Padding.padding16),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationSelector(
                            boxType = SelectorType.Checkbox,
                            isSelected = isAllSelected,
                            onClick = onSelectAllClick,
                        )

                        FoundationText(
                            text = stringResource(R.string.checker_select_all),
                            style = FDS.Typography.bodyB1,
                            color = FDS.Colors.characterPrimary,
                        )
                    }

                    // Selected count badge
                    if (selectedCount > 0) {
                        Box(
                            modifier = Modifier
                                .clip(RoundedCornerShape(FDS.Sizer.Radius.radius4))
                                .background(Color.LightGray)
                                .padding(
                                    horizontal = FDS.Sizer.Padding.padding8,
                                    vertical = FDS.Sizer.Padding.padding4,
                                ),
                        ) {
                            FoundationText(
                                text = selectedCount.toString(),
                                style = FDS.Typography.captionCaptionM,
                                color = FDS.Colors.characterHighlighted,
                            )
                        }
                    }
                }

                // Action buttons - only show for Checker role
                if (isChecker) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                    ) {
                        // Reject button
                        FoundationButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(R.string.feature_checker_reject),
                            onClick = onRejectClick,
                            isLightButton = true,
                            enabled = selectedCount > 0,
                        )

                        // Approve button (conditional based on business logic)
                        if (isShowApprove) {
                            FoundationButton(
                                modifier = Modifier.weight(1f),
                                text = stringResource(R.string.checker_approve),
                                onClick = onApproveClick,
                                isLightButton = false,
                                enabled = selectedCount > 0,
                            )
                        }
                    }
                }
            }
        }
    }
}
