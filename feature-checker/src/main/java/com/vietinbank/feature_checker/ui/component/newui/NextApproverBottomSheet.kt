package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.glassBottomGradient
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for NextApproverBottomSheet styling
 */
private object NextApproverBottomSheetConstants {
    const val GRADIENT_BOTTOM_ALPHA = 0.675f
}

/**
 * Actions for next approver bottom sheet
 */
sealed interface NextApproverAction {
    object OnDismiss : NextApproverAction
    data class OnConfirm(val selectedApprovers: List<ApproverDomains>) : NextApproverAction
}

/**
 * Next Approver Bottom Sheet
 * Allows selecting next approvers for transaction
 *
 * @param visible Whether the bottom sheet is visible
 * @param approvers List of available approvers
 * @param selectedApprovers Currently selected approvers
 * @param isLoading Whether API is being called
 * @param onAction Callback for user actions
 */
@Composable
fun NextApproverBottomSheet(
    visible: Boolean,
    approvers: List<ApproverDomains> = emptyList(),
    selectedApprovers: List<ApproverDomains> = emptyList(),
    isLoading: Boolean = false,
    onAction: (NextApproverAction) -> Unit,
) {
    // Local state for selection
    var localSelectedApprovers by remember(selectedApprovers) {
        mutableStateOf(selectedApprovers)
    }

    // Check if all are selected
    val isAllSelected = approvers.isNotEmpty() &&
        approvers.all { ap -> localSelectedApprovers.any { it.id == ap.id } }

    BaseBottomSheet<NextApproverAction>(
        visible = visible,
        onDismissRequest = {
            if (!isLoading) {
                onAction(NextApproverAction.OnDismiss)
            }
        },
        onResult = { action ->
            onAction(action)
        },
        allowTouchDismiss = false,
        secureFlag = true,
    ) { onSheetAction ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .imePadding(),
        ) {
            // White card container with rounded corners on all 4 sides
            Surface(
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                color = FDS.Colors.white,
                shadowElevation = FDS.Sizer.Gap.gap4,
                modifier = Modifier
                    .fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Padding.padding16, bottom = FDS.Sizer.Padding.padding12),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // Title
                    FoundationText(
                        text = stringResource(R.string.next_approver_title),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

                    // Full width divider
                    FoundationDivider()

                    // Approver list with "All" option
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = FDS.Sizer.Dialog.maxListHeight)
                            .padding(horizontal = FDS.Sizer.Padding.padding16),
                        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                    ) {
                        item { Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8)) }

                        // "All" option (single line)
                        item {
                            ApproverItem(
                                title = stringResource(R.string.next_approver_all),
                                subtitle = null,
                                isSelected = isAllSelected,
                                onClick = {
                                    localSelectedApprovers = if (isAllSelected) {
                                        // Unselect all
                                        emptyList()
                                    } else {
                                        // Select all
                                        approvers
                                    }
                                },
                            )
                        }

                        // Individual approvers
                        items(approvers) { approver ->
                            val title = approver.fullname ?: approver.username ?: ""
                            val subtitle = approver.username?.takeIf { !it.isNullOrBlank() }
                            ApproverItem(
                                title = title,
                                subtitle = subtitle,
                                isSelected = localSelectedApprovers.any { it.id == approver.id },
                                onClick = {
                                    val isCurrentlySelected = localSelectedApprovers.any { it.id == approver.id }
                                    localSelectedApprovers = if (isCurrentlySelected) {
                                        localSelectedApprovers.filter { it.id != approver.id }
                                    } else {
                                        localSelectedApprovers + approver
                                    }
                                },
                            )
                        }

                        item { Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8)) }
                    }
                }
            }

            // Bottom buttons section with gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .glassBottomGradient(
                        endColor = FDS.Colors.blue900,
                        alpha = NextApproverBottomSheetConstants.GRADIENT_BOTTOM_ALPHA,
                    )
                    .padding(
                        top = FDS.Sizer.Padding.padding12,
                        start = FDS.Sizer.Padding.padding16,
                        end = FDS.Sizer.Padding.padding16,
                    ),
            ) {
                // Loading overlay
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(FDS.Sizer.Button.largeButtonHeight),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(
                            color = FDS.Colors.primary,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon32),
                        )
                    }
                } else {
                    // Button group: Back + Continue (Continue disabled when no selection)
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    ) {
                        FoundationButton(
                            isLightButton = false,
                            text = stringResource(R.string.common_back),
                            onClick = { onSheetAction(NextApproverAction.OnDismiss) },
                            modifier = Modifier.weight(1f),
                        )

                        FoundationButton(
                            isLightButton = true,
                            text = stringResource(R.string.common_apply),
                            onClick = {
                                onSheetAction(NextApproverAction.OnConfirm(localSelectedApprovers))
                            },
                            enabled = localSelectedApprovers.isNotEmpty(),
                            modifier = Modifier.weight(1f),
                        )
                    }
                }
            }
        }
    }
}

/**
 * Individual approver item with checkbox
 */
@Composable
private fun ApproverItem(
    title: String,
    subtitle: String? = null,
    isSelected: Boolean,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .safeClickable(onSafeClick = onClick)
            .padding(vertical = FDS.Sizer.Padding.padding8),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = FDS.Sizer.Padding.padding8),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
        ) {
            FoundationText(
                text = title,
                style = FDS.Typography.bodyB1Emphasized,
                color = FDS.Colors.characterPrimary,
            )
            if (!subtitle.isNullOrBlank()) {
                FoundationText(
                    text = subtitle,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterSecondary,
                )
            }
        }

        Checkbox(
            checked = isSelected,
            onCheckedChange = { onClick() },
            colors = CheckboxDefaults.colors(
                checkedColor = FDS.Colors.primary,
                uncheckedColor = FDS.Colors.characterTertiary,
                checkmarkColor = FDS.Colors.white,
            ),
        )
    }
}
