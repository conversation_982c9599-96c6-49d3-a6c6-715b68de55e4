package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.component.approvallist.DocumentType
import com.vietinbank.feature_checker.ui.component.inits.GuaranteeAmendDetailRenderer.Companion.TYPE_GDN_GUARANTEE
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

/**
 * Render chi tiết giai toa bảo lanh
 * */
class GuaranteeDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        Column(
            modifier = Modifier.padding(
                horizontal = FoundationDesignSystem.Sizer.Padding.padding8,
            ),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Padding.padding24))
            FoundationStatus(
                modifier = Modifier.padding(
                    horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
                statusMessage = transaction.statusName,
                statusCode = Status.Pending,
            )
            HorizontalDivider(
                Modifier.padding(
                    FoundationDesignSystem.Sizer.Padding.padding16,
                ),
            )
            SectionHeader(title = stringResource(id = R.string.feature_checker_guarantee_request))
            TransactionDetailsCard {
                transaction.listFiles?.filter {
                    !it.attachmentType.isNullOrEmpty() && it.attachmentType == TYPE_GDN_GUARANTEE
                }?.forEach { file ->
                    KeyValueRow(
                        label = when (file.attachmentType) {
                            TYPE_GDN_GUARANTEE -> stringResource(R.string.gtbl_agreement_in_agreement)
                            else -> ""
                        },
                        value = file.fileName ?: "",
                        isHyperlink = true,
                        onClick = {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                    )
                }
            }
            HorizontalDividerDetail(havePaddingBottom = true)
            SectionHeader(title = stringResource(id = R.string.feature_checker_general_information))
            TransactionDetailsCard {
                KeyValueRow(
                    label = stringResource(id = R.string.feature_checker_transaction_no),
                    value = transaction.mtId ?: "",
                )
                KeyValueRow(
                    label = stringResource(id = R.string.vtb_bl_type),
                    value = transaction.documentTypeName ?: DocumentType.fromTypeId(transaction.documentType ?: "")
                        .getDocumentTypeLabel(),
                )
            }
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_customer),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_customer_name), value = transaction.cifName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_business_registration_number), value = transaction.guaranteeCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_date_range), value = transaction.dateRange?.toFormattedDate() ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_issue_by), value = transaction.issuesBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_head_address), value = transaction.headQuarters ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_representative_name),
                        value = transaction.representativeName ?: "",
                    )
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_authority), value = transaction.authority ?: "")
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_contract),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_mbnt_contract_number), value = transaction.contractNo ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_contract_date), value = transaction.contractDate?.toFormattedDate() ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_transaction_number), value = transaction.host_mtid ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_gtbl_publication_day), value = transaction.releaseDate?.toFormattedDate() ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_effective_date_start),
                        value = transaction.effectiveStartDate ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_effective_date_end),
                        value = transaction.effectiveEndDate ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_issuing_bank),
                        value = "${transaction.branch} - ${transaction.branchName}",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.vtb_gtbl_content_tile),
                initiallyCollapsed = true,
                content = {
                    val releaseType = ReleaseType.fromTypeId(transaction.typeChange ?: "")
                    val releaseTypeString = releaseType.getReleaseTypeLabel() ?: ""
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_type),
                        value = releaseTypeString,
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_value),
                        value = transaction.amountChange ?: "",
                    )
                    if (transaction.amountChange2 != null) {
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_gtbl_value),
                            value = transaction.amountChange2 ?: "",
                        )
                    }
                    if (transaction.amountChange3 != null) {
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_gtbl_value),
                            value = transaction.amountChange3 ?: "",
                        )
                    }
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_reason),
                        value = transaction.reasonChange ?: "",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_other_information),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(
                        label = stringResource(
                            id = R.string.vtb_gtbl_send_type,
                        ),
                        value = MessageType.fromTypeId(transaction.messageType ?: "")
                            .getMessageTypeLabel() ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_account),
                        value = transaction.depositAccount ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_gtbl_amount),
                        value = transaction.depositAmount ?: "",
                    )
                    if (transaction.feeDesc == "10") {
                        KeyValueRow(
                            label = stringResource(
                                id = R.string.fee_type,
                            ),
                            value = FeeGuarantee.TYPE_1.getFeeGuaranteeLabel(),
                        )
                    } else {
                        val feeType = FeeGuarantee.TYPE_2.getFeeGuaranteeLabel()
                        KeyValueRow(
                            label = stringResource(
                                id = R.string.fee_type,
                            ),
                            value = FeeGuarantee.TYPE_1.getFeeGuaranteeLabel(),
                        )
                        KeyValueRow(
                            label = stringResource(
                                id = R.string.details,
                            ),
                            value = transaction.feeDesc ?: "",
                        )
                    }
                },
            )
            HorizontalDividerDetail()
            Box(
                modifier = Modifier
                    .padding(
                        start = FoundationDesignSystem.Sizer.Padding.padding16,
                        end = FoundationDesignSystem.Sizer.Padding.padding16,
                        top = FoundationDesignSystem.Sizer.Padding.padding8,
                        bottom = FoundationDesignSystem.Sizer.Padding.padding16,
                    )
                    .safeClickable {
                        actions?.onAttachmentsClick?.invoke()
                    },
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(
                            id = R.string.feature_checker_attatchment_details_info,
                        ),
                        style = FoundationDesignSystem.Typography.interactionButton,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                    Icon(
                        modifier = Modifier
                            .size(FoundationDesignSystem.Sizer.Icon.icon24),
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                        contentDescription = "next",
                        tint = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                }
            }
        }
    }
}

enum class ReleaseType(val typeId: Int) {
    TYPE_10(10),
    TYPE_11(11),
    DEFAULT_TYPE(-1),
    ;

    @Composable
    fun getReleaseTypeLabel(): String {
        return when (this) {
            TYPE_11 -> stringResource(R.string.vtb_release_type_10)
            TYPE_10 -> stringResource(R.string.vtb_release_type_11)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): ReleaseType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String): ReleaseType {
            return entries.find { it.typeId.toString() == typeId } ?: DEFAULT_TYPE
        }
    }
}

enum class FeeGuarantee(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getFeeGuaranteeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.fee_guarantee_1)
            TYPE_2 -> stringResource(R.string.fee_guarantee_2)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): FeeGuarantee {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }
    }
}