package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class DisbursementConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Loại giao dịch",
                value = transaction.tranTypeName ?: "",
                stableKey = ConfirmFieldKey.DISBURSEMENT_TYPE,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mục đích giải ngân",
                value = transaction.disbursementPurposeName ?: "",
                stableKey = ConfirmFieldKey.DISBURSEMENT_PURPOSE,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày đề nghị",
                value = transaction.disbursementDate ?: "",
                stableKey = ConfirmFieldKey.DISBURSEMENT_APPLY_DATE,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Lãi suất vay",
                value = transaction.loanRate ?: "",
                stableKey = ConfirmFieldKey.INTEREST_RATE,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời hạn trả",
                value = transaction.loanTerm ?: "",
                stableKey = ConfirmFieldKey.TERM,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày trả nợ",
                value = transaction.dueDate ?: "",
                stableKey = ConfirmFieldKey.REPAYMENT_DATE,
            ),
        )

        if ("EXT" == transaction.subType) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số tiền chuyển",
                    value = transaction.transferAmount ?: "",
                    subValue = transaction.transferAmountInWord ?: "",
                    stableKey = ConfirmFieldKey.AMOUNT,
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số tiền vay",
                    value = transaction.amount ?: "",
                    subValue = transaction.amountInWords,
                    isHighlighted = true,
                    stableKey = ConfirmFieldKey.AMOUNT,
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tỷ giá",
                    value = transaction.exchangeRate ?: "",
                    stableKey = ConfirmFieldKey.EXCHANGE_RATE,
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số tiền đề nghị giải ngân",
                    value = transaction.amount ?: "",
                    subValue = transaction.amountInWords ?: "",
                    isHighlighted = true,
                    stableKey = ConfirmFieldKey.AMOUNT,
                ),
            )
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()
        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: "CT"
        val tranType = transactions.firstOrNull()?.tranType ?: "BATCH"

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
        )
    }
}