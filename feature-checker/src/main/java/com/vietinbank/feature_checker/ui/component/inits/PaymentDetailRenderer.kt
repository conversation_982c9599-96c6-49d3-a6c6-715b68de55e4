package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAvatarShimmer
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSkeletonRectangle
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class PaymentDetailRenderer @Inject constructor(
    val imageLoader: CoilImageLoader,
) : ITransactionDetailRenderer {
    override fun isNewRender(): Boolean {
        return true
    }

    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
    }

    @Composable
    override fun NewRenderTransactionDetails(
        modifier: Modifier,
        transaction: TransactionDomain,
        transactionHelper: TransactionHelperDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Gap.gap24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8), // Content padding inside white container
        ) {
            // Status chip
            FoundationStatus(
                statusCode = Status.Pending,
                statusMessage = transaction.statusName,
            )

            // From account section (without label)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                // Bank logo using circular container
                Box(
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon40)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    FoundationAvatarShimmer(
                        modifier = Modifier,
                        size = FDS.Sizer.Icon.icon40,
                    )
                    // Overlay loaded icon with crossfade
                    if (!transactionHelper?.bankFromUrl.isNullOrEmpty()) {
                        imageLoader.LoadUrl(
                            url = transactionHelper.bankFromUrl,
                            isCache = true,
                            placeholderRes = null,
                            errorRes = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    }
                }

                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                ) {
                    val accountName = transaction.fromAccountName
                    if (accountName != null) {
                        FoundationText(
                            text = accountName,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    } else {
                        // Show shimmer for account name
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding144), // Approx 150dp
                            height = FDS.Sizer.Icon.icon20,
                        )
                    }

                    val accountNo = transaction.fromAccountNo
                    if (accountNo != null) {
                        FoundationText(
                            text = accountNo,
                            style = FDS.Typography.captionL,
                            color = FDS.Colors.characterSecondary,
                        )
                    } else {
                        // Show shimmer for account number
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding100),
                            height = FDS.Sizer.Icon.icon16,
                        )
                    }
                }
            }

            // Transfer to label with divider
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationText(
                    text = stringResource(R.string.transaction_label_transfer_to),
                    style = FDS.Typography.captionMSemibold,
                    color = FDS.Colors.characterTertiary,
                )
                FoundationDivider(
                    modifier = Modifier.weight(1f),
                )
            }

            // To account section
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                // Bank logo using circular container with different color
                Box(
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon40)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    // Base shimmer avatar 40x40
                    FoundationAvatarShimmer(
                        modifier = Modifier,
                        size = FDS.Sizer.Icon.icon40,
                    )
                    // Overlay loaded icon with crossfade
                    if (!transactionHelper?.bankToUrl.isNullOrEmpty()) {
                        imageLoader.LoadUrl(
                            url = transactionHelper.bankToUrl,
                            isCache = true,
                            placeholderRes = null,
                            errorRes = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    } else {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_account_company_24),
                            contentDescription = "",
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            tint = FDS.Colors.characterPrimary,
                        )
                    }
                }

                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                ) {
                    if (transaction.receiveName != null) {
                        FoundationText(
                            text = transaction.receiveName ?: "",
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                        )
                    } else {
                        // Show shimmer for receiver name
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding144 + FDS.Sizer.Padding.padding32), // Approx 180dp
                            height = FDS.Sizer.Icon.icon20,
                        )
                    }

                    val toDisplayName =
                        (
                            transaction.receiveBankName?.plus(" - ${transaction.toAccountNo ?: ""}")
                                ?: ""
                            ).ifEmpty {
                            transaction.toAccountNo ?: ""
                        }
                    if (toDisplayName.isNotEmpty()) {
                        FoundationText(
                            text = toDisplayName,
                            style = FDS.Typography.captionL,
                            color = FDS.Colors.characterSecondary,
                        )
                    } else {
                        // Show shimmer for bank and account info
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding100 + FDS.Sizer.Padding.padding20), // Approx 120dp
                            height = FDS.Sizer.Icon.icon16,
                        )
                    }
                }
            }

            FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap8))

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_transaction_id),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transaction.mtId ?: "",
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            when {
                !transaction.fromAccountFeeNo.isNullOrEmpty() && !transaction.fromAccountFeeName.isNullOrEmpty() -> {
                    "${transaction.fromAccountFeeNo} - ${transaction.fromAccountFeeName}"
                }

                !transaction.fromAccountFeeNo.isNullOrEmpty() -> {
                    transaction.fromAccountFeeNo
                }

                else -> null
            }?.let { displayAccount ->
                FoundationInfoHorizontal(
                    title = stringResource(R.string.maker_transfer_dashboard_main_account_type_D),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = displayAccount,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )
            }

            when {
                !transaction.branchNo.isNullOrEmpty() && !transaction.branchName.isNullOrEmpty() -> {
                    "${transaction.branchNo} - ${transaction.branchName}"
                }

                else -> null
            }?.let { displayBank ->
                FoundationInfoHorizontal(
                    title = stringResource(R.string.feature_smart_ca_detail_item_branchName),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = displayBank,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_content),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transaction.remark ?: "",
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_fee),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transaction.feePayMethodDesc1,
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            // Hình thức thu phí: theo yêu cầu, feePayMethod == "0" -> Phí ngoài, else -> Phí trong
            val feeMethodText = if (transaction.feePayMethod == "0") {
                stringResource(R.string.maker_transfer_dashboard_type_OUR)
            } else {
                stringResource(R.string.maker_transfer_dashboard_type_BEN)
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_fee_method),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = feeMethodText,
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            val transferType: Triple<String, String, String> =
                if (transaction.process_time.isNullOrEmpty()) {
                    Triple(
                        stringResource(R.string.transaction_transfer_immediate),
                        stringResource(R.string.transaction_label_transfer_time),
                        transaction.createdDate ?: "",
                    )
                } else {
                    Triple(
                        stringResource(R.string.transaction_transfer_scheduled),
                        stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                        transaction.process_time ?: "",
                    )
                }

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_transfer_type),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transferType.first,
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            FoundationInfoHorizontal(
                title = transferType.second,
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transferType.third,
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )

            transaction.listFile?.firstOrNull()?.let { file ->
                if (!file.fileName.isNullOrEmpty()) {
                    FoundationInfoHorizontal(
                        title = stringResource(R.string.maker_payment_order_dashboard_upload_file_name_title),
                        titleStyle = FDS.Typography.bodyB2,
                        titleColor = FDS.Colors.characterSecondary,
                        value = file.fileName,
                        valueStyle = FDS.Typography.bodyB2,
                        valueColor = FDS.Colors.characterHighlighted,
                        onClick = {
                            onFieldClick.invoke(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                        isValueUnderline = true,
                    )
                }
            }
        }
    }
}