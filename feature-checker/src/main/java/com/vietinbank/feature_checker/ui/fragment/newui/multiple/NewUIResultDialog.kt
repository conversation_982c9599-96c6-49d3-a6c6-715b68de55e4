package com.vietinbank.feature_checker.ui.fragment.newui.multiple

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.fragment.app.FragmentManager
import com.vietinbank.core_common.extensions.getParcelableCustom
import com.vietinbank.core_domain.models.checker.MultipleResultDomain
import com.vietinbank.core_domain.models.checker.MultipleStatusDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.dialog.BaseDialog
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.dialog.DialogLayout
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.feature_checker.ui.component.multiple_transaction.MultipleTransactionFactory
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@AndroidEntryPoint
class NewUIResultDialog : BaseDialog<MultipleResultDomain>() {
    companion object {
        private const val ARG_STATUS = "arg_status"
        private const val ARG_RESULT = "arg_result"

        fun show(
            fragmentManager: FragmentManager,
            statusDomain: MultipleStatusDomain? = null,
            resultDomain: MultipleResultDomain? = null,
        ) {
            val dialog = NewUIResultDialog().apply {
                arguments = android.os.Bundle().apply {
                    putParcelable(ARG_STATUS, statusDomain)
                    putParcelable(ARG_RESULT, resultDomain)
                }
            }
            dialog.show(fragmentManager, "NewUIResultDialog")
        }
    }

    override val resultKey: String = "approve_result"
    override val layout: DialogLayout = DialogLayout.BottomSheet
    override val allowTouchDismiss: Boolean = true
    override val requiresSecureFlag: Boolean = true // Set to true for production banking app
    override val maxWidthDp: Int = 600 // For tablets
    override val resultFragmentManager: FragmentManager get() = parentFragmentManager

    @Inject
    lateinit var renderFactory: MultipleTransactionFactory

    @Composable
    override fun DialogContent(
        visible: Boolean,
        onDismissRequest: () -> Unit,
        onResult: (MultipleResultDomain) -> Unit,
    ) {
        val statusModel = arguments?.getParcelableCustom<MultipleStatusDomain>(ARG_STATUS)
        val resultModel = arguments?.getParcelableCustom<MultipleResultDomain>(ARG_RESULT)

        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false)
                    .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                    .background(FDS.Colors.backgroundBgContainer)
                    .padding(vertical = FDS.Sizer.Gap.gap24),
            ) {
                FoundationText(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    text = statusModel?.title ?: "",
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                )

                FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24))

                Column {
                    FoundationStatus(
                        modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                        statusMessage = statusModel?.statusMessage ?: "",
                        statusCode = if (true == statusModel?.isSuccess) {
                            Status.Success
                        } else {
                            Status.Fail
                        },
                    )

                    FoundationInfoHorizontal(
                        modifier = Modifier
                            .padding(top = FDS.Sizer.Gap.gap16)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        title = stringResource(R.string.checker_multiple_transaction),
                        value = resultModel?.totalTrans?.toIntOrNull().toString() ?: "",
                        valueStyle = FDS.Typography.bodyB2,
                    )

                    if (true == statusModel?.isSuccess) {
                        FoundationInfoHorizontal(
                            modifier = Modifier
                                .padding(top = FDS.Sizer.Gap.gap8)
                                .padding(horizontal = FDS.Sizer.Gap.gap24),
                            title = stringResource(R.string.feature_checker_process_time),
                            value = statusModel.tranDate ?: "",
                            valueStyle = FDS.Typography.bodyB2,
                        )
                    }

                    FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap24))

                    // render content
                    renderFactory.getRender(statusModel?.tranType)?.RenderResultContent(
                        true == statusModel?.isSuccess,
                        resultModel,
                    )
                }
            }
            FoundationButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding16,
                        vertical = FDS.Sizer.Padding.padding24,
                    ),
                text = stringResource(R.string.common_back),
                onClick = onDismissRequest,
                isLightButton = false,
            )
        }
    }
}