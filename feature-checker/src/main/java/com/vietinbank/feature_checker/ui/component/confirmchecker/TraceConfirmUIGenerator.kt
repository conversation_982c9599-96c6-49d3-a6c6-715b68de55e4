package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class TraceConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_trace_success),
                value = transaction.mtId ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_trace_success_type),
                value = transaction.tranSubTypeName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_trace_success_content),
                value = transaction.remark ?: "",
            ),
        )

        if (transaction.feeAccountNo.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_trace_init_feeAmount),
                    value = transaction.feeAmount ?: "",
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_main_account_type_D),
                    value = transaction.feeAccountNo ?: "",
                ),
            )
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(com.vietinbank.core_ui.R.string.checker_trace_init_feeAmount),
                    value = Utils.g().getDotMoneyHasCcy(
                        transaction.feeAmount ?: "",
                        transaction.currency ?: "",
                    ),
                ),
            )
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }

    // chuyen tien + lenh chi
    private fun TransferDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        var list = mutableListOf(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency} - ${transaction.fromAccountName ?: ""}",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tới tài khoản",
                value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.receiveBankName ?: "",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords,
                isHighlighted = true,
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),

            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức thu phí",
                value = if (transaction.feePayMethod == "0") "Phí ngoài" else "Phí trong",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
        )

        transaction.listFile?.firstOrNull()?.let {
            list.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "File đính kèm",
                    value = it.fileName ?: "",
                    isHighlighted = true,
//                    onClick = { onFieldClick("File đính kèm", it) },
                ),
            )
        }

        list.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian giao dịch",
                value = transaction.createdDate ?: "",
            ),
        )

        list.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian chuyển",
                value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
            ),
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            list.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                ),
            )
        }
        return list
    }

    // chi luong tu dong + chi luong qua ngan hang
    private fun SalaryDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        var listInfo = mutableListOf<ConfirmItem>()
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
        )
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords ?: "",
                isHighlighted = true,
            ),
        )

        if (transaction.tranType == "fx") {
            transaction.exchangeRate?.let {
                listInfo.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Tỷ giá (quy đổi tham khảo)",
                        value = it,
                    ),
                )
            }
            transaction.debitAmount?.let {
                listInfo.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số tiền trích nợ (tham khảo)",
                        value = it,
                    ),
                )
            }
        }

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
        )

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
        )

        transaction.listFile?.firstOrNull()?.let {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "File đính kèm",
                    value = it.fileName ?: "",
                    isHighlighted = true,
//                onClick = { onFieldClick("File đính kèm", it) },
                ),
            )
        }

        transaction.listFile2?.firstOrNull()?.let {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    value = it.fileName ?: "",
                    isHighlighted = true,
//                onClick = { onFieldClick("File đính kèm", it) },
                ),
            )
        }

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian giao dịch",
                value = transaction.createdDate ?: "",
            ),
        )

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian chuyển",
                value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
            ),
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                ),
            )
        }
        return listInfo
    }

    // chuyen tien theo file
    private fun BulkDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        val listInfo = mutableListOf<ConfirmItem>()
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
        )
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords,
                isHighlighted = true,
            ),
        )

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
        )

        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
        )

        transaction.listFile?.firstOrNull()?.let {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "File đính kèm",
                    value = it.fileName ?: "",
                    isHighlighted = true,
                ),
            )
        }

        transaction.listFile2?.firstOrNull()?.let {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hồ sơ đính kèm",
                    value = it.fileName ?: "",
                    isHighlighted = true,
                ),
            )
        }
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian giao dịch",
                value = transaction.createdDate ?: "",
            ),
        )
        listInfo.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian chuyển",
                value = if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else "Đặt lịch",
            ),
        )
        if (!transaction.process_time.isNullOrEmpty()) {
            listInfo.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                ),
            )
        }
        return listInfo
    }

    // chuyen tien tach lenh
    private fun DefaultDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        return mutableListOf<ConfirmItem>(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""}\n${transaction.fromAccountName ?: ""}",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tới tài khoản",
                value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.receiveBankName ?: "",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords ?: "",
                isHighlighted = true,
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức thu phí",
                value = transaction.feePayMethodDesc ?: "",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
            ),
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Thời gian giao dịch",
                value = transaction.createdDate ?: "",
            ),
        )
    }

    // Nộp NSNN và Theo file
    private fun CustomsInlandDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        val items = mutableListOf<ConfirmItem>()
        // Thêm header Thông tin nguời nộp thuế
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin nguời nộp thuế",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức",
                value = transaction.taxMethodName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên đơn vị nộp",
                value = transaction.payname ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mã số thuế",
                value = transaction.paycode ?: "",
            ),
        )

        if (!transaction.idNumber.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Số CMND/Hộ chiếu/CCCD",
                    value = transaction.idNumber ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Địa chỉ",
                value = transaction.payadd ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = transaction.fromAccountNo ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.branchName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Thêm header Thông tin chi tiết nộp NSNN
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết nộp NSNN",
            ),
        )

        transaction.items?.forEach { file ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Tổng số tiền thực nộp",
                    value = Utils.g().getDotMoneyHasCcy(
                        file.amount ?: "",
                        file.currency ?: "",
                    ),
                    subValue = moneyHelper.convertAmountToWords(
                        file.amount ?: "",
                        file.currency ?: "",
                    ),
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            ),
        )

        transaction.items?.forEach { file ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Nội dung nộp thuế",
                    value = file.content ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã NDKT (TM)",
                    value = file.sitCode ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Kỳ thuế",
                    value = file.taxPeriod ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )
        return items
    }

    // hạ tầng
    private fun InfrastructureDetail(transaction: TransactionDomain): MutableList<ConfirmItem> {
        val items = mutableListOf<ConfirmItem>()

        // Điện HCM
        if ("975" == transaction.providerCode) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin đơn vị thu phí",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã đơn vị thu phí",
                    value = (transaction.providerCode ?: "") + (transaction.providerName ?: ""),
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.SPACER,
                ),
            )

            // Thêm header Thông tin nguời nộp thuế
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin đơn vị nộp thuế",
                ),
            )
        } else if ("31" == transaction.providerCode) { // Điện hải phòng
            // Thêm header Thông tin nguời nộp thuế
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin nguời nộp phí hạ tầng",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức",
                value = transaction.taxMethodName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tên đơn vị nộp",
                value = transaction.payname ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Mã số thuế",
                value = transaction.payCode ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Địa chỉ",
                value = transaction.payAddress ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = if (!transaction.fromAccountNo.isNullOrEmpty()) {
                    "${transaction.fromAccountNo ?: ""} - ${transaction.currency}"
                } else {
                    ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.branchName ?: "",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Điện HCM

        // Thêm header Thông tin nguời nộp thuế
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin chi tiết khoản nộp",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "ID chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.invoiceId ?: ""
                } else {
                    transaction.docId ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherNumber ?: ""
                } else {
                    transaction.docNum ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ký hiệu chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherSymbol ?: ""
                } else {
                    transaction.docSign ?: ""
                },
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngày chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherDate ?: ""
                } else {
                    transaction.docDate ?: ""
                },
            ),
        )

        if ("975" != transaction.providerCode) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã chương",
                    value = transaction.chapterCode ?: "",
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Mã tiểu mục",
                    value = transaction.subsect ?: "",
                ),
            )
        }

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền giao dịch",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.amount ?: "",
                    transaction.currency ?: "",
                ),
                subValue = moneyHelper.convertAmountToWords(
                    transaction.amount ?: "",
                    transaction.currency ?: "",
                ),
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.feeAmount ?: "",
                    transaction.currency ?: "",
                ),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )
        return items
    }
}
