package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_2
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.safeClickable
import java.util.Calendar
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class BulkTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                if (isSelectionMode) {
                    FoundationSelector(
                        boxType = SelectorType.Checkbox,
                        isSelected = isSelected,
                        onClick = onChangeClick,
                    )
                }

                FoundationIconText(
                    modifier = Modifier
                        .fillMaxWidth()
                        .safeClickable {},
                    text = transaction.fileName?.trim() ?: "",
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_common_file_24,
                            size = FDS.Sizer.Icon.icon24,
                            tint = when {
                                isSelected -> FDS.Colors.characterHighlighted
                                isSelectionMode -> FDS.Colors.characterSecondary
                                else -> FDS.Colors.characterPrimary
                            },
                        ),
                    ),
                    style = FDS.Typography.captionLSemibold.copy(textDecoration = TextDecoration.Underline),
                    color = when {
                        isSelected -> FDS.Colors.characterHighlighted
                        isSelectionMode -> FDS.Colors.characterSecondary
                        else -> FDS.Colors.characterPrimary
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.maker_transfer_dashboard_amount),
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.totalAmount ?: "",
                    transaction.currency ?: "",
                ),
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.approve_file_total_transaction),
                value = when (transaction.tranType?.uppercase()) {
                    Tags.TYPE_GROUP_TRANSFER_CTTF300 -> transaction.total ?: ""
                    else -> transaction.totalTrxNo ?: ""
                },
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.approve_file_id_number),
                value = transaction.bulkID ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_create_time),
                value = when (transaction.tranType?.uppercase()) {
                    Tags.TYPE_GROUP_TRANSFER_CTTF300 -> transaction.processDate ?: ""
                    else -> transaction.createdDate ?: ""
                },
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            if (!transaction.processTime.isNullOrEmpty()) {
                val processTime = transaction.processTime.getDateToFormat(
                    dd_MM_yyyy_HH_mm_ss_2, dd_MM_yyyy_HH_mm,
                ) ?: ""
                val processTimeMillis = processTime.toTimeInMillis(dd_MM_yyyy_HH_mm_ss)
                val isOverdue =
                    processTimeMillis > 0 && processTimeMillis - Calendar.getInstance().timeInMillis < 0

                FoundationInfoHorizontal(
                    title = stringResource(R.string.manager_detail_appointment_date),
                    value = processTime,
                    titleStyle = FDS.Typography.captionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                    valueColor = if (isOverdue) FDS.Colors.stateError else null,
                )
            }
        }
    }
}