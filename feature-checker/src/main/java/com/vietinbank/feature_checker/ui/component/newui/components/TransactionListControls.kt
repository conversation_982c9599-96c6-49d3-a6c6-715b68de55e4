package com.vietinbank.feature_checker.ui.component.newui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

// Display mode for the left-side control
private enum class LeftMode { Approve, Reject, Count, Delete, None }

/**
 * Component for transaction list controls
 * Displays multi-select and sort controls
 *
 * @param onMultiSelectClick Callback when multi-select is clicked
 * @param onSortClick Callback when sort is clicked
 */
@Composable
fun TransactionListControls(
    onMultiSelectClick: () -> Unit,
    onDeleteSelectClick: () -> Unit,
    onSortClick: () -> Unit,
    isChecker: Boolean,
    selectedTabIndex: Int,
    totalTransactionsCount: Int,
    isDataLoaded: Boolean,
    modifier: Modifier = Modifier,
    isRejectTransactionType: Boolean = false,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = FDS.Sizer.Padding.padding24),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Left-side control varies by role and selected tab, with load gating
        val targetMode = when {
            isChecker && selectedTabIndex == 0 -> if (isRejectTransactionType) LeftMode.Reject else LeftMode.Approve
            !isChecker && selectedTabIndex == 2 -> LeftMode.Delete
            else -> LeftMode.Count
        }

        var displayMode by remember {
            mutableStateOf(
                if (targetMode == LeftMode.Count && !isDataLoaded) LeftMode.None else targetMode,
            )
        }

        // Only switch to Count when data is loaded to avoid showing stale numbers
        LaunchedEffect(targetMode, isDataLoaded) {
            displayMode = if (isDataLoaded) targetMode else LeftMode.None
//            when (targetMode) {
//                LeftMode.Count -> displayMode = if (isDataLoaded) LeftMode.Count else LeftMode.None
//                else -> displayMode = targetMode
//            }
        }

        when {
            // trường hợp không có giao dịch nào => chỉ hiển thị icon sort
            totalTransactionsCount <= 0 || displayMode == LeftMode.Count -> {
                FoundationText(
                    text = stringResource(
                        R.string.feature_checker_count_transaction,
                        totalTransactionsCount,
                    ),
                    style = FDS.Typography.bodyB2Emphasized,
                    color = FDS.Colors.characterSecondary,
                )
            }

            displayMode == LeftMode.Approve ||
                displayMode == LeftMode.Reject -> {
                val isRejectMode = displayMode == LeftMode.Reject
                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.safeClickable(onSafeClick = onMultiSelectClick),
                ) {
                    FoundationIconText(
                        text = stringResource(
                            if (isRejectMode) {
                                R.string.feature_checker_reject_multiple
                            } else {
                                R.string.feature_checker_approve_multiple
                            },
                        ),
                        style = FDS.Typography.interactionSmallButton,
                        color = FDS.Colors.characterHighlighted,
                        icons = mapOf(
                            IconPosition.LEFT to IconConfig(
                                icon = if (isRejectMode) {
                                    com.vietinbank.feature_checker.R.drawable.ic_disbursement_archive
                                } else {
                                    com.vietinbank.feature_checker.R.drawable.ic_checker_multi_approve_24
                                },
                            ),
                        ),
                    )
                }
            }

            displayMode == LeftMode.Delete -> {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.safeClickable(onSafeClick = onDeleteSelectClick),
                ) {
                    FoundationIconText(
                        text = stringResource(R.string.feature_checker_delete_multiple),
                        style = FDS.Typography.interactionSmallButton,
                        color = FDS.Colors.characterHighlighted,
                        icons = mapOf(
                            IconPosition.LEFT to IconConfig(
                                icon = com.vietinbank.feature_checker.R.drawable.ic_checker_trash_14,
                            ),
                        ),
                    )
                }
            }

            displayMode == LeftMode.None -> {
                // Keep layout height without showing stale info
                FoundationText(
                    text = "",
                    style = FDS.Typography.interactionSmallButton,
                    color = FDS.Colors.characterHighlighted,
                )
            }
        }

        // Sort control - icon only
        Box(
            modifier = Modifier.safeClickable(
                onSafeClick = onSortClick,
            ),
        ) {
            Icon(
                painter = painterResource(id = com.vietinbank.feature_checker.R.drawable.ic_checker_sort_desc_24),
                contentDescription = stringResource(R.string.manager_filter_option),
                tint = FDS.Colors.characterHighlighted,
                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
            )
        }
    }
}
