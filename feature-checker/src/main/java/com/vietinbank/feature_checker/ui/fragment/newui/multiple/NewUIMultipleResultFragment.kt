package com.vietinbank.feature_checker.ui.fragment.newui.multiple

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getParcelableCustom
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.AssessmentSuccessDialog
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatDialog
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatResult
import com.vietinbank.feature_checker.ui.component.multiple_transaction.MultipleTransactionFactory
import com.vietinbank.feature_checker.ui.component.newui.multiple.MultipleResultActions
import com.vietinbank.feature_checker.ui.component.newui.multiple.NewUIMultipleResultScreen
import com.vietinbank.feature_checker.ui.fragment.MultipleApprovalConfirmViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.getValue

@AndroidEntryPoint
class NewUIMultipleResultFragment : BaseFragment<MultipleApprovalConfirmViewModel>() {

    override val viewModel: MultipleApprovalConfirmViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var renderFactory: MultipleTransactionFactory

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiResultState.collectAsState()
        val isShowCSatState by viewModel.isShowCSatState.collectAsState()
        NewUIMultipleResultScreen(
            uiState = uiState,
            isShowCSat = isShowCSatState,
            actions = { action ->
                when (action) {
                    is MultipleResultActions.HomeClick -> {
                        appNavigator.goToHome()
                    }

                    is MultipleResultActions.ShareClick -> {
                        // chia sẻ
                        viewLifecycleOwner.lifecycleScope.launch {
                            try {
                                // Get the root view of the fragment
                                val rootView = view?.rootView ?: return@launch

                                // Create a full screen bounds since we want to capture the entire success screen
                                val screenBounds = androidx.compose.ui.geometry.Rect(
                                    0f,
                                    0f,
                                    rootView.width.toFloat(),
                                    rootView.height.toFloat(),
                                )

                                // Capture the screenshot
                                val bitmap =
                                    com.vietinbank.core_ui.utils.ScreenshotUtil.captureScreenshot(
                                        rootView = rootView,
                                        contentBounds = screenBounds,
                                        context = requireContext(),
                                    )

                                bitmap?.let {
                                    // Share the captured screenshot
                                    com.vietinbank.core_ui.utils.ScreenshotUtil.shareBitmap(
                                        context = requireContext(),
                                        bitmap = it,
                                    )
                                } ?: run {
                                    Toast.makeText(
                                        requireContext(),
                                        getString(com.vietinbank.core_ui.R.string.common_cannot_screenshot),
                                        Toast.LENGTH_SHORT,
                                    ).show()
                                }
                            } catch (e: Exception) {
                                Toast.makeText(
                                    requireContext(),
                                    getString(
                                        com.vietinbank.core_ui.R.string.common_error_share,
                                        e.message,
                                    ),
                                    Toast.LENGTH_SHORT,
                                ).show()
                            }
                        }
                    }

                    is MultipleResultActions.ManageClick -> {
                        // 0 - tab yeu cau, 1 - tab quan lý
                        appNavigator.gotoTransactionManager(
                            bundleOf().apply { putInt(Tags.TRANSACTION_BUNDLE, 1) },
                        )
                    }

                    is MultipleResultActions.DetailClick -> {
                        // mở dialog chi tiet tung loại giao dich
                        NewUIResultDialog.show(
                            fragmentManager = childFragmentManager,
                            statusDomain = viewModel.getStatus(action.isConfirmSuccess),
                            resultDomain = action.resultMultiple,
                        )
                    }

                    is MultipleResultActions.EmojiSelected -> {
                        // Show CSAT rating dialog
                        val dialog = CsatDialog.newInstance()
                        dialog.show(childFragmentManager, "CsatDialog")
                    }

                    else -> {}
                }
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get bundle data first
        getBundleData()

        // Initialize observers
        initObserver()

        // Listen for dialog result
        childFragmentManager.setFragmentResultListener(
            "csat_dialog_result",
            this@NewUIMultipleResultFragment,
        ) { _, bundle ->
            bundle.getParcelableCustom<CsatResult>("key_result")?.let {
                viewModel.rateCSat(Tags.CSAT_FUNC_ID_KEY, it.feedbackPoint, it.feedbackComment)
            }
        }
    }

    private fun getBundleData() {
        viewModel.initResultState(arguments)
    }

    private fun initObserver() {
        FlowUtils.collectFlow(this@NewUIMultipleResultFragment, viewModel.rateCSATStatus) {
            AssessmentSuccessDialog.newInstance().show(
                childFragmentManager,
                AssessmentSuccessDialog::class.java.name,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}