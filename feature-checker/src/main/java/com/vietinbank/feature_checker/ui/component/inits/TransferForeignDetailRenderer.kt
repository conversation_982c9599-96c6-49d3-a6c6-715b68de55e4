package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

class TransferForeignDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Thông tin chung section
        val isCollapse = remember { mutableStateOf(true) }
        SectionHeader(
            title = "Thông tin chung",
            hasMoreInfo = true,
            isCollapse = isCollapse.value,
        ) {
            isCollapse.value = !isCollapse.value
        }
        TransactionDetailsCard {
            KeyValueRow(
                label = "Loại giao dịch",
                value = (transaction.tranTypeName ?: ""),
            )
            KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
            KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")

            if (!isCollapse.value) {
                KeyValueRow(
                    label = "Người tạo",
                    value = "${transaction.activityLogs?.createdBy?.username ?: ""} - ${transaction.activityLogs?.createdBy?.processDate ?: ""}",
                )

                var verifyUser = ""
                transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                    verifyUser += "${item.username ?: ""} - ${item.processDate}"
                    if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                        verifyUser += "\n"
                    }
                }
                if (!verifyUser.isEmpty()) {
                    KeyValueRow(label = "Người phê duyệt", value = verifyUser)
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin người chuyển
        SectionHeader(title = "Thông tin người chuyển")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Tên đơn vị mua/chuyển tiền",
                value = transaction.fromAccountName ?: "",
            )
            KeyValueRow(
                label = "Hình thức",
                value = if (transaction.remitterName?.isEmpty() == false) {
                    "Nguời mua, chuyển tiền là người chuyển thực tế"
                } else {
                    "Nguời mua, chuyển tiền Không là Người chuyển thực tế"
                },
            )
            if (transaction.remitterName?.isEmpty() == false) {
                KeyValueRow(
                    label = "Tên người chuển tiền thực tế",
                    value = transaction.fromAccountName ?: "",
                )
                KeyValueRow(
                    label = "Quốc gia",
                    value = transaction.remitterCountry ?: "",
                )
                KeyValueRow(
                    label = "Tỉnh/Thành Phố",
                    value = transaction.remitterDistrict ?: "",
                )
                KeyValueRow(
                    label = "Phường/Xã",
                    value = transaction.remitterWard ?: "",
                )
                KeyValueRow(
                    label = "Đường",
                    value = transaction.remitterStreet ?: "",
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin chi tiết
        SectionHeader(title = "Thông tin chi tiết")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Cán bộ giới thiệu",
                value = transaction.introducer ?: "",
            )
            KeyValueRow(label = "Tên chi nhánh xử lý", value = transaction.branchName ?: "")
            KeyValueRow(
                label = "Số tiền chuyển",
                value = transaction.totalAmount ?: "",
                subValue = transaction.amountInWords ?: "",
//                isHighlighted = true,
            )
            KeyValueRow(label = "Nội dung giao dịch", value = transaction.remark ?: "")

            KeyValueRow(label = "Ghi chú", value = transaction.note ?: "")

            KeyValueRow(label = "Ngày giá trị", value = transaction.valueDate ?: "")

            KeyValueRow(
                label = "Mục đích sử dụng ngoại tệ",
                value = transaction.purposeTransferName ?: "",
            )
            KeyValueRow(
                label = "Ngày hoàn chứng từ",
                value = if (transaction.lcReturnDate?.isEmpty() == false) {
                    "Thanh toán sau khi nhận hàng"
                } else {
                    "Thanh toán trước khi nhận hàng"
                },
            )
            if (!transaction.lcReturnDate.isNullOrEmpty()) {
                KeyValueRow(label = "Ngày", value = transaction.lcReturnDate ?: "")
            }
            KeyValueRow(
                label = "Tổng số tiền phí",
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.totalTrxNo ?: "",
                    transaction.currency ?: "",
                ),
            )

            KeyValueRow(label = "Loại phí", value = transaction.feeType ?: "")
            KeyValueRow(label = "Tài khoản trích nợ phí", value = transaction.feeAccountNo ?: "")
        }
        // Tài khoản trích nợ 1
        SectionHeader(title = "Tài khoản trích nợ 1")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Số tài khoản",
                value = transaction.fromAccountNo ?: "",
                subValue = transaction.fromAccountName ?: "",
            )
            KeyValueRow(label = "Tỷ giá", value = transaction.debitRate1 ?: "")
            KeyValueRow(
                label = "Số tiền trích nợ tạm tính",
                value = transaction.debitAmtByRate1 ?: "",
                subValue = transaction.debitAmtByRate1AmountInWord ?: "",
            )
        }
        // Tài khoản trích nợ 2
        SectionHeader(title = "Tài khoản trích nợ 2")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Số tài khoản",
                value = transaction.fromAccount2 ?: "",
                subValue = transaction.fromAccountName2 ?: "",
            )
            KeyValueRow(label = "Tỷ giá", value = transaction.debitRate2 ?: "")
            KeyValueRow(
                label = "Số tiền trích nợ tạm tính",
                value = transaction.debitAmtByRate2 ?: "",
                subValue = transaction.debitAmtByRate2AmountInWord ?: "",
            )
        }

        // Thông tin người hưởng
        SectionHeader(title = "Thông tin người hưởng")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Số tài khoản/IBAN người hưởng",
                value = transaction.beneficiaryAccount ?: "",
            )
            KeyValueRow(
                label = "Tên",
                value = transaction.benName ?: "",
            )
            KeyValueRow(
                label = "Quốc gia",
                value = transaction.benCountry ?: "",
            )
            KeyValueRow(
                label = "Tỉnh/Thành Phố",
                value = transaction.benDistrict ?: "",
            )
            KeyValueRow(
                label = "Phường/Xã",
                value = transaction.benWard ?: "",
            )
            KeyValueRow(
                label = "Đường",
                value = transaction.benStreet ?: "",
            )
            KeyValueRow(
                label = "Người hưởng là người hưởng cuối cùng",
                value = if (transaction.remitterName?.isEmpty() == false) "Không" else "Có",
            )
            KeyValueRow(
                label = "Tên người hưởng cuối cùng",
                value = transaction.finalBenName ?: "",
            )
            KeyValueRow(
                label = "Quốc gia",
                value = transaction.finalBenCountry ?: "",
            )
            KeyValueRow(
                label = "Tỉnh/Thành Phố",
                value = transaction.finalBenDistrict ?: "",
            )
            KeyValueRow(
                label = "Phường/Xã",
                value = transaction.finalBenWard ?: "",
            )
            KeyValueRow(
                label = "Đường",
                value = transaction.finalBenStreet ?: "",
            )
        }

        SectionHeader(title = "Ngân hàng hưởng")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Bic code",
                value = transaction.receiveBank ?: "",
            )
            KeyValueRow(label = "Tên/Địa chỉ", value = transaction.benBankName ?: "")
            if (!transaction.benBankCountry.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Quốc gia",
                    value = transaction.benBankCountry ?: "",
                )
            }
            if (!transaction.benBankDistrict.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Tỉnh/Thành Phố",
                    value = transaction.benBankDistrict ?: "",
                )
            }

            if (!transaction.benBankWard.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Phường/Xã",
                    value = transaction.benBankWard ?: "",
                )
            }

            if (!transaction.benBankStreet.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Đường",
                    value = transaction.benBankStreet ?: "",
                )
            }
        }

        SectionHeader(title = "Ngân hàng trung gian")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Bic code",
                value = transaction.midBankCode ?: "",
            )
            KeyValueRow(label = "Tên/Địa chỉ", value = transaction.midBankName ?: "")
            if (!transaction.midBankCountry.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Quốc gia",
                    value = transaction.midBankCountry ?: "",
                )
            }
            if (!transaction.midBankDistrict.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Tỉnh/Thành Phố",
                    value = transaction.midBankDistrict ?: "",
                )
            }

            if (!transaction.midBankWard.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Phường/Xã",
                    value = transaction.midBankWard ?: "",
                )
            }

            if (!transaction.midBankStreet.isNullOrEmpty()) {
                KeyValueRow(
                    label = "Đường",
                    value = transaction.midBankStreet ?: "",
                )
            }
        }

        if (!transaction.listFile.isNullOrEmpty()) {
            SectionHeader(title = "File đính kèm")

            transaction.listFile?.firstOrNull()?.let { file ->
                KeyValueRow(
                    label = "Hồ sơ CMNĐSDNT",
                    value = file.fileName ?: "Chưa có file",
                    isHyperlink = true,
                ) {
                    onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                }
            }
        }
    }
}