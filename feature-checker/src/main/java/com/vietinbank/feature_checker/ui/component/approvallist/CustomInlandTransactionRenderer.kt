package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import javax.inject.Inject

/**
 * Created by phongdc on 11/6/25.
 */
class CustomInlandTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
//        Column(modifier = Modifier.fillMaxWidth()) {
//            Row(
//                modifier = Modifier.fillMaxWidth(),
//                verticalAlignment = Alignment.CenterVertically,
//            ) {
//                BaseText(
//                    text = if (transaction.uid.isNullOrEmpty()) {
//                        transaction.tranTypeName.trim()
//                    } else {
//                        transaction.tranTypeName.trim() + " ePay "
//                    },
//                    color = AppColors.blue02,
//                    textSize = 14.sp,
//                    fontCus = 5,
//                    maxLines = 2,
//                    overflow = TextOverflow.Ellipsis,
//                    modifier = Modifier.weight(1f),
//                )
//
//                BaseText(
//                    modifier = Modifier.padding(start = 16.dp),
//                    text = Utils.g().getDotMoneyHasCcy(
//                        transaction.amount.getAmountServer(),
//                        transaction.ccy,
//                    ),
//                    fontCus = 5,
//                    color = AppColors.primaryRed,
//                    textSize = 14.sp,
//                    rightDrawable = R.drawable.ic_more,
//                    rightDrawableSize = 24.dp,
//                    textAlign = TextAlign.End,
//                )
//            }
//
//            Spacer(modifier = Modifier.height(10.dp))
//
//            // Chi tiết giao dịch - không bị ảnh hưởng bởi icon ở trên
//            transaction.details.forEach { detail ->
//                if (detail.label.isNotEmpty()) {
//                    KeyValueRow(
//                        label = detail.label,
//                        value = detail.value,
//                        subValue = detail.subValue ?: "",
//                        iconResource = detail.iconResource,
//                    )
//                    Spacer(modifier = Modifier.height(4.dp))
//                } else {
//                    Row(
//                        modifier = Modifier.fillMaxWidth(),
//                        horizontalArrangement = Arrangement.End,
//                    ) {
//                        BaseText(
//                            text = detail.value,
//                            color = if (detail.isWarning) Color.Red else AppColors.textPrimary,
//                            textSize = 14.sp,
//                            textAlign = TextAlign.End,
//                            maxLines = 3,
//                            overflow = TextOverflow.Ellipsis,
//                        )
//                    }
//                    Spacer(modifier = Modifier.height(4.dp))
//                }
//            }
//        }
    }
}