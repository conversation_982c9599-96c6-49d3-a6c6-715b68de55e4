package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_2
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import java.util.Calendar
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class SalaryTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        val typeSalary = when (transaction.tranType?.uppercase()) {
            Tags.TYPE_GROUP_SALARY_AUTO -> stringResource(R.string.salary_auto)
            Tags.TYPE_GROUP_SALARY_FOREIGN -> stringResource(R.string.feature_checker_transaction_type_foreign)
            Tags.TYPE_GROUP_SALARY -> stringResource(R.string.salary_bank_auto)
            else -> ""
        }
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                if (isSelectionMode) {
                    FoundationSelector(
                        boxType = SelectorType.Checkbox,
                        isSelected = isSelected,
                        onClick = onChangeClick,
                    )
                }

                FoundationText(
                    text = stringResource(R.string.transaction_number_label),
                    style = FDS.Typography.captionL,
                    color = FDS.Colors.characterSecondary,
                )

                FoundationIconText(
                    modifier = Modifier.safeClickable {
                        // copy
                    },
                    text = transaction.mtId ?: "",
                    style = FDS.Typography.captionLSemibold,
                    color = when {
                        isSelected -> FDS.Colors.characterHighlighted
                        isSelectionMode -> FDS.Colors.characterSecondary
                        else -> FDS.Colors.characterPrimary
                    },
                    icons = mapOf(
                        IconPosition.RIGHT to IconConfig(
                            icon = R.drawable.ic_account_copy_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    text = typeSalary,
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = com.vietinbank.feature_checker.R.drawable.ic_checker_sort_arrow_16,
                            size = FDS.Sizer.Icon.icon16,
                        ),
                    ),
                )

                FoundationText(
                    modifier = Modifier.weight(1f),
                    text = Utils.g().getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.End,
                )
            }

            val (fileName, onClickFile) = if (transaction.fileName.isNullOrEmpty()) {
                stringResource(R.string.salary_not_found_file) to null
            } else {
                transaction.fileName to {
//                    onAction.invoke(ManagerAction.OnFileClick(transaction.ibFile))
                }
            }

            val (colorFileName, styleFileName) = if (transaction.fileName.isNullOrEmpty()) {
                FDS.Colors.characterPrimary to FDS.Typography.captionL
            } else {
                FDS.Colors.characterHighlighted to FDS.Typography.captionL.copy(textDecoration = TextDecoration.Underline)
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.salary_file),
                titleStyle = FDS.Typography.captionL,
                value = fileName,
                valueStyle = styleFileName,
                valueColor = colorFileName,
                onClick = onClickFile,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_content),
                value = transaction.remark ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            if (!transaction.processTime.isNullOrEmpty()) {
                val processTime = transaction.processTime.getDateToFormat(
                    dd_MM_yyyy_HH_mm_ss_2, dd_MM_yyyy_HH_mm,
                ) ?: ""
                val processTimeMillis = processTime.toTimeInMillis(dd_MM_yyyy_HH_mm_ss)
                val isOverdue =
                    processTimeMillis > 0 && processTimeMillis - Calendar.getInstance().timeInMillis < 0

                FoundationInfoHorizontal(
                    title = stringResource(R.string.manager_detail_appointment_date),
                    value = processTime,
                    titleStyle = FDS.Typography.captionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                    valueColor = if (isOverdue) FDS.Colors.stateError else null,
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }
}
