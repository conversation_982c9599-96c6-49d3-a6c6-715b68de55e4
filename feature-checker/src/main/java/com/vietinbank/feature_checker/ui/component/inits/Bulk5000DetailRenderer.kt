package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class Bulk5000DetailRenderer @Inject constructor() : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
    }

    @Composable
    override fun NewRenderTransactionDetails(
        modifier: Modifier,
        transaction: TransactionDomain,
        transactionHelper: TransactionHelperDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        Column(modifier = modifier.fillMaxWidth()) {
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                // Status chip
                FoundationStatus(
                    statusCode = Status.Pending,
                    statusMessage = transaction.statusName,
                )

                FoundationInfoHorizontal(
                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                    title = stringResource(R.string.account_transaction_no),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.mtId ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.checker_multiple_transaction),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.totalTrxNo ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                transaction.listFile?.firstOrNull()?.let {
                    FoundationInfoHorizontal(
                        title = stringResource(R.string.approve_file_name),
                        titleStyle = FDS.Typography.bodyB2,
                        titleColor = FDS.Colors.characterSecondary,
                        value = it.fileName ?: "",
                        valueStyle = FDS.Typography.bodyB2,
                        valueColor = FDS.Colors.characterHighlighted,
                        isValueUnderline = true,
                        onClick = {
                            onFieldClick.invoke(TransactionFieldEvent.FileAttachmentClick(it))
                        },
                    )
                }

                val feeMethodText = if (transaction.feePayMethod == "0") {
                    stringResource(R.string.maker_transfer_dashboard_type_OUR)
                } else {
                    stringResource(R.string.maker_transfer_dashboard_type_BEN)
                }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_fee_method),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = feeMethodText,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_total_fee),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transaction.feeAmount ?: "",
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                // hình thức chuyển - title - ngày chuyển
                val transferType: Triple<String, String, String> =
                    if (transaction.process_time.isNullOrEmpty()) {
                        Triple(
                            stringResource(R.string.transaction_transfer_immediate),
                            stringResource(R.string.transaction_label_transfer_time),
                            transaction.createdDate ?: "",
                        )
                    } else {
                        Triple(
                            stringResource(R.string.transaction_transfer_scheduled),
                            stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule),
                            transaction.process_time ?: "",
                        )
                    }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_transfer_type),
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transferType.first,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )

                FoundationInfoHorizontal(
                    title = transferType.second,
                    titleStyle = FDS.Typography.bodyB2,
                    titleColor = FDS.Colors.characterSecondary,
                    value = transferType.third,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = FDS.Colors.characterPrimary,
                )
            }
        }
    }

    override fun isNewRender(): Boolean {
        return true
    }
}