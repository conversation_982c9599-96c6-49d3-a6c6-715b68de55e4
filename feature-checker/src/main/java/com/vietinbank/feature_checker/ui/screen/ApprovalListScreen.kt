package com.vietinbank.feature_checker.ui.screen

/**
 * Created by van<PERSON><PERSON> on 10/3/25.
 */
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_domain.models.checker.TransactionListActions
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.fragment.ApprovalListViewModel
import com.vietinbank.feature_checker.ui.fragment.TransactionListUIState
import kotlinx.coroutines.delay
import java.util.UUID

/**
 * Màn hình danh sách giao dịch
 */
@Composable
fun TransactionListScreen(
    viewModel: ApprovalListViewModel,
    actions: TransactionListActions,
    factory: TransactionRendererFactory,
) {
    // Collect state từ ViewModel
    val uiState by viewModel.uiState.collectAsState()
    val isSelectionMode by viewModel.isSelectionMode.collectAsState()
    val isShowApprove by viewModel.isShowApprove.collectAsState()
    val selectedTransactions by viewModel.selectedTransactions.collectAsState()
    val totalCount by viewModel.totalTransactionsCount.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val canLoadMore by viewModel.canLoadMore.collectAsState()
    val approveTitleName = factory.getRendererTitle(viewModel.currentTransType ?: "")
    var loadMoreError by remember { mutableStateOf<String?>(null) }
    val isGlobalMaxMode by viewModel.isGlobalMaxMode.collectAsState()

    // Collect loadMoreError từ SharedFlow
    LaunchedEffect(key1 = Unit) {
        viewModel.loadMoreError.collect { error ->
            loadMoreError = error
            delay(5000)
            loadMoreError = null
        }
    }

    // Theo dõi sự thay đổi của needScrollToTop
    LaunchedEffect(key1 = Unit) {
        snapshotFlow { viewModel.needScrollToTop }
            .collect { shouldScroll ->
                if (shouldScroll) {
                    // chỉ thu thập trạng thái, việc scroll thực sự sẽ được xử lý trong TransactionListContent
                }
            }
    }

    TransactionListContent(
        uiState = uiState,
        titleName = approveTitleName,
        isSelectedMax = isGlobalMaxMode,
        isShowApprove = isShowApprove,
        isSelectionMode = isSelectionMode,
        selectedCount = selectedTransactions.size,
        totalCount = totalCount,
        isLoadingMore = isLoadingMore,
        canLoadMore = canLoadMore,
        loadMoreError = loadMoreError,
        shouldScrollToTop = viewModel.needScrollToTop,
        onScrollCompleted = { viewModel.needScrollToTop = false },
        onBackClick = actions.onBackClick,
        onHomeClick = actions.onHomeClick,
        onSortClick = actions.onSortClick,
        onSelectClick = actions.onSelectClick,
        onCloseSelectionClick = actions.onCloseSelectionClick,
        onTransactionClick = { transaction ->
            if (isSelectionMode) {
                viewModel.toggleTransactionSelection(transaction.mtID)
                actions.onTransactionSelectClick(transaction)
            } else {
                actions.onTransactionClick(transaction)
            }
        },
        onTransactionMenuClick = actions.onTransactionMenuClick,
        onCheckboxClick = { transaction ->
            viewModel.toggleTransactionSelection(transaction.mtID)
            actions.onTransactionSelectClick(transaction)
        },
        isTransactionSelected = { transaction ->
            selectedTransactions.contains(transaction.mtID)
        },
        onRetryClick = actions.onRetryClick,
        onApproveClick = actions.onApproveClick,
        onRejectClick = actions.onRejectClick,
        onLoadMore = { viewModel.loadMoreTransactions() },
        onRetryLoadMore = { viewModel.retryLoadMore() },
        factory = factory,
        onSelectAllClick = { viewModel.toggleMaxSelectAll() },
    )
}

/**
 * Nội dung màn hình danh sách giao dịch, phân tách từ ViewModel để dễ test
 */
@Composable
fun TransactionListContent(
    uiState: TransactionListUIState,
    titleName: String,
    isSelectedMax: Boolean,
    isShowApprove: Boolean,
    isSelectionMode: Boolean,
    selectedCount: Int,
    totalCount: Int,
    isLoadingMore: Boolean,
    canLoadMore: Boolean,
    loadMoreError: String?,
    shouldScrollToTop: Boolean,
    onScrollCompleted: () -> Unit,
    onBackClick: () -> Unit,
    onHomeClick: () -> Unit,
    onSortClick: () -> Unit,
    onSelectClick: () -> Unit,
    onCloseSelectionClick: () -> Unit,
    onTransactionClick: (TransactionItemUiModel) -> Unit,
    onTransactionMenuClick: (TransactionItemUiModel) -> Unit,
    onCheckboxClick: (TransactionItemUiModel) -> Unit,
    isTransactionSelected: (TransactionItemUiModel) -> Boolean,
    onRetryClick: () -> Unit,
    onApproveClick: () -> Unit,
    onRejectClick: () -> Unit,
    onLoadMore: () -> Unit,
    onRetryLoadMore: () -> Unit,
    onSelectAllClick: () -> Unit,
    factory: TransactionRendererFactory,
) {
    val compositionId = remember { UUID.randomUUID().toString() }

//    Box(modifier = Modifier.fillMaxSize()) {
//        Column(
//            modifier = Modifier.fillMaxSize(),
//        ) {
//            // Custom AppBar based on selection mode
//            if (isSelectionMode) {
//                // Selection mode AppBar
//                SelectionModeAppBar(
//                    selectedCount = selectedCount,
//                    isSelectMaxTransaction = isSelectedMax,
//                    totalCount = totalCount,
//                    onCloseClick = onCloseSelectionClick,
//                    onSelectAllClick = onSelectAllClick,
//                )
//            } else {
//                val appBarActions = mutableListOf<AppBarAction>()
//                appBarActions.add(
//                    AppBarAction(
//                        icon = com.vietinbank.core_ui.R.drawable.ic_filter,
//                        contentDescription = "SORT",
//                        onClick = { onSortClick.invoke() },
//                        tint = Color.White,
//                    ),
//                )
//                // Normal AppBar
//                BaseAppBar(
//                    title = titleName,
//                    onBackClick = onBackClick,
//                    actions = appBarActions,
//                    showSelectText = true,
//                    selectText = "Chọn",
//                    onSelectClick = { onSelectClick.invoke() },
//                )
//            }
//
//            // Content based on state
//            when (uiState) {
//                is TransactionListUIState.Loading -> {
//                    Box(
//                        modifier = Modifier.fillMaxSize(),
//                        contentAlignment = Alignment.Center,
//                    ) {
//                        CircularProgressIndicator(
//                            color = AppColors.redButton,
//                            modifier = Modifier.size(56.dp),
//                        )
//                    }
//                }
//
//                is TransactionListUIState.Success -> {
//                    if (uiState.transactions.isEmpty()) {
//                        Box(
//                            modifier = Modifier
//                                .fillMaxSize()
//                                .padding(16.dp),
//                            contentAlignment = Alignment.Center,
//                        ) {
//                            Column(
//                                horizontalAlignment = Alignment.CenterHorizontally,
//                            ) {
//                                Icon(
//                                    painter = painterResource(id = R.drawable.ic_more),
//                                    contentDescription = "No transactions",
//                                    modifier = Modifier.size(64.dp),
//                                    tint = Color.Gray,
//                                )
//
//                                Spacer(modifier = Modifier.height(16.dp))
//
//                                BaseText(
//                                    text = "Không có giao dịch nào",
//                                    color = Color.Gray,
//                                    textSize = 16.sp,
//                                    textAlign = TextAlign.Center,
//                                )
//                            }
//                        }
//                    } else {
//                        // LazyListState để điều khiển scroll
//                        val listState = remember { LazyListState() }
//
//                        // Scroll lên đầu danh sách nếu cần
//                        if (shouldScrollToTop) {
//                            LaunchedEffect(key1 = shouldScrollToTop) {
//                                listState.animateScrollToItem(0)
//                                onScrollCompleted() // Thông báo đã scroll xong để reset flag
//                            }
//                        }
//                        var hasTriggeredLoadMore by remember(uiState.transactions.size) {
//                            mutableStateOf(
//                                false,
//                            )
//                        }
//
//                        LazyColumn(
//                            modifier = Modifier
//                                .fillMaxSize()
//                                .padding(horizontal = 12.dp)
//                                .navigationBarsPadding()
//                                // Add bottom padding when in selection mode to account for action bar
//                                .padding(bottom = if (isSelectionMode) 64.dp else 0.dp),
//                            state = listState,
//                        ) {
//                            itemsIndexed(
//                                items = uiState.transactions,
//                                // Tạo key phức hợp từ mtID và index để đảm bảo tính duy nhất
//                                key = { index, _ ->
//                                    "position_${index}_in_list_$compositionId"
//                                },
//                            ) { index, transaction ->
//                                AnimatedVisibility(
//                                    visible = true,
//                                    enter = fadeIn(initialAlpha = 0.3f) +
//                                        expandVertically(
//                                            expandFrom = Alignment.Top,
//                                            initialHeight = { it / 2 },
//                                        ),
//                                    modifier = Modifier.animateItem(),
//                                ) {
//                                    TransactionCard(
//                                        transaction = transaction,
//                                        isSelectionMode = isSelectionMode,
//                                        isSelected = isTransactionSelected(transaction),
//                                        onClick = { onTransactionClick(transaction) },
//                                        onMenuClick = { onTransactionMenuClick(transaction) },
//                                        onCheckboxClick = { onCheckboxClick(transaction) },
//                                        factory = factory,
//                                    )
//                                }
//                                Spacer(modifier = Modifier.height(6.dp))
//
//                                // Logic load more:
//                                // KHÔNG trigger load more nếu danh sách < page size (15 items)
//                                // Lý do: Nếu server trả về < 15 items nghĩa là đã hết data
//                                // canLoadMore lúc này phải là false (nếu true thì ViewModel có bug)
//
//                                val pageSize = 15
//                                val shouldCheckLoadMore = uiState.transactions.size >= pageSize
//                                val isNearEnd = index >= uiState.transactions.size - 3
//
//                                if (shouldCheckLoadMore &&
//                                    isNearEnd &&
//                                    canLoadMore &&
//                                    !isLoadingMore &&
//                                    !hasTriggeredLoadMore
//                                ) {
//                                    LaunchedEffect(key1 = index) {
//                                        hasTriggeredLoadMore = true
//                                        onLoadMore()
//                                    }
//                                }
//                            }
//
//                            // Thêm item loading ở cuối nếu đang tải thêm
//                            if (isLoadingMore) {
//                                item {
//                                    Box(
//                                        modifier = Modifier
//                                            .fillMaxWidth()
//                                            .padding(vertical = 16.dp),
//                                        contentAlignment = Alignment.Center,
//                                    ) {
//                                        CircularProgressIndicator(
//                                            modifier = Modifier.size(24.dp),
//                                            color = AppColors.gradientButtonStart,
//                                            strokeWidth = 2.dp,
//                                        )
//                                    }
//                                }
//                            }
//
//                            // Hiển thị lỗi loadMore nếu có
//                            if (loadMoreError != null) {
//                                item {
//                                    Column(
//                                        modifier = Modifier
//                                            .fillMaxWidth()
//                                            .padding(vertical = 16.dp, horizontal = 12.dp),
//                                        horizontalAlignment = Alignment.CenterHorizontally,
//                                    ) {
//                                        BaseText(
//                                            text = loadMoreError,
//                                            color = Color.Red,
//                                            textSize = 14.sp,
//                                            textAlign = TextAlign.Center,
//                                        )
//
//                                        Spacer(modifier = Modifier.height(8.dp))
//
//                                        Box(
//                                            modifier = Modifier
//                                                .clip(RoundedCornerShape(6.dp))
//                                                .background(AppColors.redButton)
//                                                .padding(horizontal = 16.dp, vertical = 8.dp)
//                                                .safeClickable { onRetryLoadMore() },
//                                            contentAlignment = Alignment.Center,
//                                        ) {
//                                            BaseText(
//                                                text = "Thử lại",
//                                                color = Color.White,
//                                                textSize = 14.sp,
//                                            )
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//
//                is TransactionListUIState.Error -> {
//                    Box(
//                        modifier = Modifier
//                            .fillMaxSize()
//                            .padding(16.dp),
//                        contentAlignment = Alignment.Center,
//                    ) {
//                        Column(
//                            horizontalAlignment = Alignment.CenterHorizontally,
//                        ) {
//                            Icon(
//                                painter = painterResource(id = R.drawable.ic_more),
//                                contentDescription = "Error",
//                                modifier = Modifier.size(64.dp),
//                                tint = Color.Red,
//                            )
//
//                            Spacer(modifier = Modifier.height(16.dp))
//
//                            BaseText(
//                                text = uiState.message,
//                                color = AppColors.textPrimary,
//                                textSize = 16.sp,
//                                textAlign = TextAlign.Center,
//                            )
//
//                            Spacer(modifier = Modifier.height(24.dp))
//
//                            Box(
//                                modifier = Modifier
//                                    .clip(RoundedCornerShape(10.dp))
//                                    .background(AppColors.redButton)
//                                    .padding(horizontal = 32.dp, vertical = 12.dp)
//                                    .safeClickable { onRetryClick() },
//                                contentAlignment = Alignment.Center,
//                            ) {
//                                BaseText(
//                                    text = "Thử lại",
//                                    color = Color.White,
//                                    textSize = 16.sp,
//                                )
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        // Bottom action bar for selection mode
//        if (isSelectionMode && uiState is TransactionListUIState.Success && uiState.transactions.isNotEmpty()) {
//            Box(
//                modifier = Modifier
//                    .navigationBarsPadding()
//                    .align(Alignment.BottomCenter)
//                    .fillMaxWidth()
//                    .height(60.dp)
//                    .background(Color.White)
//                    .border(
//                        width = 0.5.dp,
//                        color = AppColors.borderColor,
//                        shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
//                    )
//                    .padding(top = 4.dp), // Thêm padding top
//            ) {
//                Row(
//                    modifier = Modifier
//                        .fillMaxSize()
//                        .padding(horizontal = 12.dp, vertical = 8.dp),
//                    horizontalArrangement = Arrangement.SpaceBetween,
//                    verticalAlignment = Alignment.CenterVertically,
//                ) {
//                    // Reject button
//                    BaseButton(
//                        modifier = Modifier
//                            .weight(1f)
//                            .safeClickable { onRejectClick() },
//                        text = "Từ chối",
//                        isCancelButton = true,
//                    )
//
//                    if (isShowApprove) {
//                        // Approve button
//                        Spacer(modifier = Modifier.width(16.dp))
//                        BaseButton(
//                            modifier = Modifier
//                                .weight(1f)
//                                .safeClickable { onApproveClick() },
//                            text = "Duyệt",
//                        )
//                    }
//                }
//            }
//        }
//    }
}

/**
 * App bar for selection mode
 */
@Composable
fun SelectionModeAppBar(
    selectedCount: Int,
    isSelectMaxTransaction: Boolean = false,
    totalCount: Int,
    onCloseClick: () -> Unit,
    onSelectAllClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .statusBarsPadding()
            .height(56.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            // Left section with selection text
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Checkbox indicator
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .background(Color.White.copy(alpha = 0.2f), CircleShape)
                        .border(
                            width = 0.5.dp,
                            color = Color.White,
                            shape = CircleShape,
                        )
                        .safeClickable { onSelectAllClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    if (isSelectMaxTransaction) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_check),
                            contentDescription = "All Selected",
                            tint = Color.White,
                            modifier = Modifier.size(14.dp),
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                BaseText(
                    text = "Chọn tối đa cho 1 lần duyệt",
                    color = Color.White,
                    textSize = 16.sp,
                    fontCus = 2, // Medium
                )
            }

            // Right section with selection count and close button
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                // Close (X) button

                BaseText(
                    text = "$selectedCount/$totalCount",
                    color = Color.White,
                    textSize = 16.sp,
                    fontCus = 2, // Medium
                )

                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                        .safeClickable { onCloseClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_close),
                        contentDescription = "Close selection mode",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp),
                    )
                }
            }
        }
    }
}

/**
 * Card hiển thị thông tin giao dịch
 */
@Composable
fun TransactionCard(
    transaction: TransactionListDomain,
    isSelectionMode: Boolean,
    isSelected: Boolean,
    onClick: () -> Unit,
    onMenuClick: () -> Unit,
    onCheckboxClick: () -> Unit,
    factory: TransactionRendererFactory,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(Color.White)
            .border(
                width = 0.5.dp,
                color = AppColors.borderColor,
                shape = RoundedCornerShape(12.dp),
            )
            .safeClickable { onClick() },
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.Top,
        ) {
            // Selection indicator (only visible in selection mode)
            if (isSelectionMode) {
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clip(CircleShape)
                        .background(
                            if (isSelected) {
                                AppColors.borderColor
                            } else {
                                Color.White
                            },
                        )
                        .border(
                            width = 0.5.dp,
                            color = if (isSelected) {
                                AppColors.borderColor
                            } else {
                                Color.Gray
                            },
                            shape = CircleShape,
                        )
                        .safeClickable { onCheckboxClick() },
                    contentAlignment = Alignment.Center,
                ) {
                    if (isSelected) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_check), // Replace with checkmark icon
                            contentDescription = "Selected",
                            tint = Color.White,
                            modifier = Modifier.size(14.dp),
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))
            }

            // Transaction content
            Column(
                modifier = Modifier.weight(1f),
            ) {
                // Sử dụng renderer cho loại transaction tương ứng
                val renderer = factory.getRenderer(transaction.tranType ?: "")
                renderer.RenderContent(
                    transaction = transaction,
                    isSelectionMode = isSelectionMode,
                    false,
                    {},
                )
            }
        }
    }
}