package com.vietinbank.feature_checker.ui.component.success.renderer

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Factory to get appropriate success renderer based on transaction type
 * Similar to TransactionDetailRendererFactory pattern
 */
@Singleton
class TransactionSuccessRendererFactory @Inject constructor(
    private val transferRenderer: TransferSuccessRenderer,
    private val paymentRenderer: PaymentSuccessRenderer,
) {

    /**
     * Get the appropriate renderer for a transaction type
     * @param tranType The transaction type code
     * @return The renderer for that transaction type
     */
    fun getRenderer(tranType: String): ITransactionSuccessRenderer {
        return when (tranType) {
            // Transfer transactions use TransferSuccessRenderer
            "in", "ou", "np", "sn" -> transferRenderer

            // Payment transaction uses PaymentSuccessRenderer
            "pm" -> paymentRenderer

            // Future custom renderers can be added here:
            // "sl", "slo", "sx" -> salaryRenderer
            // "do" -> disbursementRenderer
            // "tp" -> tuitionPaymentRenderer
            // "bg" -> bankGuaranteeRenderer
            // etc.

            // For any other type, use transfer renderer as default
            else -> transferRenderer
        }
    }
}