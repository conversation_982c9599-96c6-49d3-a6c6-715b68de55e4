package com.vietinbank.feature_checker.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.checker.ConfirmOTPActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseOtpBoxInput
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.ui.fragment.ConfirmOTPViewModel
import kotlinx.coroutines.delay

/**
 * Created by vandz on 12/3/25.
 */
@Composable
fun ConfirmOTPScreen(
    viewModel: ConfirmOTPViewModel,
    actions: ConfirmOTPActions,
) {
    val coroutineScope = rememberCoroutineScope()

    // Collect state from ViewModel
    val token by viewModel.token.collectAsState()
    val timeoutSeconds by viewModel.timeoutSeconds.collectAsState()
    val isProcessing by viewModel.isProcessing.collectAsState()
    val otpValue by viewModel.otpValue.collectAsState()

    // Timer state
    var timeRemaining by remember { mutableIntStateOf(timeoutSeconds) }
    var isTimerRunning by remember { mutableStateOf(true) }

    // Start timer effect
    LaunchedEffect(key1 = timeoutSeconds, key2 = isTimerRunning) {
        timeRemaining = timeoutSeconds
        if (isTimerRunning) {
            while (timeRemaining > 0) {
                delay(1000)
                timeRemaining--
            }
            if (timeRemaining <= 0) {
                actions.onTimeoutReached()
            }
        }
    }

    // Main layout
    Column(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        // App Bar
        BaseAppBar(
            title = "Mã Soft OTP",
            onBackClick = actions.onBackClick,
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Description text
            BaseText(
                text = "Mã xác thực OTP bằng phương thức xác thực Soft OTP của Quý khách đang được hiển thị dưới đây. Quý khách vui lòng nhấn \"Xác nhận\" để tiếp tục giao dịch.",
                color = AppColors.textPrimary,
                textSize = 14.sp,
                modifier = Modifier.padding(bottom = 24.dp),
            )

            // PIN Title
            BaseText(
                text = "Mã PIN",
                color = AppColors.textPrimary,
                textSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
            )

            // OTP Input boxes
            BaseOtpBoxInput(
                value = otpValue,
                onValueChange = { newValue ->
                    if (newValue.length <= 6) {
                        viewModel.updateOTPValue(newValue.padEnd(6, ' '))
                    }
                },
            )

            // Timer text
            BaseText(
                text = "Mã OTP sẽ cập nhật sau ${timeRemaining}s",
                color = AppColors.textPrimary,
                textSize = 14.sp,
                modifier = Modifier.padding(top = 16.dp),
            )

            // Spacer to push buttons to bottom
            Spacer(modifier = Modifier.weight(1f))
        }

        // Bottom buttons
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // Cancel button
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .border(
                        width = 0.5.dp,
                        color = AppColors.borderColor,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .safeClickable { actions.onCancelClick() },
                contentAlignment = Alignment.Center,
            ) {
                BaseText(
                    text = "Hủy",
                    color = Color.Black,
                    textSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                )
            }

            // Confirm button
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                AppColors.gradientStart,
                                AppColors.gradientEnd,
                            ),
                        ),
                        shape = RoundedCornerShape(10.dp),
                    )
                    .safeClickable {
                        val cleanOTP = otpValue.trim()
                        if (cleanOTP.length == 6) {
                            actions.onConfirmClick(cleanOTP)
                        }
                    },
                contentAlignment = Alignment.Center,
            ) {
                BaseText(
                    text = "Xác nhận",
                    color = Color.White,
                    textSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                )
            }
        }
    }
}