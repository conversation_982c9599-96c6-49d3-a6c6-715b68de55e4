package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationAvatarShimmer
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationNavigationArea
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.dialog.csat_dialog.CsatView
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.LocalAppConfigManager
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.rememberAppNavBarDp
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun NewUISuccessScreen(
    transaction: TransactionDomain,
    approveResult: ApproveDomain? = null,
    successTitle: Pair<String, String>,
    confirmType: String,
    message: String,
    tranType: String, // Add tranType parameter
    rendererFactory: com.vietinbank.feature_checker.ui.component.success.renderer.TransactionSuccessRendererFactory, // Add factory
    isRejected: Boolean = false, // Add parameter to indicate if this is a rejection
    isShowCSatState: Boolean, //  show csat
    imageLoader: CoilImageLoader? = null,
    getBankIconUrl: ((String?) -> String?)? = null,
    activityLogs: com.vietinbank.core_domain.models.trace_payment.ActivityLogsDomains? = null,
    onHomeClick: () -> Unit,
    onShareClick: () -> Unit,
    onTransactionManageClick: () -> Unit,
    onEmojiSelected: (Int) -> Unit,
    onFileClick: ((FileTransactionDomain) -> Unit)? = null,
    onShowUpdateDialog: () -> Unit = {}, // Add callback for showing update dialog
) {
    // Tab state
    var selectedTabIndex by remember { mutableStateOf(0) }

    // Force hide keyboard on screen launch
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    LaunchedEffect(Unit) {
        keyboardController?.hide()
        focusManager.clearFocus(force = true)
    }

    // AppBar collapse scroll state
    val appBarScrollState = rememberFoundationAppBarScrollState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding8),
                ) {
                    // AppBar with transaction type as title and Home/Share actions
                    FoundationAppBar(
                        title = confirmType, // Use confirmType passed in (e.g. "Chuyển tiền trong VietinBank")
                        onNavigationClick = onHomeClick,
                        navigationIcon = painterResource(id = R.drawable.ic_home), // Use home icon directly
                        isBackHome = true, // This shows home icon instead of back
                        actions = listOf(
                            AppBarAction(
                                icon = R.drawable.ic_common_share_24,
                                onClick = onShareClick,
                                contentDescription = stringResource(com.vietinbank.core_ui.R.string.content_desc_share),
                            ),
                        ),
                        isLightIcon = false,
                        showLogo = true, // Enable logo for success screen
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Padding.padding8),
                        scrollState = appBarScrollState,
                    )

                    // Transaction summary info - Type, Amount and Amount in words
                    TransactionSummaryInfo(
                        confirmType = confirmType,
                        successTitle = successTitle,
                        scrollFraction = appBarScrollState.collapsedFraction,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding16), // Keep original horizontal padding
                    )

                    // Tabs
                    FoundationTabs(
                        tabs = listOf(
                            stringResource(R.string.transaction_tab_request),
                            stringResource(R.string.transaction_tab_history),
                        ),
                        selectedIndex = selectedTabIndex,
                        onTabSelected = { index ->
                            // Open history tab directly
                            selectedTabIndex = index
                        },
                        type = TabType.Pill,
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Padding.padding8) // Keep original horizontal padding
                            .padding(bottom = FDS.Sizer.Padding.padding16),
                    )
                }
            },
        ) { paddingValues ->
            // Main content
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(horizontal = FDS.Sizer.Padding.padding8),
                contentPadding = PaddingValues(
                    bottom = FDS.Sizer.Padding.padding100, // Space for floating button
                ),
            ) {
                // White container with watermark
                item {
                    val watermarkPainter =
                        painterResource(id = R.drawable.bg_common_water_mark)

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                            .background(FDS.Colors.white),
                    ) {
                        // Watermark only for Request tab (index 0)
                        if (selectedTabIndex == 0) {
                            Image(
                                painter = watermarkPainter,
                                contentDescription = null,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.Center),
                                contentScale = ContentScale.FillWidth,
                            )
                        }

                        // Content on top
                        when (selectedTabIndex) {
                            0 -> {
                                // Use renderer factory to get appropriate renderer
                                val renderer = rendererFactory.getRenderer(tranType)
                                renderer.RenderContent(
                                    transaction = transaction,
                                    approveResult = approveResult,
                                    confirmType = confirmType,
                                    isRejected = isRejected,
                                    imageLoader = imageLoader,
                                    getBankIconUrl = getBankIconUrl,
                                    onFileClick = onFileClick,
                                    onEmojiSelected = onEmojiSelected,
                                )
                            }
                            1 -> {
                                if (activityLogs != null) {
                                    Column(
                                        modifier = Modifier.fillMaxWidth(),
                                    ) {
                                        ApprovalHistoryTimeline(activityLogs = activityLogs)
                                    }
                                } else {
                                    ApprovalHistoryContent()
                                }
                            }
                        }
                    }
                }

                // CSAT section outside white card
                if (selectedTabIndex == 0 && isShowCSatState) {
                    item {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap24))
                        CsatView(
                            onClick = { onEmojiSelected(0) }, // Trigger dialog opening via callback
                        )
                    }
                }

                // Check approval limit action at the very bottom of History tab
                if (selectedTabIndex == 1 && activityLogs != null) {
                    item {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = FDS.Sizer.Padding.padding32)
                                .safeClickable { /* TODO: navigate to limit checking */ },
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                text = stringResource(R.string.maker_transfer_account_result_check_limited_approval),
                                style = FDS.Typography.bodyB2Emphasized,
                                color = FDS.Colors.characterHighlighted,
                            )
                            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
                            Image(
                                painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                                contentDescription = null,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
                            )
                        }
                    }
                }
            }
        }

        // Floating bottom button with FoundationNavigationArea
        val bottomPadding = rememberAppNavBarDp(LocalAppConfigManager.current)
            ?: FDS.Sizer.Padding.padding0

        FoundationNavigationArea(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(),
            height = FDS.Sizer.Padding.padding144, // Close enough to 136.dp
        ) {
            FoundationButton(
                text = stringResource(R.string.manager_transaction),
                onClick = onTransactionManageClick,
                isLightButton = true,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(
                        bottom = bottomPadding + FDS.Sizer.Padding.padding16,
                        top = FDS.Sizer.Padding.padding16,
                    ),
            )
        }
    }
}

@Composable
private fun TransactionSummaryInfo(
    confirmType: String,
    successTitle: Pair<String, String>,
    scrollFraction: Float = 0f,
    modifier: Modifier = Modifier,
) {
    // Use a smoother easing curve for natural motion
    val easing = androidx.compose.animation.core.FastOutSlowInEasing
    val animatedFraction = easing.transform(scrollFraction.coerceIn(0f, 1f))

    // Calculate animated values
    val wordAmountAlpha = (1f - animatedFraction)

    // Animate the bottom padding to push tabs up when text hides
    val animatedBottomPadding = androidx.compose.ui.unit.lerp(
        FDS.Sizer.Padding.padding24, // Original bottom padding
        FDS.Sizer.Padding.padding8, // Reduced bottom padding when scrolled
        animatedFraction,
    )

    Column(
        modifier = modifier
            .padding(bottom = animatedBottomPadding), // Animated bottom padding
    ) {
        // Add small top padding for better spacing
        Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding8))

        // Amount NUMBER (first) on TOP - always visible
        if (successTitle.first.isNotEmpty()) {
            FoundationText(
                text = successTitle.first, // This is the amount number
                style = FDS.Typography.headingH2.copy(
                    fontWeight = FontWeight.SemiBold,
                ),
                color = FDS.Colors.white,
                modifier = Modifier.fillMaxWidth(),
            )
        }

        // Animate the spacer height to collapse when scrolling
        val spacerHeight = androidx.compose.ui.unit.lerp(
            FDS.Sizer.Gap.gap4,
            0.dp,
            animatedFraction,
        )
        Spacer(modifier = Modifier.height(spacerHeight))

        // Amount in WORDS (second) on BOTTOM - fades out on scroll
        if (successTitle.second.isNotEmpty()) {
            // Use AnimatedVisibility for height animation
            androidx.compose.animation.AnimatedVisibility(
                visible = wordAmountAlpha > 0.1f, // Hide when mostly faded
                enter = androidx.compose.animation.expandVertically() + androidx.compose.animation.fadeIn(),
                exit = androidx.compose.animation.shrinkVertically() + androidx.compose.animation.fadeOut(),
            ) {
                FoundationText(
                    text = successTitle.second, // This is the amount in words
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.white.copy(alpha = 0.7f * wordAmountAlpha),
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
private fun RequestTabSuccessContent(
    successList: List<SuccessItemDomain>,
    confirmType: String,
    message: String,
    isRejected: Boolean = false, // Add rejection status parameter
    imageLoader: CoilImageLoader? = null,
    getBankIconUrl: ((String?) -> String?)? = null,
    onEmojiSelected: (Int) -> Unit,
    onFileClick: ((SuccessItemDomain) -> Unit)? = null,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
    ) {
        // Status badge only at the top
        TransactionStatusHeader(isRejected = isRejected, message = message)

        // Account transfer section (similar to NewUIInitTransactionScreen)
        AccountTransferSection(
            successList = successList,
            imageLoader = imageLoader,
            getBankIconUrl = getBankIconUrl,
        )

        // Divider
        FoundationDivider(
            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
        )

        // Transaction details
        TransactionDetailsSection(
            successList = successList,
            isRejected = isRejected,
            onFileClick = onFileClick,
        )
    }
}

@Composable
private fun TransactionStatusHeader(
    isRejected: Boolean = false, // Add rejection status parameter
    message: String?,
) {
    // Status badge only - aligned left
    FoundationStatus(
        statusMessage = message ?: "",
        statusCode = if (isRejected) Status.Fail else Status.Success,
        modifier = Modifier,
    )
}

@Composable
private fun AccountTransferSection(
    successList: List<SuccessItemDomain>,
    imageLoader: CoilImageLoader? = null,
    getBankIconUrl: ((String?) -> String?)? = null,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
    ) {
        // Source account - Find exact "Từ tài khoản" in successList
        val sourceAccount = successList.find {
            it.title == "Từ tài khoản" || it.title.contains("Tài khoản nguồn")
        }

        sourceAccount?.let {
            AccountInfoRow(
                accountName = it.subValue ?: "", // Use subValue for name, empty if not available
                accountNumber = it.mainValue,
                imageLoader = imageLoader,
                bankIconUrl = getBankIconUrl?.invoke("VietinBank"), // Get icon URL from cache
                bankName = null, // No bank name for source account (VietinBank)
            )
        }

        // Destination account and bank info - use normalized labels from SuccessHelper
        val destAccount = successList.find { it.title == "Chuyển đến" || it.title == "Tới tài khoản" || it.title == "Chuyển tới" }
        val bankInfo = successList.find { it.title == "Ngân hàng" }

        // Only show divider and destination if destAccount exists
        if (destAccount != null) {
            // Transfer divider with text
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    text = stringResource(R.string.transaction_label_transfer_to),
                    style = FDS.Typography.captionCaptionM.copy(
                        fontWeight = FontWeight.SemiBold,
                    ),
                    color = FDS.Colors.characterTertiary,
                    modifier = Modifier.padding(end = FDS.Sizer.Padding.padding8),
                )
                FoundationDivider(
                    modifier = Modifier.weight(1f),
                )
            }

            AccountInfoRow(
                accountName = destAccount.subValue ?: "", // Recipient name from subValue
                accountNumber = destAccount.mainValue, // Account number from mainValue
                imageLoader = imageLoader,
                bankIconUrl = getBankIconUrl?.invoke(bankInfo?.mainValue), // Get icon URL from cache
                bankName = bankInfo?.mainValue, // Bank name from separate "Ngân hàng" field
            )
        }
    }
}

@Composable
private fun AccountInfoRow(
    accountName: String,
    accountNumber: String,
    imageLoader: CoilImageLoader? = null,
    bankIconUrl: String? = null,
    bankName: String? = null,
) {
    // Track loading state for icon
    var isIconLoading by remember(bankIconUrl) {
        mutableStateOf(!bankIconUrl.isNullOrBlank())
    }

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
        verticalAlignment = Alignment.Top,
    ) {
        // Bank icon with URL loading or fallback - Match NewUIInitTransactionScreen size
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon40) // Changed to 48dp like NewUIInitTransactionScreen
                .clip(CircleShape)
                .background(FDS.Colors.gray50),
            contentAlignment = Alignment.Center,
        ) {
            when {
                // Show shimmer while loading
                isIconLoading && !bankIconUrl.isNullOrBlank() -> {
                    FoundationAvatarShimmer(
                        modifier = Modifier,
                        size = FDS.Sizer.Icon.icon40, // 40dp icon inside 48dp container
                    )

                    // Transition from shimmer to icon after delay
                    LaunchedEffect(bankIconUrl) {
                        kotlinx.coroutines.delay(500) // Give time for icon to load
                        isIconLoading = false
                    }
                }
                // Show loaded icon
                !bankIconUrl.isNullOrBlank() && imageLoader != null && !isIconLoading -> {
                    imageLoader.LoadUrl(
                        url = bankIconUrl,
                        isCache = true,
                        placeholderRes = null,
                        errorRes = null,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    )
                }
                // Fallback to default icon
                else -> {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_account_company_24),
                        contentDescription = bankName,
                        modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        tint = FDS.Colors.characterPrimary,
                    )
                }
            }
        }

        // Account info
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
        ) {
            // Show account name only if it's not empty
            if (accountName.isNotEmpty()) {
                FoundationText(
                    text = accountName,
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.characterPrimary,
                )
            }

            if (bankName != null) {
                // Display bank name and account number on separate lines
                Column(
                    verticalArrangement = Arrangement.spacedBy(2.dp),
                ) {
                    FoundationText(
                        text = bankName,
                        style = FDS.Typography.captionL,
                        color = FDS.Colors.characterSecondary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    FoundationText(
                        text = accountNumber,
                        style = if (accountName.isEmpty()) FDS.Typography.bodyB2 else FDS.Typography.captionL,
                        color = if (accountName.isEmpty()) FDS.Colors.characterPrimary else FDS.Colors.characterSecondary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            } else {
                FoundationText(
                    text = accountNumber,
                    style = if (accountName.isEmpty()) FDS.Typography.bodyB2 else FDS.Typography.captionL,
                    color = if (accountName.isEmpty()) FDS.Colors.characterPrimary else FDS.Colors.characterSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}

@Composable
private fun TransactionDetailsSection(
    successList: List<SuccessItemDomain>,
    isRejected: Boolean = false,
    onFileClick: ((SuccessItemDomain) -> Unit)? = null,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
    ) {
        // Define the display order for transaction details
        // Format: Số giao dịch, Mã tham chiếu, Nội dung, Phí giao dịch, Hình thức thu phí, Hình thức chuyển, Thời gian giao dịch
        val fieldsOrder = listOf(
            "Giao dịch số", // Transaction number
            "Số giao dịch", // Alternative transaction number label
            "Mã giao dịch", // Transaction code
            "Mã tham chiếu", // Reference code
            "Nội dung", // Content/Remark
            "Phí giao dịch", // Transaction fee
            "Hình thức thu phí", // Fee collection method
            "Hình thức chuyển", // Transfer method
            "Thời gian giao dịch", // Transaction time
            "Thời gian chuyển", // Transfer time (alternative label)
        )

        // Filter out account-related fields that are displayed in AccountTransferSection
        // and amount fields that are displayed in the header
        val accountFieldsToExclude = setOf(
            "Từ tài khoản",
            "Tài khoản nguồn",
            "Tài khoản",
            "Chuyển đến",
            "Chuyển tới",
            "Tới tài khoản",
            "Tài khoản đích",
            "Tài khoản thụ hưởng",
            "Ngân hàng",
            "Số tiền", // Amount is shown in header
        )

        // First, show fields in the defined order
        fieldsOrder.forEach { fieldName ->
            successList.find { it.title == fieldName && it.title !in accountFieldsToExclude }?.let { item ->
                TransactionDetailRow(
                    item = item,
                    onFileClick = onFileClick,
                )
            }
        }

        // Then show any remaining fields not in the order list or excluded list
        successList
            .filter { item ->
                item.title !in accountFieldsToExclude &&
                    item.title !in fieldsOrder
            }
            .forEach { item ->
                TransactionDetailRow(
                    item = item,
                    onFileClick = onFileClick,
                )
            }
    }
}

@Composable
private fun TransactionDetailRow(
    item: SuccessItemDomain,
    onFileClick: ((SuccessItemDomain) -> Unit)? = null,
) {
    // Check if this is a file attachment
    val isFileAttachment = (
        item.title == "File đính kèm" ||
            item.title == "Hồ sơ đính kèm" ||
            item.title == "Đề nghị sửa đổi" ||
            item.title == "Đề nghị giải tỏa/giảm trừ bảo lãnh"
        ) && !item.extraValue.isNullOrEmpty()

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (isFileAttachment && onFileClick != null) {
                    Modifier.safeClickable { onFileClick(item) }
                } else {
                    Modifier
                },
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        FoundationText(
            text = item.title,
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterSecondary,
            modifier = Modifier.weight(0.45f),
        )

        Column(
            modifier = Modifier.weight(0.55f),
            horizontalAlignment = Alignment.End,
        ) {
            FoundationText(
                text = item.mainValue,
                style = if (isFileAttachment) {
                    FDS.Typography.bodyB2Emphasized.copy(
                        textDecoration = TextDecoration.Underline,
                    )
                } else {
                    FDS.Typography.bodyB2Emphasized
                },
                color = if (isFileAttachment) {
                    FDS.Colors.primary
                } else {
                    FDS.Colors.characterPrimary
                },
                textAlign = TextAlign.End,
                modifier = Modifier.fillMaxWidth(),
            )

            // Display subValue if available
            item.subValue?.let { subValue ->
                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                FoundationText(
                    text = subValue,
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterTertiary,
                    textAlign = TextAlign.End,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
private fun ApprovalHistoryContent() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
    ) {
        FoundationText(
            text = stringResource(R.string.transaction_history_title),
            style = FDS.Typography.headingH4,
            color = FDS.Colors.characterPrimary,
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

        FoundationText(
            text = stringResource(R.string.transaction_history_empty),
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterSecondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

// Preview disabled - need to provide rendererFactory
/*
@Preview(showBackground = true)
@Composable
fun NewUISuccessScreenPreview() {
    NewUISuccessScreen(
        successList = listOf(
            SuccessItemDomain(
                title = "Mã giao dịch",
                mainValue = "************",
                subValue = null,
            ),
            SuccessItemDomain(
                title = "Tài khoản nguồn",
                mainValue = "*********",
                subValue = "VietinBank",
            ),
            SuccessItemDomain(
                title = "Tài khoản đích",
                mainValue = "*************",
                subValue = "Techcombank",
            ),
            SuccessItemDomain(
                title = "Nội dung",
                mainValue = "Chi lương T6",
                subValue = null,
            ),
            SuccessItemDomain(
                title = "Phí giao dịch",
                mainValue = "0 VND",
                subValue = null,
            ),
            SuccessItemDomain(
                title = "Phí tách lệnh",
                mainValue = "9,900 VND",
                subValue = null,
            ),
        ),
        successTitle = "Một triệu sáu trăm nghìn đồng" to "1,600,000 VND",
        confirmType = "Chuyển tiền",
        isRejected = false, // Add rejection status for preview
        isShowCSatState = true,
        onHomeClick = {},
        onShareClick = {},
        onTransactionManageClick = {},
        onEmojiSelected = {},
        onFileClick = {},
        message = "Phê duyệt",
    )
}
*/
