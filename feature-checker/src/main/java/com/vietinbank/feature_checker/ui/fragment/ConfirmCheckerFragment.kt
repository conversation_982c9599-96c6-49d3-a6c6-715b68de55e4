package com.vietinbank.feature_checker.ui.fragment

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.MethodEntityDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.smartca.ISignResult
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.softotp.ISoftResult
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.KeypassAuthDialog
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.screen.ConfirmCheckerScreen
import com.vietinbank.feature_checker.ui.sheet.SignSelectionSheet
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmCheckerFragment : BaseFragment<ConfirmCheckerViewModel>(), ISoftResult, ISignResult {
    override val viewModel: ConfirmCheckerViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    private var countDownTimer: CountDownTimer? = null

    @Inject
    lateinit var smartCAManager: ISmartCAManager

    // Tham số để nhận loại dữ liệu
    private var dataType: String = ""

    @Composable
    override fun ComposeScreen() {
        val uiModel by viewModel.uiModel.collectAsState()

        val hasNextApprovers by viewModel.hasNextApprovers.collectAsState()
        val nextApprovers by viewModel.getNextApprovers.collectAsState()
        val selectedNextApprover by viewModel.selectedNextApprover.collectAsState()
        val isNextApproverBottomSheetVisible by viewModel.isNextApproverBottomSheetVisible.collectAsState()

        val nextApproverInfo = "Theo đăng ký với VietinBank"
        val authMethodState by viewModel.authMethodState.collectAsState()

        // Lifecycle-aware collect
        val termState by viewModel.termState.collectAsStateWithLifecycle() // Không collect khi Fragment ở background
        val isAgreeDisbursement by viewModel.isAgreeDisbursement.collectAsStateWithLifecycle() // Tự động resume khi Fragment quay lại
        val pdfFile by viewModel.pdfFile.collectAsStateWithLifecycle() // Tránh collect thừa, tiết kiệm CPU & RAM

        val onFieldEvent: (TransactionFieldEvent) -> Unit = { event ->
            when (event) {
                is TransactionFieldEvent.FileAttachmentClick -> {
                    printLog("event: ${Utils.g().provideGson().to(event.file)}")
                    viewModel.getDownloadFileID(event.file)
                }
                is TransactionFieldEvent.ProfileAttachmentClick -> {
                    // Handle if needed
                }
                else -> {
                    // Handle other events if needed
                }
            }
        }

        ConfirmCheckerScreen(
            uiModel = uiModel,
            onFieldEvent = onFieldEvent,
            hasSoftOTP = authMethodState.hasSoftOTP,
            hasKeypass = authMethodState.hasKeypass,
            hasSign = authMethodState.hasSign,
            termState = termState,
            isAgreeDisbursement = isAgreeDisbursement,
            pdfFile = pdfFile,
            isReject = Tags.APPROVE != viewModel.confirmType,
            selectedAuthMethod = authMethodState.selectedMethod,
            onAuthMethodSelected = { method -> viewModel.updateSelectedAuthMethod(method) },
            onConfirmClick = { authMethod, reason, termState, isTerm ->
                if (Tags.APPROVE == viewModel.confirmType) {
                    if (Tags.TERMSTATE == isTerm) {
                        showNoticeDialog(termState)
                    } else {
                        when (authMethod) {
                            Tags.SOTP -> {
                                if (!softManager.isAllowSoft()) {
                                    showNoticeDialog(getString(R.string.notice_customer_not_register_payment_method))
                                } else if (softManager.getStatusLockSoft()) {
                                    // da kich hoat soft nhung bị khóa do nhập sai 5 lần
                                    appNavigator.goToLockSoftOTPDialog()
                                } else if (softManager.isUserActive) {
                                    // user da duoc kich hoat tren thiet bi
                                    viewModel.genSOTPTransCode()
                                } else {
                                    showConfirmDialog(
                                        getString(R.string.notice_customer_active_sotp),
                                        positiveAction = {
                                            if (softManager.isAppActive) {
                                                // kich hoat lan thu 2
                                                appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.ACTIVE.name)
                                            } else {
                                                appNavigator.gotoActivSoft()
                                            }
                                        },
                                        negativeAction = {},
                                    )
                                }
                            }

                            Tags.KEYPASS -> {
                                viewModel.genKeyPassChallengeCode()
                            }

                            else -> {
                                // call api get list cert
                                viewModel.getCertVNPT()
                            }
                        }
                    }
                } else {
                    if (reason.isEmpty()) {
                        showNoticeDialog("Vui lòng nhập lý do từ chối")
                    } else {
                        viewModel.doReject("", reason)
                    }
                }
            },
            onBackClick = { appNavigator.popBackStack() },
            onHomeClick = { appNavigator.goToHome() },
            hasNextApprovers = hasNextApprovers,
            nextApproverInfo = nextApproverInfo,
            selectedNextApprover = selectedNextApprover,
            isNextApproverBottomSheetVisible = isNextApproverBottomSheetVisible,
            onNextApproverClick = { viewModel.showNextApproverBottomSheet() },
            onToggleApprover = { viewModel.toggleNextApprover(it) },
            onNextApproverBottomSheetDismiss = { viewModel.hideNextApproverBottomSheet() },
            nextApprovers = nextApprovers,
            onChangeAgreeDisbursement = { agree ->
                viewModel.onChangeAgreeDisbursement(agree)
            },
            onDownloadPdf = { url ->
                viewModel.downloadPdf(url)
            },
        )
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        setupObserver()
        setupArguments()
        // gen otp
        softManager.setSoftResultListener(this)
        // Đăng ký listener cho kết quả PIN
        setupPinResultListener()
    }

    private fun setupArguments() {
        // Lấy confirm type
        viewModel.confirmType = arguments?.getString(Tags.CONFIRM_TYPE_BUNDLE)
        printLog("confirmType: ${viewModel.confirmType}")
        if (Tags.APPROVE == viewModel.confirmType) {
            viewModel.fetchNextApprovers()
        }
        // Xử lý dữ liệu từ các bundle
        processDataFromBundle()
        // Lấy phương thức xác thực
        processAuthMethodFromBundle()
    }

    /**
     * Xử lý dữ liệu từ bundle
     */
    private fun processDataFromBundle() {
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                printLog("Received JSON: $jsonData")
                val dataType = arguments?.getString(Tags.DATA_TYPE)
                // Dựa vào dataType để xử lý dữ liệu
                viewModel.parseAndProcessData(jsonData, dataType)
            } catch (e: Exception) {
                printLog("Lỗi parse JSON: ${e.message}")
                e.printStackTrace()
                viewModel.showError("Không thể xử lý dữ liệu: ${e.message}")
            }
        } ?: run {
            printLog("Không có dữ liệu transaction trong bundle")
            viewModel.showError("Không có dữ liệu để hiển thị")
        }
    }

    /**
     * Xử lý phương thức xác thực từ bundle
     */
    private fun processAuthMethodFromBundle() {
        arguments?.getString(Tags.PAYMENT_METHOD_BUNDLE)?.let { jsonData ->
            try {
                printLog("auth methods: $jsonData")
                // Parse dữ liệu là một object đơn, không phải List
                val methods: List<MethodEntityDomain> =
                    Utils.g().provideGson().fromJson(
                        jsonData,
                        object : TypeToken<List<MethodEntityDomain>>() {}.type,
                    ) ?: listOf()
                viewModel.processAuthMethods(methods)
                printLog("Đã nhận phương thức xác thực: ${methods.firstOrNull()?.method}")
            } catch (e: Exception) {
                printLog("Lỗi parse phương thức xác thực: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    /**
     * Đăng ký listener cho kết quả xác thực PIN
     */
    private fun setupPinResultListener() {
        viewLifecycleOwner.lifecycleScope.launch {
            setFragmentResultListener(VSoftConstants.RESULT_VERIFY_PIN) { _, bundle ->
                handlePinVerificationResult(bundle)
            }
        }
        parentFragmentManager.setFragmentResultListener(
            KeypassAuthDialog.KEYPASS_AUTH_DIALOG_RESULT_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            bundle.getString(KeypassAuthDialog.RESULT_KEYPASS_VALUE)?.let { keypassValue ->
                viewModel.verifyKeypass(keypassValue)
            }
        }
    }

    private fun setupObserver() {
        viewModel.apply {
            FlowUtils.collectFlow(this@ConfirmCheckerFragment, needSyncKeypass) { message ->
                showConfirmDialog(
                    message = message ?: "Quý khách cần đồng bộ Keypass",
                    positiveButtonText = "Đồng bộ",
                    positiveAction = {
                        openBrowser(
                            this@ConfirmCheckerFragment,
                            Constants.URL_KEYPASCO,
                        )
                    },
                    negativeAction = {},
                    negativeButtonText = "Đóng",
                )
            }

            FlowUtils.collectFlow(this@ConfirmCheckerFragment, needReactiveSoft) { message ->
                showConfirmDialog(
                    message = message ?: "Quý khách cần cài đặt Soft OTP",
                    positiveButtonText = "Kích hoạt ngay",
                    positiveAction = {
                        appNavigator.gotoActivSoft()
                    },
                    negativeAction = {},
                    negativeButtonText = "Đóng",
                )
            }

            FlowUtils.collectFlow(this@ConfirmCheckerFragment, needSyncSoft) { message ->
                showConfirmDialog(
                    message = message ?: "Quý khách cần đồng bộ Soft OTP",
                    positiveButtonText = "Đồng bộ ngay",
                    positiveAction = {
                        appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.SYNC.name)
                    },
                    negativeAction = {},
                    negativeButtonText = "Đóng",
                )
            }

            FlowUtils.collectFlow(this@ConfirmCheckerFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    val downloadFileId = resource.data.downloadFileId
                    if (downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(resource.message ?: "Không thể tải file đính kèm")
                }
            }

            FlowUtils.collectFlow(this@ConfirmCheckerFragment, fileUrlReady) { resources ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, resources.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog("Không thể mở file: ${e.message}")
                }
            }

            FlowUtils.collectFlow(this@ConfirmCheckerFragment, filePermissionEvent) { message ->
                showNoticeDialog(message)
            }

            genSOTPTransCode.observe(viewLifecycleOwner) { data ->
                when (data) {
                    is Resource.Success -> {
                        viewModel.transactionID = data.data.transactionId
                        appNavigator.goToEnterPIN(
                            VSoftConstants.VtpOTPFlowType.CONFIRM_IN_EFAST.name,
                        )

                        // navigate to verify pin
                    }

                    else -> {}
                }
            }
            genKeyPassChallengeCode.observe(viewLifecycleOwner) { data ->
                val confirmDataJson = Utils.g().provideGson().toJson(viewModel.uiModel.value)
                when (data) {
                    is Resource.Success -> {
                        appNavigator.goToConfirmKeypassDialog(
                            challengeCode = data.data.challengeCode.toString(),
                        )
                    }

                    else -> {
                        // Xử lý lỗi
                    }
                }
            }

            doRejectResponse.observe(viewLifecycleOwner) { data ->
                when (data) {
                    is Resource.Success -> {
                        // chuyen man hinh
                        val successList = Utils.g().provideGson().toJson(viewModel.getSuccessList())
                        val successTitle =
                            Utils.g().provideGson().toJson(
                                viewModel.getSuccessTitle(
                                    data.data.transactions?.firstOrNull(),
                                    data.data.status
                                        ?.message
                                        .toString(),
                                ),
                            )
//                        appNavigator.gotoSuccessChecker(successList, successTitle, viewModel.confirmType ?: "", "")
                    }

                    else -> {}
                }
            }

            // lay danh sach cert -> ky so
            FlowUtils.collectFlow(this@ConfirmCheckerFragment, lstCertState) { lstCert ->
                when (lstCert.size) {
                    0 -> {
                        showNoticeDialog("Không có CTS")
                    }

                    1 -> {
                        showConfirmDialog(
                            "Quý khách sẽ được chuyển đến ứng dụng Smart CA để tiếp tục thực hiện duyệt giao dịch bằng phương thức ký số. Quý khách có muốn tiếp tục không?",
                            "Đồng ý",
                            "Đóng",
                            positiveAction = {
                                doSignApprove()
                            },
                        )
                    }

                    else -> {
                        val signVNPTSheet = SignSelectionSheet(lstCert)
                        signVNPTSheet.setOnApplyClick {
                            // call approve
                            showConfirmDialog(
                                "Quý khách sẽ được chuyển đến ứng dụng Smart CA để tiếp tục thực hiện duyệt giao dịch bằng phương thức ký số. Quý khách có muốn tiếp tục không?",
                                "Đồng ý",
                                "Đóng",
                                positiveAction = {
                                    doSignApprove()
                                },
                            )
                        }
                        signVNPTSheet.show(
                            childFragmentManager,
                            SignSelectionSheet::class.java.name,
                        )
                    }
                }
            }
            // call ky so
            FlowUtils.collectFlow(this@ConfirmCheckerFragment, doApproveResponse) { data ->
                // show thong bao
                activity?.let {
                    smartCAManager.signTransaction(
                        requireActivity(),
                        data.verifySignInfo?.androidKey,
                        data.verifySignInfo?.tranCode,
                    )
                    changeSignState(SignCallState.Loading)
                }
            }

            // cap nhat ket qua ky so smart ca tu VNPT
            FlowUtils.collectFlow(this@ConfirmCheckerFragment, signFlowState) { signState ->
                when (signState) {
                    is SignCallState.Error -> {
                        forceAllHideLoading()
                        countDownTimer?.cancel()
                        countDownTimer = null
                        signState.exception?.let { onError(it) } ?: run {
                            showNoticeDialog(
                                signState.message ?: "",
                            ) { appNavigator.popBackStack() }
                        }
                    }

                    // trang thai ky thanh cong + case ky tren device khac
                    is SignCallState.Success, is SignCallState.Loading -> {
                        showLoading()
                        if (countDownTimer == null) {
                            countDownTimer = object : CountDownTimer(expiredCount * 1000L, 1000) {
                                override fun onTick(timeLeft: Long) {
                                    if ((timeLeft / 1000) % 5 == 0L) {
                                        // 5s call lai 1 lan nha -> recall detail -> kiem tra trang thai
                                        getTransactionDetail { appError ->
                                            /**
                                             * 1 thành công, ký xong -> case thanh cong -> khong can xu ly
                                             * 0 lỗi
                                             * 3 pending sign chờ ký số, check tiếp
                                             * */
                                            if (appError is AppException.ApiException && appError.code == "3") {
                                                // tiep tuc goi ky so theo count time -> khong can xu ly gi
                                            } else {
                                                // show thong bao loi -> dung luong recall api check
                                                changeSignState(SignCallState.Error(exception = appError))
                                            }
                                        }
                                    }
                                }

                                override fun onFinish() {
                                    changeSignState(SignCallState.Error(message = "Thời gian ký số đã kết thúc. Quý khách vui lòng thực hiện lại"))
                                }
                            }.start()
                        }
                    }

                    else -> {}
                }
            }

            // cap nhat ket qua ky so smart ca tu BE
            FlowUtils.collectFlow(this@ConfirmCheckerFragment, transactionDetailState) { data ->
                // phe duyet thanh cong
                countDownTimer?.cancel()
                countDownTimer = null
                val successList = Utils.g().provideGson().toJson(viewModel.getSuccessList())
                val successTitle = Utils.g().provideGson()
                    .toJson(getSuccessTitle(null, data.status?.message ?: ""))
//                appNavigator.gotoSuccessChecker(
//                    successList,
//                    successTitle,
//                    viewModel.confirmType ?: "",
//                )
            }
        }

        handleSingleEvent {
            (it as? KeypassAuthEvent)?.let { keypassEvent ->
                when (keypassEvent) {
                    KeypassAuthEvent.OnReachMaxFail ->
                        showNoticeDialog(
                            resources.getString(com.vietinbank.feature_soft.R.string.keypass_auth_blocked_message),
                            icon = com.vietinbank.core_ui.R.drawable.ic_commom_fail_24,
                        )
                    is KeypassAuthEvent.OnVerifyKeypassSuccess -> {
                        dismissAllDialogs()
                        navigateToSuccessScreen(keypassEvent.approveData)
                    }

                    is KeypassAuthEvent.OnVerifyFail -> {
                        showNoticeDialog(keypassEvent.message)
                    }

                    else -> {
                        // suppress
                    }
                }
            }
        }
    }

    private fun navigateToSuccessScreen(approveData: ApproveDomain) {
        val successList = Utils.g().provideGson().toJson(viewModel.getSuccessList())
        val successTitle = Utils.g().provideGson()
            .toJson(
                viewModel.getSuccessTitle(
                    approveData.transactions?.firstOrNull(),
                    approveData.status?.message ?: "",
                ),
            )
//        appNavigator.gotoSuccessChecker(
//            successList,
//            successTitle,
//            viewModel.confirmType ?: "",
//        )
    }

    private fun openBrowser(
        fragment: Fragment,
        url: String,
    ) {
        val webpage = Uri.parse(if (url.startsWith("http")) url else "https://$url")
        val intent = Intent(Intent.ACTION_VIEW, webpage)

        try {
            fragment.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handlePinVerificationResult(bundle: Bundle) {
        try {
            if (bundle.getBoolean(VSoftConstants.Bundle.KEY_DATA_1, false)) {
                softManager.genSoftOtp(
                    userId = viewModel.getKeypassProfile() ?: "",
                    transactionId = viewModel.transactionID ?: "",
                    messageId = "",
                )
            }
        } catch (_: Exception) {
        }
    }

    override fun onSuccess(otpCode: String?, timeCount: Int?) {
        // Chuyển đối tượng confirmData sang JSON
        val confirmDataJson =
            Utils.g().provideGson().toJson(viewModel.uiModel.value)
        printLog("selectedNextApprover: ${viewModel.selectedNextApprover.value}")
        val selectedNextApprover =
            Utils
                .g()
                .provideGson()
                .toJson(viewModel.selectedNextApprover.value)
        printLog("confirmDataJson: $confirmDataJson")
        printLog("OTP from Confirm Checker: $otpCode")
        appNavigator.goToConfirmOTP(
            transaction = confirmDataJson,
            transactionID = viewModel.transactionID ?: "",
            token = otpCode ?: "",
            confirmType = viewModel.confirmType ?: "",
            step = timeCount ?: 0,
            nextApprover = selectedNextApprover,
            originalTransaction = viewModel.getOriginalTransaction()?.let {
                Utils.g().provideGson().toJson(it)
            } ?: "",
            mtId = "",
        )
    }

    override fun isActive(isActive: Boolean?) {
        // chua kich hoat soft otp -> show thong bao neu can
    }

    override fun onError(message: String?, code: Int?) {
        showNoticeDialog(message ?: "")
    }

    override fun onResult(statusCode: Int, statusMessage: String) {
        // lang nghe ket qua cua smart
        println("=== $statusCode ===")
        if (statusCode == 0) {
            viewModel.changeSignState(SignCallState.Success)
        } else if ((statusCode != 3 && statusCode != -1)) {
            viewModel.changeSignState(SignCallState.Error(message = statusMessage))
        } else {
            viewModel.changeSignState(SignCallState.Loading)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }
}