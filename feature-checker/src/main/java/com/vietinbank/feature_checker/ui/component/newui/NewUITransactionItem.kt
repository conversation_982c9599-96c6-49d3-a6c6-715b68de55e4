package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_domain.models.checker.TransactionDetailItem
import com.vietinbank.core_domain.models.checker.TransactionItemUiModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Constants for NewUITransactionItem styling
 */
private object TransactionItemConstants {
    const val GLASS_OPACITY = 0.1f
    const val BORDER_OPACITY = 0.2f
    const val SELECTED_BORDER_OPACITY = 0.5f
    const val ICON_BACKGROUND_OPACITY = 0.15f
    const val CHECKBOX_UNCHECKED_OPACITY = 0.5f
    const val DETAIL_TEXT_OPACITY = 0.8f
    const val REFERENCE_TEXT_OPACITY = 0.6f
    const val MAKER_TEXT_OPACITY = 0.5f
}

/**
 * New UI Transaction Item Component
 * Displays individual transaction with glass morphism effect
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun NewUITransactionItem(
    transaction: TransactionItemUiModel,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // Pre-resolve colors for DrawScope
    val glassGradientColors = listOf(
        FDS.Colors.white.copy(alpha = TransactionItemConstants.GLASS_OPACITY),
        FDS.Colors.white.copy(alpha = TransactionItemConstants.GLASS_OPACITY * 0.5f),
    )

    val borderColor = if (isSelected) {
        FDS.Colors.primary.copy(alpha = TransactionItemConstants.SELECTED_BORDER_OPACITY)
    } else {
        FDS.Colors.white.copy(alpha = TransactionItemConstants.BORDER_OPACITY)
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick,
            )
            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius16))
            .drawBehind {
                // Glass morphism background
                drawRect(
                    brush = Brush.verticalGradient(glassGradientColors),
                    size = size,
                )
            }
            .border(
                width = if (isSelected) FDS.Sizer.Stroke.stroke2 else FDS.Sizer.Stroke.stroke1,
                color = borderColor,
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius16),
            ),
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius16),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp,
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16),
            verticalAlignment = Alignment.Top,
        ) {
            // Selection checkbox (if in selection mode)
            if (isSelectionMode) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onClick() },
                    colors = CheckboxDefaults.colors(
                        checkedColor = FDS.Colors.primary,
                        uncheckedColor = FDS.Colors.white.copy(alpha = TransactionItemConstants.CHECKBOX_UNCHECKED_OPACITY),
                        checkmarkColor = FDS.Colors.white,
                    ),
                )

                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
            }

            // Transaction icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(
                        FDS.Colors.primary.copy(alpha = TransactionItemConstants.ICON_BACKGROUND_OPACITY),
                    ),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    painter = painterResource(getTransactionIcon(transaction.tranType)),
                    contentDescription = null,
                    tint = FDS.Colors.primary,
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                )
            }

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

            // Transaction details
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                // Transaction title
                FoundationText(
                    text = transaction.tranTypeName,
                    style = FDS.Typography.bodyB1.copy(
                        fontWeight = FontWeight.SemiBold,
                    ),
                    color = FDS.Colors.white,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )

                // Transaction details - show first two detail items
                transaction.details.take(2).forEach { detail ->
                    FoundationText(
                        text = "${detail.label}: ${detail.value}",
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.white.copy(alpha = TransactionItemConstants.DETAIL_TEXT_OPACITY),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }

                // Reference number
                FoundationText(
                    text = "${stringResource(R.string.transaction_reference_prefix)}${transaction.referenceNo}",
                    style = FDS.Typography.captionCaptionM,
                    color = FDS.Colors.white.copy(alpha = TransactionItemConstants.REFERENCE_TEXT_OPACITY),
                )
            }

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

            // Amount
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                FoundationText(
                    text = transaction.amount,
                    style = FDS.Typography.bodyB1.copy(
                        fontWeight = FontWeight.Bold,
                    ),
                    color = FDS.Colors.success,
                )

                FoundationText(
                    text = transaction.ccy,
                    style = FDS.Typography.captionCaptionM,
                    color = FDS.Colors.white.copy(alpha = TransactionItemConstants.REFERENCE_TEXT_OPACITY),
                )

                // Maker info
                FoundationText(
                    text = transaction.uid,
                    style = FDS.Typography.captionCaptionM,
                    color = FDS.Colors.white.copy(alpha = TransactionItemConstants.MAKER_TEXT_OPACITY),
                )
            }
        }
    }
}

/**
 * Get appropriate icon for transaction type
 */
private fun getTransactionIcon(type: String): Int {
    return when (type.lowercase()) {
        "transfer", "ct" -> R.drawable.ic_upload_white
        "payment", "tt" -> R.drawable.ic_download
        "batch", "file" -> R.drawable.ic_document
        else -> R.drawable.ic_more
    }
}

/**
 * Get status color based on status text
 */
@Composable
private fun getStatusColor(status: String): Color {
    return when (status.lowercase()) {
        "pending", "waiting" -> FDS.Colors.warning
        "approved", "success" -> FDS.Colors.success
        "rejected", "failed" -> FDS.Colors.error
        else -> FDS.Colors.white.copy(alpha = TransactionItemConstants.REFERENCE_TEXT_OPACITY)
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000C28)
@Composable
private fun NewUITransactionItemPreview() {
    val sampleTransaction = TransactionItemUiModel(
        tranTypeName = "Chuyển tiền nội bộ",
        referenceNo = "FT24123456",
        tranType = "INTERNAL",
        mtID = "1",
        amount = "10,000,000",
        uid = "NVTUAN",
        ccy = "VND",
        fileName = "",
        details = listOf(
            TransactionDetailItem(
                label = "Người thụ hưởng",
                value = "NGUYEN VAN A",
            ),
            TransactionDetailItem(
                label = "Số tài khoản",
                value = "**********",
            ),
        ),
        status = "",
        toAccountNo = "",
        receiveName = "",
        receiveBankName = "",
        statusName = "",
    )

    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        NewUITransactionItem(
            transaction = sampleTransaction,
            isSelected = false,
            isSelectionMode = false,
            onClick = {},
            onLongClick = {},
        )

        NewUITransactionItem(
            transaction = sampleTransaction,
            isSelected = true,
            isSelectionMode = true,
            onClick = {},
            onLongClick = {},
        )
    }
}