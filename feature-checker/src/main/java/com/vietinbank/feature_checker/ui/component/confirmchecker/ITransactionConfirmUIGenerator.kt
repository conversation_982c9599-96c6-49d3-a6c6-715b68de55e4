package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel

/**
 * Created by vand<PERSON> on 9/4/25.
 */
interface ITransactionConfirmUIGenerator {
    /**
     * Tạo ConfirmUiModel cho giao dịch đơn lẻ (data chua format)
     */
    fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)? = null,
    ): ConfirmUiModel

    /**
     * Tạo ConfirmUiModel cho giao dịch lo
     */
    fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel
}