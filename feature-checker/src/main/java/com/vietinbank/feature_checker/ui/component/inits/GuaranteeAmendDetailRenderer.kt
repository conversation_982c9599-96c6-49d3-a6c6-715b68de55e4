package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.component.approvallist.TypeChange
import com.vietinbank.feature_checker.ui.component.inits.GuaranteeAmendDetailRenderer.Companion.FADE_DURATION
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import java.text.SimpleDateFormat
import java.util.Locale
import javax.inject.Inject

// sua doi bao lanh
class GuaranteeAmendDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : ITransactionDetailRenderer {

    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        Column(
            modifier = Modifier.padding(
                horizontal = FoundationDesignSystem.Sizer.Padding.padding8,
            ),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Padding.padding24))
            FoundationStatus(
                modifier = Modifier.padding(
                    horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
                statusMessage = transaction.statusName,
                statusCode = Status.Pending,
            )
            HorizontalDivider(
                Modifier.padding(
                    FoundationDesignSystem.Sizer.Padding.padding16,
                ),
            )
            SectionHeader(title = stringResource(id = R.string.feature_checker_guarantee_request))
            TransactionDetailsCard {
                transaction.listFiles?.filter {
                    !it.attachmentType.isNullOrEmpty() && it.attachmentType == TYPE_GDN_GUARANTEE
                }?.forEach { file ->
                    KeyValueRow(
                        label = when (file.attachmentType) {
                            TYPE_GDN_GUARANTEE -> stringResource(R.string.effective_start_date_change)
                            else -> ""
                        },
                        value = file.fileName ?: "",
                        isHyperlink = true,
                        onClick = {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                    )
                }
            }
            HorizontalDividerDetail(havePaddingBottom = true)
            SectionHeader(title = stringResource(id = R.string.feature_checker_general_information))

            val typeChangesInt = TypeChange.getTypeChangeList(transaction.typeChange ?: "")
            val typeChangeDesc = buildString {
                for (typeChange in typeChangesInt) {
                    val typeChangeLabel =
                        TypeChange.fromTypeId(typeChange)?.getTypeChangeLabel()
                    if (isNotEmpty()) append(", ")
                    append(typeChangeLabel)
                }
            }
            TransactionDetailsCard {
                KeyValueRow(label = stringResource(id = R.string.feature_checker_transaction_no), value = transaction.mtId ?: "")
                KeyValueRow(label = stringResource(id = R.string.feature_checker_type_changes), value = typeChangeDesc)
            }
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_customer),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_customer_name), value = transaction.cifName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_business_registration_number), value = transaction.guaranteeCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_date_range), value = transaction.dateRange ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_issue_by), value = transaction.issuesBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_head_address), value = transaction.headQuarters ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_representative_name),
                        value = transaction.representativeName ?: "",
                    )
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_authority), value = transaction.authority ?: "")
                    KeyValueRow(label = stringResource(id = R.string.authority_date), value = transaction.authorizationDate ?: "", true)
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_contract),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_mbnt_contract_number), value = transaction.contractNo ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_mbnt_contract_date), value = transaction.contractDate ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_transaction_number), value = transaction.host_mtid ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_issuing_bank),
                        value = "${transaction.branch ?: ""} - ${transaction.branchName ?: ""}",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_content_for_change_bl),
                initiallyCollapsed = true,
                content = {
                    val typeChangesInt = TypeChange.getTypeChangeList(transaction.typeChange ?: "")
                    var typeChanges = mutableListOf<String?>()
                    for (typeChange in typeChangesInt) {
                        val typeChangeLabel =
                            TypeChange.fromTypeId(typeChange)?.getTypeChangeLabel()
                        typeChanges = typeChanges.plus(typeChangeLabel).toMutableList()
                    }
                    if (typeChangesInt.contains(1)) {
                        KeyValueRow(
                            label = stringResource(id = R.string.effective_start_date),
                            value = transaction.effectiveStartDate ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.effective_start_date_change),
                            value = transaction.effectiveStartDateChange ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.effective_end_date),
                            value = transaction.effectiveEndDate ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.effective_end_date_change),
                            value = transaction.effectiveEndDateChange ?: "",
                        )
                    }
                    if (typeChangesInt.contains(2)) {
                        if (typeChangesInt.size > 1) {
                            HorizontalDividerDetail()
                        }
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_bl),
                            value = transaction.amount ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_change),
                            value = transaction.amountChange ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_bl),
                            value = transaction.amount2 ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_change),
                            value = transaction.amountChange2 ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_bl),
                            value = transaction.amount3 ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.amount_commitment_change),
                            value = transaction.amountChange3 ?: "",
                        )
                    }
                    if (typeChangesInt.contains(3)) {
                        if (typeChangesInt.size > 1) {
                            HorizontalDividerDetail()
                        }
                        KeyValueRow(
                            label = stringResource(id = R.string.guarantee_commitment_content),
                            value = transaction.guaranteeCommitContent ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.guarantee_commitment_content_change),
                            value = transaction.guaranteeCommitContentChange ?: "",
                        )
                    }
                    KeyValueRow(
                        label = stringResource(id = R.string.vtb_reason_change),
                        value = transaction.reasonChange ?: "",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_other_information),
                initiallyCollapsed = true,
                content = {
                    val feeType = FeeType.fromTypeId(transaction.feeTime ?: "")
                    val feeTypeString = feeType.getFeeTypeLabel()
                    val messageType = MessageType
                        .fromTypeId(transaction.messageType ?: "")
                    if (feeType == FeeType.TYPE_1) {
                        KeyValueRow(label = stringResource(id = R.string.fee_type), value = feeTypeString)
                    } else if (feeType == FeeType.TYPE_2) {
                        KeyValueRow(label = stringResource(id = R.string.fee_type), value = feeTypeString)
                        KeyValueRow(label = stringResource(id = R.string.details), value = transaction.feeDesc ?: "")
                    }

                    val measures = Measure
                        .fromTypeId(transaction.measure ?: "")
                    val measureLabels = buildString {
                        for (i in measures.indices) {
                            if (i > 0) append(",\n")
                            append(measures[i].getMeasureLabel())
                        }
                    }
                    if (messageType == MessageType.TYPE_3) {
                        KeyValueRow(
                            label = stringResource(id = R.string.sdbl_message_type),
                            value = MessageType
                                .fromTypeId(transaction.messageType ?: "")
                                .getMessageTypeLabel(),
                        )
                        KeyValueRow(label = stringResource(id = R.string.vtb_bic_code), value = transaction.biCodeInfo?.bicCode ?: "")
                        KeyValueRow(label = stringResource(id = R.string.vtb_name), value = transaction.biCodeInfo?.bankName ?: "")
                        KeyValueRow(label = stringResource(id = R.string.vtb_address), value = transaction.biCodeInfo?.bankAddr ?: "")
                        KeyValueRow(
                            label = stringResource(id = R.string.measure_added),
                            value = measureLabels,
                        )
                    } else if (messageType == MessageType.TYPE_2) {
                        KeyValueRow(
                            label = stringResource(id = R.string.sdbl_message_type),
                            value = MessageType
                                .fromTypeId(transaction.messageType ?: "")
                                .getMessageTypeLabel(),
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.sdbl_send_type),
                            value = SendType.fromTypeId(transaction.sendType ?: "").getSendTypeLabel(),

                        )
                        KeyValueRow(
                            label = stringResource(
                                id = R.string.measure_added,
                            ),
                            value = measureLabels,
                        )
                    } else if (messageType == MessageType.TYPE_1) {
                        KeyValueRow(
                            label = stringResource(id = R.string.sdbl_message_type),
                            value = MessageType
                                .fromTypeId(transaction.messageType ?: "")
                                .getMessageTypeLabel(),
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.sdbl_send_type),
                            value = SendType.fromTypeId(transaction.sendType ?: "").getSendTypeLabel(),
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.nhct_commit_bl),
                            value = transaction.sendTypeCmnd ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.content_cccd_passport),
                            value = transaction.sendTypeNo ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.send_type_date),
                            value = transaction.sendTypeDate?.toFormattedDate() ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_checker_issue_by),
                            value = transaction.sendTypeBy ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.measure_added),
                            value = measureLabels,
                        )
                    }

                    if (measures.contains(Measure.TYPE_1)) {
                        KeyValueRow(
                            label = stringResource(id = R.string.measure_account),
                            value = transaction.measureAcct ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.measure_amount),
                            value = transaction.measureAmount ?: "",
                        )
                    }
                    if (measures.contains(Measure.TYPE_2)) {
                        KeyValueRow(
                            label = stringResource(id = R.string.measure_description),
                            value = transaction.measureDesc ?: "",
                        )
                    }
                },
            )
            HorizontalDividerDetail()
            Box(
                modifier = Modifier
                    .padding(
                        start = FoundationDesignSystem.Sizer.Padding.padding16,
                        end = FoundationDesignSystem.Sizer.Padding.padding16,
                        top = FoundationDesignSystem.Sizer.Padding.padding8,
                        bottom = FoundationDesignSystem.Sizer.Padding.padding16,
                    )
                    .safeClickable {
                        actions?.onAttachmentsClick?.invoke()
                    },
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(
                            id = R.string.feature_checker_attatchment_details_info,
                        ),
                        style = FoundationDesignSystem.Typography.interactionButton,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                    Icon(
                        modifier = Modifier
                            .size(FoundationDesignSystem.Sizer.Icon.icon24),
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                        contentDescription = "next",
                        tint = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                }
            }
        }
    }

    companion object {
        const val FADE_DURATION: Int = 300
        const val TYPE_GDN_GUARANTEE = "GDN"
    }
}

@Composable
fun HorizontalDividerDetail(
    color: androidx.compose.ui.graphics.Color = FoundationDesignSystem.Colors.paletteNeutral100,
    thickness: Dp = FoundationDesignSystem.Sizer.Gap.gap1,
    havePaddingBottom: Boolean = false,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = FoundationDesignSystem.Sizer.Padding.padding16,
                end = FoundationDesignSystem.Sizer.Padding.padding16,
                top = FoundationDesignSystem.Sizer.Padding.padding4,
                bottom = if (havePaddingBottom) FoundationDesignSystem.Sizer.Padding.padding16 else FoundationDesignSystem.Sizer.Padding.padding0,
            )
            .height(thickness)
            .background(color),
    )
}

@Composable
fun CollapsibleSection(
    title: String,
    initiallyCollapsed: Boolean = true,
    content: @Composable () -> Unit,
) {
    var isCollapse by remember { mutableStateOf(initiallyCollapsed) }

    Column {
        SectionHeader(
            title = title,
            hasMoreInfo = true,
            isCollapse = isCollapse,
            onInfoClick = {
                isCollapse = !isCollapse
            },
        )

        AnimatedVisibility(
            visible = !isCollapse,
            enter = expandVertically(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ) + fadeIn(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ),
            exit = shrinkVertically(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ) + fadeOut(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ),
        ) {
            TransactionDetailsCard {
                content()
            }
        }
    }
}

enum class FeeType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getFeeTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(id = R.string.fee_type_1)
            TYPE_2 -> stringResource(id = R.string.fee_type_2)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): FeeType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String): FeeType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: DEFAULT_TYPE
        }
    }
}

enum class MessageType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    TYPE_10(10),
    TYPE_11(typeId = 11),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getMessageTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.feature_checker_message_type_1)
            TYPE_2 -> stringResource(R.string.feature_checker_message_type_2)
            TYPE_3 -> stringResource(R.string.feature_checker_message_type_3)
            TYPE_10 -> stringResource(R.string.feature_checker_message_type_1)
            TYPE_11 -> stringResource(R.string.feature_checker_message_type_11)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): MessageType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String = "0"): MessageType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: DEFAULT_TYPE
        }
    }
}

enum class Measure(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getMeasureLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.measure_1)
            TYPE_2 -> stringResource(R.string.measure_2)
            TYPE_3 -> stringResource(R.string.measure_3)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): Measure {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String): List<Measure> {
            return typeId.split("-").map { it.toIntOrNull() ?: 0 }.map { fromTypeId(it) }
        }
    }
}

enum class SendType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getSendTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.send_type_1)
            TYPE_2 -> stringResource(R.string.send_type_2)
            TYPE_3 -> stringResource(R.string.send_type_3)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: String): SendType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: SendType.DEFAULT_TYPE
        }
    }
}

fun String.toFormattedDate(inputPattern: String = "yyyy-MM-dd HH:mm:ss"): String {
    return try {
        val inputFormat = SimpleDateFormat(inputPattern, Locale.getDefault())
        val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        val date = inputFormat.parse(this)
        date?.let { outputFormat.format(it) } ?: ""
    } catch (e: Exception) {
        this
    }
}
