package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import javax.inject.Inject

class GuaranteeTransactionRenderer @Inject constructor() : ITransactionContentRenderer {

    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: Boolean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier.padding(
                    bottom = FoundationDesignSystem.Sizer.Padding.padding8,
                ),
                verticalAlignment = Alignment.Bottom,
            ) {
                FoundationText(
                    text = stringResource(id = com.vietinbank.core_ui.R.string.account_transaction_no),
                    color = FoundationDesignSystem.Colors.characterSecondary,
                    style = FoundationDesignSystem.Typography.captionL,
                )
                FoundationText(
                    text = transaction.mtId ?: "",
                    modifier = Modifier.padding(start = 4.dp),
                    color = FoundationDesignSystem.Colors.characterPrimary,
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
                Icon(
                    painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_copy),
                    contentDescription = "Copy icon",
                    tint = AppColors.characterHighlighted,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .safeClickable {
                            // TODO: copy transaction id
                        },
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_switch_vertical_24),
                        contentDescription = "Switch icon vertically",
                        tint = AppColors.characterPrimary,
                        modifier = Modifier
                            .padding(end = 4.dp)
                            .safeClickable {
                                // TODO: switch vertical action
                            },
                    )

                    FoundationText(
                        text = transaction.tranTypeName?.trim() ?: "",
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        style = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                }

                val formattedAmount = remember(transaction.amount, transaction.currency) {
                    try {
                        val cleanAmount = transaction.amount?.getAmountServer()
                        Utils.g().getDotMoneyHasCcy(cleanAmount ?: "", transaction.currency ?: "")
                    } catch (_: Exception) {
                        "${transaction.amount} ${transaction.currency}"
                    }
                }
                FoundationText(
                    text = formattedAmount,
                    modifier = Modifier.align(Alignment.Top),
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.feature_checker_guaruatee_receiver),
                value = transaction.beneficiaryName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.feature_checker_documentType),
                value = DocumentType.fromTypeId(transaction.documentType ?: "")
                    .getDocumentTypeLabel(),
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.feature_checker_creator),
                value = transaction.creator ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.feature_checker_creation_time),
                value = transaction.createdDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                valueColor = transaction.status?.getColorStatus(),
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GuaranteeTransactionRendererPreview() {
    // Mock transaction data
    val mockTransaction = TransactionListDomain()
    mockTransaction.mtId = "************"
    mockTransaction.tranTypeName = "Giải toả bảo lãnh"

    // Mock renderer instance
    val mockRenderer = object : ITransactionContentRenderer {
        @Composable
        override fun RenderContent(
            transaction: TransactionListDomain,
            isSelectionMode: Boolean,
            isSelected: Boolean,
            onChangeClick: () -> Unit,
        ) {
            Column(modifier = Modifier.fillMaxWidth()) {
                Row(
                    modifier = Modifier.padding(
                        bottom = FoundationDesignSystem.Sizer.Padding.padding8,
                    ),
                    verticalAlignment = Alignment.Bottom,
                ) {
                    FoundationText(
                        text = stringResource(id = com.vietinbank.core_ui.R.string.account_transaction_no),
                        color = FoundationDesignSystem.Colors.characterSecondary,
                        style = FoundationDesignSystem.Typography.captionL,
                    )
                    FoundationText(
                        text = transaction.mtId ?: "",
                        modifier = Modifier.padding(start = 4.dp),
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        style = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_copy),
                        contentDescription = "Copy icon",
                        tint = AppColors.characterHighlighted,
                        modifier = Modifier
                            .padding(start = 4.dp)
                            .safeClickable {
                                // TODO: copy transaction id
                            },
                    )
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start,
                    ) {
                        Icon(
                            painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_switch_vertical_24),
                            contentDescription = "Switch icon vertically",
                            tint = AppColors.characterPrimary,
                            modifier = Modifier
                                .padding(end = 4.dp)
                                .safeClickable {
                                    // TODO: switch vertical action
                                },
                        )

                        FoundationText(
                            text = transaction.tranTypeName?.trim() ?: "",
                            color = FoundationDesignSystem.Colors.characterPrimary,
                            style = FoundationDesignSystem.Typography.captionLSemibold,
                        )
                    }

                    val formattedAmount = remember(transaction.amount, transaction.currency) {
                        try {
                            val cleanAmount = transaction.amount?.getAmountServer()
                            Utils.g().getDotMoneyHasCcy(cleanAmount ?: "", transaction.currency ?: "")
                        } catch (_: Exception) {
                            "${transaction.amount} ${transaction.currency}"
                        }
                    }
                    FoundationText(
                        text = formattedAmount,
                        modifier = Modifier.align(Alignment.Top),
                        style = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.feature_checker_guaruatee_receiver),
                    modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                    value = transaction.beneficiaryName ?: "",
                    titleStyle = FoundationDesignSystem.Typography.captionL,
                    valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                )

                println("Loai sua doi: ${transaction.typeChange}")
                val typeChangesInt = TypeChange.getTypeChangeList(transaction.typeChange ?: "")
                if (typeChangesInt.isEmpty()) {
                    FoundationInfoHorizontal(
                        title = stringResource(R.string.feature_checker_documentType),
                        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                        value = DocumentType.fromTypeId(transaction.documentType ?: "")
                            .getDocumentTypeLabel(),
                        titleStyle = FoundationDesignSystem.Typography.captionL,
                        valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                } else {
                    val typesChangeLabel = ""
                    typeChangesInt.forEachIndexed { index, typeChangeInt ->
                        val typeChange = TypeChange.fromTypeId(typeChangeInt)
                        if (typeChange != null) {
                            typesChangeLabel.plus(typeChange.getTypeChangeLabel())
                            if (index < typeChangesInt.size - 1) {
                                typesChangeLabel.plus("\n")
                            }
                        }
                    }
                    FoundationInfoHorizontal(
                        title = stringResource(R.string.feature_checker_type_changes),
                        modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                        value = typesChangeLabel,
                        titleStyle = FoundationDesignSystem.Typography.captionL,
                        valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.feature_checker_creator),
                    modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                    value = transaction.creator ?: "",
                    titleStyle = FoundationDesignSystem.Typography.captionL,
                    valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.feature_checker_creation_time),
                    modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                    value = transaction.createdDate ?: "",
                    titleStyle = FoundationDesignSystem.Typography.captionL,
                    valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                )

                FoundationInfoHorizontal(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_status),
                    modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                    value = transaction.statusName ?: "",
                    titleStyle = FoundationDesignSystem.Typography.captionL,
                    valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                    valueColor = transaction.status?.getColorStatus(),
                )
            }
        }
    }

    // Apply your app's theme
    MaterialTheme {
        Surface(
            modifier = Modifier.padding(16.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            mockRenderer.RenderContent(
                transaction = mockTransaction,
                isSelectionMode = false,
                isSelected = false,
                onChangeClick = { /* Preview - no action */ },
            )
        }
    }
}

enum class DocumentType(val typeId: String) {
    TYPE_1("1"),
    TYPE_2("2"),
    TYPE_3("3"),
    TYPE_4("4"),
    TYPE_5("5"),
    TYPE_6("6"),
    TYPE_7("7"),
    TYPE_8("8"),
    DEFAULT_TYPE("0"),
    ;

    @Composable
    fun getDocumentTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(id = R.string.feature_checker_document_type_1)
            TYPE_2 -> stringResource(id = R.string.feature_checker_document_type_2)
            TYPE_3 -> stringResource(id = R.string.feature_checker_document_type_3)
            TYPE_4 -> stringResource(id = R.string.feature_checker_document_type_4)
            TYPE_5 -> stringResource(id = R.string.feature_checker_document_type_5)
            TYPE_6 -> stringResource(id = R.string.feature_checker_document_type_6)
            TYPE_7 -> stringResource(id = R.string.feature_checker_document_type_7)
            TYPE_8 -> stringResource(id = R.string.feature_checker_document_type_8)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: String): DocumentType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }
    }
}

enum class TypeChange(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    ;

    @Composable
    fun getTypeChangeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.feature_checker_type_change_1)
            TYPE_2 -> stringResource(R.string.feature_checker_type_change_2)
            TYPE_3 -> stringResource(R.string.feature_checker_type_change_3)
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): TypeChange? {
            return TypeChange.values().find { it.typeId == typeId }
        }

        fun getTypeChangeList(typeChange: String): List<Int> {
            return try {
                if (typeChange.contains("-")) {
                    typeChange.split("-").map { it.toIntOrNull() ?: throw IllegalArgumentException("Invalid number: $it") }
                } else {
                    listOf(typeChange.toIntOrNull() ?: throw IllegalArgumentException("Invalid number: $typeChange"))
                }
            } catch (e: IllegalArgumentException) {
                emptyList<Int>()
            }
        }
    }
}