package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.getDayAgo
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.processor.TransactionProcessorFactory
import com.vietinbank.core_domain.models.checker.CurrencyEntity
import com.vietinbank.core_domain.models.checker.FilterApproveDomain
import com.vietinbank.core_domain.models.checker.FilterApproveState
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.PreRejectParams
import com.vietinbank.core_domain.models.checker.PreTransactionEntity
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_domain.models.checker.TransactionDetailItem
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.manage.DECREASES
import com.vietinbank.core_domain.models.manage.FIELD_AMOUNT
import com.vietinbank.core_domain.models.manage.FIELD_TIME
import com.vietinbank.core_domain.models.manage.INCREASES
import com.vietinbank.core_domain.models.manage.TSortDomain
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.components.Status
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.sheet.TransactionType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.math.BigDecimal
import javax.inject.Inject

@HiltViewModel
class ApprovalListViewModel
@Inject constructor(
    val useCase: CheckerUserCase,
    val homeUseCase: HomeUseCase,
    val factory: TransactionRendererFactory,
    val processorFactory: TransactionProcessorFactory,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    var originalTransactions: List<TransactionListDomain> = emptyList()
        private set

    // Cờ chỉ ra rằng cần scroll lên đầu danh sách sau khi làm mới
    var needScrollToTop: Boolean = false

    // UI State để hiển thị - sử dụng StateFlow
    private val _uiState = MutableStateFlow<TransactionListUIState>(TransactionListUIState.Loading)
    val uiState: StateFlow<TransactionListUIState> = _uiState.asStateFlow()

    var currentGroupType: String? = null
    var currentTransType: String? = null
    var currentServiceType: String? = null

    // Selection mode state
    private val _isSelectionMode = MutableStateFlow(false)
    val isSelectionMode = _isSelectionMode.asStateFlow()
    private val _isShowApprove = MutableStateFlow(true)
    val isShowApprove = _isShowApprove.asStateFlow()
    fun updateApproveButton() {
        // button "Phê duyệt" khi chọn giao dich
        _isShowApprove.value = _selectedTransactions.value.size <= 1 ||
            (currentServiceType != "DO" && currentServiceType != "GO")
    }

    // Selected transactions
    private val _selectedSubTransactions = MutableStateFlow<Set<String>>(emptySet())
    val selectedSubTransactions = _selectedSubTransactions.asStateFlow()
    private val _selectedTransactions = MutableStateFlow<Set<String>>(emptySet())
    val selectedTransactions = _selectedTransactions.asStateFlow()

    // Total transactions count
    private val _totalTransactionsCount = MutableStateFlow(0)
    val totalTransactionsCount = _totalTransactionsCount.asStateFlow()

    private val _preApprove = MutableSharedFlow<PreApproveDomain?>()
    val preApprove = _preApprove.asSharedFlow()

    private val _preApproveSingle = MutableSharedFlow<PreApproveDomain?>()
    val preApproveSingle = _preApproveSingle.asSharedFlow()

    private val _errorEvent = MutableSharedFlow<String?>()
    val errorEvent = _errorEvent.asSharedFlow()

    // State quản lý phan trnang
    private val _currentPage = MutableStateFlow(0) // Lưu trang hiện tại
    private val _isLoadingMore = MutableStateFlow(false) // Đang tải thêm?
    val isLoadingMore = _isLoadingMore.asStateFlow()
    private val _canLoadMore = MutableStateFlow(true) // Còn data để tải?
    val canLoadMore = _canLoadMore.asStateFlow()
    private val _isLoadMoreRequest = MutableStateFlow(false) // Đánh dấu request load more

    // co danh dau chon so giao dich toi da cho 1 lan duyet
    private val _isGlobalMaxMode = MutableStateFlow(false)
    val isGlobalMaxMode = _isGlobalMaxMode.asStateFlow()

    // Transaction type selection state (from dialog)
    private val _selectedTransactionItem = MutableStateFlow<SubTranTypeListDomain?>(null)
    val selectedTransactionItem = _selectedTransactionItem.asStateFlow()

    // Chip filter selection state (from chip clicks)
    private val _selectedChipCode = MutableStateFlow<String?>(null)
    val selectedChipCode = _selectedChipCode.asStateFlow()

    // Count transaction list from API
    private val _countTransactionList = MutableStateFlow<List<SubTranTypeListDomain>>(emptyList())
    val countTransactionList = _countTransactionList.asStateFlow()

    // Computed state: should show chip filter when serviceType = "CT" (Chuyển tiền)
//    val shouldShowChipFilter: StateFlow<Boolean> = combine(
//        _selectedTransactionItem,
//        _selectedChipCode,
//    ) { selectedItem, chipCode ->
//        when (selectedItem?.servicetype) {
//            // Show chips if serviceType is CT and no chip selected yet
//            // Keep showing if a valid chip is selected within CT service
//            Tags.TYPE_GROUP_TRANSFER, Tags.TYPE_GROUP_TRANSFER_CTTF300 -> true
//            // Otherwise hide chips
//            else -> false
//        }
//    }.stateIn(
//        scope = viewModelScope,
//        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
//        initialValue = false,
//    )

//    // Computed state: chip filter options - KEEP ORIGINAL NAMES
//    val chipFilterOptions: StateFlow<List<Pair<String, String?>>> = combine(
//        _selectedTransactionItem) { selectedItem, isShowChip ->
//        if (isShowChip) {
//            val lst = factory.getRenderSubType(selectedItem?.servicetype)
//            _selectedChipCode.value = lst.firstOrNull()?.second
//            lst
//        } else {
//            emptyList()
//        }
//    }.stateIn(
//        scope = viewModelScope,
//        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
//        initialValue = emptyList(),
//    )

    // Computed state: transaction types for dialog - KEEP ORIGINAL LOGIC
    val transactionTypesForDialog: StateFlow<List<TransactionType>> =
        _countTransactionList.map { list ->
            list.map { item ->
                val displayName = when {
                    !item.servicetypename.isNullOrEmpty() -> item.servicetypename!!
                    !item.tranType.isNullOrEmpty() -> "Type: ${item.tranType}"
                    !item.functionId.isNullOrEmpty() -> "Function: ${item.functionId}"
                    else -> "Unknown Type"
                }

//                val count = item.count_transaction?.toIntOrNull() ?: 0
//                val nameWithCount = if (count > 0) {
//                    "$displayName ($count)"
//                } else {
//                    displayName
//                }

                TransactionType(
                    id = item.functionId ?: item.tranType ?: "unknown",
                    code = item.tranType ?: "",
                    name = displayName,
                    icon = com.vietinbank.core_ui.R.drawable.ic_common_transfer_32, // dang để icon mặc định
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList(),
        )

    // Role: expose isChecker from user profile so UI can render by role
    val isChecker: StateFlow<Boolean> = userProf.userProfFlow
        .map { it.roleId == Tags.CHECKER }
        .stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = userProf.isChecker(),
        )

    // Current selected transaction type code (for compatibility)
//    val selectedTransactionTypeCode: StateFlow<String?> = combine(
//        _selectedTransactionItem,
//        _selectedChipCode,
//    ) { item, chipCode ->
//        chipCode // ?: (item?.functionId ?: item?.tranType)
//    }.stateIn(
//        scope = viewModelScope,
//        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
//        initialValue = null,
//    )

    // Header title based on selected transaction item
    val headerTitle: StateFlow<String> = _selectedTransactionItem.map { item ->
        when {
            item?.servicetypename?.isNotEmpty() == true -> item.servicetypename!!
            else -> "Giao dịch" // Default title when nothing selected
        }
    }.stateIn(
        scope = viewModelScope,
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
        initialValue = "Giao dịch",
    )

    //    1. Initial Load (getTransactionList):
//    - Load page 0, size 15
//    - Set _canLoadMore.value = (data.size >= 15)
//    2. Load More:
//    - Increment page number
//    - Append new data
//    - Update canLoadMore based on response size
//    3. canLoadMore Logic:
//    - true: Response có >= 15 items (full page)
//    - false: Response < 15 items hoặc empty
    // State để theo dõi lỗi khi loadMore
    private val _loadMoreError = MutableSharedFlow<String>()
    val loadMoreError = _loadMoreError.asSharedFlow()

    fun retryLoadMore() {
        if (_isLoadingMore.value) return
        loadMoreTransactions()
    }

    /**
     * Update count transaction list from API
     */
    fun updateCountTransactionList(list: List<SubTranTypeListDomain>) {
        _countTransactionList.value = list
        // Initialize with first item if available
        if (_selectedTransactionItem.value == null && list.isNotEmpty()) {
            _selectedTransactionItem.value = list.first()
        }
    }

    /**
     * Handle transaction type selection from dialog
     */
    fun selectTransactionType(functionId: String?) {
        val selectedItem = _countTransactionList.value.find { item ->
            item.functionId == functionId
        }
        currentTransType = selectedItem?.tranType.orEmpty()
        currentGroupType = selectedItem?.groupType.orEmpty()
        currentServiceType = selectedItem?.servicetype.orEmpty()
        if (_selectedTransactionItem.value?.functionId != functionId) {
            // lay group da chon
            _selectedTransactionItem.value = selectedItem
            // mac dinh chon the dau tien cua subtrantype neu co
            if (!selectedItem?.subTranTypeList.isNullOrEmpty()) {
                // chỉ update tranType
                val subTranTypeDomain = selectedItem.subTranTypeList?.firstOrNull()
                _selectedChipCode.value = subTranTypeDomain?.tranType
                currentTransType = subTranTypeDomain?.tranType
            }
            resetFilterWithNewService(_currentTabState.value)
        }
    }

    /**
     * Handle chip filter selection
     */
    fun selectChipFilter(chipCode: String?) {
        _selectedChipCode.value = chipCode
        // Keep the selected transaction item but use chip code for filtering
    }

    fun getSelectedTranTypeFromList(): String {
        return if (_selectedTransactions.value.isNotEmpty()) {
            getTransactionById(
                _selectedTransactions.value.firstOrNull() ?: "",
            )?.tranType ?: ""
        } else {
            ""
        }
    }

    fun getSelectedGroupTypeFromList(): String {
        return if (_selectedTransactions.value.isNotEmpty()) {
            getTransactionById(
                _selectedTransactions.value.firstOrNull() ?: "",
            )?.groupType ?: ""
        } else {
            ""
        }
    }

    fun getSelectedServiceTypeFromList(): String {
        return if (_selectedTransactions.value.isNotEmpty()) {
            getTransactionById(
                _selectedTransactions.value.firstOrNull() ?: "",
            )?.serviceType ?: ""
        } else {
            ""
        }
    }

    fun updateTotalTransactionsCount(count: Int) {
        if (count > _totalTransactionsCount.value) {
            _totalTransactionsCount.value = count
        }
    }

    fun toggleSelectionMode() {
        val newMode = !_isSelectionMode.value
        _isSelectionMode.value = newMode
        // Clear selections when exiting selection mode
        if (!newMode) {
            deselectAllTransactions()
        }
    }

    fun deselectAllTransactions() {
        // bo check trang thai phe duyet max giao dich
        _isGlobalMaxMode.value = false
        _selectedTransactions.value = emptySet()
        _selectedSubTransactions.value = emptySet()
        // button "Phê duyệt" khi chọn giao dich
        updateApproveButton()
    }

    private val _transactionSheet = MutableStateFlow<TransactionSheetUi?>(null)
    val transactionSheet = _transactionSheet.asStateFlow()

    /** Mở sheet Transaction*/
    fun showSheetFor(transaction: TransactionListDomain) {
        if (transaction.tranType == Tags.TYPE_DISBURSEMENT_ONLINE.lowercase()) {
            _transactionSheet.value = DisbursementTransactionUiState(
                transaction,
            )
        } else {
            _transactionSheet.value = TransactionSheetUiModel(
                tranTypeName = transaction.tranTypeName ?: "",
                tranType = transaction.tranType ?: "",
                mtID = transaction.mtId ?: "",
                amount = transaction.amount ?: "",
                ccy = transaction.currency ?: "",
                details = listOf(),
                status = transaction.status ?: "",
                toAccountNo = transaction.toAccountNo ?: "",
                receiveName = transaction.receiveName ?: "",
                receiveBankName = transaction.receiveBankName ?: "",
                imageBank = "",
                statusCode = mapUiStatusToFoundationStatus(
                    mapRawToUiStatus(
                        transaction.status ?: "",
                    ),
                ),
                sheetDownloads = buildDownloads(mapRawToUiStatus(transaction.status ?: "")),
                sheetButtons = buildFooterButtons(mapRawToUiStatus(transaction.status ?: "")),
            )
        }
    }

    /** Đóng sheet */
    fun dismissSheet() {
        _transactionSheet.value = null
    }

    /**
     * Toggle a transaction selection
     */
    fun toggleTransactionSelection(transactionId: String) {
        val currentSelection = _selectedTransactions.value.toMutableSet()
        val currentSubSelection = _selectedSubTransactions.value.toMutableSet()
        val subTransactionLst =
            originalTransactions.firstOrNull { it.mtId == transactionId }?.mtIdList ?: emptyList()
        if (currentSelection.contains(transactionId)) {
            // Deselect transaction
            currentSelection.remove(transactionId)
            currentSubSelection.removeAll(subTransactionLst)
        } else {
            // Select transaction
            currentSelection.add(transactionId)
            currentSubSelection.addAll(subTransactionLst)
        }
        // Update states
        // khi update 1 item -> se khong còn trạng thai chọn max giao dịch phe duyet nua
        _isGlobalMaxMode.value = false
        _selectedTransactions.value = currentSelection
        _selectedSubTransactions.value = currentSubSelection
        updateApproveButton()
    }

    /**
     * Get selected transaction IDs as ArrayList for API calls
     * Nếu ở chế độ chọn tất cả, trả về mtIds từ API
     * Nếu không, trả về danh sách ID đã chọn từ UI
     */
    fun getSelectedTransactionsAsList(): ArrayList<String> = if (isBatchFile()) {
        ArrayList(_selectedSubTransactions.value)
    } else {
        ArrayList(_selectedTransactions.value)
    }

    /**
     * Helper method để lấy transaction theo ID
     */
    private fun getTransactionById(id: String): TransactionListDomain? =
        originalTransactions.find { it.mtId == id }

    fun preApprove() {
        launchJob(showLoading = true) {
            val transactionSelected = _selectedTransactions.value.firstOrNull()?.let { trans ->
                getTransactionById(trans)
            }
            val res = useCase.preApprove(
                PreApproveParams(
                    userName = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = "",
                    serviceType = transactionSelected?.serviceType ?: "",
                    tranType = transactionSelected?.tranType ?: "",
                    transactions = getSelectedTransactionsAsList(),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = true
                if (isBatchFile() || _selectedTransactions.value.size > 1) {
                    _preApprove.emit(data)
                } else {
                    data.transactionProcessor = transactionSelected?.let { transaction ->
                        processorFactory.getProcessor(transaction.tranType ?: "")
                            .processTransaction(transaction)
                    } ?: TransactionDomain()

                    // trương hop la dien tra soat
                    data.transactionProcessor?.apply {
                        if (Tags.TYPE_GROUP_LOOKUP.equals(tranType, true)) {
                            pref = transactionSelected?.pref?.let { pref ->
                                processorFactory.getProcessor(pref.tranType ?: "")
                                    .processTransaction(pref)
                            }
                        }
                    }
                    _preApproveSingle.emit(data)
                }
            }
        }
    }

    fun preReject() {
        launchJob(showLoading = true) {
            val transactionSelected = _selectedTransactions.value.firstOrNull()?.let { trans ->
                getTransactionById(trans)
            }
            val res = useCase.preReject(
                PreRejectParams(
                    username = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = "",
                    serviceType = transactionSelected?.serviceType ?: "",
                    tranType = transactionSelected?.tranType ?: "",
                    transactions = getSelectedTransactionsAsList(),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = false
                if (isBatchFile() || _selectedTransactions.value.size > 1) {
                    _preApprove.emit(data)
                } else {
                    data.transactionProcessor = transactionSelected?.let { transaction ->
                        processorFactory.getProcessor(transaction.tranType ?: "")
                            .processTransaction(transaction)
                    } ?: TransactionDomain()

                    // trương hop la dien tra soat
                    data.transactionProcessor?.apply {
                        if (Tags.TYPE_GROUP_LOOKUP.equals(tranType, true)) {
                            pref = transactionSelected?.pref?.let { pref ->
                                processorFactory.getProcessor(pref.tranType ?: "")
                                    .processTransaction(pref)
                            }
                        }
                    }
                    _preApproveSingle.emit(data)
                }
            }
        }
    }

    fun getOriginalTransactionByMTId(mtID: String): TransactionListDomain? =
        originalTransactions.find {
            it.mtId == mtID
        }

    /**
     * Xử lý danh sách giao dịch nhận được từ HomeFragment
     */
    fun processTransactions(
        transactions: List<TransactionListDomain>,
        isLoadMore: Boolean = false,
        countTransaction: String? = null,
    ) {
        // Nếu là loadMore, thì giữ lại originalTransactions cũ và thêm vào
        // Nếu không phải loadMore, thì thay thế hoàn toàn
        originalTransactions = if (isLoadMore) {
            val current = originalTransactions.toMutableList()
            current.addAll(transactions)
            current
        } else {
            transactions
        }
        try {
//            if (transactions.isEmpty()) {
//                _uiState.value = TransactionListUIState.Success(emptyList())
//                return
//            }
            // Set the correct total count from API response (only on initial load)
            if (!isLoadMore && countTransaction != null) {
                val totalCount = countTransaction.toIntOrNull() ?: 0
                _totalTransactionsCount.value = totalCount
                printLog("ApprovalListViewModel: Setting totalTransactionsCount from API = $totalCount (countTransaction = $countTransaction)")
            } else if (!isLoadMore && transactions.isNotEmpty()) {
                // Fallback to list size only if countTransaction not provided (for backward compatibility)
                printLog("ApprovalListViewModel: WARNING - countTransaction is null, falling back to list size = ${transactions.size}")
                updateTotalTransactionsCount(transactions.size)
            } else if (isLoadMore) {
                printLog("ApprovalListViewModel: LoadMore - keeping existing totalTransactionsCount = ${_totalTransactionsCount.value}")
            }

            // Cập nhật UI state
            _uiState.value = TransactionListUIState.Success(transactions)
//            printLog("Đã xử lý ${transactionItems.size} giao dịch")
        } catch (e: Exception) {
            printLog("Lỗi xử lý giao dịch: ${e.message}")
            _uiState.value = TransactionListUIState.Error("Không thể xử lý dữ liệu giao dịch")
        }
    }

    /**
     * Hiển thị lỗi
     */
    fun showError(message: String) {
        viewModelScope.launch {
            _errorEvent.emit(message)
        }
        _uiState.value = TransactionListUIState.Error(message)
    }

    private val _refreshTransactionList = MutableSharedFlow<GetTransactionListDomain>()
    val refreshTransactionList = _refreshTransactionList.asSharedFlow()

    // Store current queryType for use in loadMore
    private var currentQueryType: String = Tags.QUERY_TYPE_PENDING

    /**
     * Get transaction list with full params from transaction type selection
     */
    fun getTransactionListWithParams(
        tranType: String?,
        groupType: String?,
        serviceType: String?,
    ) {
        // Update current types
        currentTransType = tranType
        currentGroupType = groupType
        currentServiceType = serviceType

        // Reset pagination
        _currentPage.value = 0
        _canLoadMore.value = true
        _isLoadMoreRequest.value = false
        // Switch UI to Loading to avoid showing stale data/counts while fetching
        _uiState.value = TransactionListUIState.Loading

        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    queryType = currentQueryType,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = groupType,
                    serviceType = serviceType,
                    tranType = tranType.orEmpty(),
                    pageNum = Tags.CHECKER_INITIAL_PAGE_STR,
                    pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                    orderByAmount = Tags.SORT_NONE,
                    orderByApproveDate = Tags.SORT_DESCENDING,
                ),
            )
            handleResource(res) { data ->
                printLog("transaction with params: ${data.transactions?.size}")
                // Check if can load more
                _canLoadMore.value = (data.transactions?.size ?: 0) >= Tags.CHECKER_PAGE_SIZE
                processTransactions(
                    transactions = data.transactions ?: listOf(),
                    countTransaction = data.countTransaction,
                )
            }
        }
    }

    // chi goi khi thay doi tab or thay doi loai dien or thay doi giao dich con
    fun getTransactionList() {
        // Reset về trang đầu tiên khi lấy danh sách mới
        _currentPage.value = 0
        _canLoadMore.value = true
        _isLoadMoreRequest.value = false
        val filterApply = _filterTransaction.value.originFilter

        // Use queryType from filter if available, otherwise use default
        currentQueryType = filterApply?.currentQueryType ?: Tags.QUERY_TYPE_PENDING
        // Switch UI to Loading to avoid showing stale data/counts while fetching
        _uiState.value = TransactionListUIState.Loading
        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    queryType = currentQueryType,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = currentGroupType,
                    serviceType = currentServiceType,
                    tranType = getTranType(filterApply?.tranType, currentServiceType),
                    pageNum = Tags.CHECKER_INITIAL_PAGE_STR,
                    pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                    orderByAmount = when {
                        filterApply?.sortField?.orderBy == FIELD_AMOUNT && filterApply.sortField?.orderDirect == INCREASES -> Tags.SORT_ASCENDING
                        filterApply?.sortField?.orderBy == FIELD_AMOUNT -> Tags.SORT_DESCENDING
                        else -> Tags.SORT_NONE
                    },
                    // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                    orderByApproveDate = when {
                        filterApply?.sortField?.orderBy == FIELD_TIME && filterApply.sortField?.orderDirect == INCREASES -> Tags.SORT_ASCENDING
                        filterApply?.sortField?.orderBy == FIELD_TIME -> Tags.SORT_DESCENDING
                        else -> Tags.SORT_NONE
                    },
                    // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                    fromDate = filterApply?.startDate ?: "",
                    toDate = filterApply?.endDate ?: "",
                    minAmount = filterApply?.startAmount ?: "",
                    maxAmount = filterApply?.endAmount ?: "",
                    isSchedule = filterApply?.dateTransfer ?: "",
                    tranSubType = filterApply?.tranSubType ?: "",
                ),
            )
            handleResource(res) { data ->
                printLog("transaction: ${data.transactions?.size}")
                // Kiểm tra xem có thể tải thêm không
                _canLoadMore.value = (data.transactions?.size ?: 0) >= Tags.CHECKER_PAGE_SIZE
                processTransactions(
                    transactions = data.transactions ?: listOf(),
                    countTransaction = data.countTransaction,
                )
            }
        }
    }

    private fun getTranType(tranType: String?, serviceType: String?): String {
        return if (serviceType != "BTX") tranType.orEmpty() else ""
    }

    private fun getAllMTID() {
        val filterApply = _filterTransaction.value.originFilter
        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    all = "Y",
                    queryType = currentQueryType,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = currentGroupType,
                    serviceType = currentServiceType,
                    tranType = getTranType(
                        filterApply?.tranType ?: currentTransType,
                        currentServiceType,
                    ),
                    pageNum = Tags.CHECKER_INITIAL_PAGE_STR,
                    orderByApproveDate = Tags.SORT_DESCENDING,
                ),
            )
            handleResource(res) { data ->
                // Lấy danh sách mtIds từ API
                val mtIds = data.mtIds?.toSet() ?: emptySet()
                // Lấy tổng giá trị từ API nếu có
                val totalAmountFromAPI = data.totalAmount
                // chon max so giao dich
                _isGlobalMaxMode.value = true
                // phần này trả về => có thể mang đi phê duyệt luôn => khong can xu ly gi ca
                _selectedTransactions.value = mtIds
                _selectedSubTransactions.value = data.subMtIds?.toSet() ?: emptySet()
                updateApproveButton()
                // Note: Don't update total count here - it should only be set from initial API response
                // The mtIds.size is just the selected transactions, not the total count
            }
        }
    }

    fun refreshTransactionList(groupType: String, serviceType: String, tranType: String) {
        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    queryType = currentQueryType,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = groupType,
                    serviceType = serviceType,
                    tranType = getTranType(tranType, serviceType),
                    pageNum = Tags.CHECKER_INITIAL_PAGE_STR,
                    pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                    orderByAmount = Tags.SORT_NONE,
                    orderByApproveDate = Tags.SORT_DESCENDING,
                ),
            )
            handleResource(res) { data ->
                _refreshTransactionList.emit(data)
            }
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        when (exception) {
            is AppException.ApiException -> {
                if (_isLoadMoreRequest.value && exception.requestPath == Constants.MB_GET_TRANSACTION_LIST) {
                    // Lỗi khi loadMore - không hiển thị dialog lỗi mà chỉ hiển thị lỗi ở cuối danh sách
                    viewModelScope.launch {
                        _loadMoreError.emit(exception.message ?: "Không thể tải thêm dữ liệu")
                    }
                    // Vẫn cho phép loadMore để thử lại
                    _canLoadMore.value = true
                    _isLoadingMore.value = false
                } else {
                    super.onDisplayErrorMessage(exception)
                }
            }

            else -> super.onDisplayErrorMessage(exception)
        }
    }

    fun loadMoreTransactions() {
        // Kiểm tra nếu đang tải hoặc không thể tải thêm thì không làm gì
        if (_isLoadingMore.value || !_canLoadMore.value) return

        _isLoadingMore.value = true
        _isLoadMoreRequest.value = true
        val nextPage = _currentPage.value + 1
        val filterApply = _filterTransaction.value.originFilter
        launchJob(showLoading = false) {
            try {
                val currentIds = originalTransactions.map { it.mtId }
                printLog("DEBUG: Before loadMore - Page: ${_currentPage.value}, Items count: ${currentIds.size}")

                val res = homeUseCase.getTranList(
                    GetTransactionListParams(
                        queryType = currentQueryType,
                        cifNo = userProf.getCifNo().toString(),
                        username = userProf.getUserName().toString(),
                        groupType = currentGroupType,
                        serviceType = currentServiceType,
                        tranType = getTranType(
                            filterApply?.tranType ?: currentTransType,
                            currentServiceType,
                        ),
                        pageNum = nextPage.toString(), // Trang tiếp theo
                        pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                        orderByAmount = when {
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT && filterApply.sortField?.orderDirect == INCREASES -> Tags.SORT_ASCENDING
                            filterApply?.sortField?.orderBy == FIELD_AMOUNT -> Tags.SORT_DESCENDING
                            else -> Tags.SORT_NONE
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        orderByApproveDate = when {
                            filterApply?.sortField?.orderBy == FIELD_TIME && filterApply.sortField?.orderDirect == INCREASES -> Tags.SORT_ASCENDING
                            filterApply?.sortField?.orderBy == FIELD_TIME -> Tags.SORT_DESCENDING
                            else -> Tags.SORT_NONE
                        },
                        // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                        fromDate = filterApply?.startDate ?: "",
                        toDate = filterApply?.endDate ?: "",
                        minAmount = filterApply?.startAmount ?: "",
                        maxAmount = filterApply?.endAmount ?: "",
                        isSchedule = filterApply?.dateTransfer ?: "",
                    ),
                )

                handleResource(res) { data ->
                    val newTransactions = data.transactions ?: emptyList()
                    printLog("Tải thêm: ${newTransactions.size} giao dịch trang $nextPage")

                    // Kiểm tra xem có dữ liệu mới hay không
                    if (newTransactions.isNotEmpty()) {
                        // Cập nhật trang hiện tại
                        _currentPage.value = nextPage

                        // Lọc ra các transaction không trùng mtId
                        val existingMtIds = originalTransactions.map { it.mtId }.toSet()
                        val uniqueNewTransactions = newTransactions.filter { newTrans ->
                            !existingMtIds.contains(newTrans.mtId)
                        }

                        printLog("Filtered duplicates: ${newTransactions.size - uniqueNewTransactions.size} duplicate(s) removed")

                        // Nối dữ liệu mới (đã lọc duplicate) vào dữ liệu hiện có
                        val updatedList = originalTransactions.toMutableList().apply {
                            addAll(uniqueNewTransactions)
                        }

                        // Cập nhật danh sách gốc và xử lý lại UI
                        processTransactions(updatedList)

                        // Kiểm tra xem có thể tải thêm không
                        _canLoadMore.value = newTransactions.size >= Tags.CHECKER_PAGE_SIZE
                    } else {
                        // Không còn dữ liệu để tải
                        _canLoadMore.value = false
                    }
                }
            } finally {
                _isLoadingMore.value = false
                _isLoadMoreRequest.value = false
            }
        }
    }

    fun buildConfirmItems(dataPre: PreApproveDomain): List<ConfirmItem> {
        val tranType = getSelectedTranTypeFromList()
        val amountTotalDisplay = StringBuilder()
        val amountTotalDisplayWords = StringBuilder()
        val totalFeeDisplay = StringBuilder()
        var selectedAmount: HashMap<String?, MutableList<CurrencyEntity>> = HashMap()
        // tinh tong so tien trong truong hop co nhieu loai tien trong 1 dien
        try {
            // currency1
            mappingCurrency(dataPre.transactionPres, 1)?.let {
                selectedAmount = it
            }

            // currency2
            mappingCurrency(dataPre.transactionPres, 2)?.forEach { (ccy, lstCcy) ->
                selectedAmount.getOrPut(ccy) { mutableListOf() }.addAll(lstCcy)
            }

            // currency3
            mappingCurrency(dataPre.transactionPres, 3)?.forEach { (ccy, lstCcy) ->
                selectedAmount.getOrPut(ccy) { mutableListOf() }.addAll(lstCcy)
            }
        } catch (_: Exception) {
        }
        selectedAmount.forEach { (currency, lstTransaction) ->
            if (!currency.isNullOrEmpty()) {
                val totalAmountCcy =
                    lstTransaction.sumOf { it.amount?.toBigDecimalOrNull() ?: BigDecimal(0) }
                amountTotalDisplay.append(
                    "\n${
                        Utils.g().getDotMoneyHasCcy(totalAmountCcy.toString(), currency)
                    }",
                )
                amountTotalDisplayWords.append(
                    "\n${
                        moneyHelper.convertAmountToWords(
                            totalAmountCcy.toString(),
                            currency,
                        )
                    }",
                )
            }
        }

        val lstConfirm = mutableListOf<ConfirmItem>()
        lstConfirm.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = resourceProvider.getString(R.string.transaction_info),
            ),
        )
        if ("fx" == tranType || "fxr" == tranType) {
            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_info_endow),
                    value = dataPre.countTransactionFXR.toString(),
                ),
            )
            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_info_total),
                    value = "${dataPre.totalAmountFXR?.entries?.firstOrNull()?.value ?: BigDecimal.ZERO} ${dataPre.totalAmountFXR?.entries?.firstOrNull()?.key.orEmpty()}",
                ),
            )
            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_info_agree),
                    value = dataPre.countTransactionFX.toString(),
                ),
            )
            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_info_total),
                    value = "${dataPre.totalAmountFX?.entries?.firstOrNull()?.value ?: BigDecimal.ZERO} ${dataPre.totalAmountFX?.entries?.firstOrNull()?.key.orEmpty()}",
                ),
            )
        } else {
            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.total_transactions),
                    value = (
                        dataPre.transactionPres?.size
                            ?: ""
                        ).toString(), // totalTransactionSelected,
                ),
            )

            lstConfirm.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = when (tranType) {
                        "do" -> "Tổng số tiền vay"
                        else -> resourceProvider.getString(R.string.total_amount)
                    },
                    value = amountTotalDisplay.toString().replaceFirst("\n", ""),
                    subValue = amountTotalDisplayWords.toString().replaceFirst("\n", ""),
                    isHighlighted = isHighlightAmount(tranType), // với trường hợp bảo lãnh-> khong highlight tổng số tiền tổng
                ),
            )
            when (tranType) {
                "do" -> {
                }

                "slo", "tx", "if", "btx" -> {
                    mappingCurrency(dataPre.transactionPres, 0)?.let {
                        it.keys.forEach { ccy ->
                            val totalFee =
                                it[ccy]?.sumOf {
                                    it.amount?.toBigDecimalOrNull() ?: BigDecimal(0)
                                }
                            totalFeeDisplay.append(
                                Utils.g().getDotMoneyHasCcy(totalFee.toString(), ccy ?: ""),
                            )
                        }
                    }
                }

                "go" -> totalFeeDisplay.append("Sẽ được NHCT tính toán và thu phí")
                "gor", "goc" -> totalFeeDisplay.append("Theo biểu phí của NHCT")
                else -> totalFeeDisplay.append("Phí Giao dịch được tính bởi ngân hàng khi xử lý giao dịch")
            }
            if (totalFeeDisplay.toString().isNotEmpty()) {
                lstConfirm.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = resourceProvider.getString(R.string.total_fee),
                        value = totalFeeDisplay.toString(),
                    ),
                )
            }
        }
        return lstConfirm
    }

    // tinh tong so tien trong truong hop co nhieu loai tien trong 1 dien
    private fun mappingCurrency(
        lstTransPre: ArrayList<PreTransactionEntity>?,
        ccyIndex: Int,
    ): HashMap<String?, MutableList<CurrencyEntity>>? {
        if (lstTransPre.isNullOrEmpty()) return null
        return lstTransPre.mapNotNull { item ->
            val (amount, currency) = when (ccyIndex) {
                0 -> item.feeAmount to item.currency // phi giao dich
                1 -> item.amount to item.currency // so tien
                2 -> item.amount2 to item.currency2 // so tien 2
                3 -> item.amount3 to item.currency3 // so tien 3
                else -> null to null
            }
            if (amount.isNullOrEmpty() || currency.isNullOrEmpty()) return@mapNotNull null
            CurrencyEntity(amount, currency)
        }.sortedBy { it.currency }
            .groupBy { it.currency } as HashMap<String?, MutableList<CurrencyEntity>>
    }

    private fun isHighlightAmount(tranType: String?) = when (tranType) {
        Tags.TYPE_GROUP_FILENSNN, Tags.TYPE_GROUP_GUARANTEE,
        Tags.TYPE_GROUP_GUARANTEE_AMEND, Tags.TYPE_GROUP_GUARANTEE_ISSUE,
        -> false

        else -> true
    }

    // phe duyet toi da
    fun toggleMaxSelectAll() {
        // neu selected = total && dang load toan bo item -> bỏ chon tat ca
        if (_isGlobalMaxMode.value) {
            // Nếu đang ở chế độ chọn tất cả -> hủy chọn tất cả
            deselectAllTransactions()
        } else {
            // Nếu chưa chọn tất cả, gọi API để lấy tổng số và chọn tất cả
            getAllMTID()
        }
    }

    fun validateTotalTransactions(): String? = when {
        _selectedTransactions.value.isEmpty() -> {
            "Vui lòng chọn ít nhất một giao dịch để duyệt"
        }
        // duyet file 5000
        Tags.TYPE_GROUP_TRANSFER_CTTF5000 == currentServiceType -> {
            val totalSubItem = originalTransactions.filter {
                it.mtId in _selectedTransactions.value
            }.sumOf { it.totalTrxNo?.toIntOrNull() ?: 0 }
            if (_selectedTransactions.value.size > Tags.LIMIT_FILE_TRANSACTION) {
                "Vui lòng chỉ chọn tối đa ${Tags.LIMIT_FILE_TRANSACTION} file/lần"
            } else if (totalSubItem > Tags.LIMIT_TRANSACTION) {
                "Vui lòng chỉ chọn tối đa ${Tags.LIMIT_TRANSACTION} giao dịch/lần"
            } else {
                null
            }
        }
        // dien duyet theo file -> co giao dich con
        isBatchFile() && _selectedTransactions.value.size > Tags.LIMIT_FILE_TRANSACTION -> {
            "Vui lòng chỉ chọn tối đa ${Tags.LIMIT_FILE_TRANSACTION} file/lần"
        }

        isBatchFile() && _selectedSubTransactions.value.size > Tags.LIMIT_TRANSACTION -> {
            "Vui lòng chỉ chọn tối đa ${Tags.LIMIT_TRANSACTION} giao dịch/lần"
        }

        _selectedTransactions.value.size > Tags.LIMIT_TRANSACTION -> {
            "Vui lòng chỉ chọn tối đa ${Tags.LIMIT_TRANSACTION} giao dịch/lần"
        }

        else -> null
    }

    private fun isBatchFile() = Tags.TYPE_GROUP_TRANSFER_CTTF300 == currentServiceType ||
        Tags.TYPE_GROUP_FILENSNN == currentServiceType

    companion object {
        // input
        const val INPUT_START_AMOUNT = "INPUT_START_AMOUNT"
        const val INPUT_END_AMOUNT = "INPUT_END_AMOUNT"
        const val INPUT_ACCOUNT_NO = "INPUT_ACCOUNT_NO"
        const val INPUT_ACCOUNT_NAME = "INPUT_ACCOUNT_NAME"

        // seletor
        const val SELECTOR_DATE_TRANSFER = "SELECTOR_DATE_TRANSFER"
        const val SELECTOR_DATE_RANGE = "SELECTOR_DATE_RANGE"
    }

    val sortLstLock = listOf(
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_time_decreases),
            FIELD_TIME,
            DECREASES,
        ),
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_time_increases),
            FIELD_TIME,
            INCREASES,
        ),
    )

    // option transaction
    val sortLst = listOf(
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_time_decreases),
            FIELD_TIME,
            DECREASES,
        ),
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_time_increases),
            FIELD_TIME,
            INCREASES,
        ),
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_amount_increases),
            FIELD_AMOUNT,
            INCREASES,
        ),
        TSortDomain(
            resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_sort_amount_decreases),
            FIELD_AMOUNT,
            DECREASES,
        ),
    )
    private val _filterTransaction = MutableStateFlow(FilterApproveState())
    val filterTransaction = _filterTransaction.asStateFlow()

    // init trasaction lst
    fun initTransaction(bundle: Bundle?) {
        if (!isKeepCallInit()) {
            currentTransType = bundle?.getString(Tags.TRAN_TYPE_BUNDLE)
            currentGroupType = bundle?.getString(Tags.GROUP_TYPE_BUNDLE)
            currentServiceType = bundle?.getString(Tags.SERVICE_TYPE_BUNDLE)
            bundle?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
                try {
                    // Parse JSON to transaction list
                    val type = object : TypeToken<List<TransactionListDomain>>() {}.type
                    val transactions: List<TransactionListDomain> =
                        Utils.g().provideGson().fromJson(jsonData, type)
                    // Get the total count from bundle (now contains full countTransactionList as JSON)
                    val countTransaction = bundle.getString(Tags.COUNT_TRANSACTION_BUNDLE)
                    // Parse countTransactionList from JSON
                    countTransaction?.let { countJson ->
                        try {
                            val countType =
                                object : TypeToken<List<SubTranTypeListDomain>>() {}.type
                            val countTransactionList: List<SubTranTypeListDomain> =
                                Utils.g().provideGson().fromJson(countJson, countType)
                            printLog("Parsed countTransactionList with ${countTransactionList.size} items")
                            // Update ViewModel with countTransactionList
                            _countTransactionList.value = countTransactionList
                            // Initialize with first item if available
                            if (_selectedTransactionItem.value == null && countTransactionList.isNotEmpty()) {
                                // lay subTranTypeList đầu tiên
                                countTransactionList.firstOrNull { selectedItem ->
                                    selectedItem.servicetype == currentServiceType
                                }?.let {
                                    _selectedTransactionItem.value = it
                                    _selectedChipCode.value = it.subTranTypeList
                                        ?.firstOrNull()
                                        ?.takeIf { _ -> it.servicetype != Tags.TYPE_DISBURSEMENT_ONLINE }
                                        ?.tranType

                                    // chi update lại trantype
                                    currentTransType =
                                        it.subTranTypeList?.firstOrNull()?.tranType
//                                    currentGroupType = it.groupType
//                                    currentServiceType = it.servicetype
                                }
                            }
                        } catch (e: Exception) {
                            printLog("Error parsing countTransactionList: ${e.message}")
                        }
                    }

                    // Process transactions in ViewModel
                    processTransactions(transactions)
                    printLog("Received ${transactions.size} transactions from bundle")

                    printLog(
                        "First transaction: ${
                            Utils.g().provideGson().toJson(transactions.firstOrNull())
                        }",
                    )
                } catch (e: Exception) {
                    printLog("Error parsing transaction data: ${e.message}")
                    showError("Cannot process transaction data")
                }
            } ?: run {
                printLog("No transaction data in bundle")
                showError("No transaction data found")
            }
            // chi khoi tao 1 lan => khi back ve khong khoi tao lai nua
            resetFilterWithNewService(currentTab = 0, isUpdateList = false)
            setKeepCallInit()
        }
    }

    // an hien filter sheet
    fun onShowFilterSheet(isShow: Boolean) {
        _filterTransaction.value = _filterTransaction.value.copy(isShowFilter = isShow)
    }

    // an hien picker sheet
    fun onShowPickerSheet(isShow: Boolean) {
        _filterTransaction.value = _filterTransaction.value.copy(isShowPicker = isShow)
    }

    // an hien sort sheet
    fun onShowSortSheet(isShow: Boolean) {
        _filterTransaction.value = _filterTransaction.value.copy(isShowSort = isShow)
    }

    // thay doi truong sap xep theo
    fun onChangeSortField(selector: TSortDomain) {
        _filterTransaction.value.changedFilter?.copy(sortField = selector)
            ?.let {
                _filterTransaction.value = _filterTransaction.value.copy(
                    changedFilter = it,
                    isShowSort = false,
                )
            }
    }

    // thay doi ngay
    fun onChangeDate(startDate: String?, endDate: String?) {
        _filterTransaction.value.changedFilter?.copy(
            startDate = startDate,
            endDate = endDate,
            dateRange = null,
        )?.let { _filterTransaction.value = _filterTransaction.value.copy(changedFilter = it) }
    }

    // reset filter
    fun onResetFilter() {
        _filterTransaction.value.initFilter?.copy()?.let {
            _filterTransaction.value = _filterTransaction.value.copy(changedFilter = it)
        }
    }

    // ap dung filter transaction
    fun onApplyFilter() {
        _filterTransaction.value.changedFilter?.copy()?.let {
            _filterTransaction.value =
                _filterTransaction.value.copy(originFilter = it, isShowFilter = false, isFilterAdvance = true)
        }
        // call api update
        getTransactionList()
    }

    // Handle tab selection (status filter)
    private val _currentTabState = MutableStateFlow(0)
    fun onTabSelected(tabIndex: Int) {
        _currentTabState.value = tabIndex
        // chip đang chọn
        currentTransType = _selectedChipCode.value
        resetFilterWithNewService(tabIndex)
    }

    // reset trangj thái filter khi thay đổi tab, chip, subList
    fun resetFilterWithNewService(currentTab: Int, isUpdateList: Boolean = true) {
        val (queryType, dateRange) = when (currentTab) {
            1 -> Tags.QUERY_TYPE_APPROVED to Tags.DAY_30.toString() // Đã duyệt tab
            2 -> Tags.QUERY_TYPE_REJECTED to Tags.DAY_30.toString() // Đã từ chối tab
            else -> Tags.QUERY_TYPE_PENDING to null // Chờ duyệt tab (default)
        }

        // Update filter with new queryType
        // update range date filter da duyet + da tu choi => 30 day else null
        var initFilter = FilterApproveDomain(
            tranType = currentTransType,
            groupType = currentGroupType,
            serviceType = currentServiceType,
            dateTransfer = "",
            dateRange = dateRange,
            startDate = if (dateRange.isNullOrEmpty()) {
                null
            } else {
                getDayAgo(dayAgo = (dateRange.toIntOrNull() ?: 0) - 1)
            },
            endDate = if (dateRange.isNullOrEmpty()) {
                null
            } else {
                todayAsString()
            },
            sortField = sortLst[0],
            currentQueryType = queryType, // Default pending
        )
        _filterTransaction.value = _filterTransaction.value.copy(
            initFilter = initFilter,
            originFilter = initFilter,
            changedFilter = initFilter,
            isFilterAdvance = false,
        )
        // Refresh list with new queryType
        if (isUpdateList) { getTransactionList() }
    }

    // Handle chip filter selection (transaction type)
    fun onChipFilterSelected(tranType: String?) {
        // Update filter with new tranType
        with(_filterTransaction.value) {
            originFilter?.let { filter ->
                val updated = if (filter.serviceType == Tags.TYPE_DISBURSEMENT_ONLINE) {
                    filter.copy(
                        tranType = Tags.TYPE_DISBURSEMENT_ONLINE,
                        tranSubType = tranType,
                    )
                } else {
                    filter.copy(tranType = tranType)
                }
                _filterTransaction.value = copy(originFilter = updated)
            }
        }

        // Refresh list with new filter
        getTransactionList()
    }

    // ap dung filter transaction
    fun onChangeChipFilter(type: String, value: String) {
        val filterCopy = _filterTransaction.value.changedFilter?.copy()?.apply {
            when (type) {
                SELECTOR_DATE_TRANSFER -> {
                    dateTransfer = value
                }

                SELECTOR_DATE_RANGE -> {
                    dateRange = value
                    startDate = try {
                        getDayAgo(dayAgo = value.toInt() - 1)
                    } catch (_: Exception) {
                        todayAsString()
                    }
                    endDate = todayAsString()
                }

                else -> {
                }
            }
        }
        _filterTransaction.value = _filterTransaction.value.copy(changedFilter = filterCopy)
    }

    // thay doi key in
    fun onChangeInputFilter(inputType: String, inputValue: String) {
        _filterTransaction.value.changedFilter?.copy()?.apply {
            when (inputType) {
                INPUT_START_AMOUNT -> startAmount = inputValue
                INPUT_END_AMOUNT -> endAmount = inputValue
                else -> {}
            }
        }?.let {
            _filterTransaction.value = _filterTransaction.value.copy(changedFilter = it)
        }
    }

    fun isLockTransactionType(tranType: String?): Boolean {
        return when (tranType?.uppercase()) {
            Tags.TYPE_GROUP_UNLOCK_USER, Tags.TYPE_GROUP_RESET_PASSWORD -> true
            else -> false
        }
    }
}

sealed class TransactionListUIState {
    object Loading : TransactionListUIState()

    data class Success(
        val transactions: List<TransactionListDomain>,
    ) : TransactionListUIState()

    data class Error(
        val message: String,
    ) : TransactionListUIState()
}

// ====== Mappers private ======
private fun mapRawToUiStatus(raw: String): TxUiStatus = when (raw.uppercase()) {
    Tags.STATUS_SUCCESS -> TxUiStatus.SUCCESS
    Tags.STATUS_USER_REJECT -> TxUiStatus.USER_REJECT
    Tags.STATUS_AWAITING_APPROVAL -> TxUiStatus.PENDING
    else -> TxUiStatus.OTHER
}

private fun mapUiStatusToFoundationStatus(s: TxUiStatus): Status = when (s) {
    TxUiStatus.SUCCESS -> Status.Success
    TxUiStatus.USER_REJECT -> Status.Fail
    else -> Status.Pending
}

private fun buildDownloads(s: TxUiStatus): List<DownloadItemType> =
    if (s == TxUiStatus.SUCCESS) {
        listOf(DownloadItemType.SIGNATURE, DownloadItemType.SIGNATURE_NO, DownloadItemType.BILL)
    } else {
        listOf(DownloadItemType.SIGNATURE_NO)
    }

private fun buildFooterButtons(s: TxUiStatus): List<FooterButtonType> = when (s) {
    TxUiStatus.SUCCESS, TxUiStatus.USER_REJECT -> listOf(
        FooterButtonType.BACK,
        FooterButtonType.DETAIL,
    )

    TxUiStatus.PENDING -> listOf(FooterButtonType.CANCEL, FooterButtonType.APPROVE)
    else -> emptyList()
}

// ===== UI enums / status =====
enum class TxUiStatus { SUCCESS, USER_REJECT, PENDING, OTHER }
enum class DownloadItemType { SIGNATURE, SIGNATURE_NO, BILL }
enum class FooterButtonType { BACK, DETAIL, CANCEL, APPROVE }

sealed interface TransactionSheetUi

data class TransactionSheetUiModel(
    val tranTypeName: String,
    val tranType: String,
    val mtID: String,
    val amount: String,
    val ccy: String,
    val details: List<TransactionDetailItem>,
    val status: String,
    val toAccountNo: String,
    val receiveName: String,
    val receiveBankName: String,
    val imageBank: String,
    val statusCode: Status = Status.Pending,
    val sheetDownloads: List<DownloadItemType> = emptyList(),
    val sheetButtons: List<FooterButtonType> = emptyList(),
) : TransactionSheetUi

data class DisbursementTransactionUiState(
    val transaction: TransactionListDomain,
) : TransactionSheetUi

sealed class FilterApprovalAction {
    data class TapShowFilter(val isShow: Boolean) : FilterApprovalAction()
    data class TapShowPicker(val isShow: Boolean) : FilterApprovalAction()
    data class TapShowSort(val isShow: Boolean) : FilterApprovalAction()
    data class TapSelectSort(val sortOption: TSortDomain) : FilterApprovalAction()
    data object TapReset : FilterApprovalAction()
    data object TapApply : FilterApprovalAction()
    data class TapChip(val chipType: String, val chipValue: String) : FilterApprovalAction()
    data class TapInput(val inputType: String, val inputValue: String) : FilterApprovalAction()
}
