package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_ui.R
import javax.inject.Inject

/**
 * Created by vandz on 9/4/25.
 */

class PaymentConfirmUIGenerator @Inject constructor(
    val resourceProvider: IResourceProvider,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Thêm thông tin tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: "VND"}",
                subValue = transaction.fromAccountName, // Add account name as subValue
                stableKey = ConfirmFieldKey.FROM_ACCOUNT,
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Chuyển tới",
                value = transaction.toAccountNo?.toString() ?: "",
                subValue = transaction.receiveName,
                stableKey = ConfirmFieldKey.TO_ACCOUNT,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.receiveBankName ?: "",
                stableKey = ConfirmFieldKey.BANK_NAME,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
                stableKey = ConfirmFieldKey.AMOUNT,
            ),
        )

        // // bat dau tu day
        // so giao dich
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(R.string.transaction_label_transaction_id),
                value = transaction.mtId ?: "",
                stableKey = ConfirmFieldKey.MTID,
            ),
        )
        // tai khoan thu phi

        when {
            !transaction.fromAccountFeeNo.isNullOrEmpty() && !transaction.fromAccountFeeName.isNullOrEmpty() -> {
                "${transaction.fromAccountFeeNo} - ${transaction.fromAccountFeeName}"
            }

            !transaction.fromAccountFeeNo.isNullOrEmpty() -> {
                transaction.fromAccountFeeNo
            }

            else -> null
        }?.let { displayAccount ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.maker_transfer_dashboard_main_account_type_D),
                    value = displayAccount,
                    stableKey = ConfirmFieldKey.FEE_ACCOUNT_NO,
                ),
            )
        }

        // chi nhanh xu ly
        when {
            !transaction.branchNo.isNullOrEmpty() && !transaction.branchName.isNullOrEmpty() -> {
                "${transaction.branchNo} - ${transaction.branchName}"
            }

            else -> null
        }?.let { displayBank ->
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.maker_payment_order_dashboard_processing_branch),
                    value = displayBank,
                    stableKey = ConfirmFieldKey.BRANCH_PROCESSING,
                ),
            )
        }

        // noi dung
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(R.string.maker_transfer_dashboard_content),
                value = transaction.remark ?: "",
                stableKey = ConfirmFieldKey.CONTENT,
            ),
        )

        // phi giao dich
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(R.string.maker_transfer_dashboard_fee),
                value = transaction.feePayMethodDesc1 ?: "",
                stableKey = ConfirmFieldKey.FEE,
            ),
        )

        // hinh thuc thu phi
        val feeMethodDisplay = if (transaction.feePayMethod == "0") {
            resourceProvider.getString(R.string.maker_transfer_dashboard_type_OUR)
        } else {
            resourceProvider.getString(R.string.maker_transfer_dashboard_type_BEN)
        }
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(R.string.transaction_label_fee_method),
                value = feeMethodDisplay,
                stableKey = ConfirmFieldKey.FEE_METHOD,
            ),
        )

        // hinh thuc chuyen
        if (transaction.process_time.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_label_transfer_type),
                    value = resourceProvider.getString(R.string.transaction_transfer_immediate),
                    stableKey = ConfirmFieldKey.TRANSFER_METHOD,
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.transaction_label_transfer_type),
                    value = resourceProvider.getString(R.string.transaction_transfer_scheduled),
                    stableKey = ConfirmFieldKey.TRANSFER_METHOD,
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.maker_transfer_dashboard_transfer_date_schedule),
                    value = transaction.process_time ?: "",
                    stableKey = ConfirmFieldKey.SCHEDULE_TIME,
                ),
            )
        }

        // file đính kèm
        if (!transaction.fileName.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = resourceProvider.getString(R.string.maker_payment_order_dashboard_upload_file_name_title),
                    value = transaction.fileName ?: "",
//                    isHighlighted = true,
                    stableKey = ConfirmFieldKey.ATTACHMENT_FILE,
                ),
            )
        }

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}