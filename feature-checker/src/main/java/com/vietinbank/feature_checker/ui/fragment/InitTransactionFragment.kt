package com.vietinbank.feature_checker.ui.fragment

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.InitTransactionActions
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.screen.InitTransactionScreen
import com.vietinbank.feature_checker.ui.sheet.FileSheet
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class InitTransactionFragment : BaseFragment<InitTransactionViewModel>() {
    override val viewModel: InitTransactionViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    override lateinit var appNavigator: IAppNavigator

    private var fileSheet: FileSheet? = null

    @Composable
    override fun ComposeScreen() {
        AppTheme {
            val actions = InitTransactionActions(
                onBackClick = { appNavigator.popBackStack() },
                onHomeClick = { appNavigator.goToHome() },
                onApproveClick = {
                    viewModel.preApprove()
                },
                onRejectClick = {
                    viewModel.preReject()
                },
                onBatchApproveClick = {
                    viewModel.preApproveBatch()
                },
                onBatchRejectClick = {
                    viewModel.preRejectBatch()
                },
                onErrorClick = {
                    showNoticeDialog(it)
                },
                onDetailFileClick = { type ->
                    viewModel.getListFile(type)?.let { rule ->
                        fileSheet = FileSheet(rule).apply {
                            setOnFileClick { file ->
                                viewModel.getDownloadFileID(file)
                            }
                        }
                        fileSheet?.show(childFragmentManager, FileSheet::class.java.name)
                    }
                },
            )
            InitTransactionScreen(
                viewModel = viewModel,
                actions = actions,
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // Lấy dữ liệu từ Bundle arguments
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                val transaction: TransactionListDomain =
                    Utils.g().provideGson().fromJson(jsonData, TransactionListDomain::class.java)

                viewModel.processTransaction(transaction)
                printLog("Đã nhận giao dịch: ${transaction.mtId}")
            } catch (e: Exception) {
                printLog("Lỗi parse JSON: ${e.message}")
                e.printStackTrace()
            }
        } ?: run {
            // Không có dữ liệu trong arguments
            printLog("Không có dữ liệu transaction trong bundle")
        }

        // Khởi tạo observers
        initObservers()
    }

    private fun initObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        commonErrorEvent.collect { exception ->
                            exception?.let {
                                val errorMessage = it.message ?: "Lỗi xử lý phê duyệt"
                                showNoticeDialog(
                                    message = errorMessage,
                                    positiveAction = {
                                        appNavigator.popBackStack()
                                    },
                                )
                            }
                        }
                    }

                    launch {
                        filePermissionEvent.collect { message ->
                            showNoticeDialog(message)
                        }
                    }

                    launch {
                        preApprove.collect { resource ->
                            resource?.let { data ->
                                viewModel.getTransaction().let { transaction ->
                                    // Check if this was a batch approval with selections
                                    val selectedTransactions = viewModel.getBatchFileSelected()
                                    val isBatchOperation =
                                        (transaction.tranType == "ba" && selectedTransactions.isNotEmpty()) || (transaction.tranType == "btx" && selectedTransactions.isNotEmpty())
                                    // Handle batch approval with selections isBatchOperation = true
                                    // Handle regular approval isBatchOperation = false
                                    handleTransactionResult(
                                        success = data,
                                        confirmType = if (data.isApprove) {
                                            Tags.APPROVE
                                        } else {
                                            Tags.REJECT
                                        },
                                        transaction = transaction,
                                        isBatchOperation = isBatchOperation,
                                    )
                                }
                            }
                        }
                    }

                    launch {
                        getDownloadFileID.collect { resource ->
                            if (resource != null && resource is Resource.Success) {
                                // Lấy downloadFileId từ kết quả
                                val downloadFileId = resource.data.downloadFileId
                                if (downloadFileId.isNullOrEmpty()) {
                                    showNoticeDialog("Không tìm thấy file đính kèm hoặc file không khả dụng")
                                }
                            } else if (resource is Resource.Error) {
                                showNoticeDialog(resource.message ?: "Không thể tải file đính kèm")
                            }
                        }
                    }

                    launch {
                        viewModel.fileUrlReady.collect { url ->
                            try {
                                // Mở URL trong trình duyệt
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                                startActivity(intent)
                            } catch (e: Exception) {
                                showNoticeDialog("Không thể mở file: ${e.message}")
                            }
                        }
                    }

                    launch {
                        openFileIntent.collect { intent ->
                            startActivity(intent)
                        }
                    }

                    launch {
                        errorMessage.collect { message ->
                            showNoticeDialog(message)
                        }
                    }
                    launch {
                        errorEvent.collect { message ->
                            message?.let { showNoticeDialog(it) }
                        }
                    }
                }
            }
        }
    }

    /**
     * Xử lý kết quả từ API preApprove hoặc preReject và điều hướng đến màn hình xác nhận
     * @param success Resource chứa kết quả từ API
     * @param confirmType Loại xác nhận (APPROVE hoặc REJECT)
     * @param transaction Đối tượng giao dịch gốc
     * @param isBatchOperation Cờ đánh dấu giao dịch xử lý theo lô (phê duyệt hoặc từ chối)
     */
    private fun handleTransactionResult(
        success: PreApproveDomain,
        confirmType: String,
        transaction: TransactionDomain,
        isBatchOperation: Boolean = false,
    ) {
        // Lấy thông tin phương thức thanh toán
        val paymentMethod = confirmLauncher.getPaymentMethod(success)

        // Tạo bản sao của transaction để cập nhật
        val updatedTransaction = transaction.copy()

        // Cập nhật amount từ kết quả API
        if (isBatchOperation) {
            // Lấy tổng tiền từ kết quả API
            // Tính tổng amount từ các transaction được chọn trong kết quả API
            val totalAmount =
                success.transactionPres?.sumOf {
                    it.amount?.toDoubleOrNull() ?: 0.0
                }?.toString() ?: "0"
            printLog("Sum amount from API result: $totalAmount")
            updatedTransaction.amount = totalAmount
            // For batch operations, we need to pass the selected transaction IDs
            val selectedTransactions = viewModel.getBatchFileSelected()
            // Điều hướng đến màn hình xác nhận cho batch transactions
            confirmLauncher.launchWithBatchTransactions(
                transaction = updatedTransaction,
                confirmType = confirmType,
                paymentMethod = paymentMethod,
                selectedMtIds = ArrayList(selectedTransactions),
            )
        } else {
            // Điều hướng đến màn hình xác nhận cho single transaction
            confirmLauncher.launchWithSingleTransaction(
                transaction = updatedTransaction,
                confirmType = confirmType,
                paymentMethod = paymentMethod,
            )
        }
    }
}