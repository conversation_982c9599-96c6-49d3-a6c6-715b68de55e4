package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.component.approvallist.DocumentType
import com.vietinbank.feature_checker.ui.component.inits.GuaranteeAmendDetailRenderer.Companion.TYPE_GDN_GUARANTEE
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

/**
 * Render phat hành bảo lanh
 * */
class GuaranteeIssueDetailRenderer @Inject constructor(
    private val resourceProvider: IResourceProvider,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        Column(
            modifier = Modifier.padding(
                horizontal = FoundationDesignSystem.Sizer.Padding.padding8,
            ),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Padding.padding24))
            FoundationStatus(
                modifier = Modifier.padding(
                    horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
                statusMessage = transaction.statusName,
                statusCode = Status.Pending,
            )
            HorizontalDivider(
                Modifier.padding(
                    FoundationDesignSystem.Sizer.Padding.padding16,
                ),
            )
            SectionHeader(title = stringResource(id = R.string.feature_checker_guarantee_request))
            TransactionDetailsCard {
                transaction.listFilesGO?.filter {
                    !it.attachmentType.isNullOrEmpty() && it.attachmentType == TYPE_GDN_GUARANTEE
                }?.forEach { file ->
                    KeyValueRow(
                        label = when (file.attachmentType) {
                            TYPE_GDN_GUARANTEE -> stringResource(R.string.sdbl_online_paper)
                            else -> ""
                        },
                        value = file.fileName ?: "",
                        isHyperlink = true,
                        onClick = {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                    )
                }
            }
            HorizontalDividerDetail(havePaddingBottom = true)
            SectionHeader(title = stringResource(id = R.string.feature_checker_general_information))
            TransactionDetailsCard {
                KeyValueRow(
                    label = stringResource(id = R.string.feature_checker_transaction_no),
                    value = transaction.mtId ?: "",
                )
                KeyValueRow(
                    label = stringResource(id = R.string.vtb_bl_type),
                    value = transaction.documentTypeName ?: DocumentType.fromTypeId(transaction.documentType ?: "")
                        .getDocumentTypeLabel(),
                )
            }
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_customer),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_customer_name), value = transaction.cifName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_business_registration_number), value = transaction.guaranteeCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_date_range), value = transaction.dateRange?.toFormattedDate() ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_issue_by), value = transaction.issuesBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_head_address), value = transaction.headQuarters ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_representative_name),
                        value = transaction.representativeName ?: "",
                    )
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_authority), value = transaction.authority ?: "")
                },
            )
            HorizontalDividerDetail(havePaddingBottom = true)
            SectionHeader(title = stringResource(id = R.string.feature_checker_info_contract))
            TransactionDetailsCard {
                KeyValueRow(label = stringResource(id = R.string.vtb_mbnt_contract_number), value = transaction.contractNo ?: "")
                KeyValueRow(label = stringResource(id = R.string.vtb_contract_date), value = transaction.contractDate?.toFormattedDate() ?: "")
            }
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_propose),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.feature_form_ckbl), value = transaction.documentTypeName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_issuing_bank), value = "${transaction.branch} - ${transaction.branchName}")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_info_propose),
                        value = transaction.guaranteePurpose ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_type_change_only),
                        value = "${transaction.effectiveStartDate ?: ""} - ${transaction.effectiveEndDate ?: ""}",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_receiver),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_receiver_info), value = transaction.beneficiaryName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_head_transaction), value = transaction.beneficiaryAddress ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_register_business), value = transaction.beneficiaryCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_issue_by), value = transaction.receiverIssuedBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_checker_date_range), value = transaction.receiverDateRange?.toFormattedDate() ?: "")
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_checker_info_guarantee),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_how_to_ph),
                        value = MessageType.fromTypeId(transaction.messageType ?: "").getMessageTypeLabel(),
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_type_ph),
                        value = transaction.issueTypeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_lang_used),
                        value = transaction.language ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_condition),
                        value = transaction.conditionPerform ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.sdbl_send_type),
                        value = transaction.sendTypeDesc ?: "",
                    )
                    if (transaction.messageType == "1" || transaction.messageType == "01") {
                        KeyValueRow(
                            label = stringResource(id = R.string.nhct_commit_bl),
                            value = transaction.sendTypeCmnd ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.content_cccd_passport),
                            value = transaction.sendTypeNo ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_checker_date_range),
                            value = transaction.sendTypeDate?.toFormattedDate() ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_checker_issue_by),
                            value = transaction.sendTypeBy ?: "",
                        )
                    }
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_time_to_fee),
                        value = transaction.feeTimeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.details),
                        value = transaction.feeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_fee_bl),
                        value = transaction.feeGuarantee ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.details),
                        value = transaction.feeGuaranteeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_checker_way_to_ensure),
                        value = Measure.fromTypeId(transaction.measure ?: "").firstOrNull()?.getMeasureLabel() ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.measure_account),
                        value = transaction.measureAcct ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.measure_amount),
                        value = transaction.measureAmount ?: "",
                    )

                    val measure = Measure.fromTypeId(transaction.measure ?: "").firstOrNull()
                    if (measure == Measure.TYPE_3) {
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_checker_detail_way_to_eusure),
                            value = transaction.measureDesc ?: "",
                        )
                    }
                    KeyValueRow(
                        label = stringResource(id = R.string.content_customer_ensure),
                        value = transaction.commitContent ?: "",
                    )
                    if (transaction.messageType == "3") {
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_bic_code),
                            value = transaction.biCodeInfo?.bicCode ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_name),
                            value = transaction.biCodeInfo?.bankName ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_address),
                            value = transaction.biCodeInfo?.bankAddr ?: "",
                        )
                    }
                },
            )
            HorizontalDividerDetail()
            Box(
                modifier = Modifier
                    .padding(
                        start = FoundationDesignSystem.Sizer.Padding.padding16,
                        end = FoundationDesignSystem.Sizer.Padding.padding16,
                        top = FoundationDesignSystem.Sizer.Padding.padding8,
                        bottom = FoundationDesignSystem.Sizer.Padding.padding16,
                    )
                    .safeClickable {
                        actions?.onAttachmentsClick?.invoke()
                    },
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(
                            id = R.string.feature_checker_attatchment_details_info,
                        ),
                        style = FoundationDesignSystem.Typography.interactionButton,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                    Icon(
                        modifier = Modifier
                            .size(FoundationDesignSystem.Sizer.Icon.icon24),
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                        contentDescription = "next",
                        tint = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                }
            }
        }
    }
}
