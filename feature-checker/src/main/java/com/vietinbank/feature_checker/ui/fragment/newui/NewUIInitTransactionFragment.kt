package com.vietinbank.feature_checker.ui.fragment.newui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.isInstalledApp
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.MethodEntityDomain
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.MultipleConfirmUIModel
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_domain.smartca.ISignResult
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.AuthenticationMethodDialog
import com.vietinbank.core_ui.base.dialog.AuthenticationMethodResult
import com.vietinbank.core_ui.base.dialog.KeypassAuthDialog
import com.vietinbank.core_ui.components.dialog.DialogType
import com.vietinbank.core_ui.components.foundation.pin.PinAuthDialog
import com.vietinbank.feature_checker.ui.component.newui.InitTransactionActions
import com.vietinbank.feature_checker.ui.component.newui.NewUIInitTransactionScreen
import com.vietinbank.feature_checker.ui.component.newui.NextApproverAction
import com.vietinbank.feature_checker.ui.component.newui.NextApproverBottomSheet
import com.vietinbank.feature_checker.ui.component.newui.NextApproverState
import com.vietinbank.feature_checker.ui.component.newui.RejectReasonAction
import com.vietinbank.feature_checker.ui.component.newui.RejectReasonBottomSheet
import com.vietinbank.feature_checker.ui.fragment.ConfirmCheckerViewModel
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import com.vietinbank.feature_checker.ui.fragment.KeypassAuthEvent
import com.vietinbank.feature_checker.ui.sheet.FileSheet
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.ConfirmSmartCA.PACKAGE_VNPT
import com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen.SmartCAConfirmViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.common.setupSoftOTPDialogListener
import com.vietinbank.feature_soft.common.simpleHandleSoftOTPEvent
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPAuthEvent
import com.vietinbank.feature_soft.views.newui.dialog.SoftOTPAuthViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * New UI implementation of Init Transaction using Foundation Design System
 * Reuses the existing InitTransactionViewModel for business logic
 */
@AndroidEntryPoint
class NewUIInitTransactionFragment : BaseFragment<InitTransactionViewModel>(), ISignResult {

    override val viewModel: InitTransactionViewModel by viewModels()

    // Add ConfirmCheckerViewModel for handling authentication flows
    private val confirmViewModel: ConfirmCheckerViewModel by viewModels()
    private val softOTPAuthViewModel: SoftOTPAuthViewModel by viewModels()
    private val smartCAConfirmViewModel: SmartCAConfirmViewModel by viewModels()

    override val useCompose: Boolean = true

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Inject
    lateinit var userProf: IUserProf

    @Inject
    lateinit var softManager: ISoftManager

    @Inject
    lateinit var smartCAManager: ISmartCAManager

    // Store pre-approve result for later use after dialog
    private var currentPreApproveResult: PreApproveDomain? = null
    private var currentConfirmType: String? = null
    private var currentTransaction: TransactionDomain? = null
    private var tempIsBatchOperation: Boolean? = null
    private var currentIsBatchOperation: Boolean? = null
    private var currentMethodList: ArrayList<MethodEntityDomain>? = null
    private var currentRejectReason: String? = null

    // State for rejection dialog - using mutableStateOf for Compose reactivity
    private val isRejectReasonDialogVisible = mutableStateOf(false)
    private val isRejectLoading = mutableStateOf(false)

    @Inject
    override lateinit var appNavigator: IAppNavigator

    private var fileSheet: FileSheet? = null

    private var countDownTimer: CountDownTimer? = null

    @Composable
    override fun ComposeScreen() {
        // Ensure bank list cache is available for icon mapping (runs once when screen composes)
        LaunchedEffect(Unit) {
            viewModel.ensureBankListLoaded()
        }

        // Get dialog state as Compose state
        val isDialogVisible = isRejectReasonDialogVisible.value
        val isLoading = isRejectLoading.value

        // Collect next approver states
        val isNextApproverEnabled by viewModel.isNextApproverEnabled.collectAsState()
        val nextApprovers by viewModel.nextApprovers.collectAsState()
        val selectedNextApprovers by viewModel.selectedNextApprovers.collectAsState()
        val isNextApproverBottomSheetVisible by viewModel.isNextApproverBottomSheetVisible.collectAsState()
        val isNextApproverLoading by viewModel.isNextApproverLoading.collectAsState()

        // Create next approver state
        val nextApproverState = NextApproverState(
            isEnabled = isNextApproverEnabled,
            allApprovers = nextApprovers,
            selectedApprovers = selectedNextApprovers,
            isBottomSheetVisible = isNextApproverBottomSheetVisible,
        )

        // Create actions for the UI
        val actions = InitTransactionActions(
            onBackClick = {
                appNavigator.popBackStack()
            },
            onApproveClick = {
                printLog("NewUIInitTransactionFragment: onApproveClick called")
                viewModel.preApprove()
            },
            onRejectClick = {
                viewModel.preReject()
            },
            onBatchApproveClick = {
                viewModel.preApproveBatch()
            },
            onBatchRejectClick = {
                viewModel.preRejectBatch()
            },
            onDetailFileClick = { type ->
                val fileRule = when (type) {
                    0 -> viewModel.getListFile(0)
                    1 -> viewModel.getListFile(1)
                    else -> null
                }
                fileRule?.let { rule ->
                    fileSheet = FileSheet(rule).apply {
                        setOnFileClick { file ->
                            viewModel.getDownloadFileID(file)
                        }
                    }
                    fileSheet?.show(childFragmentManager, FileSheet::class.java.name)
                }
            },
            onAgreeDisbursementChanged = {
                viewModel.onChangeAgreeDisbursement()
            },
            onToggleNextApprover = {
                viewModel.toggleNextApproverEnabled()
            },
            onShowNextApproverSelection = {
                viewModel.showNextApproverBottomSheet()
            },
            onShowUpdateDialog = {
                // Show update dialog when history tab is selected
                showNoticeDialog(getString(R.string.common_feature_development))
            },
            onToggleBatch = { mtId ->
                viewModel.toggleTransactionSelection(mtId)
            },
            onToggleBatchAllClick = {
                viewModel.toggleAllTransactions()
            },
            onLoadMoreBatch = {
                viewModel.loadMoreBatchTransactions()
            },
        )

        // Render the new UI screen
        NewUIInitTransactionScreen(
            viewModel = viewModel,
            actions = actions,
            nextApproverState = nextApproverState,
        )

        // Rejection reason dialog
        RejectReasonBottomSheet(
            visible = isDialogVisible,
            isLoading = isLoading,
            onAction = { action ->
                when (action) {
                    is RejectReasonAction.OnDismiss,
                    is RejectReasonAction.OnBackClick,
                    -> {
                        isRejectReasonDialogVisible.value = false
                        isRejectLoading.value = false
                    }

                    is RejectReasonAction.OnContinueClick -> {
                        printLog("RejectReasonBottomSheet: User entered reason: ${action.reason}")
                        // Save reject reason for success screen
                        currentRejectReason = action.reason
                        isRejectLoading.value = true
                        viewModel.doReject(action.reason)
                    }
                }
            },
        )

        // Next approver selection dialog
        NextApproverBottomSheet(
            visible = isNextApproverBottomSheetVisible,
            approvers = nextApprovers,
            selectedApprovers = selectedNextApprovers,
            isLoading = isNextApproverLoading,
            onAction = { action ->
                when (action) {
                    is NextApproverAction.OnDismiss -> {
                        viewModel.hideNextApproverBottomSheet()
                    }

                    is NextApproverAction.OnConfirm -> {
                        viewModel.updateSelectedApprovers(action.selectedApprovers)
                        // Keep ConfirmCheckerViewModel in sync so nextApprovers get sent in approval APIs
                        confirmViewModel.setSelectedNextApprovers(action.selectedApprovers)
                    }
                }
            },
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        observeViewModel(confirmViewModel)
        smartCAManager.setSignListener(this@NewUIInitTransactionFragment)

        // Set up result listener for authentication method dialog
        // IMPORTANT: Use childFragmentManager because dialog is shown with childFragmentManager
        childFragmentManager.setFragmentResultListener(
            "authentication_method_result",
            viewLifecycleOwner,
        ) { _, bundle ->
            printLog("setFragmentResultListener: Received result from AuthenticationMethodDialog")
            // IMPORTANT: BaseDialog uses "key_result" as the key, not "result"
            val result = bundle.getParcelable<AuthenticationMethodResult>("key_result")
            printLog("setFragmentResultListener: Parsed result = $result")
            result?.let {
                printLog("setFragmentResultListener: Selected method = ${it.selectedMethod}")
                // Continue with the selected authentication method
                handleAuthenticationMethodSelected(it.selectedMethod)
            } ?: printLog("setFragmentResultListener: Result is null")
        }

        // Set up result listener for PinAuthDialog
        setupSoftOTPDialogListener(
            onVerifyPin = {
                softOTPAuthViewModel.verifyPin(it)
            },
            onApprove = {
                confirmViewModel.doApprove(
                    confirmType = currentConfirmType ?: Tags.APPROVE,
                    otpCode = it,
                )
            },
        )

        parentFragmentManager.setFragmentResultListener(
            KeypassAuthDialog.KEYPASS_AUTH_DIALOG_RESULT_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            bundle.getString(KeypassAuthDialog.RESULT_KEYPASS_VALUE)?.let { keypassValue ->
                confirmViewModel.verifyKeypass(keypassValue)
            }
        }

        // Process transaction from Bundle arguments
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                val transaction: TransactionListDomain =
                    Utils.g().provideGson().fromJson(jsonData, TransactionListDomain::class.java)

                viewModel.processTransaction(transaction)
                printLog("Đã nhận giao dịch: ${transaction.mtId}")
            } catch (e: Exception) {
                printLog("Lỗi parse JSON: ${e.message}")
                e.printStackTrace()
            }
        } ?: run {
            printLog("Không có dữ liệu transaction trong bundle")
        }
        try {
            arguments?.getString(Tags.DATA_TYPE)?.let { type ->
                viewModel.setMultipleType(type)
            }
        } catch (_: Exception) {
        }
        // Initialize observers
        initObservers()
    }

    private fun initObservers() {
        observeViewModel(softOTPAuthViewModel)
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    // Observe error events
                    launch {
                        commonErrorEvent.collect { exception ->
                            exception?.let {
                                val errorMessage = it.message ?: "Lỗi xử lý phê duyệt"
                                showNoticeDialog(
                                    message = errorMessage,
                                    type = DialogType.ERROR,
                                    positiveAction = {
                                        appNavigator.popBackStack()
                                    },
                                )
                            }
                        }
                    }

                    // Observe file permission events
                    launch {
                        filePermissionEvent.collect { message ->
                            showNoticeDialog(
                                message = message,
                                type = DialogType.ERROR,
                            )
                        }
                    }

                    // Observe pre-approve results
                    launch {
                        preApprove.collect { resource ->
                            printLog("NewUIInitTransactionFragment: preApprove collected, resource = $resource")
                            resource?.let { data ->
                                printLog("NewUIInitTransactionFragment: preApprove data received, isApprove = ${data.isApprove}")
                                printLog("NewUIInitTransactionFragment: Calling handleTransactionResult, confirmType = ${if (data.isApprove) Tags.APPROVE else Tags.REJECT}")
                                handleTransactionResult(
                                    success = data,
                                    confirmType = if (data.isApprove) {
                                        Tags.APPROVE
                                    } else {
                                        Tags.REJECT
                                    },
                                    transaction = viewModel.getTransaction(),
                                    isBatchOperation = viewModel.isSpecialTransactionType(),
                                )
                            }
                                ?: printLog("NewUIInitTransactionFragment: preApprove resource is NULL!")
                        }
                    }

                    // Observe file download events
                    launch {
                        getDownloadFileID.collect { resource ->
                            if (resource != null && resource is Resource.Success) {
                                val downloadFileId = resource.data.downloadFileId
                                if (downloadFileId.isNullOrEmpty()) {
                                    showNoticeDialog(
                                        message = "Không tìm thấy file đính kèm hoặc file không khả dụng",
                                        type = DialogType.ERROR,
                                    )
                                }
                            } else if (resource is Resource.Error) {
                                showNoticeDialog(
                                    message = resource.message ?: "Không thể tải file đính kèm",
                                    type = DialogType.ERROR,
                                )
                            }
                        }
                    }

                    // Observe reject result
                    launch {
                        doRejectResponse.collect { resource ->
                            printLog("NewUIInitTransactionFragment: doRejectResponse collected, resource = $resource")
                            when (resource) {
                                is Resource.Success -> {
                                    printLog("NewUIInitTransactionFragment: Reject successful, navigating to success")
                                    // Hide dialog and loading
                                    isRejectReasonDialogVisible.value = false
                                    isRejectLoading.value = false

                                    // Navigate to success screen
                                    val rejectData = resource.data
                                    navigateToRejectSuccess(rejectData)
                                }

                                is Resource.Error -> {
                                    printLog("NewUIInitTransactionFragment: Reject failed: ${resource.message}")
                                    // Hide loading but keep dialog open
                                    isRejectLoading.value = false
                                    showNoticeDialog(
                                        message = resource.message ?: "Từ chối giao dịch thất bại",
                                        type = DialogType.ERROR,
                                    )
                                }

                                else -> {
                                    // Hide loading
                                    isRejectLoading.value = false
                                }
                            }
                        }
                    }

                    // Observe file URL ready events
                    launch {
                        viewModel.fileUrlReady.collect { url ->
                            try {
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                                startActivity(intent)
                            } catch (e: Exception) {
                                showNoticeDialog(
                                    message = "Không thể mở file: ${e.message}",
                                    type = DialogType.ERROR,
                                )
                            }
                        }
                    }

                    // Observe open file intents
                    launch {
                        openFileIntent.collect { intent ->
                            startActivity(intent)
                        }
                    }

                    // Observe error messages
                    launch {
                        errorMessage.collect { message ->
                            showNoticeDialog(
                                message = message,
                                type = DialogType.ERROR,
                            )
                        }
                    }

                    launch {
                        errorEvent.collect { message ->
                            message?.let {
                                showNoticeDialog(
                                    message = it,
                                    type = DialogType.ERROR,
                                )
                            }
                        }
                    }
                }

                // Observe doApprove result
                lifecycleScope.launch {
                    repeatOnLifecycle(Lifecycle.State.STARTED) {
                        confirmViewModel.doApproveResponse.collect { data ->
                            // Don't hide loading - viewmodel's launchJob handles it

                            // Dismiss any existing PinAuthDialog
                            val existingDialog =
                                childFragmentManager.findFragmentByTag("PinAuthDialog")
                            (existingDialog as? PinAuthDialog)?.dismiss()

                            // Navigate to Success screen with transaction data
                            navigateToSuccessScreen(data)
                        }
                    }
                }

                // Observe genKeyPassChallengeCode result
                confirmViewModel.genKeyPassChallengeCode.observe(viewLifecycleOwner) { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            printLog("Keypass challenge code generated: ${resource.data.challengeCode}")
                            val confirmDataJson =
                                Utils.g().provideGson().toJson(confirmViewModel.uiModel.value)
                            appNavigator.goToConfirmKeypassDialog(
                                challengeCode = resource.data.challengeCode.toString(),
                            )
                        }

                        is Resource.Error -> {
                            printLog("Keypass challenge code error: ${resource.message}")
                            showNoticeDialog(
                                message = resource.message ?: "Không thể tạo mã xác thực Keypass",
                                type = DialogType.ERROR,
                            )
                        }

                        else -> {}
                    }
                }

                launch {
                    confirmViewModel.oneTimeEvent.collect {
                        (it as? KeypassAuthEvent)?.let { keypassEvent ->
                            when (keypassEvent) {
                                KeypassAuthEvent.OnReachMaxFail ->
                                    showNoticeDialog(
                                        message = resources.getString(com.vietinbank.feature_soft.R.string.keypass_auth_blocked_message),
                                        type = DialogType.ERROR,
                                        icon = R.drawable.ic_commom_fail_24,
                                    )

                                is KeypassAuthEvent.OnVerifyKeypassSuccess -> {
                                    dismissAllDialogs()
                                    navigateToSuccessScreen(keypassEvent.approveData)
                                }

                                is KeypassAuthEvent.OnVerifyFail -> {
                                    showNoticeDialog(
                                        message = keypassEvent.message,
                                        type = DialogType.ERROR,
                                    )
                                }

                                is KeypassAuthEvent.OnVerifyingKeypass -> {
                                    if (keypassEvent.isDone) {
                                        viewModel.hideLoading()
                                    } else {
                                        viewModel.showLoading()
                                    }
                                }
                            }
                        }
                    }
                }

                launch {
                    softOTPAuthViewModel.oneTimeEvent.collect {
                        simpleHandleSoftOTPEvent(
                            event = it as SoftOTPAuthEvent,
                            onGetTransactionIdSuccess = { transactionId ->
                                confirmViewModel.transactionID = transactionId
                            },
                        )
                    }
                }
            }
        }

        confirmViewModel.apply {
            FlowUtils.collectFlow(
                this@NewUIInitTransactionFragment,
                doSignApproveState,
            ) { data ->
                changeSignState(SignCallState.Loading)
                if (!activity.isInstalledApp(PACKAGE_VNPT)) {
                    // kiem tra device da co app vnpt smart ca hay chua
                    // chua co app
                    showConfirmDialog(
                        resources.getString(R.string.feature_smart_ca_confirm_install_SDK_VNPT_VALIDATE),
                        resources.getString(R.string.common_agree),
                        resources.getString(R.string.common_close),
                        positiveAction = {
                            dismissNoticeDialog()
                        },
                    )
                } else {
                    // show loadding
                    showNoticeDialog(
                        title = resources.getString(R.string.feature_smart_ca_confirm_process),
                        message = resources.getString(R.string.feature_smart_ca_confirm_process_content),
                        positiveAction = {
                        },
                    )
                    // điều hướng sang vnpt để ký số
                    activity?.let {
                        smartCAManager.signTransaction(
                            requireActivity(),
                            data.verifySignInfo?.androidKey,
                            data.verifySignInfo?.tranCode,
                        )
                    }
                }
            }

            // cap nhat ket qua ky so smart ca tu VNPT
            FlowUtils.collectFlow(this@NewUIInitTransactionFragment, signFlowState) { signState ->
                when (signState) {
                    is SignCallState.Error -> {
                        forceAllHideLoading()
                        countDownTimer?.cancel()
                        countDownTimer = null
                        signState.exception?.let { onError(it) } ?: run {
                            showNoticeDialog(
                                signState.message ?: "",
                            ) { appNavigator.popBackStack() }
                        }
                    }

                    // trang thai ky thanh cong + case ky tren device khac
                    is SignCallState.Success, is SignCallState.Loading -> {
                        if (countDownTimer == null) {
                            countDownTimer = object : CountDownTimer(expiredCount * 1000L, 1000) {
                                override fun onTick(timeLeft: Long) {
                                    if ((timeLeft / 1000) % 5 == 0L) {
                                        // 5s call lai 1 lan nha -> recall detail -> kiem tra trang thai
                                        getTransactionDetail { appError ->
                                            /**
                                             * 1 thành công, ký xong -> case thanh cong -> khong can xu ly
                                             * 0 lỗi
                                             * 3 pending sign chờ ký số, check tiếp
                                             * */
                                            if (appError is AppException.ApiException && appError.code == "3") {
                                                // tiep tuc goi ky so theo count time -> khong can xu ly gi
                                            } else {
                                                // show thong bao loi -> dung luong recall api check
                                                changeSignState(SignCallState.Error(exception = appError))
                                            }
                                        }
                                    }
                                }

                                override fun onFinish() {
                                    changeSignState(SignCallState.Error(message = "Thời gian ký số đã kết thúc. Quý khách vui lòng thực hiện lại"))
                                }
                            }.start()
                        }
                    }

                    else -> {}
                }
            }

            // cap nhat ket qua ky so smart ca tu BE
            FlowUtils.collectFlow(
                this@NewUIInitTransactionFragment,
                transactionDetailState,
            ) { data ->
                // phe duyet thanh cong
                countDownTimer?.cancel()
                countDownTimer = null

                // điều hướng sang màn keets quả
                // todo
            }
        }
    }

    private fun handleTransactionResult(
        success: PreApproveDomain,
        confirmType: String,
        transaction: TransactionDomain,
        isBatchOperation: Boolean,
    ) {
        // nếu là phê duyệt theo file => điều hướng sang màn hình xác nhận của duyệt theo lo
        // nếu btx giao diện cũng giống cttf => đổi viewModel.isFile3000() => isBatchOperation
        if (viewModel.isFile3000()) {
            navigateToConfirmMultiple(success)
            return
        }

        // điều hướng sang màn confirm của duyệt đơn
        printLog("handleTransactionResult called: confirmType = $confirmType, methodList size = ${success.methodList?.size}")

        // Store transaction result for later use
        currentPreApproveResult = success
        currentConfirmType = confirmType
        currentTransaction = transaction
        currentIsBatchOperation = isBatchOperation
        currentMethodList = success.methodList

        // Check confirmType first
        if (confirmType == Tags.REJECT) {
            printLog("handleTransactionResult: REJECT flow - preparing data and showing rejection reason dialog")
            // REJECT: First prepare confirmViewModel with transaction data for success screen
            prepareConfirmViewModelForReject(transaction, isBatchOperation)
            // Then show dialog to collect rejection reason
            isRejectReasonDialogVisible.value = true
            isRejectLoading.value = false
        } else {
            printLog("handleTransactionResult: APPROVE flow - checking authentication methods")
            // APPROVE: Need to select authentication method
            success.methodList?.let { methods ->
                if (methods.isNotEmpty()) {
                    // Process available methods
                    val hasSoftOTP = methods.any { it.method?.lowercase() == "softotp" }
                    val hasKeypass = methods.any { it.method?.lowercase() == "keypass" }
                    val hasSmartCA = methods.any { it.method?.lowercase() == "vnptsmartca" }

                    printLog("handleTransactionResult: Available methods - SoftOTP: $hasSoftOTP, Keypass: $hasKeypass, SmartCA: $hasSmartCA")
                    printLog("handleTransactionResult: Checking if at least one method is available")

                    // Validate at least one method is available
                    if (!hasSoftOTP && !hasKeypass && !hasSmartCA) {
                        printLog("handleTransactionResult: ERROR - No methods available despite having methodList")
                        showNoticeDialog(
                            message = "Không có phương thức xác thực khả dụng",
                            type = DialogType.ERROR,
                        )
                        return
                    }

                    // If only one supported method is available, skip dialog and continue
                    val availableMethods = methods.mapNotNull { it.method?.lowercase() }
                        .filter { it == "softotp" || it == "keypass" || it == "vnptsmartca" }
                        .distinct()

                    if (availableMethods.size == 1) {
                        val onlyMethod = availableMethods.first()
                        printLog("handleTransactionResult: Only one method available ($onlyMethod). Skipping dialog.")
                        handleAuthenticationMethodSelected(onlyMethod)
                        return
                    }

                    // Show dialog for multi-method selection
                    printLog("handleTransactionResult: Showing AuthenticationMethodDialog")
                    try {
                        val dialog = AuthenticationMethodDialog.show(
                            fragmentManager = childFragmentManager,
                            hasSoftOTP = hasSoftOTP,
                            hasKeypass = hasKeypass,
                            hasSmartCA = hasSmartCA,
                            isApprove = true,
                        )
                        printLog("handleTransactionResult: Dialog show() returned, dialog=$dialog")

                        // Check if dialog is actually in fragment manager
                        childFragmentManager.executePendingTransactions()
                        val foundDialog =
                            childFragmentManager.findFragmentByTag("AuthenticationMethodDialog")
                        printLog("handleTransactionResult: Dialog in FragmentManager? ${foundDialog != null}")
                        if (foundDialog != null) {
                            printLog("handleTransactionResult: Dialog state - isAdded=${foundDialog.isAdded}, isVisible=${foundDialog.isVisible}, isResumed=${foundDialog.isResumed}")
                        }
                    } catch (e: Exception) {
                        printLog("handleTransactionResult: Exception showing dialog: ${e.message}")
                        e.printStackTrace()
                        showNoticeDialog(
                            message = "Không thể hiển thị phương thức xác thực: ${e.message}",
                            type = DialogType.ERROR,
                        )
                    }
                } else {
                    printLog("handleTransactionResult: No authentication methods available")
                    showNoticeDialog(
                        message = "Không có phương thức xác thực khả dụng cho phê duyệt",
                        type = DialogType.ERROR,
                    )
                }
            } ?: run {
                printLog("handleTransactionResult: methodList is NULL")
                showNoticeDialog(
                    message = "Không có phương thức xác thực khả dụng cho phê duyệt",
                    type = DialogType.ERROR,
                )
            }
        }
    }

    private fun handleAuthenticationMethodSelected(selectedMethod: String) {
        // Use stored values to continue the flow
        val success = currentPreApproveResult ?: return
        val confirmType = currentConfirmType ?: return
        val transaction = currentTransaction ?: return
        val isBatchOperation = currentIsBatchOperation ?: false
        val methodList = currentMethodList ?: return

        // Handle different authentication methods
        when (selectedMethod.lowercase()) {
            "softotp" -> {
                printLog("handleAuthenticationMethodSelected: SoftOTP selected")
                // Prepare ConfirmCheckerViewModel with transaction data
                prepareConfirmViewModelForSoftOTP(transaction, isBatchOperation)
                // Handle Soft OTP with all validations
                handleSoftOtpSelection()
            }

            "keypass" -> {
                printLog("handleAuthenticationMethodSelected: Keypass selected")
                // Prepare ConfirmCheckerViewModel with transaction data
                prepareConfirmViewModelForKeypass(transaction, isBatchOperation, methodList)
                // Generate Keypass challenge code
                confirmViewModel.genKeyPassChallengeCode()
            }

            "vnptsmartca" -> {
                showConfirmDialog(
                    "Chuyển sang app smartca để ký số",
                    resources.getString(R.string.common_agree),
                    resources.getString(R.string.common_close),
                    positiveAction = {
                        confirmViewModel.doSignApprove(
                            currentTransaction = currentTransaction,
                            approveLst = viewModel.selectedNextApprovers.value,
                            transactionLst = confirmViewModel.uiModel.value.transactionIds,
                        )
                    },
                )
            }

            else -> {
                // Fallback to old flow
                val filteredMethods = ArrayList(
                    methodList.filter {
                        it.method?.lowercase() == selectedMethod.lowercase()
                    },
                )
                navigateToConfirmChecker(
                    transaction = transaction,
                    confirmType = confirmType,
                    paymentMethod = filteredMethods,
                    isBatchOperation = isBatchOperation,
                )
            }
        }
    }

    /**
     * Prepare ConfirmCheckerViewModel with transaction data for Soft OTP flow
     */
    private fun prepareConfirmViewModelForSoftOTP(
        transaction: TransactionDomain,
        isBatchOperation: Boolean,
    ) {
        // Store batch operation flag for later use
        tempIsBatchOperation = isBatchOperation

        // Set confirm type - use actual transaction type name
        confirmViewModel.confirmType = transaction.tranTypeName ?: currentConfirmType

        // IMPORTANT: Ensure transaction has mtId from processorTransaction
        val transactionWithMtId = if (transaction.mtId.isNullOrEmpty()) {
            // Use mtId from processorTransaction if not present
            transaction.apply {
                mtId = viewModel.getTransaction().mtId ?: ""
            }
        } else {
            transaction
        }

        printLog("prepareConfirmViewModelForSoftOTP: mtId = ${transactionWithMtId.mtId}, tranType = ${transactionWithMtId.tranType}, serviceType = ${transactionWithMtId.serviceType}")

        // Parse and process transaction data
        val transactionJson = Utils.g().provideGson().toJson(transactionWithMtId)
        val dataType = if (isBatchOperation) Tags.UI_BATCH else Tags.UI_MODEL

        // Process transaction data in ConfirmCheckerViewModel
        confirmViewModel.parseAndProcessData(transactionJson, dataType)

        // Process auth methods (for Soft OTP)
        currentMethodList?.let { methods ->
            confirmViewModel.processAuthMethods(methods)
        }
    }

    /**
     * Prepare ConfirmCheckerViewModel with transaction data for Keypass flow
     */
    private fun prepareConfirmViewModelForKeypass(
        transaction: TransactionDomain,
        isBatchOperation: Boolean,
        methodList: ArrayList<MethodEntityDomain>,
    ) {
        // Store batch operation flag for later use
        tempIsBatchOperation = isBatchOperation

        // Set confirm type - use actual transaction type name
        confirmViewModel.confirmType = transaction.tranTypeName ?: currentConfirmType

        // Parse and process transaction data
        val transactionJson = Utils.g().provideGson().toJson(transaction)
        val dataType = if (isBatchOperation) Tags.UI_BATCH else Tags.UI_MODEL

        // Process transaction data in ConfirmCheckerViewModel
        confirmViewModel.parseAndProcessData(transactionJson, dataType)

        // Process auth methods (for Keypass)
        confirmViewModel.processAuthMethods(methodList)
    }

    /**
     * Prepare ConfirmCheckerViewModel with transaction data for Reject flow
     * This ensures successList has data when navigating to success screen
     */
    private fun prepareConfirmViewModelForReject(
        transaction: TransactionDomain,
        isBatchOperation: Boolean,
    ) {
        printLog("prepareConfirmViewModelForReject: Preparing data for reject flow")

        // If this is a batch transaction but mtId is not set, use the first selected transaction ID
        val transactionWithMtId = if (isBatchOperation && transaction.mtId == null) {
            val firstSelectedId = viewModel.getBatchFileSelected().firstOrNull()
            transaction.also { it.mtId = firstSelectedId }
        } else {
            transaction
        }

        printLog("prepareConfirmViewModelForReject: mtId = ${transactionWithMtId.mtId}, tranType = ${transactionWithMtId.tranType}, serviceType = ${transactionWithMtId.serviceType}")

        // Parse and process transaction data (same as approve flow)
        val transactionJson = Utils.g().provideGson().toJson(transactionWithMtId)
        val dataType = if (isBatchOperation) Tags.UI_BATCH else Tags.UI_MODEL

        // Process transaction data in ConfirmCheckerViewModel
        confirmViewModel.parseAndProcessData(transactionJson, dataType)

        // Set confirmType for transaction type name (same as approve flow)
        confirmViewModel.confirmType = transaction.tranTypeName ?: currentConfirmType

        // No need to process auth methods for reject flow (no authentication required)
        printLog("prepareConfirmViewModelForReject: Data prepared for reject flow, confirmType = ${confirmViewModel.confirmType}")
    }

    /**
     * Handle Soft OTP selection with all validation checks
     * Following the exact flow from ConfirmCheckerFragment
     */
    private fun handleSoftOtpSelection() {
        printLog("handleSoftOtpSelection: Starting Soft OTP validation")

        // Check 1: Is user allowed to use Soft OTP?
        if (!softManager.isAllowSoft()) {
            printLog("handleSoftOtpSelection: User not registered for Soft OTP")
            showNoticeDialog(
                message = "Quý khách chưa đăng ký phương thức xác thực Soft OTP",
                type = DialogType.ERROR,
            )
            return
        }

        // Check 2: Is Soft OTP locked (due to multiple failed attempts)?
        if (softManager.getStatusLockSoft()) {
            appNavigator.goToLockSoftOTPDialog()
            return
        }

        // Check 3: Is user active on this device?
        if (softManager.isUserActive) {
            printLog("handleSoftOtpSelection: User is active, generating transaction code")
            // User is activated on this device - proceed with transaction code generation
            softOTPAuthViewModel.gentOTPTransCode(
                groupType = confirmViewModel.uiModel.value.groupType,
                mtIds = confirmViewModel.uiModel.value.transactionIds.joinToString(","),
                tranType = confirmViewModel.uiModel.value.tranType,
            )
        } else {
            printLog("handleSoftOtpSelection: User not active, checking app activation status")
            // User not activated on this device - need to activate
            showConfirmDialog(
                message = "Quý khách cần kích hoạt Soft OTP để thực hiện giao dịch này",
                positiveButtonText = getString(R.string.common_agree),
                negativeButtonText = getString(R.string.common_cancel),
                positiveAction = {
                    if (softManager.isAppActive) {
                        printLog("handleSoftOtpSelection: App is active, going to PIN entry for activation")
                        // App is active - second activation, need PIN
                        appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.ACTIVE.name)
                    } else {
                        printLog("handleSoftOtpSelection: App not active, going to activation screen")
                        // App not active - first activation
                        appNavigator.gotoActivSoft()
                    }
                },
                negativeAction = {
                    printLog("handleSoftOtpSelection: User cancelled activation")
                },
            )
        }
    }

    /**
     * Helper method to navigate to ConfirmChecker screen
     * Handles both single and batch transactions
     */
    private fun navigateToConfirmChecker(
        transaction: TransactionDomain,
        confirmType: String,
        paymentMethod: ArrayList<MethodEntityDomain>,
        isBatchOperation: Boolean,
    ) {
        when (transaction.tranType) {
            "ba", "btx" -> {
                if (isBatchOperation) {
                    // Handle batch confirmation with selected transactions
                    val selectedMtIds = ArrayList(viewModel.getBatchFileSelected())
                    confirmLauncher.launchWithBatchTransactions(
                        transaction = transaction,
                        confirmType = confirmType,
                        paymentMethod = paymentMethod,
                        selectedMtIds = selectedMtIds,
                        onError = { message ->
                            showNoticeDialog(
                                message = message,
                                type = DialogType.ERROR,
                            )
                        },
                    )
                } else {
                    // Handle regular batch confirmation
                    confirmLauncher.launchWithSingleTransaction(
                        transaction = transaction,
                        confirmType = confirmType,
                        paymentMethod = paymentMethod,
                        onError = { message ->
                            showNoticeDialog(
                                message = message,
                                type = DialogType.ERROR,
                            )
                        },
                    )
                }
            }

            else -> {
                // Handle regular transaction confirmation
                confirmLauncher.launchWithSingleTransaction(
                    transaction = transaction,
                    confirmType = confirmType,
                    paymentMethod = paymentMethod,
                    onError = { message ->
                        showNoticeDialog(
                            message = message,
                            type = DialogType.ERROR,
                        )
                    },
                )
            }
        }
    }

    /**
     * Navigate to Success screen after successful approval
     */
    private fun navigateToSuccessScreen(approveData: ApproveDomain) {
        // Pass TransactionDomain and ApproveDomain directly
        val transactionJson = currentTransaction?.let {
            Utils.g().provideGson().toJson(it)
        } ?: ""

        val approveDataJson = Utils.g().provideGson().toJson(approveData)

        // Use getSuccessTitle() from ConfirmCheckerViewModel for proper title formatting
        val successTitle = confirmViewModel.getSuccessTitle(
            approveData.transactions?.firstOrNull(),
            approveData.status?.message ?: "Phê duyệt thành công",
        )
        val successTitleJson = Utils.g().provideGson().toJson(successTitle)

        // Navigate to Success screen using appNavigator with TransactionDomain
        // Note: Using successListJson parameter but passing TransactionDomain JSON instead
        // The SuccessFragment will handle this appropriately
        appNavigator.gotoSuccessChecker(
            transactionJson, // Pass TransactionDomain JSON instead of SuccessItemDomain list
            successTitleJson,
            confirmViewModel.confirmType ?: "",
            approveDataJson, // Pass full ApproveDomain JSON for message and other data
        )
    }

    /**
     * Navigate to Success screen after successful rejection
     */
    private fun navigateToRejectSuccess(rejectData: ApproveDomain) {
        printLog("navigateToRejectSuccess: Preparing navigation data")

        // Pass TransactionDomain as-is
        val transactionJson = currentTransaction?.let {
            Utils.g().provideGson().toJson(it)
        } ?: ""

        // Create ApproveDomain with reject reason embedded in messageReject
        // We'll parse it in the renderer to display separately
        val rejectDataWithReason = rejectData.apply {
            // Embed reject reason with a special delimiter for parsing
            currentRejectReason?.let { reason ->
                // Use special delimiter "||" to separate message and reason
                messageReject = "${rejectData.messageReject ?: "Từ chối giao dịch thành công"}||$reason"
            }
        }
        val approveDataJson = Utils.g().provideGson().toJson(rejectDataWithReason)

        // Use getSuccessTitle() from ConfirmCheckerViewModel for proper title formatting (with amounts)
        val successTitleBase = confirmViewModel.getSuccessTitle(
            rejectData.transactions?.firstOrNull(),
            rejectData.status?.message ?: "Từ chối giao dịch thành công",
        )

        // Create new instance with updated status to indicate rejection
        val successTitle = successTitleBase.copy(
            status = "Từ chối",
            statusTrans = "Rejected",
        )
        val successTitleJson = Utils.g().provideGson().toJson(successTitle)

        // Navigate to success screen with TransactionDomain
        printLog("navigateToRejectSuccess: Navigating with TransactionDomain")
        appNavigator.gotoSuccessChecker(
            transactionJson, // Pass TransactionDomain JSON
            successTitleJson,
            confirmViewModel.confirmType
                ?: Tags.REJECT, // Use confirmType for transaction type name
            approveDataJson, // Pass full ApproveDomain JSON with reject reason
        )
    }

    override fun onResult(statusCode: Int, statusMessage: String) {
        if (statusCode == 0) {
            confirmViewModel.changeSignState(SignCallState.Success)
        } else if ((statusCode != 3 && statusCode != -1)) {
            confirmViewModel.changeSignState(SignCallState.Error(message = statusMessage))
        } else {
            confirmViewModel.changeSignState(SignCallState.Loading)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }

    // confirm multiple
    private fun navigateToConfirmMultiple(success: PreApproveDomain) {
        val uiModel = MultipleConfirmUIModel(
            titleScreen = if (success.isApprove) {
                getString(R.string.approve_file_title_approve)
            } else {
                getString(R.string.approve_file_title_reject)
            },
            transactionIds = viewModel.getBatchFileSelected(),
            serviceType = viewModel.getTransaction().serviceType ?: "",
            tranType = viewModel.getTransaction().tranType ?: "",
            groupType = viewModel.getTransaction().groupType ?: "",
            groupTransactions = success.groupTransactions,
            methodList = success.methodList,
            isApprove = success.isApprove,
            totalFee = success.totalFee.toString(),
            isBatchFile = true,
        )

        appNavigator.goToMultipleConfirmApproval(
            bundleOf().apply {
                putString(Tags.TRANSACTION_BUNDLE, Utils.g().provideGson().toJson(uiModel))
            },
        )
    }
}
