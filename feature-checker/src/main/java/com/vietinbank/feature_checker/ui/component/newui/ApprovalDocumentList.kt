package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.feature_checker.ui.component.inits.ExpandableSection
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ApprovalDocumentList(
    transaction: TransactionDomain,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.commonRoundedCornerCard().padding(horizontal = FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
    ) {
        FoundationText(
            stringResource(R.string.disbursement_section_beneficiary),
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.characterHighlighted,
        )

        ExpandableSection(
            title = transaction.benName,
        ) {
            FoundationText(
                transaction.receiveAccount.orEmpty(),
                style = FDS.Typography.headingH6,
                color = FDS.Colors.characterHighlighted,
            )
            transaction.fileList?.forEach { document ->
                if (!document.fileName.isNullOrEmpty()) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_common_transfer_32),
                            contentDescription = null,
                        )
                        FoundationText(
                            document.fileName.orEmpty(),
                            style = FDS.Typography.bodyB2.copy(
                                textDecoration = TextDecoration.Underline,
                            ),
                            color = FDS.Colors.characterHighlighted,
                        )
                    }
                }
            }
        }
    }
}