package com.vietinbank.feature_checker.ui.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FieldVariant
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.glassBottomGradient
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * NextApproverBottomSheet (legacy ConfirmChecker screen)
 * Updated to match Figma design system (title, search, list, gradient button bar)
 * API kept unchanged: both buttons dismiss the sheet after live selection
 */
@Composable
fun NextApproverBottomSheet(
    isVisible: Boolean,
    approvers: List<ApproverDomains>,
    selectedApprover: List<ApproverDomains>?,
    onToggleApprover: (ApproverDomains) -> Unit,
    onDismiss: () -> Unit,
) {
    if (!isVisible) return

    // Initialize selected with all approvers if not set
    val initialSelectedApprovers = remember(approvers, selectedApprover) {
        selectedApprover?.takeIf { it.isNotEmpty() } ?: approvers
    }

    // Local search state
    var searchQuery by remember { mutableStateOf("") }
    val filteredApprovers = remember(searchQuery, approvers) {
        val q = searchQuery.trim().lowercase()
        if (q.isEmpty()) {
            approvers
        } else {
            approvers.filter { ap ->
                (ap.fullname ?: "").lowercase().contains(q) ||
                    (ap.username ?: "").lowercase().contains(q)
            }
        }
    }

    // Compute isAllSelected within filtered set
    val isAllSelected = initialSelectedApprovers.isNotEmpty() && filteredApprovers.isNotEmpty() &&
        filteredApprovers.all { ap -> initialSelectedApprovers.any { it.id == ap.id } }

    BaseBottomSheet(
        visible = isVisible,
        onDismissRequest = onDismiss,
        allowTouchDismiss = false,
    ) { close ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .imePadding(),
        ) {
            // White card area with rounded corners and shadow
            Surface(
                shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                color = FDS.Colors.backgroundBgContainer,
                shadowElevation = FDS.Sizer.Gap.gap4,
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(modifier = Modifier.fillMaxWidth()) {
                    // Title
                    FoundationText(
                        text = stringResource(CoreR.string.next_approver_title),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                        modifier = Modifier
                            .padding(
                                horizontal = FDS.Sizer.Padding.padding24,
                                vertical = FDS.Sizer.Gap.gap24,
                            )
                            .align(Alignment.CenterHorizontally),
                    )

                    // Divider
                    FoundationDivider()

                    // Search area - background #EDF4F8, placeholder color tertiary
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .padding(top = FDS.Sizer.Gap.gap12, bottom = FDS.Sizer.Gap.gap8),
                    ) {
                        androidx.compose.foundation.layout.Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = FDS.Colors.homeBackgroundIcon, // #EDF4F8
                                    shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                                )
                                .padding(
                                    horizontal = FDS.Sizer.Padding.padding16,
                                    vertical = FDS.Sizer.Padding.padding8,
                                ),
                        ) {
                            FoundationFieldType(
                                value = searchQuery,
                                onValueChange = { searchQuery = it },
                                variant = FieldVariant.TRANSPARENT, // use outer Box for bg & radius
                                inputType = InputType.TEXT,
                                placeholder = stringResource(CoreR.string.next_approver_search_placeholder),
                                singleLine = true,
                                showCharacterCounter = false,
                                isHaveClearIcon = searchQuery.isNotEmpty(),
                                clearValue = { searchQuery = "" },
                                leadingIcon = {
                                    androidx.compose.foundation.Image(
                                        painter = androidx.compose.ui.res.painterResource(id = CoreR.drawable.ic_search),
                                        contentDescription = null,
                                    )
                                },
                            )
                        }
                    }

                    // List content
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                    ) {
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                        // Select All row
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .safeClickable {
                                    if (isAllSelected) {
                                        // Unselect all filtered items
                                        filteredApprovers.forEach { ap ->
                                            if (initialSelectedApprovers.any { it.id == ap.id }) {
                                                onToggleApprover(ap)
                                            }
                                        }
                                    } else {
                                        // Select all filtered items
                                        filteredApprovers.forEach { ap ->
                                            if (initialSelectedApprovers.none { it.id == ap.id }) {
                                                onToggleApprover(ap)
                                            }
                                        }
                                    }
                                }
                                .padding(vertical = FDS.Sizer.Padding.padding8),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                FoundationText(
                                    text = stringResource(CoreR.string.next_approver_all),
                                    style = FDS.Typography.bodyB2,
                                    color = FDS.Colors.characterPrimary,
                                )
                                // Show "Theo đăng ký với VietinBank" when all selected
                                if (isAllSelected) {
                                    FoundationText(
                                        text = "Theo đăng ký với VietinBank",
                                        style = FDS.Typography.captionCaptionL,
                                        color = FDS.Colors.characterSecondary,
                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap2),
                                    )
                                }
                            }
                            FoundationSelector(
                                boxType = SelectorType.Checkbox,
                                isSelected = isAllSelected,
                                isClickable = false,
                            )
                        }

                        // Approver list
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = FDS.Sizer.Dialog.maxListHeight),
                            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
                        ) {
                            items(filteredApprovers) { ap ->
                                val isSelected = initialSelectedApprovers.any { it.id == ap.id }

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .safeClickable { onToggleApprover(ap) }
                                        .padding(vertical = FDS.Sizer.Padding.padding8),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {
                                    Column(modifier = Modifier.weight(1f)) {
                                        FoundationText(
                                            text = ap.fullname ?: ap.username ?: "",
                                            style = FDS.Typography.bodyB2,
                                            color = FDS.Colors.characterPrimary,
                                        )
                                        val username = ap.username
                                        if (!username.isNullOrBlank()) {
                                            FoundationText(
                                                text = username,
                                                style = FDS.Typography.captionCaptionL,
                                                color = FDS.Colors.characterSecondary,
                                            )
                                        }
                                    }

                                    FoundationSelector(
                                        boxType = SelectorType.Checkbox,
                                        isSelected = isSelected,
                                        isClickable = false,
                                    )
                                }
                            }
                            item { Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8)) }
                        }
                    }
                }
            }

            // Bottom gradient button bar
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                FDS.Colors.blue900.copy(alpha = 0.675f), // -> #002E51 with alpha
                            ),
                        ),
                    )
                    .padding(
                        horizontal = FDS.Sizer.Padding.padding24,
                        vertical = FDS.Sizer.Padding.padding16,
                    ),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth()
                        .glassBottomGradient(
                            endColor = FDS.Colors.blue900,
                            alpha = 0.675f,
                        ).padding(bottom = FDS.Sizer.Padding.padding16),
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    // Secondary: Quay lại
                    FoundationButton(
                        isLightButton = false,
                        text = stringResource(CoreR.string.common_back),
                        onClick = { close() },
                        modifier = Modifier.weight(1f),
                    )

                    // Primary: Tiếp tục
                    FoundationButton(
                        isLightButton = true,
                        text = stringResource(CoreR.string.button_continue),
                        onClick = { close() },
                        enabled = initialSelectedApprovers.isNotEmpty(),
                        modifier = Modifier.weight(1f),
                    )
                }
            }
        }
    }
}
