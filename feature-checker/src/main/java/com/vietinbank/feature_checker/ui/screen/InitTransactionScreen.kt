package com.vietinbank.feature_checker.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.InitTransactionActions
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel

/**
 * Created by vandz on 11/3/25.
 */
@Composable
fun InitTransactionScreen(
    viewModel: InitTransactionViewModel,
    actions: InitTransactionActions,
) {
    // Collect state từ ViewModel
    val transaction by viewModel.transactionState.collectAsState()
    val renderer by viewModel.renderer.collectAsState()
    // Check if this is a batch file transaction and has selections
    val isBatchFile = viewModel.isBatchFile(transaction.tranType)
    val hasSelections = viewModel.getBatchFileSelected().isNotEmpty()
    val isAgreeDisbursement by viewModel.isAgreeDisbursement.collectAsState()
    val termState by viewModel.termState.collectAsState()
    val context = LocalContext.current

    Column(
        modifier =
        Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_home,
                contentDescription = "HOME",
                onClick = { actions.onHomeClick.invoke() },
                tint = Color.White,
            ),
        )

        // AppBar
        BaseAppBar(
            title = "Thông tin chi tiết",
            onBackClick = actions.onBackClick,
            actions = appBarActions,
        )

        // Content
        Column(
            modifier =
            Modifier
                .fillMaxSize()
                .navigationBarsPadding()
                .weight(1f)
                .verticalScroll(rememberScrollState()),
        ) {
            Column(
                modifier =
                Modifier
                    .fillMaxSize()
                    .weight(1f)
                    .padding(horizontal = 16.dp)
                    .verticalScroll(rememberScrollState()),
            ) {
                // Hiển thị layout dựa vào loại giao dịch
                // Sử dụng renderer từ ViewModel
                renderer?.RenderTransactionDetails(
                    transaction = transaction,
                    modifier = Modifier.fillMaxWidth(),
                    onFieldClick = { event ->
                        when (event) {
                            is TransactionFieldEvent.FileAttachmentClick -> {
                                viewModel.getDownloadFileID(event.file, true)
                            }

                            is TransactionFieldEvent.ProfileAttachmentClick -> {
                                viewModel.getDownloadFileID(event.file, false)
                            }

                            is TransactionFieldEvent.ListAttachmentClick -> {
                                // show popup danh sach tài khoan gi do
                                actions.onDetailFileClick(event.titleType)
                            }

                            is TransactionFieldEvent.FileBaseAttachmentClick -> {
                                viewModel.getDownloadBase64File(context, event.transaction.mtId, Tags.DOWNLOAD_FILE_TYPE_BM)
                            }

                            TransactionFieldEvent.ViewAttachmentDocument -> {
                            }
                        }
                    },
                    viewModel = viewModel,
                    actions = null,
                )

                Spacer(modifier = Modifier.height(24.dp))
            }

            // Buttons
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .padding(horizontal = 16.dp, vertical = 20.dp),
            ) {
                termState?.let { ruleTerm ->
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(bottom = 20.dp)
                            .safeClickable {
                                viewModel.onChangeAgreeDisbursement()
                            },
                    ) {
                        Image(
                            painter = painterResource(
                                id = if (isAgreeDisbursement) {
                                    R.drawable.ic_checkbox
                                } else {
                                    R.drawable.ic_uncheckbox
                                },
                            ),
                            contentDescription = null,
                        )

                        Text(
                            text = ruleTerm.first,
                            style = getComposeFont(
                                4,
                                14.sp,
                                AppColors.contentSecondary,
                            ),
                            modifier = Modifier.padding(start = 8.dp),
                        )
                    }
                }

                Row(modifier = Modifier.fillMaxWidth()) {
                    // Từ chối button
                    BaseButton(
                        modifier = Modifier
                            .weight(1f)
                            .safeClickable {
                                // từ chối thì k cần xác nhận
//                                if (termState != null && !isAgreeDisbursement){
//                                    actions.onErrorClick(termState?.second ?: "")
//                                } else
                                if (isBatchFile && hasSelections) {
                                    actions.onBatchRejectClick()
                                } else {
                                    actions.onRejectClick()
                                }
                            },
                        text = "Từ chối",
                        isCancelButton = true,
                    )

                    Spacer(modifier = Modifier.width(16.dp))

                    // Duyệt button (show different text for batch files)
                    BaseButton(
                        modifier = Modifier
                            .weight(1f)
                            .safeClickable {
                                if (termState != null && !isAgreeDisbursement) {
                                    actions.onErrorClick(termState?.second ?: "")
                                } else if (isBatchFile && hasSelections) {
                                    actions.onBatchApproveClick()
                                } else {
                                    actions.onApproveClick()
                                }
                            },
                        text = "Duyệt",
                    )
                }
            }
        }
    }
}
