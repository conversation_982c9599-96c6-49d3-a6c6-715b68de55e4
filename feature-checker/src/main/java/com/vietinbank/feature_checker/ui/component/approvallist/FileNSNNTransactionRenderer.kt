package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import javax.inject.Inject

class FileNSNNTransactionRenderer @Inject constructor() : ITransactionContentRenderer {
    @Composable
    override fun RenderContent(
        transaction: TransactionListDomain,
        isSelectionMode: <PERSON>olean,
        isSelected: Boolean,
        onChangeClick: () -> Unit,
    ) {
//        Column(modifier = Modifier.fillMaxWidth()) {
//            // Transaction type and amount row
//            Row(
//                modifier = Modifier.fillMaxWidth(),
//                horizontalArrangement = Arrangement.SpaceBetween,
//                verticalAlignment = Alignment.CenterVertically,
//            ) {
//                // Tên giao dịch (căn trái)
//                BaseText(
//                    text = transaction.fileName.trim(),
//                    color = AppColors.blue02,
//                    textSize = 16.sp,
//                    fontCus = 5,
//                    maxLines = 2,
//                    overflow = TextOverflow.Ellipsis,
//                    modifier = Modifier.weight(1f, fill = false),
//                )
//
//                Spacer(modifier = Modifier.height(4.dp))
//
//                // Row chứa amount+ccy và icon (căn phải)
//                Row(
//                    verticalAlignment = Alignment.CenterVertically,
//                ) {
//                    BaseText(
//                        text = Utils.g().getDotMoneyHasCcy(
//                            transaction.amount.getAmountServer(),
//                            transaction.ccy,
//                        ),
//                        color = AppColors.primaryRed,
//                        textSize = 16.sp,
//                        fontCus = 1,
//                    )
//
//                    Spacer(modifier = Modifier.width(8.dp))
//
//                    Icon(
//                        painter = painterResource(id = R.drawable.ic_more),
//                        contentDescription = "Menu",
//                        tint = Color.Unspecified,
//                        modifier = Modifier.size(24.dp),
//                    )
//                }
//            }
//
//            Spacer(modifier = Modifier.height(4.dp))
//
//            // Chi tiết giao dịch - không bị ảnh hưởng bởi icon ở trên
//            transaction.details.forEach { detail ->
//                if (detail.label.isNotEmpty()) {
//                    KeyValueRow(label = detail.label, value = detail.value)
//                    Spacer(modifier = Modifier.height(4.dp))
//                } else {
//                    Row(
//                        modifier = Modifier.fillMaxWidth(),
//                        horizontalArrangement = Arrangement.End,
//                    ) {
//                        BaseText(
//                            text = detail.value,
//                            color = AppColors.textPrimary,
//                            textSize = 14.sp,
//                            textAlign = TextAlign.End,
//                            maxLines = 3,
//                            overflow = TextOverflow.Ellipsis,
//                        )
//                    }
//                    Spacer(modifier = Modifier.height(4.dp))
//                }
//            }
//        }
    }
}