package com.vietinbank.feature_checker.ui.component.approvallist

import androidx.annotation.ColorInt
import com.vietinbank.core_common.constants.StatusType
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.checker.TransactionDetailItem
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.R
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Created by vandz on 9/4/25.
 */

@Singleton
class TransactionRendererFactory @Inject constructor(
    private val resourceProvider: IResourceProvider,
    private val transferTransactionRenderer: TransferTransactionRenderer,
    private val newTransferTransactionRenderer: NewTransferTransactionRenderer,
    private val paymentTransactionRenderer: PaymentOrderTransactionRenderer,
    private val salaryTransactionRenderer: SalaryTransactionRenderer,
    private val bulkTransactionRenderer: BulkTransactionRenderer,
    private val traceTransactionRenderer: NewTraceTransactionRenderer,
    private val disbursementTransactionRenderer: DisbursementTransactionRenderer,
    private val guaranteeTransactionRenderer: GuaranteeTransactionRenderer,
    private val customInlandTransactionRenderer: CustomInlandTransactionRenderer,
    private val fileNSNNTransactionRenderer: FileNSNNTransactionRenderer,
    private val infrastructureTransactionRenderer: InfrastructureTransactionRenderer,
    private val transferForeignTransactionRenderer: TransferForeignTransactionRenderer,
    private val unLockUserTransactionRenderer: NewUnLockUserTransactionRenderer,
) {

    fun getRenderer(tranType: String, useNewLayout: Boolean = false): ITransactionContentRenderer {
        return when (tranType) {
            "in", "ou", "np", "sn" -> if (useNewLayout) newTransferTransactionRenderer else transferTransactionRenderer
            "pm", "BILL" -> paymentTransactionRenderer
            "sx", "sl", "slo" -> salaryTransactionRenderer
            "hu", "ba" -> bulkTransactionRenderer
            "tr" -> traceTransactionRenderer
            "tx" -> customInlandTransactionRenderer
            "btx" -> fileNSNNTransactionRenderer
            "IF" -> infrastructureTransactionRenderer
            "fx", "fxr" -> transferForeignTransactionRenderer
            Tags.TYPE_DISBURSEMENT_ONLINE.lowercase() -> disbursementTransactionRenderer
            "go", "goc", "gor" -> guaranteeTransactionRenderer
            Tags.TYPE_GROUP_UNLOCK_USER.lowercase(), Tags.TYPE_GROUP_RESET_PASSWORD.lowercase() -> unLockUserTransactionRenderer
            else -> if (useNewLayout) newTransferTransactionRenderer else transferTransactionRenderer
        }
    }

    fun getRendererTitle(tranType: String): String {
        // Handle composite tranTypes like CHECKER_TRANTYPE_CT = "pm,in,ou,np,sn"
        return when {
            // Check for CHECKER_TRANTYPE_CT pattern
            tranType.lowercase() == Tags.CHECKER_TRANTYPE_CT.lowercase() || tranType.contains(",") && tranType.lowercase()
                .split(",").all {
                    it in listOf("pm", "in", "ou", "np", "sn")
                } -> "Chuyển tiền"

            // Single transfer types
            tranType.lowercase() == "in" -> "Chuyển tiền"
            tranType.lowercase() == "ou" -> "Chuyển tiền"
            tranType.lowercase() == "np" -> "Chuyển tiền"
            tranType.lowercase() == "pm" -> "Chuyển tiền" // TYPE_PAYMENT_ORDER_TRANSFER
            tranType.lowercase() == "sn" -> "Chuyển tiền" // Split transfer

            // Salary types
            tranType.lowercase() == "slo" -> "Chi lương tự động"
            tranType.lowercase() == "sl" -> "Chi lương qua ngân hàng"
            tranType.lowercase() == "sx" -> "Chi lương ngoại tệ"

            // Batch/bulk types
            tranType.lowercase() == "hu" -> "Chuyển tiền theo lô"
            tranType.lowercase() == "ba" -> "Chuyển tiền theo file"

            // Tax and fees
            tranType.lowercase() == "tx" -> "Thuế Nội địa/Hải quan"
            tranType.lowercase() == "btx" -> "Nộp NSNN theo file"
            tranType.lowercase() == "if" -> "Nộp phí hạ tầng"

            // Foreign currency
            tranType.lowercase() in listOf("fx", "fxr") -> "Chuyển tiền ngoại tệ"

            // Bills
            tranType.lowercase() == "bill" -> "Thanh toán hóa đơn"

            // Special types
            tranType.lowercase() == "tr" -> "Tra soát"
            tranType.lowercase() == "do" -> "Giải ngân Online"
            tranType.lowercase() == "go" -> "Phát hành bảo lãnh Online"
            tranType.lowercase() == "goc" -> "Sửa đổi bảo lãnh Online"
            tranType.lowercase() == "gor" -> "Giải tỏa bảo lãnh Online"
            tranType.lowercase() == Tags.TYPE_DISBURSEMENT_ONLINE.lowercase() -> "Giải ngân Online"
            tranType.lowercase() == Tags.TYPE_GROUP_GUARANTEE_ISSUE.lowercase() -> "Phát hành bảo lãnh Online"
            tranType.lowercase() == Tags.TYPE_GROUP_GUARANTEE_AMEND.lowercase() -> "Sửa đổi bảo lãnh Online"
            tranType.lowercase() == Tags.TYPE_GROUP_GUARANTEE -> "Giải tỏa bảo lãnh Online"
            tranType.lowercase() in listOf(
                Tags.TYPE_GROUP_UNLOCK_USER.lowercase(),
                Tags.TYPE_GROUP_RESET_PASSWORD.lowercase(),
            ) -> "Duyệt yêu cầu từ người dùng"

            // Default - return generic title instead of empty string
            else -> "Giao dịch"
        }
    }

    fun getRenderSubType(serviceType: String?): List<Pair<String, String?>> {
        return when (serviceType) {
            Tags.TYPE_GROUP_TRANSFER -> {
                listOf(
                    resourceProvider.getString(R.string.account_detail_transaction_all) to "in,ou,np,pm,sn",
                    resourceProvider.getString(R.string.feature_checker_transaction_type_internal) to Tags.TYPE_GROUP_TRANSFER_IN.lowercase(),
                    resourceProvider.getString(R.string.maker_transfer_dashboard_np247) to Tags.TYPE_GROUP_TRANSFER_NAPAS.lowercase(),
                    resourceProvider.getString(R.string.maker_transfer_dashboard_normal) to Tags.TYPE_GROUP_TRANSFER_OUT.lowercase(),
                    resourceProvider.getString(R.string.feature_checker_transaction_type_payment_order) to Tags.TYPE_GROUP_PAYMENT.lowercase(),
                )
            }

            Tags.TYPE_GROUP_TRANSFER_CTTF300 -> {
                listOf(
                    resourceProvider.getString(R.string.approve_file_300) to Tags.TYPE_GROUP_TRANSFER_CTTF300.lowercase(),
                    resourceProvider.getString(R.string.approve_file_5000) to Tags.TYPE_GROUP_TRANSFER_CTTF5000.lowercase(),
                )
            }

            else -> emptyList()
        }
    }

    private fun processTransferTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        if (!domain.process_time.isNullOrEmpty()) {
            details.add(
                0,
                TransactionDetailItem(
                    label = domain.process_time.toString(),
                    value = domain.process_status ?: "",
                    iconResource = com.vietinbank.feature_checker.R.drawable.ic_clock,
                    isWarning = true,
                ),
            )
        }

        // Thông tin người gửi và tài khoản
        domain.toAccountNo?.let {
            details.add(
                TransactionDetailItem(
                    "Tài khoản thụ hưởng",
                    it,
                    subValue = domain.receiveName,
                ),
            )
        }

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        // Nội dung giao dịch
        domain.remark?.let {
            details.add(TransactionDetailItem("Nội dung", it))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        return details
    }

    private fun processTransferForeignTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        // Thông tin người gửi và tài khoản
        domain.beneficiaryAccount?.let {
            details.add(
                TransactionDetailItem(
                    "Số tài khoản/IBAN người hưởng",
                    it,
                    subValue = domain.beneficiaryAccount,
                ),
            )
        }

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        // Nội dung giao dịch
        domain.remark?.let {
            details.add(TransactionDetailItem("Nội dung", it))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        domain.valueDate?.let {
            details.add(TransactionDetailItem("Ngày giá trị", it))
        }

        domain.valueDate?.let {
            details.add(TransactionDetailItem("Quá ngày giá trị", it))
        }

        return details
    }

    // Nộp NSNN theo file
    private fun processFileNSNNTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số file ID", it))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        domain.countTransaction?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        return details
    }

    // Nộp phí hạ tầng cảng biển
    private fun processInfrastructureTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        domain.declareNumber?.let {
            details.add(TransactionDetailItem("Số tờ khai/QĐ/TB", it))
        }

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        domain.payname?.let {
            details.add(TransactionDetailItem("Đơn vị nộp thuế", it))
        }

        domain.providerCode?.let {
            details.add(
                TransactionDetailItem(
                    "ID chứng từ",
                    if ("975" == domain.providerCode) {
                        domain.invoiceId ?: ""
                    } else {
                        domain.docId ?: ""
                    },
                ),
            )
        }

        domain.providerCode?.let {
            details.add(TransactionDetailItem("Đơn vị thu phí", it + " - " + domain.providerName))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        return details
    }

    private fun processPaymentTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        domain.toAccountNo?.let {
            details.add(
                TransactionDetailItem(
                    "Tài khoản thụ hưởng",
                    it,
                    subValue = domain.receiveName,
                ),
            )
        }

        domain.remark?.let {
            details.add(TransactionDetailItem("Nội dung", it))
        }

        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        return details
    }

    private fun processDefaultTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        // Thông tin người gửi và tài khoản
        domain.receiveName?.let {
            details.add(TransactionDetailItem("Tài khoản thụ hưởng", it))
        }

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        // Nội dung giao dịch
        domain.remark?.let {
            details.add(TransactionDetailItem("Nội dung", it))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        return details
    }

    // tra soat
    private fun processTraceTransactionDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        domain.referenceNo?.let {
            details.add(TransactionDetailItem(resourceProvider.getString(R.string.checker_trace_list_transaction_number), it))
        }

        domain.tranSubTypeName?.let {
            details.add(TransactionDetailItem(resourceProvider.getString(R.string.account_detail_transaction_type), it))
        }

        domain.remark?.let {
            details.add(TransactionDetailItem(resourceProvider.getString(R.string.transaction_label_content), it))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem(resourceProvider.getString(R.string.manager_detail_create_time), it))
        }

        return details
    }

    // Nộp NSNN
    private fun processCustomsInlandDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()

        domain.declareNumber?.let {
            details.add(TransactionDetailItem("Số tờ khai/QĐ/TB", it))
        }

        domain.mtId?.let {
            details.add(TransactionDetailItem("Số giao dịch", it))
        }

        domain.payname?.let {
            details.add(TransactionDetailItem("Đơn vị nộp thuế", it))
        }

        domain.treasuryCode?.let {
            details.add(TransactionDetailItem("Mã KBNN", it + "-" + (domain.treasuryName ?: "")))
        }

        // Ngày thực hiện
        domain.createdDate?.let {
            details.add(TransactionDetailItem("Ngày tạo", it))
        }

        return details
    }

    // giai ngan ngoai te online
    private fun processDisbursementOnlineDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()
        domain.mtId?.let { details.add(TransactionDetailItem("Số giao dịch", it)) }
        domain.disbursementDate?.let { details.add(TransactionDetailItem("Ngày đề nghị", it)) }
        domain.loanRate?.let {
            details.add(
                TransactionDetailItem(
                    "Lãi suất vay",
                    it.plus("%/năm"),
                ),
            )
        }
        domain.loanTerm?.let { details.add(TransactionDetailItem("Thời hạn trả nợ", it)) }
        domain.createdDate?.let { details.add(TransactionDetailItem("Ngày tạo", it)) }
        return details
    }

    // giai toa + phat hanh + sua dôi bao lanh
    private fun processGuaranteeDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()
        domain.mtId?.let { details.add(TransactionDetailItem("Số giao dịch", it)) }
        when (domain.tranType) {
            "go", "gor" -> {
                details.add(
                    TransactionDetailItem(
                        "Loại bảo lãnh",
                        domain.documentTypeName ?: when (domain.documentType) {
                            "1" -> "Bảo lãnh tiền ứng trước"
                            "2" -> "Bảo lãnh vay vốn"
                            "3" -> "Bảo lãnh thanh toán"
                            "4" -> "Bảo lãnh dự thầu"
                            "5" -> "Bảo lãnh thực hiện hợp đồng"
                            "6" -> "Bảo lãnh bảo đảm chất lượng sản phẩm"
                            "7" -> "Bảo lãnh trả tiền ứng trước"
                            "8" -> "Bảo lãnh khác"
                            else -> ""
                        },
                    ),
                )
            }

            "goc" -> {
                val typeChangeDesc = StringBuilder()
                if (true == domain.typeChange?.contains("1")) {
                    typeChangeDesc.append("\nThời gian hiệu lực của bảo lãnh")
                }
                if (true == domain.typeChange?.contains("2")) {
                    typeChangeDesc.append("\nSố tiền bảo lãnh")
                }
                if (true == domain.typeChange?.contains("3")) {
                    typeChangeDesc.append("\nCác nội dung khác")
                }
                details.add(
                    TransactionDetailItem(
                        "Loại sửa đổi",
                        typeChangeDesc.toString().replaceFirst("\n".toRegex(), ""),
                    ),
                )
            }

            else -> {}
        }
        domain.beneficiaryName?.let { details.add(TransactionDetailItem("Bên nhận bảo lãnh", it)) }
        domain.creator?.let { details.add(TransactionDetailItem("Người tạo", it)) }
        domain.createDateDesc?.let { details.add(TransactionDetailItem("Ngày tạo", it)) }
        return details
    }

    // mo khoa nguoi dung & cap lai mat khau
    private fun processUnLockUserDetails(domain: TransactionListDomain): List<TransactionDetailItem> {
        val details = mutableListOf<TransactionDetailItem>()
        domain.creator?.let { details.add(TransactionDetailItem("Tên truy cập", it)) }
        domain.createdDate?.let { details.add(TransactionDetailItem("Ngày tạo", it)) }
        return details
    }

    @ColorInt
    private fun getColorRes(statusCode: String?): Int {
        return when (StatusType.fromValue(statusCode)) {
            StatusType.SUCCESS, StatusType.DISBURSED_OF_DOCUMENTS -> {
                R.color.foundation_state_success
            }

            StatusType.KTT_CTK_REJECT, StatusType.DELETED_13, StatusType.DELETED_93, StatusType.ERROR, StatusType.USER_DELETE_DOCUMENTS, StatusType.USER_CANCEL_DOCUMENTS -> {
                R.color.foundation_state_error
            }

            else -> {
                R.color.foundation_state_warning
            }
        }
    }
}