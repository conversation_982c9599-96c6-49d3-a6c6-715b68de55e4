package com.vietinbank.feature_checker.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.TermContent
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseEditText
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.InputFilters
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.compose.NextApproverBottomSheet
import java.io.File

/**
 * Created by vandz on 11/3/25.
 */
@Composable
fun ConfirmCheckerScreen(
    uiModel: ConfirmUiModel,
    termState: TermContent?,
    isAgreeDisbursement: Boolean,
    pdfFile: File?,
    hasSoftOTP: Boolean = false,
    hasKeypass: Boolean = false,
    hasSign: Boolean = false,
    isReject: Boolean = false,
    selectedAuthMethod: String,
    onAuthMethodSelected: (String) -> Unit,
    onConfirmClick: (String, String, String, String) -> Unit,
    onBackClick: () -> Unit,
    onHomeClick: () -> Unit,
    hasNextApprovers: Boolean = false,
    nextApproverInfo: String = "",
    nextApprovers: List<ApproverDomains> = emptyList(),
    selectedNextApprover: List<ApproverDomains> = emptyList(),
    isNextApproverBottomSheetVisible: Boolean = false,
    onNextApproverClick: () -> Unit,
    onToggleApprover: (ApproverDomains) -> Unit,
    onNextApproverBottomSheetDismiss: () -> Unit,
    onFieldEvent: ((TransactionFieldEvent) -> Unit)? = null,
    onChangeAgreeDisbursement: (Boolean) -> Unit,
    onDownloadPdf: (String) -> Unit,
) {
    // Lấy focus manager và keyboard controller để xử lý ẩn bàn phím
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    // Trạng thái để hiển thị Dialog PDF
    var pdfUrlToShow by remember { mutableStateOf<String?>(null) }
    val context = LocalContext.current

    // Hàm ẩn bàn phím khi click ra ngoài
    fun hideKeyboard() {
        focusManager.clearFocus()
        keyboardController?.hide()
    }

    var reasonReject by remember { mutableStateOf("") }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.itemBackground)
            .safeClickable { hideKeyboard() },
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
        ) {
            val appBarActions = mutableListOf<AppBarAction>()
            appBarActions.add(
                AppBarAction(
                    icon = com.vietinbank.core_ui.R.drawable.ic_home,
                    contentDescription = "HOME",
                    onClick = { onHomeClick.invoke() },
                    tint = Color.White,
                ),
            )
            BaseAppBar(
                title = "Xác nhận",
                onBackClick = onBackClick,
                actions = appBarActions,
            )

            // Content
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                // Xử lý items theo section
                var currentSectionItems = mutableListOf<ConfirmItem>()
                var currentSectionTitle = ""

                uiModel.items.forEach { item ->
                    when (item.type) {
                        ConfirmItemType.HEADER -> {
                            // Hiển thị section trước đó nếu có
                            if (currentSectionItems.isNotEmpty()) {
                                Section(
                                    title = currentSectionTitle,
                                    items = currentSectionItems,
                                    onFieldEvent = onFieldEvent,
                                )
                                Spacer(modifier = Modifier.height(16.dp))
                                currentSectionItems = mutableListOf()
                            }
                            currentSectionTitle = item.title
                        }

                        else -> currentSectionItems.add(item)
                    }
                }
                // Hiển thị section cuối cùng
                if (currentSectionItems.isNotEmpty()) {
                    Section(
                        title = currentSectionTitle,
                        items = currentSectionItems,
                        onFieldEvent = onFieldEvent,
                    )
                }

                // Hiển thị nội dung từ chối
                if (isReject) {
                    Spacer(modifier = Modifier.height(16.dp))
                    SectionHeader(title = "Nội dung từ chối")
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(10.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                    ) {
                        BaseEditText(
                            value = reasonReject,
                            onValueChange = {
                                reasonReject = if (it.length > 146) {
                                    it.substring(0, 146)
                                } else {
                                    it
                                }
                            },
                            hint = "Nội dung *",
                            showClearButton = true,
                            fontCus = 0,
                            hintColor = AppColors.grey08,
                            textColor = AppColors.grey08,
                            clearIconTint = AppColors.grey08,
                            borderColor = Color.White,
                            backgroundColor = Color.White,
                            isCounter = true,
                            maxLength = 146,
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 56.dp),
                            pattern = InputFilters.REJECT_PATTERN,
                        )
                    }
                }

                if (hasNextApprovers) {
                    SectionHeader(title = "Người phê duyệt tiếp theo")
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(10.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                                .safeClickable { onNextApproverClick() },
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween,
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = if (selectedNextApprover.isEmpty()) {
                                        "Theo đăng ký với VietinBank"
                                    } else {
                                        "Đã chọn ${selectedNextApprover.size} người phê duyệt"
                                    },
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = Color.Gray,
                                )

                                // Hiển thị tên người được chọn nếu không quá nhiều
                                if (selectedNextApprover.isNotEmpty() && selectedNextApprover.size <= 3) {
                                    Text(
                                        text = selectedNextApprover.joinToString(", ") { it.username + "_" + it.fullname },
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFF006594),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                    )
                                }
                            }

                            Icon(
                                imageVector = Icons.Default.KeyboardArrowDown,
                                contentDescription = "Select approver",
                                tint = Color.Gray,
                            )
                        }

                        // Bottom sheet for approver selection
                        NextApproverBottomSheet(
                            isVisible = isNextApproverBottomSheetVisible,
                            approvers = nextApprovers,
                            selectedApprover = selectedNextApprover,
                            onDismiss = onNextApproverBottomSheetDismiss,
                            onToggleApprover = onToggleApprover,
                        )

                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
                // Phương thức xác thực
                if (hasSoftOTP || hasKeypass || hasSign) {
                    SectionHeader(title = "Phương thức xác thực")
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(10.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectableGroup()
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                        ) {
                            // Tính toán số lượng item hiển thị để đặt weight phù hợp
                            val visibleItemCount =
                                listOf(hasSoftOTP, hasKeypass, hasSign).count { it }

                            // Soft OTP
                            if (hasSoftOTP) {
                                AuthMethodRadioButton(
                                    text = "Soft OTP",
                                    selected = selectedAuthMethod == "softotp",
                                    onClick = { onAuthMethodSelected("softotp") },
                                    modifier = Modifier.weight(1f),
                                )
                            }

                            // Keypass
                            if (hasKeypass) {
                                AuthMethodRadioButton(
                                    text = "Keypass",
                                    selected = selectedAuthMethod == "keypass",
                                    onClick = { onAuthMethodSelected("keypass") },
                                    modifier = Modifier.weight(1f),
                                )
                            }

                            // Ký số
                            if (hasSign) {
                                AuthMethodRadioButton(
                                    text = "Ký số",
                                    selected = selectedAuthMethod == "sign",
                                    onClick = { onAuthMethodSelected("sign") },
                                    modifier = Modifier.weight(1f),
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                if (!isReject) {
                    termState?.let { ruleTerm ->
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(Color.White)
                                .padding(horizontal = 16.dp, vertical = 20.dp),
                        ) {
                            Row(
                                Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 20.dp)
                                    .safeClickable {
                                    },
                            ) {
                                Checkbox(
                                    checked = isAgreeDisbursement,
                                    onCheckedChange = { onChangeAgreeDisbursement(it) },
                                )

                                Column(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .padding(start = 5.dp),
                                ) {
                                    termState?.annotatedText?.let {
                                        AnnotatedText(
                                            segments = it,
                                            onClickUrl = { url ->
                                                pdfUrlToShow = url
                                            }, // Gán URL PDF vào state
                                        )
                                    }
                                }
                            }
                        }
                    }
                    pdfUrlToShow?.let { url ->

                        // Gọi tải file khi url thay đổi
                        LaunchedEffect(url) {
                            onDownloadPdf(url)
                        }

                        pdfFile?.let {
                            PdfViewerDialog(
                                pdfFile = it,
                                title = "Nội dung quy định mua bán ngoại tệ",
                                onDismiss = { pdfUrlToShow = null },
                                confirmButtonText = "Đóng",
                                dialogModifier = Modifier
                                    .fillMaxWidth(0.95f)
                                    .fillMaxHeight(0.9f),
                            )
                        }
                    }
                }
                // Xác nhận button
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .navigationBarsPadding()
                        .padding(16.dp),
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFF0066B3), // Gradient xanh dương
                                        Color(0xFF7B1FA2), // Gradient tím
                                    ),
                                ),
                                shape = RoundedCornerShape(10.dp),
                            )
                            .safeClickable {
                                onConfirmClick(
                                    if (selectedAuthMethod == "softotp") {
                                        Tags.SOTP
                                    } else if (selectedAuthMethod == "keypass") {
                                        Tags.KEYPASS
                                    } else {
                                        Tags.VNPT_SMARTCA
                                    },
                                    reasonReject,
                                    termState?.fallbackMessage ?: "",
                                    if (termState != null && !isAgreeDisbursement && !isReject) Tags.TERMSTATE else "",
                                )
                            },
                        contentAlignment = Alignment.Center,
                    ) {
                        BaseText(
                            text = "Xác nhận",
                            color = Color.White,
                            textSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }
            }
        }
    }
}

/**
 * Hiển thị một section
 */
@Composable
private fun Section(
    title: String,
    items: List<ConfirmItem>,
    onFieldEvent: ((TransactionFieldEvent) -> Unit)? = null,
) {
    SectionHeader(title = title)
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(10.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            items.forEachIndexed { index, item ->
                when (item.type) {
                    ConfirmItemType.KEY_VALUE -> {
                        KeyValueRow(
                            item = item,
                            isLast = index == items.size - 1,
                            onItemClick = { clickedItem ->
                                // Use the handler passed from parent
                                clickedItem.clickEvent?.let { event ->
                                    onFieldEvent?.invoke(event)
                                }
                            },
                        )
                    }

                    ConfirmItemType.SPACER -> {
                        Spacer(modifier = Modifier.height(item.height.dp))
                    }

                    ConfirmItemType.DIVIDER -> {
                        Divider(
                            color = AppColors.borderColor,
                            thickness = 0.5.dp,
                        )
                    }

                    else -> {} // Đã xử lý HEADER ở bên ngoài
                }
            }
        }
    }
}

/**
 * Hiển thị key-value row
 */
@Composable
private fun KeyValueRow(
    item: ConfirmItem,
    isLast: Boolean,
    onItemClick: ((ConfirmItem) -> Unit)? = null,
) {
    val clickModifier = if (item.isClickable && onItemClick != null) {
        Modifier.safeClickable { onItemClick(item) }
    } else {
        Modifier
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(clickModifier)
            .padding(vertical = 8.dp),
    ) {
        if (item.label.isNotEmpty()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                BaseText(
                    text = item.label,
                    color = Color.Gray,
                    textSize = 14.sp,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.widthIn(max = 200.dp),
                )

                BaseText(
                    text = item.value,
                    color = when {
                        item.isClickable -> AppColors.primary
                        item.isHighlighted -> Color(0xFF0066B3)
                        else -> Color.Gray
                    },
                    textSize = 14.sp,
                    fontWeight = if (item.isHighlighted) FontWeight.Bold else FontWeight.Normal,
                    textAlign = TextAlign.End,
                    textDecoration = if (item.isClickable) TextDecoration.Underline else null,
                )
            }

            // Hiển thị subValue nếu có
            if (!item.subValue.isNullOrEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    // Spacer chiếm vị trí của label, đảm bảo subValue chỉ hiển thị bên phải
                    Spacer(modifier = Modifier.widthIn(max = 200.dp))

                    BaseText(
                        text = item.subValue.toString(),
                        color = Color.Gray,
                        textSize = 14.sp,
                        textAlign = TextAlign.End,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.widthIn(max = 180.dp),
                    )
                }
            }
        } else {
            // Chỉ hiển thị value
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
            ) {
                BaseText(
                    text = item.value,
                    color = if (item.isHighlighted) Color(0xFF0066B3) else Color.Gray,
                    textSize = 14.sp,
                    fontWeight = if (item.isHighlighted) FontWeight.Bold else FontWeight.Normal,
                    textAlign = TextAlign.End,
                )
            }
        }

        if (!isLast) {
            Divider(
                color = AppColors.borderColor,
                thickness = 0.5.dp,
                modifier = Modifier.padding(top = 8.dp),
            )
        }
    }
}

/**
 * Radio button cho phương thức xác thực
 */
@Composable
private fun AuthMethodRadioButton(
    text: String,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .height(48.dp)
            .selectable(
                selected = selected,
                onClick = onClick,
                role = Role.RadioButton,
            )
            .padding(horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        RadioButton(
            selected = selected,
            onClick = onClick,
            colors = RadioButtonDefaults.colors(
                selectedColor = Color(0xFF0066B3),
                unselectedColor = Color.Gray,
            ),
        )
        Spacer(modifier = Modifier.width(8.dp))
        BaseText(
            text = text,
            color = Color.Gray,
            textSize = 14.sp,
            modifier = Modifier.clickable { onClick() },
        )
    }
}

@Composable
fun AnnotatedText(
    segments: List<Pair<String, String?>>,
    onClickUrl: (String) -> Unit,
) {
    val annotatedString = remember(segments) {
        buildAnnotatedString {
            var start = 0
            for ((text, url) in segments) {
                append(text)
                val end = start + text.length

                if (url != null) {
                    addStyle(
                        style = SpanStyle(
                            color = Color.Blue,
                            textDecoration = TextDecoration.Underline,
                        ),
                        start = start,
                        end = end,
                    )
                    addStringAnnotation(
                        tag = "URL",
                        annotation = url,
                        start = start,
                        end = end,
                    )
                }

                start = end
            }
        }
    }

    val textLayoutResult = remember { mutableStateOf<TextLayoutResult?>(null) }

    // để tự kiểm soát gesture((bắt sự kiện tap theo vị trí chữ) và layout, truy cập TextLayoutResult
    BasicText(
        text = annotatedString,
        style = MaterialTheme.typography.bodyMedium,
        onTextLayout = { textLayoutResult.value = it },
        modifier = Modifier
            .safeClickable { } // Để nhận gesture
            .pointerInput(Unit) {
                detectTapGestures { offset ->
                    textLayoutResult.value?.let { layoutResult ->
                        val position = layoutResult.getOffsetForPosition(offset)
                        annotatedString
                            .getStringAnnotations("URL", position, position)
                            .firstOrNull()
                            ?.let { onClickUrl(it.item) }
                    }
                }
            },
    )
}
