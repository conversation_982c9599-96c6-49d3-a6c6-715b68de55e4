package com.vietinbank.feature_checker.ui.component.success.renderer

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAvatarShimmer
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.safeClickable
import kotlinx.coroutines.delay
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Renderer for payment transactions (pm)
 * Renders the success screen content for payment transactions - using TransactionDomain directly
 */
class PaymentSuccessRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionSuccessRenderer {

    @Composable
    override fun RenderContent(
        transaction: TransactionDomain,
        approveResult: ApproveDomain?,
        confirmType: String,
        isRejected: Boolean,
        imageLoader: CoilImageLoader?,
        getBankIconUrl: ((String?) -> String?)?,
        onFileClick: ((FileTransactionDomain) -> Unit)?,
        onEmojiSelected: (Int) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
        ) {
            // Status badge - parse and use only main message from ApproveDomain
            val (statusMessage, rejectReason) = when {
                isRejected -> {
                    val fullMessage = approveResult?.messageReject ?: stringResource(R.string.checker_multiple_reject_success_dialog)
                    // Parse message and reject reason if separated by "||"
                    if (fullMessage.contains("||")) {
                        val parts = fullMessage.split("||")
                        parts[0] to parts.getOrNull(1)
                    } else {
                        fullMessage to null
                    }
                }
                else -> (approveResult?.messageApprove ?: stringResource(R.string.feature_maker_transfer_success)) to null
            }

            FoundationStatus(
                statusMessage = statusMessage,
                statusCode = if (isRejected) Status.Fail else Status.Success,
                modifier = Modifier,
            )

            // Account transfer section
            AccountTransferSection(
                transaction = transaction,
                imageLoader = imageLoader,
                getBankIconUrl = getBankIconUrl,
            )

            // Divider
            FoundationDivider(
                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
            )

            // Transaction details
            TransactionDetailsSection(
                transaction = transaction,
                approveResult = approveResult,
                isRejected = isRejected,
                rejectReason = rejectReason,
                onFileClick = onFileClick,
            )
        }
    }

    @Composable
    private fun AccountTransferSection(
        transaction: TransactionDomain,
        imageLoader: CoilImageLoader? = null,
        getBankIconUrl: ((String?) -> String?)? = null,
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            // Source account - use directly from transaction
            if (!transaction.fromAccountNo.isNullOrEmpty()) {
                AccountInfoRow(
                    accountName = transaction.fromAccountName ?: "",
                    accountNumber = "${transaction.fromAccountNo} - ${transaction.currency ?: "VND"}",
                    imageLoader = imageLoader,
                    bankIconUrl = getBankIconUrl?.invoke("VietinBank"),
                    bankName = null,
                )
            }

            // Only show divider and destination if toAccount exists
            if (!transaction.toAccountNo.isNullOrEmpty()) {
                // Transfer divider with text
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding8),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.transaction_label_transfer_to),
                        style = FDS.Typography.captionM.copy(fontWeight = FontWeight.SemiBold),
                        color = FDS.Colors.characterTertiary,
                        modifier = Modifier.padding(end = FDS.Sizer.Padding.padding8),
                    )
                    FoundationDivider(
                        modifier = Modifier.weight(1f),
                    )
                }

                // To account section
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    // Bank logo using circular container with different color
                    Box(
                        modifier = Modifier
                            .size(FDS.Sizer.Icon.icon40)
                            .clip(CircleShape),
                        contentAlignment = Alignment.Center,
                    ) {
                        // Overlay loaded icon with crossfade

                        if (!getBankIconUrl?.invoke(transaction.receiveBankName).isNullOrEmpty()) {
                            imageLoader?.LoadUrl(
                                url = getBankIconUrl.invoke(transaction.receiveBankName),
                                isCache = true,
                                placeholderRes = null,
                                errorRes = null,
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            )
                        } else {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_account_company_24),
                                contentDescription = "",
                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                tint = FDS.Colors.characterPrimary,
                            )
                        }
                    }

                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    ) {
                        if (transaction.receiveName != null) {
                            FoundationText(
                                text = transaction.receiveName ?: "",
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterPrimary,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }

                        val toDisplayName =
                            (
                                transaction.receiveBankName?.plus(" - ${transaction.toAccountNo ?: ""}")
                                    ?: ""
                                ).ifEmpty {
                                transaction.toAccountNo ?: ""
                            }
                        if (toDisplayName.isNotEmpty()) {
                            FoundationText(
                                text = toDisplayName,
                                style = FDS.Typography.captionL,
                                color = FDS.Colors.characterSecondary,
                            )
                        }
                    }
                }
            }
        }
    }

    @Composable
    private fun AccountInfoRow(
        accountName: String,
        accountNumber: String,
        imageLoader: CoilImageLoader? = null,
        bankIconUrl: String? = null,
        bankName: String? = null,
    ) {
        var isIconLoading by remember(bankIconUrl) {
            mutableStateOf(!bankIconUrl.isNullOrBlank())
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
            verticalAlignment = Alignment.Top,
        ) {
            // Bank icon
            Box(
                modifier = Modifier
                    .size(FDS.Sizer.Icon.icon40)
                    .clip(CircleShape)
                    .background(FDS.Colors.gray50),
                contentAlignment = Alignment.Center,
            ) {
                when {
                    isIconLoading && !bankIconUrl.isNullOrBlank() -> {
                        FoundationAvatarShimmer(
                            modifier = Modifier,
                            size = FDS.Sizer.Icon.icon40,
                        )
                        LaunchedEffect(bankIconUrl) {
                            delay(500)
                            isIconLoading = false
                        }
                    }
                    !bankIconUrl.isNullOrBlank() && imageLoader != null && !isIconLoading -> {
                        imageLoader.LoadUrl(
                            url = bankIconUrl,
                            isCache = true,
                            placeholderRes = null,
                            errorRes = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    }
                    else -> {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_account_company_24),
                            contentDescription = bankName,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                            tint = FDS.Colors.characterPrimary,
                        )
                    }
                }
            }

            // Account info
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                if (accountName.isNotEmpty()) {
                    FoundationText(
                        text = accountName,
                        style = FDS.Typography.bodyB2,
                        color = FDS.Colors.characterPrimary,
                    )
                }

                if (bankName != null) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(2.dp),
                    ) {
                        FoundationText(
                            text = bankName,
                            style = FDS.Typography.captionL,
                            color = FDS.Colors.characterSecondary,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                        )
                        FoundationText(
                            text = accountNumber,
                            style = if (accountName.isEmpty()) FDS.Typography.bodyB2 else FDS.Typography.captionL,
                            color = if (accountName.isEmpty()) FDS.Colors.characterPrimary else FDS.Colors.characterSecondary,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                        )
                    }
                } else {
                    FoundationText(
                        text = accountNumber,
                        style = if (accountName.isEmpty()) FDS.Typography.bodyB2 else FDS.Typography.captionL,
                        color = if (accountName.isEmpty()) FDS.Colors.characterPrimary else FDS.Colors.characterSecondary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }
        }
    }

    @Composable
    private fun TransactionDetailsSection(
        transaction: TransactionDomain,
        approveResult: ApproveDomain? = null,
        isRejected: Boolean = false,
        rejectReason: String? = null,
        onFileClick: ((FileTransactionDomain) -> Unit)? = null,
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
        ) {
            // Số giao dịch from core (if available)
            approveResult?.transactions?.firstOrNull()?.let { txResult ->
                if (!txResult.mtId.isNullOrEmpty()) {
                    TransactionDetailRow(
                        label = stringResource(R.string.transaction_label_transaction_id),
                        value = txResult.mtId ?: "",
                    )
                }
            }

            when {
                !transaction.fromAccountFeeNo.isNullOrEmpty() && !transaction.fromAccountFeeName.isNullOrEmpty() -> {
                    "${transaction.fromAccountFeeNo} - ${transaction.fromAccountFeeName}"
                }

                !transaction.fromAccountFeeNo.isNullOrEmpty() -> {
                    transaction.fromAccountFeeNo
                }

                else -> null
            }?.let { displayAccount ->
                TransactionDetailRow(
                    label = stringResource(R.string.maker_transfer_dashboard_main_account_type_D),
                    value = displayAccount,
                )
            }

            when {
                !transaction.branchNo.isNullOrEmpty() && !transaction.branchName.isNullOrEmpty() -> {
                    "${transaction.branchNo} - ${transaction.branchName}"
                }

                else -> null
            }?.let { displayBank ->
                TransactionDetailRow(
                    label = stringResource(R.string.feature_smart_ca_detail_item_branchName),
                    value = displayBank,
                )
            }

            // Nội dung
            if (!transaction.remark.isNullOrEmpty()) {
                TransactionDetailRow(
                    label = stringResource(R.string.transaction_label_content),
                    value = transaction.remark ?: "",
                )
            }

            // Phí giao dịch
            if (!transaction.feePayMethodDesc1.isNullOrEmpty()) {
                TransactionDetailRow(
                    label = stringResource(R.string.transaction_label_fee),
                    value = transaction.feePayMethodDesc1 ?: "",
                )
            }

            // Hình thức thu phí
            val feeMethodText = if (transaction.feePayMethod == "0") {
                stringResource(R.string.transaction_fee_type_external)
            } else {
                stringResource(R.string.maker_transfer_dashboard_type_BEN)
            }
            TransactionDetailRow(
                label = stringResource(R.string.transaction_label_fee_method),
                value = feeMethodText,
            )

            // Hình thức chuyển và Ngày đặt lịch
            if (transaction.process_time.isNullOrEmpty()) {
                TransactionDetailRow(
                    label = stringResource(R.string.transaction_label_transfer_type),
                    value = stringResource(R.string.transaction_transfer_immediate),
                )
            } else {
                TransactionDetailRow(
                    label = stringResource(R.string.transaction_label_transfer_type),
                    value = stringResource(R.string.transaction_transfer_scheduled),
                )
                TransactionDetailRow(
                    label = stringResource(R.string.manager_detail_appointment_date),
                    value = transaction.process_time ?: "",
                )
            }

            // Thời gian giao dịch (if available from approval)
            approveResult?.defineCreatedDate?.let { date ->
                if (date.isNotEmpty()) {
                    TransactionDetailRow(
                        label = stringResource(R.string.transaction_label_transfer_time),
                        value = date,
                    )
                }
            }

            // Reject reason (if rejected)
            if (isRejected && !rejectReason.isNullOrEmpty()) {
                TransactionDetailRow(
                    label = stringResource(R.string.reject_reason_title),
                    value = rejectReason,
                )
            }

            // File attachments
            transaction.listFile?.forEach { file ->
                if (!file.fileName.isNullOrEmpty()) {
                    TransactionDetailRow(
                        label = stringResource(R.string.maker_payment_order_dashboard_upload_file_name_title),
                        value = file.fileName ?: "",
                        isFile = true,
                        onFileClick = { onFileClick?.invoke(file) },
                    )
                }
            }
        }
    }

    @Composable
    private fun TransactionDetailRow(
        label: String,
        value: String,
        subValue: String? = null,
        isFile: Boolean = false,
        onFileClick: (() -> Unit)? = null,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .then(
                    if (isFile && onFileClick != null) {
                        Modifier.safeClickable { onFileClick() }
                    } else {
                        Modifier
                    },
                ),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            FoundationText(
                text = label,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterSecondary,
                modifier = Modifier.weight(0.45f),
            )

            Column(
                modifier = Modifier.weight(0.55f),
                horizontalAlignment = Alignment.End,
            ) {
                FoundationText(
                    text = value,
                    style = if (isFile) {
                        FDS.Typography.bodyB2Emphasized.copy(
                            textDecoration = TextDecoration.Underline,
                        )
                    } else {
                        FDS.Typography.bodyB2Emphasized
                    },
                    color = if (isFile) {
                        FDS.Colors.primary
                    } else {
                        FDS.Colors.characterPrimary
                    },
                    textAlign = TextAlign.End,
                    modifier = Modifier.fillMaxWidth(),
                )

                subValue?.let { subVal ->
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
                    FoundationText(
                        text = subVal,
                        style = FDS.Typography.captionL,
                        color = FDS.Colors.characterTertiary,
                        textAlign = TextAlign.End,
                        modifier = Modifier.fillMaxWidth(),
                    )
                }
            }
        }
    }
}
