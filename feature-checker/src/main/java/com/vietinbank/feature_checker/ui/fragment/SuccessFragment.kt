package com.vietinbank.feature_checker.ui.fragment

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_domain.models.checker.SuccessActions
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.AssessmentQualityDialog
import com.vietinbank.core_ui.base.dialog.AssessmentSuccessDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.screen.SuccessScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SuccessFragment : BaseFragment<SuccessViewModel>() {
    override val viewModel: SuccessViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    private fun onBackToListClick() {
        appNavigator.setFragmentResult(
            APPROVAL_FLOW_RESULT_KEY,
            bundleOf(
                "status" to "SUCCESS",
                "message" to "Giao dịch đã được xử lý thành công",
                "timestamp" to System.currentTimeMillis(),
                "needRefresh" to true,
            ),
        )
        appNavigator.popToApprovalList()
    }

    companion object {
        const val APPROVAL_FLOW_RESULT_KEY = "approval_flow_result"
    }

    @Composable
    override fun ComposeScreen() {
        // Collect state from ViewModel
        val successItems by viewModel.successItems.collectAsState()
        val title by viewModel.title.collectAsState()
        val actions = SuccessActions(
            onBackClick = { appNavigator.goToHome() },
            onRateServiceClick = {
                /* Xử lý đánh giá dịch vụ */
                val dialog = AssessmentQualityDialog.newInstance()
                dialog.setOnConfirmListener { point, comment ->
                    viewModel.rateCSat(Tags.CSAT_FUNC_ID_KEY, point, comment)
                }
                dialog.show(childFragmentManager, AssessmentQualityDialog::class.java.name)
            },
            onBackToListClick = { onBackToListClick() },
            onManageTransactionClick = { appNavigator.gotoFilterTransaction() },
            onDownloadClick = {},
            onShareClick = {},
        )
        AppTheme {
            title?.let {
                SuccessScreen(
                    viewModel = viewModel,
                    successItems = successItems,
                    successTitle = it,
                    actions = actions,
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupObserver()
        // Lấy dữ liệu từ bundle
        val successListJson = arguments?.getString(Tags.SUCCESS_LIST_BUNDLE)
        val successTitle = arguments?.getString(Tags.TRANSACTION_BUNDLE)
        val confirmType = arguments?.getString(Tags.CONFIRM_TYPE_BUNDLE)

        viewModel.processData(successListJson, successTitle, confirmType)

        viewModel.configCSat(Tags.CSAT_FUNC_ID_KEY)

        //  "DDA" - tk thanh toán, "LN" - tk vay, "CD" - tk tiền gửi
        // tùy thuộc vào loại giao dịch -> gọi update các loại tk tương ứng
        viewModel.forceUpdateAccountList("DDA")
    }

    /**
     * Override phương thức onBackPressed từ BaseFragment
     * để xử lý nút back vật lý theo cách riêng
     */
    override fun onBackPressed(): Boolean {
        onBackToListClick()
        // Trả về true để chỉ ra rằng fragment đã xử lý sự kiện back
        return true
    }

    private fun setupObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                // Collect error events
                launch {
                    viewModel.errorEvent.collect { errorMessage ->
                        showNoticeDialog(errorMessage)
                    }
                }

                launch {
                    viewModel.toastEvent.collect { message ->
                        // Show toast message
                        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                    }
                }

                launch {
                    viewModel.rateCSATStatus.collect {
                        AssessmentSuccessDialog.newInstance().show(
                            childFragmentManager,
                            AssessmentSuccessDialog::class.java.name,
                        )
                    }
                }

                launch {
                    viewModel.filePermissionEvent.collect { message ->
                        showNoticeDialog(message)
                    }
                }

                // THÊM: Observer cho file URL ready
                launch {
                    viewModel.fileUrlReady.collect { url ->
                        try {
                            // Mở URL trong trình duyệt
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            startActivity(intent)
                        } catch (e: Exception) {
                            showNoticeDialog("Không thể mở file: ${e.message}")
                        }
                    }
                }
            }
        }
    }
}