package com.vietinbank.feature_checker.ui.fragment

import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.account.AccountLstParams
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.SuccessTitleDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.trace_payment.ActivityLogsDomains
import com.vietinbank.core_domain.usecase.account.AccountUseCase
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.transfer.CSatUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SuccessViewModel @Inject constructor(
    val useCase: CheckerUserCase,
    override val cSatUseCase: CSatUseCase,
    private val accountUseCase: AccountUseCase,
    private val transferCacheManager: com.vietinbank.core_domain.repository.cache.ITransferCacheManager,
    override val sessionManager: ISessionManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val dataSourceProperties: DataSourceProperties,
) : CSatViewModel(cSatUseCase, sessionManager, resourceProvider, appConfig, userProf, ottSetupService) {
    private val _successItems = MutableStateFlow<List<SuccessItemDomain>>(emptyList())
    val successItems: StateFlow<List<SuccessItemDomain>> = _successItems.asStateFlow()

    // New: TransactionDomain for direct access
    private val _transaction = MutableStateFlow(TransactionDomain())
    val transaction: StateFlow<TransactionDomain> = _transaction.asStateFlow()

    // New: ApproveDomain for approval result
    private val _approveResult = MutableStateFlow<ApproveDomain?>(null)
    val approveResult: StateFlow<ApproveDomain?> = _approveResult.asStateFlow()

    private val _title = MutableStateFlow<SuccessTitleDomain?>(null)
    val title: StateFlow<SuccessTitleDomain?> = _title.asStateFlow()

    private val _confirmType = MutableStateFlow<String?>(null)
    val confirmType = _confirmType.asStateFlow()

    private val _messageStatus = MutableStateFlow<String>("")
    val messageStatus = _messageStatus.asStateFlow()

    // Approval history extracted from successItems (Plan A)
    private val _approvalHistory = MutableStateFlow<ActivityLogsDomains?>(null)
    val approvalHistory: StateFlow<ActivityLogsDomains?> = _approvalHistory.asStateFlow()

    private val _errorEvent = MutableSharedFlow<String>()
    val errorEvent: SharedFlow<String> = _errorEvent.asSharedFlow()

    private val _toastEvent = MutableSharedFlow<String>()
    val toastEvent: SharedFlow<String> = _toastEvent.asSharedFlow()

    // Add file permission event (similar to InitTransactionViewModel)
    private val _filePermissionEvent = MutableSharedFlow<String>()
    val filePermissionEvent = _filePermissionEvent.asSharedFlow()

    // Download file
    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    // Overload for FileTransactionDomain (new approach)
    fun downloadFile(file: FileTransactionDomain) {
        // Get tranType from current transaction
        val tranType = _transaction.value.tranType

        if (!file.isViewSlFile.isNullOrEmpty() && file.isViewSlFile != "Y") {
            // Emit event để Fragment show dialog
            viewModelScope.launch {
                _filePermissionEvent.emit("Quý khách không có quyền tải xuống hay chia sẻ file lương")
            }
            return
        }

        // Download với đầy đủ thông tin
        launchJob(showLoading = true) {
            val res = useCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = getFileIDFollowTranType(tranType, file),
                    mtID = file.mtId ?: "",
                    tranType = tranType,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl =
                            baseUrl +
                                Constants.MB_DOWNLOAD_FILE.replace(
                                    "{encryptStr}",
                                    fileId,
                                )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    // Keep old method for backward compatibility
    fun downloadFile(item: SuccessItemDomain) {
        val file = item.extraValue?.let {
            try {
                Utils.g().provideGson().fromJson(it, FileTransactionDomain::class.java)
            } catch (e: Exception) {
                null
            }
        }

        val tranType = item.tranType

        if (file != null && !file.isViewSlFile.isNullOrEmpty() && file.isViewSlFile != "Y") {
            // Emit event để Fragment show dialog
            viewModelScope.launch {
                _filePermissionEvent.emit("Quý khách không có quyền tải xuống hay chia sẻ file lương")
            }
            return
        }

        // Download với đầy đủ thông tin
        launchJob(showLoading = true) {
            val res = useCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = getFileIDFollowTranType(tranType, file),
                    mtID = file?.mtId ?: "",
                    tranType = tranType,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl =
                            baseUrl +
                                Constants.MB_DOWNLOAD_FILE.replace(
                                    "{encryptStr}",
                                    fileId,
                                )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    fun processData(successListJson: String?, successTitle: String?, confirmType: String?, message: String? = null) {
        try {
            message?.let { _messageStatus.value = it }
            confirmType?.let { _confirmType.value = it }

            // Try to parse as TransactionDomain first (new approach)
            successListJson?.let { json ->
                try {
                    // Attempt to parse as TransactionDomain
                    val transactionData = Utils.g().provideGson().fromJson(json, TransactionDomain::class.java)
                    if (transactionData != null && !transactionData.mtId.isNullOrEmpty()) {
                        // Successfully parsed as TransactionDomain
                        _transaction.value = transactionData

                        // Extract approval history from transaction if available
                        transactionData.activityLogs?.let { logs ->
                            _approvalHistory.value = logs
                        }

                        // Parse ApproveDomain from message parameter if it contains JSON
                        message?.let { msg ->
                            try {
                                if (msg.startsWith("{")) {
                                    val approveData = Utils.g().provideGson().fromJson(msg, ApproveDomain::class.java)
                                    _approveResult.value = approveData
                                    _messageStatus.value = approveData.messageApprove ?: approveData.messageReject ?: ""
                                }
                            } catch (e: Exception) {
                                // message is just a plain string, not JSON
                                printLog("Message is not ApproveDomain JSON: ${e.message}")
                            }
                        }
                    } else {
                        // Fallback to old approach with SuccessItemDomain list
                        parseSuccessItemList(json)
                    }
                } catch (e: Exception) {
                    // Failed to parse as TransactionDomain, try old approach
                    printLog("Failed to parse as TransactionDomain, using fallback: ${e.message}")
                    parseSuccessItemList(json)
                }
            }

            // Parse title object
            successTitle?.let {
                val titleData = Utils.g().provideGson()
                    .fromJson(it, SuccessTitleDomain::class.java)
                _title.value = titleData
            }
        } catch (e: Exception) {
            viewModelScope.launch {
                _errorEvent.emit("Lỗi xử lý dữ liệu: ${e.message}")
            }
        }
    }

    private fun parseSuccessItemList(json: String) {
        try {
            val type = object : TypeToken<List<SuccessItemDomain>>() {}.type
            val allItems = Utils.g().provideGson().fromJson<List<SuccessItemDomain>>(json, type)

            // Find embedded approval history item
            val historyItem = allItems.firstOrNull { item -> item.title == "__approval_history__" && !item.extraValue.isNullOrEmpty() }
            historyItem?.extraValue?.let { historyJson ->
                runCatching {
                    _approvalHistory.value = Utils.g().provideGson().fromJson(historyJson, ActivityLogsDomains::class.java)
                }.onFailure { e ->
                    printLog("SuccessViewModel: Failed to parse approval history: ${e.message}")
                }
            }

            // Exclude the hidden history item from UI list
            _successItems.value = allItems.filterNot { it.title == "__approval_history__" }
        } catch (e: Exception) {
            printLog("Failed to parse success item list: ${e.message}")
        }
    }

//    // danh gia cl dich vu
//    private val _rateCSATStatus = MutableSharedFlow<Resource<CSatRateDomain>?>()
//    val rateCSATStatus = _rateCSATStatus.asSharedFlow()
//    fun rateCSat(functionId: String? = null, point: String? = null, comment: String? = null) {
//        launchJob {
//            val params = CSatRateParams(
//                comment = comment,
//                functionId = functionId,
//                ratePoint = point,
//                userName = userProf.getUserName(),
//            )
//            val res = cSatUseCase.rateCSAT(params)
//            handleResource(res) { data ->
//                _rateCSATStatus.emit(Resource.Success(data))
//            }
//        }
//    }
//
//    // cau hinh danh gia cl dich vu
//    private val _configCSATStatus = MutableStateFlow<CSatConfigDomain?>(null)
//    val configCSatStatus: StateFlow<CSatConfigDomain?> = _configCSATStatus.asStateFlow()
//    fun configCSat(functionId: String? = null) = launchJobSilent {
//        val params = CSatConfigParams(
//            functionId = functionId,
//            userName = userProf.getUserName(),
//        )
//        val res = cSatUseCase.configCSAT(params)
//        handleResourceSilent(
//            resource = res,
//            onSuccess = { data ->
//                _configCSATStatus.value = data
//            },
//        )
//    }

    /**
     * Show toast message
     * @param message The message to display in the toast
     */
    fun showToast(message: String) {
        viewModelScope.launch {
            _toastEvent.emit(message)
        }
    }

    // force account list
    fun forceUpdateAccountList(inquiryType: String = "") = launchJobSilent {
        val params = AccountLstParams(
            serviceAcctType = inquiryType,
            username = userProf.getUserName() ?: "",
            isForceReq = "1",
            cifno = userProf.getCifNo().toString(),
        )
        accountUseCase.getAccountList(params)
    }

    private fun getFileIDFollowTranType(
        tranType: String?,
        file: FileTransactionDomain?,
    ): String? {
        return when (tranType) {
            "go", "goc", "gor" -> {
                file?.objectId
            }

            else -> file?.mtId
        }
    }

    // Expose banks cache flow for UI recomposition
    val banksFlow = transferCacheManager.banksCacheFlow

    /**
     * Ensure bank list is loaded for icon mapping
     */
    fun ensureBankListLoaded() {
        // Check if cache already has banks, if not it will trigger loading
        transferCacheManager.getBankList()
    }

    /**
     * Get bank icon URL by bank name
     * Maps bank name to icon URL from cached bank list
     */
    fun getBankIconUrl(bankName: String?): String? {
        if (bankName.isNullOrBlank()) return null

        val banks = transferCacheManager.getBankList() ?: return null

        // Try exact match first
        val exactMatch = banks.firstOrNull {
            it.shortName?.equals(bankName, ignoreCase = true) == true ||
                it.bankName?.equals(bankName, ignoreCase = true) == true ||
                it.name?.equals(bankName, ignoreCase = true) == true
        }
        if (exactMatch != null) return exactMatch.icon

        // Try contains match
        val containsMatch = banks.firstOrNull {
            it.shortName?.contains(bankName, ignoreCase = true) == true ||
                it.bankName?.contains(bankName, ignoreCase = true) == true ||
                it.name?.contains(bankName, ignoreCase = true) == true ||
                bankName.contains(it.shortName ?: "", ignoreCase = true)
        }

        return containsMatch?.icon
    }
}
