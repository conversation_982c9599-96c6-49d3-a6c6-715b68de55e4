package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject

class InfrastructureDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // HCM
        if ("975" == transaction.providerCode) {
            // Thông tin chung section
            val isCollapse = remember { mutableStateOf(true) }
            SectionHeader(title = "Thông tin chung", true, isCollapse.value) {
                isCollapse.value = !isCollapse.value
            }
            TransactionDetailsCard {
                KeyValueRow(label = "Loại giao dịch", value = transaction.tranTypeName ?: "")
                KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
                KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")
                if (!isCollapse.value) {
                    transaction.activityLogs?.let {
                        it.createdBy?.let { user ->
                            KeyValueRow(
                                label = "Người khởi tạo",
                                value = "${user.username} - ${user.processDate}",
                            )
                        }

                        var verifyUser = ""
                        it.verifiedBy?.forEachIndexed { index, user ->
                            verifyUser += "${user.username ?: ""} - ${user.processDate}"
                            if (index < (it.verifiedBy?.size ?: 0) - 1) {
                                verifyUser += "\n"
                            }
                        }
                        if (!verifyUser.isEmpty()) {
                            KeyValueRow(label = "Người phê duyệt", value = verifyUser)
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            SectionHeader(title = "Thông tin đơn vị thu phí")
            TransactionDetailsCard {
                KeyValueRow(
                    label = "Mã đơn vị thu phí",
                    value = (transaction.providerCode ?: "") + (transaction.providerName ?: ""),
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Thông tin người nộp thuế
        SectionHeader(title = "Thông tin người nộp phí")
        TransactionDetailsCard {
            if ("0" == transaction.payMethod) {
                KeyValueRow(label = "Hình thức", value = "Nộp cho chính đơn vị")
            } else if ("1" == transaction.payMethod) {
                KeyValueRow(label = "Hình thức", value = "Nộp thay cho đơn vị khác")
            }

            KeyValueRow(label = "Tên đơn vị nộp", value = transaction.payname ?: "")
            KeyValueRow(label = "Mã số thuế", value = transaction.payCode ?: "")
            KeyValueRow(label = "Địa chỉ", value = transaction.payAddress ?: "")
            KeyValueRow(
                label = "Từ tài khoản",
                value = if (!transaction.fromAccountNo.isNullOrEmpty()) {
                    "${transaction.fromAccountNo ?: ""} - ${transaction.currency}"
                } else {
                    ""
                },

            )
            KeyValueRow(label = "Ngân hàng", value = transaction.branchName ?: "")
        }

        Spacer(modifier = Modifier.height(16.dp))

        if ("975" != transaction.providerCode) {
            // Thông tin cơ quan thu
            SectionHeader(title = "Thông tin cơ quan thu")
            TransactionDetailsCard {
                KeyValueRow(
                    label = "Tỉnh/Thành phố",
                    value = "${transaction.provinceCode ?: ""} - ${transaction.provinceName ?: ""}",
                )
                KeyValueRow(
                    label = "Địa bàn hành chính",
                    value = "${transaction.areaCode ?: ""} - ${transaction.areaName ?: ""}",
                )
                KeyValueRow(
                    label = "Mã kho bạc nhà nước",
                    value = "${transaction.treasuryCode ?: ""} - ${transaction.treasuryName ?: ""}",
                )
                KeyValueRow(
                    label = "Tài khoản ghi thu",
                    value = transaction.collectingAccount ?: "",
                )
                KeyValueRow(
                    label = "Mã cơ quan quản lý thu",
                    value = "${transaction.collectionAgencyCode ?: ""} - ${transaction.collectionAgencyName ?: ""}",
                )
                if (!transaction.declareNumber.isNullOrEmpty()) {
                    KeyValueRow(
                        label = "Số tờ khai hải quan",
                        value = transaction.declareNumber ?: "",
                    )
                }
                KeyValueRow(
                    label = "Ngày tờ khai hải quan",
                    value = transaction.collectingAccount ?: "",
                )
                KeyValueRow(
                    label = "Số tờ khai nộp phí",
                    value = transaction.collectingAccount ?: "",
                )
                KeyValueRow(
                    label = "Ngày giá trị",
                    value = transaction.collectingAccount ?: "",
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Thông tin
        SectionHeader(title = "Thông tin chi tiết khoản nộp")
        TransactionDetailsCard {
            KeyValueRow(
                label = "ID chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.invoiceId ?: ""
                } else {
                    transaction.docId ?: ""
                },
            )
            KeyValueRow(
                label = "Số chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherNumber ?: ""
                } else {
                    transaction.docNum ?: ""
                },
            )
            KeyValueRow(
                label = "Ký hiệu chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherSymbol ?: ""
                } else {
                    transaction.docSign ?: ""
                },
            )
            KeyValueRow(
                label = "Ngày chứng từ",
                value = if ("975" == transaction.providerCode) {
                    transaction.voucherDate ?: ""
                } else {
                    transaction.docDate ?: ""
                },
            )

            if ("975" != transaction.providerCode) {
                KeyValueRow(label = "Mã chương", value = transaction.chapterCode ?: "")

                KeyValueRow(label = "Mã tiểu mục", value = transaction.subsect ?: "")
            }

            KeyValueRow(
                label = "Số tiền giao dich",
                value = transaction.amount ?: "",
                subValue = transaction.amountInWords,
            )
            KeyValueRow(
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}