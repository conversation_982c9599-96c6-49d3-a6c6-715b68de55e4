package com.vietinbank.feature_checker.ui.component.newui.multiple

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.checker.MultipleConfirmActions
import com.vietinbank.core_domain.models.checker.MultipleConfirmUIState
import com.vietinbank.core_domain.models.checker.util.IMultipleAction
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationNavigationArea
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_checker.ui.component.newui.RejectReasonAction
import com.vietinbank.feature_checker.ui.component.newui.RejectReasonBottomSheet
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewUIMultipleConfirmScreen(
    uiState: MultipleConfirmUIState,
    actions: (IMultipleAction) -> Unit,
) {
    val listState = rememberLazyListState()
    val appBarScrollState = rememberFoundationAppBarScrollState()

    // Rejection reason dialog
    RejectReasonBottomSheet(
        visible = uiState.isShowRejectReason,
        isLoading = false,
        onAction = { action ->
            when (action) {
                is RejectReasonAction.OnDismiss, is RejectReasonAction.OnBackClick -> {
                    actions(MultipleConfirmActions.RejectReason(false))
                }

                is RejectReasonAction.OnContinueClick -> {
                    actions(MultipleConfirmActions.RejectClick(action.reason))
                }
            }
        },
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackgroundLevel2()
            .systemBarsPadding()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap8, bottom = FDS.Sizer.Gap.gap16),
                ) {
                    FoundationAppBar(
                        modifier = Modifier.fillMaxWidth(),
                        title = uiState.confirmModel?.titleScreen ?: "",
                        onNavigationClick = { actions(MultipleConfirmActions.BackClick) },
                        isLightIcon = false,
                        scrollState = appBarScrollState,
                    )
                }
            },
        ) { paddingValues ->
            uiState.confirmModel?.let {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.BottomCenter,
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = FDS.Sizer.Gap.gap8)
                            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32)),
                        contentPadding = PaddingValues(bottom = FDS.Sizer.Padding.padding100),
                    ) {
                        item {
                            if (true == uiState.confirmModel?.isBatchFile) {
                                // duyet nhiều điện con trong file => luồng duyệt đơn của file
                                uiState.renderUI?.RenderConfirmBatchContent(uiState.confirmModel)
                            } else {
                                uiState.renderUI?.RenderConfirmContent(uiState.confirmModel, actions)
                            }
                        }
                    }

                    FoundationNavigationArea(
                        height = FDS.Sizer.Gap.gap108,
                        topRadius = FDS.Sizer.Radius.radius0,
                        startStop = 0.5f,
                    ) {
                        FoundationButton(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .padding(vertical = FDS.Sizer.Gap.gap16),
                            text = if (it.isApprove) {
                                stringResource(R.string.common_confirm)
                            } else {
                                stringResource(R.string.transaction_button_reject)
                            },
                            onClick = {
                                if (it.isApprove) {
                                    actions(MultipleConfirmActions.ApproveClick)
                                } else {
                                    actions(MultipleConfirmActions.RejectReason(true))
                                }
                            },
                        )
                    }
                }
            }
        }
    }
}
