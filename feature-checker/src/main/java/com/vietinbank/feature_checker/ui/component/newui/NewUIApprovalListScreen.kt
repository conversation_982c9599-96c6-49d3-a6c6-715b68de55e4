package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_data.util.impl.ResourceProviderImpl
import com.vietinbank.core_domain.models.checker.SubTranTypeDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_checker.ui.component.approvallist.BulkTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.CustomInlandTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.DisbursementTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.FileNSNNTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.GuaranteeTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.InfrastructureTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.NewTraceTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.NewTransferTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.NewUnLockUserTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.PaymentOrderTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.SalaryTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.approvallist.TransferForeignTransactionRenderer
import com.vietinbank.feature_checker.ui.component.approvallist.TransferTransactionRenderer
import com.vietinbank.feature_checker.ui.component.newui.components.ChipsFilter
import com.vietinbank.feature_checker.ui.component.newui.components.EmptyStateContent
import com.vietinbank.feature_checker.ui.component.newui.components.SelectionBottomBar
import com.vietinbank.feature_checker.ui.component.newui.components.TransactionListControls
import com.vietinbank.feature_checker.ui.component.newui.components.TransactionTypeHeader
import com.vietinbank.feature_checker.ui.fragment.DisbursementTransactionUiState
import com.vietinbank.feature_checker.ui.fragment.TransactionListUIState
import com.vietinbank.feature_checker.ui.fragment.TransactionSheetUi
import com.vietinbank.feature_checker.ui.fragment.TransactionSheetUiModel
import com.vietinbank.feature_checker.ui.sheet.TransactionType
import com.vietinbank.feature_checker.ui.sheet.TransactionTypeBottomSheet
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * New UI Approval List Screen with Scaffold pattern
 * - Uses proper Scaffold with CenteredTitleAppBar as topBar
 * - Tabs and content in the Scaffold content area
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun NewUIApprovalListScreen(
    uiState: TransactionListUIState,
    headerTitle: String,
    isSelectionMode: Boolean,
    selectedTransactions: Set<String>,
    totalTransactionsCount: Int,
    isGlobalMaxMode: Boolean,
    isShowApprove: Boolean,
    isLoadingMore: Boolean,
    canLoadMore: Boolean,
    actions: Actions,
    transactionSheetState: TransactionSheetUi?,
    transactionRendererFactory: TransactionRendererFactory,
    isChecker: Boolean = true,
    // New UI state parameters from ViewModel
//    shouldShowChipFilter: Boolean = false,
    chipFilterOptions: List<SubTranTypeDomain> = emptyList(),
    transactionTypesForDialog: List<TransactionType> = emptyList(),
    selectedTransactionTypeCode: String? = null,
    selectedTransactionItemId: String? = null,
    isAdvanceFilter: Boolean = false,
) {
    var selectedTabIndex by remember { mutableStateOf(0) }
    var showTransactionTypeBottomSheet by remember { mutableStateOf(false) }
    val listState = rememberLazyListState()

    // All UI state is now managed in ViewModel

    // Server-driven filtering: keep showing current list while fetching new data
    // Do NOT apply client-side filtering here to avoid flicker/empty state during API calls
    val transactionsToShow = remember(uiState) {
        when (uiState) {
            is TransactionListUIState.Success -> uiState.transactions
            else -> emptyList()
        }
    }

    // Load more detection when scrolling near end
    LaunchedEffect(listState, canLoadMore, isLoadingMore, transactionsToShow) {
        snapshotFlow { listState.layoutInfo }
            .collect { layoutInfo ->
                // Avoid triggering load more when there is no transaction item
                if (transactionsToShow.isEmpty()) return@collect
                val totalItems = layoutInfo.totalItemsCount
                val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0

                // Trigger load more when near end (last 3 items visible)
                if (lastVisibleItem >= totalItems - 3 &&
                    totalItems > 0 &&
                    canLoadMore &&
                    !isLoadingMore
                ) {
                    actions.onLoadMore()
                }
            }
    }

    // Map sealed action -> Actions bằng 1 dispatcher
    val transactionInforDispatcher = remember(actions) {
        {
                a: ITransactionInforAction ->
            when (a) {
                ITransactionInforAction.OnDismiss -> actions.onDismiss()
                ITransactionInforAction.OnRejectClick -> actions.onRejectClick()
                ITransactionInforAction.OnApproveClick -> actions.onApproveClick()
                is ITransactionInforAction.OnOpenDetail -> TODO()
                is ITransactionInforAction.OnDownloadCustomer -> TODO()
                is ITransactionInforAction.OnDownloadDelegate -> TODO()
                is ITransactionInforAction.OnDownloadSignature -> TODO()
            }
        }
    }

    // Enable large-title collapse on scroll for AppBar
    val appBarScrollState = rememberFoundationAppBarScrollState()
// Bottom sheet for transaction info
    transactionSheetState?.let {
        when (it) {
            is DisbursementTransactionUiState -> {
                DisbursementTransactionSheet(
                    transaction = it.transaction,
                    visible = true,
                    onNext = {
                        actions.onTransactionClick(it.transaction.mtId ?: "")
                    },
                    onBack = {
                        actions.onDismiss()
                    },
                )
            }

            is TransactionSheetUiModel -> {
                TranasctionInfoSheet(
                    visible = true,
                    txUiState = it,
                    onTransactionInforAction = transactionInforDispatcher,
                )
            }
        }
    }

    // Bottom sheet for transaction type selection (groupTypes)
    TransactionTypeBottomSheet(
        isVisible = showTransactionTypeBottomSheet,
        transactionTypes = transactionTypesForDialog,
        selectedTypeId = selectedTransactionItemId ?: "",
        onItemSelected = { type ->
            // Pass the selected type id to actions
            // The ViewModel will determine the full params from countTransactionList
            actions.onTransactionTypeSelected(
                type.id, // This is functionId
            )
            showTransactionTypeBottomSheet = false
        },
        onDismiss = {
            showTransactionTypeBottomSheet = false
        },
    )
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent, // Keep gradient background from MainActivity
            topBar = {
                // Both AppBar and Tabs are sticky/pinned
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    // FoundationAppBar with title below buttons (as designed)
                    FoundationAppBar(
                        title = stringResource(id = R.string.feature_checker_my_request),
                        onNavigationClick = actions.onBackClick,
                        isLightIcon = false,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = FDS.Sizer.Gap.gap8), // Add spacing after title
                        scrollState = appBarScrollState,
                    )

                    // FoundationTabs - also sticky/pinned with AppBar
                    FoundationTabs(
                        tabs = if (isChecker) {
                            listOf(
                                stringResource(R.string.feature_checker_need_request), // Cần duyệt
                                stringResource(R.string.feature_checker_need_approved),
                                stringResource(R.string.feature_checker_need_rejected),
                            )
                        } else {
                            listOf(
                                stringResource(R.string.feature_checker_need_pending), // Chờ duyệt
                                stringResource(R.string.feature_checker_need_approved),
                                stringResource(R.string.feature_checker_need_rejected),
                            )
                        },
                        selectedIndex = selectedTabIndex,
                        onTabSelected = { index ->
                            selectedTabIndex = index
                            actions.onTabSelected(index)
                        },
                        type = TabType.Pill,
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .padding(bottom = FDS.Sizer.Padding.padding16),
                    )
                }
            },
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
            ) {
                // Scrollable content with tabs and transactions
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = FDS.Sizer.Radius.radius32,
                                topEnd = FDS.Sizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.white),
                ) {
                    // White container with all content (Tabs moved to topBar)
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(
                                topStart = FDS.Sizer.Radius.radius32,
                                topEnd = FDS.Sizer.Radius.radius32,
                                bottomStart = FDS.Sizer.Radius.radius0,
                                bottomEnd = FDS.Sizer.Radius.radius0,
                            ),
                            colors = CardDefaults.cardColors(
                                containerColor = FDS.Colors.white,
                            ),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = FDS.Effects.elevationNone,
                            ),
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .animateContentSize(animationSpec = tween(220))
                                    .padding(top = FDS.Sizer.Padding.padding24),
                            ) {
                                // Transaction Type Header with Count
                                TransactionTypeHeader(
                                    headerTitle = headerTitle,
                                    totalTransactionsCount = totalTransactionsCount,
                                    onHeaderClick = {
                                        showTransactionTypeBottomSheet = true
                                    },
                                )

                                // Divider
                                FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap16))

                                // Show chip filter when available
                                if (chipFilterOptions.isNotEmpty()) {
                                    ChipsFilter(
                                        transactionTypes = chipFilterOptions,
                                        selectedTransactionTypeCode = selectedTransactionTypeCode,
                                        onTransactionTypeSelected = { code ->
                                            actions.onFilterChipSelected(code)
                                        },
                                    )
                                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                                }

                                val isRejectTransactionType by remember(uiState) {
                                    derivedStateOf {
                                        (uiState as? TransactionListUIState.Success)?.transactions?.any {
                                            // Giải ngân thay vì duyệt nhiều thì từ chối chiều
                                            it.tranType == Tags.TYPE_DISBURSEMENT_ONLINE.lowercase()
                                        } ?: false
                                    }
                                }

                                // Always show controls (including sort) even when list is empty
                                TransactionListControls(
                                    onMultiSelectClick = { actions.onMultiSelectClick(isRejectTransactionType) },
                                    onDeleteSelectClick = actions.onDeleteSelectClick,
                                    onSortClick = actions.onSortClick,
                                    isChecker = isChecker,
                                    selectedTabIndex = selectedTabIndex,
                                    totalTransactionsCount = totalTransactionsCount,
                                    isDataLoaded = uiState is TransactionListUIState.Success,
                                    isRejectTransactionType = isRejectTransactionType,
                                )

                                FoundationDivider(
                                    modifier = Modifier
                                        .padding(top = FDS.Sizer.Gap.gap16)
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                )
                            }
                        }
                    }

                    // Handle different UI states
                    when (uiState) {
                        is TransactionListUIState.Loading -> {
                            // Keep layout structure but avoid showing a second spinner;
                            // global loading from BaseViewModel is already active.
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(FDS.Sizer.Container.contentMinHeight)
                                        .background(FDS.Colors.white),
                                ) {}
                            }
                        }

                        is TransactionListUIState.Success -> {
                            if (transactionsToShow.isEmpty()) {
                                // Show empty state when no transactions - use fillParentMaxHeight to fill remaining space
                                item {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(
                                                FDS.Colors.white,
                                                RoundedCornerShape(
                                                    bottomStart = FDS.Sizer.Radius.radius32,
                                                    bottomEnd = FDS.Sizer.Radius.radius32,
                                                ),
                                            ),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        EmptyStateContent(
                                            message = stringResource(R.string.checker_approve_not_transaction),
                                            subMessage = if (isAdvanceFilter) {
                                                stringResource(R.string.checker_filter_not_transaction)
                                            } else {
                                                stringResource(R.string.checker_default_not_transaction)
                                            },
                                        )
                                    }
                                }
                            } else {
                                // Show transaction items when list is not empty
                                items(
                                    items = transactionsToShow,
                                    key = { it.mtId ?: "" },
                                    contentType = { "transaction" }, // Optimize composition with contentType
                                ) { transaction ->
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .animateItemPlacement(animationSpec = tween(220))
                                            .background(FDS.Colors.white)
                                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    ) {
                                        TransactionItem(
                                            transaction = transaction,
                                            isSelected = selectedTransactions.contains(transaction.mtId),
                                            isSelectionMode = isSelectionMode,
                                            onClick = {
                                                actions.onTransactionClick(transaction.mtId ?: "")
                                            },
                                            onLongClick = {
                                                actions.onTransactionLongClick(transaction)
                                            },
                                            transactionRendererFactory = transactionRendererFactory,
                                        )

                                        // Divider after each item except the last
                                        if (transaction != transactionsToShow.lastOrNull()) {
                                            FoundationDivider()
                                        }
                                    }
                                }

                                // Load more section
                                if (isLoadingMore) {
                                    // Loading indicator while fetching more
                                    item(
                                        key = "loading-more",
                                        contentType = "loading",
                                    ) {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .background(FDS.Colors.white)
                                                .padding(FDS.Sizer.Padding.padding16),
                                            contentAlignment = Alignment.Center,
                                        ) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                                color = FDS.Colors.primary,
                                                strokeWidth = FDS.Sizer.Stroke.stroke2,
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        is TransactionListUIState.Error -> {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(FDS.Sizer.Container.contentMinHeight)
                                        .background(FDS.Colors.white),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        modifier = Modifier.padding(FDS.Sizer.Padding.padding24),
                                    ) {
                                        FoundationText(
                                            text = uiState.message,
                                            style = FDS.Typography.bodyB1,
                                            color = FDS.Colors.error,
                                            textAlign = TextAlign.Center,
                                        )

                                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))

                                        FoundationButton(
                                            text = stringResource(R.string.feature_checker_retry),
                                            onClick = actions.onRetryLoadMore,
                                            isLightButton = false,
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } // End of Scaffold

        // Selection Bottom Bar (outside Scaffold, inside Box)
        if (isSelectionMode && selectedTransactions.isNotEmpty()) {
            SelectionBottomBar(
                selectedCount = selectedTransactions.size,
                isAllSelected = isGlobalMaxMode,
                isShowApprove = isShowApprove,
                onSelectAllClick = actions.onSelectAllClick,
                onApproveClick = actions.onApproveClick,
                onRejectClick = actions.onRejectClick,
                modifier = Modifier.align(Alignment.BottomCenter),
            )
        }
    } // End of Box
}

/**
 * Actions interface for handling user interactions
 * Legacy data class kept for backward compatibility
 */
data class Actions(
    val onBackClick: () -> Unit,
    val onSortClick: () -> Unit,
    val onMultiSelectClick: (isRejectMode: Boolean) -> Unit,
    val onDeleteSelectClick: () -> Unit,
    val onTabSelected: (Int) -> Unit,
    val onFilterChipSelected: (String?) -> Unit,
    val onTransactionClick: (String) -> Unit,
    val onTransactionLongClick: (TransactionListDomain) -> Unit,
    val onApproveClick: () -> Unit,
    val onRejectClick: () -> Unit,
    val onSelectAllClick: () -> Unit,
    val onLoadMore: () -> Unit,
    val onRetryLoadMore: () -> Unit,
    val onDismiss: () -> Unit,
    val onTransactionTypeSelected: (functionId: String?) -> Unit,
)

/**
 * Sealed class representing all possible actions in the Approval List screen
 * Using sealed class pattern for better type safety and single responsibility
 */
sealed class ApprovalListAction {
    // Navigation actions
    data object BackClick : ApprovalListAction()
    data object SortClick : ApprovalListAction()
    data class MultiSelectClick(val isRejectMode: Boolean = false) : ApprovalListAction()
    data object DeleteSelectClick : ApprovalListAction()
    data object DismissClick : ApprovalListAction()

    // Tab actions
    data class TabSelected(val tabIndex: Int) : ApprovalListAction()
    data class FilterChipSelected(val tranTypeCode: String?) : ApprovalListAction()
    data class TransactionTypeSelected(
//        val tranType: String?,
//        val groupType: String?,
//        val serviceType: String?,
        val functionId: String?, // chi can functionId
    ) : ApprovalListAction()

    // Transaction item actions
    data class TransactionClick(val transactionId: String) : ApprovalListAction()
    data class TransactionLongClick(val transaction: TransactionListDomain) : ApprovalListAction()

    // Bottom bar actions
    data object ApproveClick : ApprovalListAction()
    data object RejectClick : ApprovalListAction()
    data object SelectAllClick : ApprovalListAction()

    // Loading actions
    data object LoadMoreClick : ApprovalListAction()
    data object RetryLoadMoreClick : ApprovalListAction()
}

/**
 * New composable using sealed class action handler
 * This is the recommended approach for new code
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewUIApprovalListScreenWithActions(
    uiState: TransactionListUIState,
    headerTitle: String,
    isSelectionMode: Boolean,
    selectedTransactions: Set<String>,
    totalTransactionsCount: Int,
    isGlobalMaxMode: Boolean,
    isShowApprove: Boolean,
    isLoadingMore: Boolean,
    canLoadMore: Boolean,
    onAction: (ApprovalListAction) -> Unit,
    transactionSheetState: TransactionSheetUi?,
    transactionRendererFactory: TransactionRendererFactory,
    isChecker: Boolean,
    // New UI state parameters from ViewModel
//    shouldShowChipFilter: Boolean = false,
    chipFilterOptions: List<SubTranTypeDomain> = emptyList(),
    transactionTypesForDialog: List<TransactionType> = emptyList(),
    selectedTransactionTypeCode: String? = null,
    selectedTransactionItemId: String? = null,
    isAdvanceFilter: Boolean = false,
) {
    // Convert sealed class action handler to legacy Actions
    val actions = Actions(
        onBackClick = { onAction(ApprovalListAction.BackClick) },
        onSortClick = { onAction(ApprovalListAction.SortClick) },
        onMultiSelectClick = { onAction(ApprovalListAction.MultiSelectClick(it)) },
        onDeleteSelectClick = { onAction(ApprovalListAction.DeleteSelectClick) },
        onTabSelected = { index -> onAction(ApprovalListAction.TabSelected(index)) },
        onFilterChipSelected = { code -> onAction(ApprovalListAction.FilterChipSelected(code)) },
        onTransactionClick = { id -> onAction(ApprovalListAction.TransactionClick(id)) },
        onTransactionLongClick = { transaction ->
            onAction(
                ApprovalListAction.TransactionLongClick(
                    transaction,
                ),
            )
        },
        onTransactionTypeSelected = { functionId ->
            onAction(ApprovalListAction.TransactionTypeSelected(functionId))
        },
        onApproveClick = { onAction(ApprovalListAction.ApproveClick) },
        onRejectClick = { onAction(ApprovalListAction.RejectClick) },
        onSelectAllClick = { onAction(ApprovalListAction.SelectAllClick) },
        onLoadMore = { onAction(ApprovalListAction.LoadMoreClick) },
        onRetryLoadMore = { onAction(ApprovalListAction.RetryLoadMoreClick) },
        onDismiss = { onAction(ApprovalListAction.DismissClick) },
    )

    // Call the original composable with converted actions
    NewUIApprovalListScreen(
        uiState = uiState,
        headerTitle = headerTitle,
        isSelectionMode = isSelectionMode,
        selectedTransactions = selectedTransactions,
        totalTransactionsCount = totalTransactionsCount,
        isGlobalMaxMode = isGlobalMaxMode,
        isShowApprove = isShowApprove,
        isLoadingMore = isLoadingMore,
        canLoadMore = canLoadMore,
        actions = actions,
        transactionSheetState = transactionSheetState,
        transactionRendererFactory = transactionRendererFactory,
        isChecker = isChecker,
//        shouldShowChipFilter = shouldShowChipFilter,
        chipFilterOptions = chipFilterOptions,
        transactionTypesForDialog = transactionTypesForDialog,
        selectedTransactionTypeCode = selectedTransactionTypeCode,
        selectedTransactionItemId = selectedTransactionItemId,
        isAdvanceFilter = isAdvanceFilter,
    )
}

/**
 * Transaction Item Component
 * Displays individual transaction in the list matching Figma design
 * Optimized with remember for expensive computations
 */
@Composable
fun TransactionItem(
    modifier: Modifier = Modifier,
    transaction: TransactionListDomain,
    isSelected: Boolean,
    isSelectionMode: Boolean,
    transactionRendererFactory: TransactionRendererFactory,
    onChangeSelected: () -> Unit = {},
    onClick: () -> Unit,
    onLongClick: () -> Unit,
) {
    // Get the appropriate renderer for this transaction type
    val renderer = transactionRendererFactory.getRenderer(
        tranType = transaction.tranType ?: "",
        useNewLayout = true,
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable(
                onSafeClick = onClick,
                onLongClick = onLongClick,
            )
            .padding(vertical = FDS.Sizer.Padding.padding16),
    ) {
        renderer.RenderContent(
            transaction = transaction,
            isSelectionMode = isSelectionMode,
            isSelected = isSelected,
            onChangeClick = onChangeSelected,
        )
    }
}

/**
 * Get appropriate icon for transaction type
 */
private fun getTransactionIcon(type: String): Int {
    return when (type.lowercase()) {
        "transfer", "ct", "internal" -> com.vietinbank.feature_checker.R.drawable.ic_checker_sort_arrow_16
        "payment", "tt", "external" -> R.drawable.ic_download
        "batch", "file" -> R.drawable.ic_document
        else -> com.vietinbank.feature_checker.R.drawable.ic_checker_sort_arrow_16
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000C28)
@Composable
private fun NewUIApprovalListScreenPreview() {
    val sampleTransactions = listOf(
        TransactionListDomain().apply {
            tranTypeName = "Nhanh 24/7"
            referenceNo = "************"
            tranType = "np"
            mtId = "1"
            amount = "200,000,000,000"
            uid = "NVTUAN"
            fileName = ""
            status = "1.1"
            toAccountNo = ""
            receiveName = ""
            receiveBankName = ""
            statusName = ""
        },
    )

    // Create mock factory for preview
    val mockFactory = TransactionRendererFactory(
        resourceProvider = ResourceProviderImpl(context = LocalContext.current),
        transferTransactionRenderer = TransferTransactionRenderer(),
        newTransferTransactionRenderer = NewTransferTransactionRenderer(),
        paymentTransactionRenderer = PaymentOrderTransactionRenderer(),
        salaryTransactionRenderer = SalaryTransactionRenderer(),
        bulkTransactionRenderer = BulkTransactionRenderer(),
        traceTransactionRenderer = NewTraceTransactionRenderer(),
        disbursementTransactionRenderer = DisbursementTransactionRenderer(),
        guaranteeTransactionRenderer = GuaranteeTransactionRenderer(),
        customInlandTransactionRenderer = CustomInlandTransactionRenderer(),
        fileNSNNTransactionRenderer = FileNSNNTransactionRenderer(),
        infrastructureTransactionRenderer = InfrastructureTransactionRenderer(),
        transferForeignTransactionRenderer = TransferForeignTransactionRenderer(),
        unLockUserTransactionRenderer = NewUnLockUserTransactionRenderer(),
    )

    NewUIApprovalListScreen(
        uiState = TransactionListUIState.Success(sampleTransactions),
        headerTitle = "Điện chuyển tiền",
        isSelectionMode = false,
        selectedTransactions = emptySet(),
        totalTransactionsCount = 999,
        isGlobalMaxMode = false,
        isShowApprove = true,
        isLoadingMore = false,
        canLoadMore = true,
        actions = Actions(
            onBackClick = {},
            onSortClick = {},
            onMultiSelectClick = {},
            onTabSelected = {},
            onFilterChipSelected = {},
            onTransactionClick = {},
            onTransactionLongClick = {},
            onApproveClick = {},
            onRejectClick = {},
            onSelectAllClick = {},
            onLoadMore = {},
            onRetryLoadMore = {},
            onDismiss = {},
            onDeleteSelectClick = {},
            onTransactionTypeSelected = {},
        ),
        transactionSheetState = null,
        transactionRendererFactory = mockFactory,
    )
}
