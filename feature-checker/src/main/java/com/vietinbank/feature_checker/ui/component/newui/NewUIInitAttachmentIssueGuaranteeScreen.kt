import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.inits.GuaranteeAmendDetailRenderer.Companion.TYPE_GDN_GUARANTEE

// tab Hồ sơ PHBL
@Composable
fun NewUIInitAttachmentIssueGuaranteeScreen(
    transaction: TransactionDomain,
    onFieldClick: (TransactionFieldEvent) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32))
            .background(FoundationDesignSystem.Colors.white)
            .padding(FoundationDesignSystem.Sizer.Padding.padding8),
    ) {
        FoundationStatus(
            statusMessage = transaction.statusName,
            statusCode = Status.Pending,
            modifier = Modifier.padding(
                FoundationDesignSystem.Sizer.Padding.padding16,
            ),
        )
        // Group files by attachmentType
        val groupedFiles = transaction.listFiles?.filter {
            !it.attachmentType.isNullOrEmpty() && it.attachmentType != TYPE_GDN_GUARANTEE
        }?.groupBy { it.attachmentType }

        // Display grouped files with headers
        groupedFiles?.forEach { (attachmentType, files) ->
            // Header for each group
            FoundationText(
                text = files.firstOrNull()?.typeName ?: "", // This is your typeName/header
                style = FoundationDesignSystem.Typography.bodyB2, // Adjust style as needed
                color = FoundationDesignSystem.Colors.characterSecondary,
                modifier = Modifier.padding(
                    start = FoundationDesignSystem.Sizer.Padding.padding16,
                    end = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )

            // Files in this group
            files.forEach { file ->
                Row(
                    modifier = Modifier.padding(
                        vertical = FoundationDesignSystem.Sizer.Padding.padding12,
                        horizontal = FoundationDesignSystem.Sizer.Padding.padding16,
                    ),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_common_transfer_32),
                        contentDescription = null,
                        modifier = Modifier
                            .size(FoundationDesignSystem.Sizer.Icon.icon32)
                            .safeClickable {
                                onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                            },
                    )
                    Spacer(
                        modifier = Modifier.padding(
                            end = FoundationDesignSystem.Sizer.Padding.padding12,
                        ),
                    )
                    FoundationClickableText(
                        text = file.fileName ?: "",
                        style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                        overflow = TextOverflow.Ellipsis,
                        underline = true,
                        color = FoundationDesignSystem.Colors.textSelected,
                        onClick = {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                    )
                }
            }
        }
    }
}