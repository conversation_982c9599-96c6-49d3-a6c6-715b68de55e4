package com.vietinbank.feature_checker.ui.component.newui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags.EMPTY_STATE_MIN_HEIGHT
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun EmptyStateContent(
    modifier: Modifier = Modifier,
    message: String? = null,
    subMessage: String? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(min = EMPTY_STATE_MIN_HEIGHT.dp)
            .padding(vertical = FDS.Sizer.Gap.gap48),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Image(
            painter = painterResource(
                id = R.drawable.ic_common_empty_search_96,
            ),
            contentDescription = null,
//            modifier = Modifier.size(Tags.EMPTY_STATE_ICON_SIZE.dp),
        )

        FoundationText(
            text = message
                ?: stringResource(com.vietinbank.feature_checker.R.string.feature_checker_empty_list),
            style = FDS.Typography.headingH4,
            color = FDS.Colors.characterHighlighted,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
        )

        subMessage?.let {
            FoundationText(
                modifier = Modifier
                    .padding(top = FDS.Sizer.Gap.gap16)
                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                text = it,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                textAlign = TextAlign.Center,

            )
        }
    }
}
