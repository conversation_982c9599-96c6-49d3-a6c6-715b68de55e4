package com.vietinbank.feature_checker.ui.component.success.renderer

import androidx.compose.runtime.Composable
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader

/**
 * Interface for transaction success screen renderers
 * Each transaction type will have its own implementation
 */
interface ITransactionSuccessRenderer {

    /**
     * Render the success screen content for a specific transaction type
     * @param transaction The transaction domain object
     * @param approveResult The approval result (contains hostMtId, status message, etc)
     * @param confirmType Confirmation type (e.g. "Chuyển tiền trong VietinBank")
     * @param isRejected Whether this is a rejection success screen
     * @param imageLoader Image loader for bank icons etc
     * @param getBankIconUrl Function to get bank icon URL from bank code
     * @param onFileClick Callback when user clicks on file attachment
     * @param onEmojiSelected Callback when user selects emoji for CSAT
     */
    @Composable
    fun RenderContent(
        transaction: TransactionDomain,
        approveResult: ApproveDomain? = null,
        confirmType: String,
        isRejected: Boolean = false,
        imageLoader: CoilImageLoader? = null,
        getBankIconUrl: ((String?) -> String?)? = null,
        onFileClick: ((FileTransactionDomain) -> Unit)? = null,
        onEmojiSelected: (Int) -> Unit = {},
    )
}