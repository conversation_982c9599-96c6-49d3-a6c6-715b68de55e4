package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

class UnLockUserConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.account_transaction_no),
                value = transaction.mtId ?: "",
                stableKey = ConfirmFieldKey.MTID,
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_cif_mst),
                value = transaction.cifno ?: "",
                stableKey = ConfirmFieldKey.CIFNO,
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_name),
                value = transaction.creator ?: "",
                stableKey = ConfirmFieldKey.CREATOR,
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_cccd),
                value = transaction.idCard ?: "",
                stableKey = ConfirmFieldKey.IDCARD,
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_init_account),
                value = transaction.paymentAccount ?: "",
                stableKey = ConfirmFieldKey.PAYMENT_ACCOUNT,
            ),
        )
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.lock_confirm_method_pass),
                value = transaction.typeSend ?: "",
                stableKey = ConfirmFieldKey.TYPE_SEND,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_checker_process_time),
                value = transaction.createdDate ?: "",
                stableKey = ConfirmFieldKey.CREATED_DATE,
            ),
        )

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        return ConfirmUiModel()
    }
}