package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_checker.ui.fragment.DownloadItemType
import com.vietinbank.feature_checker.ui.fragment.FooterButtonType
import com.vietinbank.feature_checker.ui.fragment.TransactionSheetUiModel

@Composable
fun TranasctionInfoSheet(
    visible: Boolean,
    txUiState: TransactionSheetUiModel,
    onTransactionInforAction: (ITransactionInforAction) -> Unit,
) {
    BaseBottomSheet<ITransactionInforAction>(
        visible = visible,
        onDismissRequest = {
            onTransactionInforAction.invoke(ITransactionInforAction.OnDismiss)
        },
        onResult = { onTransactionInforAction(it) },
        allowTouchDismiss = true,
        secureFlag = true,
    ) { onAction ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .imePadding()
                .padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
            verticalArrangement = Arrangement.Bottom, // ️ dồn Surface + Footer xuống đáy
        ) {
            // Card trắng
            Surface(
                shape = RoundedCornerShape(FoundationDesignSystem.Sizer.Radius.radius32),
                color = FoundationDesignSystem.Colors.white,
                tonalElevation = FoundationDesignSystem.Sizer.Gap.gap1,
                shadowElevation = FoundationDesignSystem.Sizer.Gap.gap2,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false) // nở theo nội dung; khi dư chỗ, vẫn ở sát Footer
                    .padding(
                        horizontal = FoundationDesignSystem.Sizer.Gap.gap12,
                        vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                    ),
            ) {
                Column(
                    Modifier.padding(FoundationDesignSystem.Sizer.Gap.gap16)
                        .verticalScroll(rememberScrollState()),
                ) {
                    Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                        Column(Modifier.weight(1f)) {
                            FoundationText(
                                text = txUiState.tranTypeName,
                                style = FoundationDesignSystem.Typography.headingH4,
                                color = FoundationDesignSystem.Colors.characterPrimary,
                            )
                            FoundationText(
                                text = Utils.g().getDotMoneyHasCcy(
                                    txUiState.amount.getAmountServer(),
                                    txUiState.ccy,
                                ),
                                style = FoundationDesignSystem.Typography.headingH4,
                                color = FoundationDesignSystem.Colors.characterPrimary,
                            )
                        }
                        FoundationStatus(statusCode = txUiState.statusCode ?: Status.Success)
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap8),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationDivider(modifier = Modifier.weight(1f))

                        FoundationIconText(
                            modifier = Modifier
                                .safeClickable {}
                                .padding(start = FoundationDesignSystem.Sizer.Gap.gap8),
                            text = "",
                            icons = mapOf(
                                IconPosition.LEFT to IconConfig(
                                    icon = R.drawable.ic_common_arrow_20,
                                    size = FoundationDesignSystem.Sizer.Icon.icon20,
                                ),
                            ),
                        )

                        FoundationDivider(modifier = Modifier.weight(1f))
                    }

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        FoundationIconText(
                            modifier = Modifier
                                .safeClickable {}
                                .padding(start = FoundationDesignSystem.Sizer.Gap.gap8),
                            text = "",
                            icons = mapOf(
                                IconPosition.LEFT to IconConfig(
                                    icon = R.drawable.ic_commom_avatar_none_64,
                                    size = FoundationDesignSystem.Sizer.Icon.icon32,
                                ),
                            ),
                        )
                        Spacer(Modifier.width(FoundationDesignSystem.Sizer.Gap.gap8))
                        Column {
                            FoundationText(
                                text = txUiState.receiveName,
                                style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                                color = FoundationDesignSystem.Colors.characterPrimary,
                                modifier = Modifier.padding(FoundationDesignSystem.Sizer.Gap.gap4),
                            )
                            FoundationText(
                                text = txUiState.receiveBankName,
                                style = FoundationDesignSystem.Typography.captionCaptionL,
                                color = FoundationDesignSystem.Colors.characterSecondary,
                                modifier = Modifier.padding(FoundationDesignSystem.Sizer.Gap.gap2),
                            )
                            FoundationText(
                                text = txUiState.toAccountNo,
                                style = FoundationDesignSystem.Typography.captionCaptionL,
                                color = FoundationDesignSystem.Colors.characterSecondary,
                                modifier = Modifier.padding(FoundationDesignSystem.Sizer.Gap.gap4),
                            )
                        }
                    }

                    FoundationDivider(
                        modifier = Modifier
                            .padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap8,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap8,
                            ),
                    )

                    // Render toàn bộ details động
                    txUiState.details.forEach { d ->
                        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                            FoundationText(
                                text = d.label,
                                style = FoundationDesignSystem.Typography.bodyB2,
                                color = FoundationDesignSystem.Colors.characterSecondary,
                            )
                            Column(horizontalAlignment = Alignment.End) {
                                FoundationText(
                                    text = d.value,
                                    style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                                    color = FoundationDesignSystem.Colors.characterPrimary,
                                )
                                d.subValue?.let {
                                    FoundationText(
                                        text = it,
                                        style = FoundationDesignSystem.Typography.bodyB2Emphasized,
                                        color = FoundationDesignSystem.Colors.characterPrimary,
                                    )
                                }
                            }
                        }
                        Spacer(Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    }

                    FoundationDivider(
                        modifier = Modifier
                            .padding(
                                top = FoundationDesignSystem.Sizer.Gap.gap8,
                                bottom = FoundationDesignSystem.Sizer.Gap.gap8,
                            ),
                    )

                    // ===== DOWNLOADS =====
                    txUiState.sheetDownloads.forEach { item ->
                        when (item) {
                            DownloadItemType.SIGNATURE -> FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(
                                            ITransactionInforAction
                                                .OnDownloadSignature(txUiState.mtID, txUiState.tranType),
                                        )
                                    },
                                text = stringResource(com.vietinbank.feature_checker.R.string.download_signature),
                                icons = mapOf(
                                    IconPosition.LEFT to IconConfig(
                                        icon = com.vietinbank.feature_checker.R.drawable.ic_downloadnew,
                                        size = FoundationDesignSystem.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FoundationDesignSystem.Typography.interactionSmallButton,
                                color = FoundationDesignSystem.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                            DownloadItemType.SIGNATURE_NO -> FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(
                                            ITransactionInforAction
                                                .OnDownloadDelegate(txUiState.mtID, txUiState.tranType),
                                        )
                                    },
                                text = stringResource(com.vietinbank.feature_checker.R.string.download_signature_no),
                                icons = mapOf(
                                    IconPosition.LEFT to IconConfig(
                                        icon = com.vietinbank.feature_checker.R.drawable.ic_downloadnew,
                                        size = FoundationDesignSystem.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FoundationDesignSystem.Typography.interactionSmallButton,
                                color = FoundationDesignSystem.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                            DownloadItemType.BILL -> FoundationIconText(
                                modifier = Modifier
                                    .safeClickable {
                                        onAction.invoke(
                                            ITransactionInforAction
                                                .OnDownloadCustomer(txUiState.mtID, txUiState.tranType),
                                        )
                                    },
                                text = stringResource(com.vietinbank.feature_checker.R.string.download_bill),
                                icons = mapOf(
                                    IconPosition.LEFT to IconConfig(
                                        icon = com.vietinbank.feature_checker.R.drawable.ic_downloadnew,
                                        size = FoundationDesignSystem.Sizer.Icon.icon32,
                                    ),
                                ),
                                style = FoundationDesignSystem.Typography.interactionSmallButton,
                                color = FoundationDesignSystem.Colors.blue900,
                                textAlign = TextAlign.Center,
                                horizontalAlignment = CenterHorizontally,
                            )
                        }
                    }
                }
            }
            Spacer(Modifier.height(FoundationDesignSystem.Sizer.Gap.gap12))

            // ===== FOOTER =====

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap16)
                    .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap16),
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
            ) {
                txUiState.sheetButtons.forEach { btn ->
                    when (btn) {
                        FooterButtonType.BACK -> FoundationButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(R.string.common_back),
                            isLightButton = false,
                            onClick = {
                                onAction.invoke(ITransactionInforAction.OnDismiss)
                            },
                        )

                        FooterButtonType.DETAIL -> FoundationButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(R.string.common_detail),
                            onClick = {
                                onAction.invoke(
                                    ITransactionInforAction
                                        .OnOpenDetail(txUiState.mtID, txUiState.tranType),
                                )
                            },
                        )

                        FooterButtonType.CANCEL -> FoundationButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(R.string.dialog_button_cancel),
                            isLightButton = false,
                            onClick = {
                                onAction.invoke(ITransactionInforAction.OnRejectClick)
                            },
                        )

                        FooterButtonType.APPROVE -> FoundationButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(R.string.common_appro),
                            onClick = {
                                onAction.invoke(ITransactionInforAction.OnApproveClick)
                            },
                            leadingIcon = painterResource(id = com.vietinbank.feature_checker.R.drawable.ic_approva_button),
                        )
                    }
                }
            }

            Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
        }
    }
}