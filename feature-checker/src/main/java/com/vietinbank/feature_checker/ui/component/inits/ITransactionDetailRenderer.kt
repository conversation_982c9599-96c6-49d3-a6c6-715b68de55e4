package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel

/**
 * Created by vand<PERSON> on 9/4/25.
 */
interface ITransactionDetailRenderer {
    @Composable
    fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    )

    @Composable
    fun NewRenderTransactionDetails(
        modifier: Modifier,
        transaction: TransactionDomain,
        transactionHelper: TransactionHelperDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {}

    fun isNewRender(): Boolean = false
}