package com.vietinbank.feature_checker.ui.component.inits

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAvatarShimmer
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationSkeletonRectangle
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.feature_checker.ui.component.KeyValueRow
import com.vietinbank.feature_checker.ui.component.SectionHeader
import com.vietinbank.feature_checker.ui.component.TransactionDetailsCard
import com.vietinbank.feature_checker.ui.component.newui.NewUIInitTransactionConstants
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * Created by vandz on 9/4/25.
 */
class TransferDetailRenderer @Inject constructor(
    private val moneyHelper: MoneyHelper,
    val imageLoader: CoilImageLoader,
) : ITransactionDetailRenderer {
    @Composable
    override fun RenderTransactionDetails(
        transaction: TransactionDomain,
        modifier: Modifier,
        onFieldClick: (TransactionFieldEvent) -> Unit,
        viewModel: InitTransactionViewModel?,
        actions: TransactionDetailActions?,
    ) {
        // Thông tin chung section
        val isCollapse = remember { mutableStateOf(true) }
        SectionHeader(
            title = "Thông tin chung",
            hasMoreInfo = true,
            isCollapse = isCollapse.value,
        ) {
            isCollapse.value = !isCollapse.value
        }
        TransactionDetailsCard {
            KeyValueRow(
                label = "Loại giao dịch",
                value = (transaction.tranTypeName ?: "") + if (transaction.tranType != "sn") {
                    " - Tách lệnh"
                } else {
                    ""
                },
            )
            KeyValueRow(label = "Số giao dịch", value = transaction.mtId ?: "")
            KeyValueRow(label = "Trạng thái", value = transaction.statusName ?: "")

            if (!isCollapse.value) {
                KeyValueRow(
                    label = "Người tạo",
                    value = "${transaction.activityLogs?.createdBy?.username ?: ""} - ${transaction.activityLogs?.createdBy?.processDate ?: ""}",
                )

                var verifyUser = ""
                transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                    verifyUser += "${item.username ?: ""} - ${item.processDate}"
                    if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                        verifyUser += "\n"
                    }
                }
                if (!verifyUser.isEmpty()) {
                    KeyValueRow(label = "Người phê duyệt", value = verifyUser)
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin tài khoản chuyển
        SectionHeader(title = "Thông tin tài khoản chuyển")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: ""} - ${transaction.fromAccountName ?: ""}",
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Thông tin tài khoản thụ hưởng
        SectionHeader(title = "Thông tin tài khoản thụ hưởng")
        TransactionDetailsCard {
            KeyValueRow(
                label = "Tới tài khoản",
                value = transaction.toAccountNo ?: "",
                subValue = transaction.receiveName,
            )
            KeyValueRow(label = "Ngân hàng", value = transaction.receiveBankName ?: "")
            KeyValueRow(
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
                subValue = transaction.amountInWords,
            )
            val fee = if (transaction.feePayMethod == "3") {
                transaction.feePayMethodDesc1 ?: ""
            } else {
                transaction.feeAmount ?: ""
            }
            KeyValueRow(label = "Phí giao dịch", value = fee)

            val feeMethod = if (transaction.feePayMethod == "0") {
                "Phí ngoài"
            } else {
                "Phí trong"
            }
            KeyValueRow(label = "Hình thức thu phí", value = feeMethod)

            KeyValueRow(label = "Nội dung", value = transaction.remark ?: "")

            val label =
                if (transaction.process_time.isNullOrEmpty()) "Thời gian chuyển" else "Ngày đặt lịch"
            val value =
                if (transaction.process_time.isNullOrEmpty()) "Chuyển ngay" else transaction.process_time!!
            KeyValueRow(label = label, value = value)
        }

        transaction.splitTransaction?.let { subTransaction ->
            // giao dich tach lenh
            Spacer(modifier = Modifier.height(16.dp))
            SectionHeader(title = "Thông tin tách lệnh giao dịch")
            TransactionDetailsCard {
                subTransaction.forEach { item ->
                    KeyValueRow(label = "Số giao dịch", value = item.mtId ?: "")
                    KeyValueRow(
                        label = "Số tiền",
                        value = Utils.g().getDotMoneyHasCcy(item.amount ?: "", "VND"),
                        isHighlighted = true,
                        subValue = moneyHelper.convertAmountToWords(item.amount ?: "", "VND"),
                    )
                    val fee =
                        (item.feeAmt?.toDoubleOrNull() ?: 0.0) + (
                            item.feeVat?.toDoubleOrNull()
                                ?: 0.0
                            )
                    KeyValueRow(
                        label = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(fee.toString(), "VND"),
                    )
                    KeyValueRow(label = "Trạng thái", value = item.statusName ?: "")
                    Spacer(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(1.dp)
                            .background(AppColors.lineColor),
                    )
                }
                KeyValueRow(
                    label = "Hình thức thu phí",
                    value = if (transaction.feePayMethod == "0") {
                        "Phí ngoài"
                    } else {
                        "Phí trong"
                    },
                )
            }
        }
    }

    override fun isNewRender(): Boolean {
        return true
    }

    @Composable
    override fun NewRenderTransactionDetails(
        modifier: Modifier,
        transaction: TransactionDomain,
        transactionHelper: TransactionHelperDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Gap.gap24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            FoundationStatus(
                statusMessage = transaction.statusName
                    ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
                statusCode = when (transaction.status) {
                    "approved" -> Status.Success
                    "rejected" -> Status.Fail
                    else -> Status.Pending
                },
            )

            // From account section (without label) - KEEP THIS
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Bank logo using circular container
                Box(
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon40)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    if (!transactionHelper?.bankFromUrl.isNullOrBlank()) {
                        imageLoader.LoadUrl(
                            url = transactionHelper?.bankFromUrl,
                            isCache = true,
                            placeholderRes = null,
                            errorRes = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    } else {
                        // Show shimmer only when no icon URL
                        FoundationAvatarShimmer(
                            modifier = Modifier,
                            size = FDS.Sizer.Icon.icon40,
                        )
                    }
                }
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                Column(modifier = Modifier.weight(1f)) {
                    val accountName = transaction.fromAccountName
                    if (accountName != null) {
                        FoundationText(
                            text = accountName,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                        )
                    } else {
                        // Show shimmer for account name
                        com.vietinbank.core_ui.components.FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding144), // Approx 150dp
                            height = FDS.Sizer.Icon.icon20,
                        )
                    }

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

                    val accountNo = transaction.fromAccountNo
                    if (accountNo != null) {
                        FoundationText(
                            text = accountNo,
                            style = FDS.Typography.captionL,
                            color = FDS.Colors.characterSecondary,
                        )
                    } else {
                        // Show shimmer for account number
                        com.vietinbank.core_ui.components.FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding100),
                            height = FDS.Sizer.Icon.icon16,
                        )
                    }
                }
            }

            // Transfer to label with divider
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationText(
                    text = stringResource(com.vietinbank.core_ui.R.string.transaction_label_transfer_to),
                    style = FDS.Typography.captionMSemibold,
                    color = FDS.Colors.characterTertiary,
                )
            }

            // To account section
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                // Bank logo using circular container with different color
                Box(
                    modifier = Modifier
                        .size(FDS.Sizer.Icon.icon40)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    if (!transactionHelper?.bankToUrl.isNullOrBlank()) {
                        imageLoader.LoadUrl(
                            url = transactionHelper?.bankToUrl,
                            isCache = true,
                            placeholderRes = null,
                            errorRes = null,
                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                        )
                    } else {
                        // Show shimmer only when no icon URL
                        FoundationAvatarShimmer(
                            modifier = Modifier,
                            size = FDS.Sizer.Icon.icon40,
                        )
                    }
                }
                Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))
                Column(modifier = Modifier.weight(1f)) {
                    val receiverName = transaction.receiveName
                    if (receiverName != null) {
                        FoundationText(
                            text = receiverName,
                            style = FDS.Typography.bodyB2,
                            color = FDS.Colors.characterPrimary,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                        )
                    } else {
                        // Show shimmer for receiver name
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding144 + FDS.Sizer.Padding.padding32), // Approx 180dp
                            height = FDS.Sizer.Icon.icon20,
                        )
                    }

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

                    val bankName = transaction.receiveBankName
                    val accountNo = transaction.toAccountNo
                    if (bankName != null || accountNo != null) {
                        FoundationText(
                            text = buildString {
                                append(bankName ?: "")
                                if (bankName != null && accountNo != null) {
                                    append(NewUIInitTransactionConstants.BANK_SEPARATOR)
                                }
                                append(accountNo ?: "")
                            },
                            style = FDS.Typography.captionL,
                            color = FDS.Colors.characterSecondary,
                        )
                    } else {
                        // Show shimmer for bank and account info
                        FoundationSkeletonRectangle(
                            modifier = Modifier.width(FDS.Sizer.Padding.padding100 + FDS.Sizer.Padding.padding20), // Approx 120dp
                            height = FDS.Sizer.Icon.icon16,
                        )
                    }
                }
            }

            FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Gap.gap8))

            // Check if this is a split transaction (tranType = "sn")
            if (transaction.tranType == "sn") {
                RenderSplitTransactionDetails(transaction)
            } else {
                RenderStandardTransactionDetails(transaction)
            }
        }
    }

    @Composable
    private fun RenderSplitTransactionDetails(transaction: TransactionDomain) {
        // Transaction ID
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_transaction_id),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transaction.mtId ?: "",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Content
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_content),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transaction.remark ?: "",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Transaction fee
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_fee),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount?.getAmountServer() ?: "",
                MoneyCurrency.VND.value,
            ),
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Split fee (feeSplit field from API)
        if (!transaction.feeSplit.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.feature_checker.R.string.transaction_label_split_fee),
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = Utils.g().getDotMoneyHasCcy(
                    transaction.feeSplit?.getAmountServer() ?: "",
                    MoneyCurrency.VND.value,
                ),
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )
        }

        // Fee method
        val feeMethodText = if (transaction.feePayMethod == "0") {
            stringResource(R.string.maker_transfer_dashboard_type_OUR)
        } else {
            stringResource(R.string.maker_transfer_dashboard_type_BEN)
        }
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_fee_method),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = feeMethodText,
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Split transactions details
        transaction.splitTransaction?.let { splitTransactions ->
            if (splitTransactions.isNotEmpty()) {
                splitTransactions.forEach { splitTx ->
                    FoundationInfoHorizontal(
                        title = "01 giao dịch",
                        titleStyle = FDS.Typography.bodyB2,
                        titleColor = FDS.Colors.characterSecondary,
                        value = Utils.g().getDotMoneyHasCcy(
                            splitTx.amount ?: "",
                            MoneyCurrency.VND.value,
                        ),
                        valueStyle = FDS.Typography.bodyB2,
                        valueColor = FDS.Colors.characterPrimary,
                    )
                }
            }
        }

        // Transfer type
        val (transferType, transferLabel) = if (transaction.process_time.isNullOrEmpty()) {
            stringResource(R.string.transaction_transfer_immediate) to stringResource(R.string.transaction_label_transfer_time)
        } else {
            stringResource(R.string.transaction_transfer_scheduled) to stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule)
        }

        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_transfer_type),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transferType,
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Show process time if scheduled
        if (!transaction.process_time.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = transferLabel,
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transaction.process_time ?: "",
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )
        }
    }

    @Composable
    private fun RenderStandardTransactionDetails(
        transaction: TransactionDomain,
    ) {
        // Transaction details
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_transaction_id),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transaction.mtId ?: "",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_content),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transaction.remark ?: "",
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_fee),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = Utils.g().getDotMoneyHasCcy(
                transaction.feeAmount?.getAmountServer() ?: "",
                MoneyCurrency.VND.value,
            ),
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Hình thức thu phí: theo yêu cầu, feePayMethod == "0" -> Phí ngoài, else -> Phí trong
        val feeMethodText = if (transaction.feePayMethod == "0") {
            stringResource(R.string.maker_transfer_dashboard_type_OUR)
        } else {
            stringResource(R.string.maker_transfer_dashboard_type_BEN)
        }
        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_fee_method),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = feeMethodText,
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        val (transferType, transferLabel) = if (transaction.process_time.isNullOrEmpty()) {
            stringResource(R.string.transaction_transfer_immediate) to stringResource(R.string.transaction_label_transfer_time)
        } else {
            stringResource(R.string.transaction_transfer_scheduled) to stringResource(R.string.maker_transfer_dashboard_transfer_date_schedule)
        }

        FoundationInfoHorizontal(
            title = stringResource(R.string.transaction_label_transfer_type),
            titleStyle = FDS.Typography.bodyB2,
            titleColor = FDS.Colors.characterSecondary,
            value = transferType,
            valueStyle = FDS.Typography.bodyB2,
            valueColor = FDS.Colors.characterPrimary,
        )

        // Show process time if scheduled
        if (!transaction.process_time.isNullOrEmpty()) {
            FoundationInfoHorizontal(
                title = transferLabel,
                titleStyle = FDS.Typography.bodyB2,
                titleColor = FDS.Colors.characterSecondary,
                value = transaction.process_time ?: "",
                valueStyle = FDS.Typography.bodyB2,
                valueColor = FDS.Colors.characterPrimary,
            )
        }
    }
}