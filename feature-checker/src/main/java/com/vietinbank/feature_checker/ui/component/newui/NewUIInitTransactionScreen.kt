package com.vietinbank.feature_checker.ui.component.newui

import NewUIInitAttachmentAmendGuaranteeScreen
import NewUIInitAttachmentGuaranteeScreen
import NewUIInitAttachmentIssueGuaranteeScreen
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.constants.Tags.CHECKER_TRANTYPE_GO
import com.vietinbank.core_common.constants.Tags.CHECKER_TRANTYPE_GOC
import com.vietinbank.core_common.constants.Tags.CHECKER_TRANTYPE_GOR
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyCurrency
import com.vietinbank.core_domain.models.checker.BatchUiModel
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationCategorySection
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationNavigationArea
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.LocalAppConfigManager
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.rememberAppNavBarDp
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_checker.ui.component.inits.TransactionDetailActions
import com.vietinbank.feature_checker.ui.fragment.InitTransactionViewModel
import kotlinx.coroutines.flow.distinctUntilChanged
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

// Component constants for non-design values
internal object NewUIInitTransactionConstants {
    const val AMOUNT_IN_WORDS_ALPHA = 0.7f
    const val CARD_BACKGROUND_ALPHA = 0.05f
    const val BANK_SEPARATOR = " - "
    const val CURRENCY_SUFFIX = " VND"

    // Animation durations for smooth UX transitions
    const val FADE_IN_DURATION = 300
    const val EXPAND_DURATION = 350
    const val FADE_OUT_DURATION = 200
    const val SHRINK_DURATION = 250
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewUIInitTransactionScreen(
    viewModel: InitTransactionViewModel,
    actions: InitTransactionActions,
    nextApproverState: NextApproverState = NextApproverState(),
) {
    // Collect states from ViewModel
    val transaction by viewModel.transactionState.collectAsState()
    val detailTransaction by viewModel.detailTransactionState.collectAsState()
    val isAgreeDisbursement by viewModel.isAgreeDisbursement.collectAsState()
    val termState by viewModel.termState.collectAsState()
    val isLoading by viewModel.isLoading.observeAsState(false)
    val renderer by viewModel.renderer.collectAsState()

    // Observe bank list cache to ensure icon recomposes when cache updates
    val banksCacheSnapshot = viewModel.banksFlow.collectAsState()

    // Derive bank icon URLs using cached bank list
    val transactionHelperState by viewModel.transactionHelperState.collectAsState()
    // Tab state
    var selectedTabIndex by remember { mutableStateOf(0) }

    // Only show header info when API has provided real data
    val hasHeaderData =
        (transaction.tranTypeName?.isNotBlank() == true) && (transaction.amount?.isNotBlank() == true)
    val appBarTitle = transaction.tranTypeName?.takeIf { it.isNotBlank() } ?: ""

    // loadmore file
    val batchFileState by viewModel.batchFileState.collectAsState()

    // Load more detection when scrolling near end
    val listState = rememberLazyListState()

    LaunchedEffect(listState, batchFileState.canLoadMore, batchFileState.isLoadingMore) {
        snapshotFlow { listState.layoutInfo }.distinctUntilChanged().collect { layoutInfo ->
            val totalItems = layoutInfo.totalItemsCount
            val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
            if (lastVisibleItem >= totalItems - 3 && totalItems > 0 && true == batchFileState.canLoadMore && false == batchFileState.isLoadingMore) {
                actions.onLoadMoreBatch()
            }
        }
    }

    // AppBar scroll behavior for large-title collapse
    val appBarScrollState = rememberFoundationAppBarScrollState()

    val isExpandableContent by remember(transaction.serviceType) {
        derivedStateOf {
            transaction.serviceType == Tags.TYPE_DISBURSEMENT_ONLINE
        }
    }

    val isRequestDataReady by remember(transaction) {
        derivedStateOf {
            !transaction.tranType.isNullOrBlank() || !transaction.mtId.isNullOrBlank()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackgroundLevel2()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding8),
                ) {
                    // AppBar with transaction type as title
                    FoundationAppBar(
                        title = appBarTitle,
                        onNavigationClick = actions.onBackClick,
                        actions = emptyList(),
                        isLightIcon = false,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Padding.padding8),
                        scrollState = appBarScrollState,
                    )

                    // Transaction header info - amount only (padding 8 as per Figma)
                    AnimatedVisibility(
                        visible = hasHeaderData,
                        enter = fadeIn(animationSpec = tween(NewUIInitTransactionConstants.FADE_IN_DURATION)) + expandVertically(
                            animationSpec = tween(NewUIInitTransactionConstants.EXPAND_DURATION),
                        ),
                        exit = fadeOut(animationSpec = tween(NewUIInitTransactionConstants.FADE_OUT_DURATION)) + shrinkVertically(
                            animationSpec = tween(NewUIInitTransactionConstants.SHRINK_DURATION),
                        ),
                    ) {
                        TransactionHeaderInfo(
                            transaction = transaction,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Padding.padding16)
                                .padding(
                                    top = FDS.Sizer.Padding.padding8,
                                    bottom = FDS.Sizer.Padding.padding16,
                                ),
                        )
                    }
                    // Tabs (padding 8 as per Figma)
                    FoundationTabs(
                        tabs = getTransactionTabs(transaction),
                        selectedIndex = selectedTabIndex,
                        onTabSelected = { index ->
                            selectedTabIndex = index
                            actions.onTabSelected(index)
                        },
                        horizontalItemPadding = 0.dp,
                        type = TabType.Pill,
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Padding.padding8)
                            .padding(bottom = FDS.Sizer.Padding.padding16),
                    )
                }
            },
        ) { paddingValues ->
            // Main content with padding 8 as per Figma
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(horizontal = FDS.Sizer.Padding.padding8), // Padding 8 as per Figma
                contentPadding = PaddingValues(
                    bottom = if (selectedTabIndex == 0) {
                        FDS.Sizer.Padding.padding200 // Always add space for buttons on Request tab
                    } else {
                        FDS.Sizer.Padding.padding16
                    },
                ),
            ) {
                val onFieldClick: (TransactionFieldEvent) -> Unit = { event ->
                    when (event) {
                        is TransactionFieldEvent.FileAttachmentClick -> {
                            viewModel.getDownloadFileID(
                                event.file,
                                isFileAttach = true,
                            )
                        }

                        is TransactionFieldEvent.ProfileAttachmentClick -> {
                            viewModel.getDownloadFileID(
                                event.file,
                                isFileAttach = true,
                            )
                        }

                        is TransactionFieldEvent.ListAttachmentClick -> {
                            actions.onDetailFileClick(event.titleType)
                        }

                        is TransactionFieldEvent.FileBaseAttachmentClick -> {
                            // Fallback: open file list
                            actions.onDetailFileClick(TransactionFieldEvent.FILE)
                        }

                        is TransactionFieldEvent.ViewAttachmentDocument -> {
                            selectedTabIndex++
                        }
                    }
                }
                // White container background with conditional watermark
                val hasAttachmentTab = isGuaranteeTransaction(transaction)
                when (selectedTabIndex) {
                    0 -> {
                        // Map renderer click events back to ViewModel/Actions

                        // doan nay khong can thiet co the bo
                        val tranType = transaction.tranType?.lowercase()
                        val isDefaultTransferType =
                            tranType == "in" || tranType == "ou" || tranType == "np"
                        val hasHeaderData =
                            (transaction.tranTypeName?.isNotBlank() == true) && (transaction.amount?.isNotBlank() == true)
                        val dataReady = if (!isDefaultTransferType) {
                            (renderer != null) && !transaction.mtId.isNullOrBlank()
                        } else {
                            hasHeaderData
                        }

                        item {
                            val (shapeContentItem, paddingContent) = if (transaction.isBatchTransaction()) {
                                RoundedCornerShape(
                                    topStart = FDS.Sizer.Radius.radius32,
                                    topEnd = FDS.Sizer.Radius.radius32,
                                ) to FDS.Sizer.Gap.gap16
                            } else {
                                RoundedCornerShape(FDS.Sizer.Radius.radius32) to FDS.Sizer.Gap.gap24
                            }
                            Crossfade(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(shapeContentItem)
                                    .background(FDS.Colors.white)
                                    .padding(top = FDS.Sizer.Gap.gap24, bottom = paddingContent),
                                targetState = (!dataReady),
                                animationSpec = tween(durationMillis = 220),
                                label = "request-content-crossfade",
                            ) { showSkeleton ->
                                if (showSkeleton) {
                                    RequestTabShimmer()
                                } else {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        // default render transfer
                                        renderer?.let {
                                            if (it.isNewRender()) {
                                                it.NewRenderTransactionDetails(
                                                    modifier = Modifier.fillMaxWidth(),
                                                    transaction = transaction,
                                                    transactionHelper = transactionHelperState,
                                                    onFieldClick = onFieldClick,
                                                )
                                            } else {
                                                it.RenderTransactionDetails(
                                                    transaction = transaction,
                                                    modifier = Modifier.fillMaxWidth(),
                                                    onFieldClick = onFieldClick,
                                                    viewModel = viewModel,
                                                    actions = TransactionDetailActions(
                                                        onAttachmentsClick = {
                                                            selectedTabIndex = 1
                                                            actions.onTabSelected(1)
                                                        },
                                                    ),
                                                )
                                            }
                                        }

                                        // Người phê duyệt chỉ hiển thị với loại điện đơn
                                        NextApproveShimmer(
                                            nextApproveState = nextApproverState,
                                            onShowClick = { actions.onShowNextApproverSelection() },
                                        )
                                    }
                                }
                            }
                        }

                        // Chuyên tiền theo file => không có người người phê duyêt
                        if (transaction.isBatchTransaction()) {
                            item {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(FDS.Colors.white)
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                                ) {
                                    FoundationSelector(
                                        boxType = SelectorType.Checkbox,
                                        isSelected = (batchFileState.batchMaximum != 0 && batchFileState.batchMaximum == batchFileState.batchSelectedLst.size),
                                        title = stringResource(
                                            R.string.approve_file_selector,
                                            batchFileState.batchSelectedLst.size.toString(),
                                            batchFileState.batchMaximum.toString(),
                                        ),
                                        onClick = {
                                            // chon tat ca + huy chon tat ca
                                            actions.onToggleBatchAllClick.invoke()
                                        },
                                    )

                                    FoundationDivider()
                                }
                            }

                            itemsIndexed(
                                batchFileState.batchLst ?: emptyList(),
                                key = { index, fileItem -> "key_${index}_${fileItem.mtId}" },
                            ) { index, fileItem ->
                                val shapeItem = if (index != batchFileState.batchLst?.lastIndex) {
                                    RoundedCornerShape(FDS.Sizer.Radius.radius0)
                                } else {
                                    RoundedCornerShape(
                                        bottomStart = FDS.Sizer.Radius.radius32,
                                        bottomEnd = FDS.Sizer.Radius.radius32,
                                    )
                                }
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clip(shapeItem)
                                        .background(FDS.Colors.white)
                                        .padding(horizontal = FDS.Sizer.Gap.gap24),

                                ) {
                                    BatchTransactionsCard(
                                        isSelected = true == batchFileState.batchSelectedLst.contains(
                                            fileItem.mtId,
                                        ),
                                        transaction = fileItem,
                                        onSelectionToggle = {
                                            actions.onToggleBatch.invoke(
                                                fileItem.mtId ?: "",
                                            )
                                        },
                                    )

                                    if (fileItem != batchFileState.batchLst?.lastOrNull()) {
                                        FoundationDivider()
                                    }
                                }
                            }

                            // Loadmore thêm điện con
                            if (true == batchFileState.isLoadingMore) {
                                // Loading indicator while fetching more
                                item(
                                    key = "loading-more",
                                    contentType = "loading",
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(FDS.Sizer.Padding.padding16),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                            color = FDS.Colors.primary,
                                            strokeWidth = FDS.Sizer.Stroke.stroke2,
                                        )
                                    }
                                }
                            }
                        }
                    }

                    1 -> {
                        item {
                            if (hasAttachmentTab) {
                                GetAttachmentGuaranteeContent(
                                    transaction = transaction,
                                    onFieldClick = onFieldClick,
                                )
                            } else {
                                getApprovalHistoryContent(transaction = transaction)()
                            }
                        }
                    }

                    2 -> {
                        if (hasAttachmentTab) {
                            item {
                                getApprovalHistoryContent(transaction = transaction)()
                            }
                        }
                    }
                }

                item {
                    AnimatedVisibility(
                        visible = selectedTabIndex == 0 && isRequestDataReady && isExpandableContent,
                        modifier = Modifier.fillMaxWidth(),
                    ) {
                        Box(
                            modifier = Modifier
                                .wrapContentWidth()
                                .align(Alignment.Center)
                                .padding(vertical = FDS.Sizer.Padding.padding16)
                                .clip(RoundedCornerShape(percent = 50))
                                .safeClickable {
                                    if (viewModel.isExpanded) {
                                        viewModel.collapseAll()
                                    } else {
                                        viewModel.expandAll()
                                    }
                                }
                                .background(FDS.Colors.backgroundBgHighlight)
                                .padding(
                                    vertical = FDS.Sizer.Padding.padding8,
                                    horizontal = FDS.Sizer.Padding.padding24,
                                ),
                        ) {
                            FoundationText(
                                stringResource(
                                    if (!viewModel.isExpanded) {
                                        R.string.feature_checker_expand_all
                                    } else {
                                        R.string.feature_checker_collapse_all
                                    },
                                ),
                                style = FDS.Typography.headingH6,
                                color = FDS.Colors.characterHighlighted,
                            )
                        }
                    }
                }
            }
        }

        // điều kiện điều khoản sẽ show cùng với các action bottom
//        termState?.let { (term, _) ->
//            TermsAndConditionsCard(
//                term = term,
//                isAgreed = isAgreeDisbursement,
//                onAgreementChanged = actions.onAgreeDisbursementChanged,
//            )
//        }
        // Floating bottom buttons - only show for CHECKER role when data is ready
        val isRequestDataReady =
            !transaction.tranType.isNullOrBlank() || !transaction.mtId.isNullOrBlank()
        // Check if user has Checker role to show approve/reject buttons
        val isChecker = viewModel.isChecker()
        AnimatedVisibility(
            visible = selectedTabIndex == 0 && isRequestDataReady && isChecker,
            enter = fadeIn(animationSpec = tween(300)) + expandVertically(animationSpec = tween(300)),
            exit = fadeOut(animationSpec = tween(200)) + shrinkVertically(animationSpec = tween(200)),
            modifier = Modifier.align(Alignment.BottomCenter),
        ) {
            val bottomPadding =
                rememberAppNavBarDp(LocalAppConfigManager.current) ?: FDS.Sizer.Padding.padding0

            // Using FoundationNavigationArea for the bottom scrim with buttons
            val expanded by remember(listState) {
                derivedStateOf {
                    listState.firstVisibleItemIndex == 0 &&
                        listState.firstVisibleItemScrollOffset < 20
                }
            }

            TermAndConditionCompactBottom(
                expanded = expanded,
                term = termState,
                checked = isAgreeDisbursement,
                onCheckedChange = { actions.onAgreeDisbursementChanged() },
            ) {
                BottomActionButtons(
                    isLoading = isLoading, // This will disable buttons during approve/reject, but won't hide them
                    multipleAction = viewModel.getMultipleAction(),
                    onRejectClick = actions.onRejectClick,
                    onApproveClick = actions.onApproveClick,
                    isAcceptTerm = isAgreeDisbursement,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Padding.padding24)
                        .padding(
                            bottom = bottomPadding + FDS.Sizer.Padding.padding16,
                            top = FDS.Sizer.Padding.padding16,
                        ),
                )
            }
        }
    }
}

@Composable
fun getTransactionTabs(transaction: TransactionDomain): List<String> {
    return buildList {
        add(stringResource(com.vietinbank.core_ui.R.string.transaction_tab_request))
        if (isGuaranteeTransaction(transaction)) {
            add(stringResource(com.vietinbank.feature_checker.R.string.feature_checker_attachment))
        }
        add(stringResource(com.vietinbank.core_ui.R.string.transaction_tab_history))
    }
}

@Composable
fun GetAttachmentGuaranteeContent(
    transaction: TransactionDomain,
    onFieldClick: (TransactionFieldEvent) -> Unit,
) {
    when (transaction.tranType) {
        CHECKER_TRANTYPE_GOR -> {
            NewUIInitAttachmentGuaranteeScreen(
                transaction = transaction,
                onFieldClick = onFieldClick,
            )
        }

        CHECKER_TRANTYPE_GO -> {
            NewUIInitAttachmentIssueGuaranteeScreen(
                transaction = transaction,
                onFieldClick = onFieldClick,
            )
        }

        CHECKER_TRANTYPE_GOC -> {
            NewUIInitAttachmentAmendGuaranteeScreen(
                transaction = transaction,
                onFieldClick = onFieldClick,
            )
        }

        else -> {
            if (transaction.serviceType == Tags.TYPE_DISBURSEMENT_ONLINE) {
                ApprovalDocumentList(transaction)
            } else {
                NewUIInitAttachmentAmendGuaranteeScreen(
                    transaction = transaction,
                    onFieldClick = {},
                )
            }
        }
    }
}

@Composable
fun getApprovalHistoryContent(
    transaction: TransactionDomain,
): @Composable () -> Unit = {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
            .background(FDS.Colors.white),
    ) {
        ApprovalHistoryTabContent(
            transaction = transaction,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = FDS.Sizer.Padding.padding32)
                .safeClickable {},
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = stringResource(R.string.maker_transfer_account_result_check_limited_approval),
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
            Image(
                painter = painterResource(id = R.drawable.ic_right),
                contentDescription = null,
                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
            )
        }
    }
}

// Helper function to determine if transaction has attachment tab
private fun isGuaranteeTransaction(transaction: TransactionDomain): Boolean {
    return transaction.tranType == CHECKER_TRANTYPE_GO ||
        transaction.tranType == CHECKER_TRANTYPE_GOC ||
        transaction.tranType == CHECKER_TRANTYPE_GOR ||
        transaction.serviceType == Tags.TYPE_DISBURSEMENT_ONLINE
}

@Composable
private fun TermAndConditionCompactBottom(
    modifier: Modifier = Modifier,
    expanded: Boolean,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    term: Pair<String, String>?,
    gradientColor: List<Color> = listOf(
        Color(0xFF1A4E74),
        FDS.Colors.blue900,
    ),
    bottomButton: @Composable () -> Unit,
) {
    Column(
        modifier = modifier
            .clip(
                RoundedCornerShape(
                    topStart = FDS.Sizer.Radius.radius32,
                    topEnd = FDS.Sizer.Radius.radius32,
                ),
            )
            .drawWithCache {
                val gradient = Brush.verticalGradient(
                    colors = gradientColor,
                    startY = 0f,
                    endY = size.height,
                )
                onDrawBehind {
                    drawRect(gradient)
                }
            }
            .padding(top = FDS.Sizer.Padding.padding16)
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioNoBouncy,
                    stiffness = Spring.StiffnessMediumLow,
                ),
            ),
    ) {
        Row(
            modifier = Modifier
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                ) {
                    onCheckedChange.invoke(!checked)
                }
                .padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalAlignment = Alignment.Top,
        ) {
            term?.let {
                FoundationSelector(
                    modifier = Modifier.align(Alignment.Top),
                    boxType = SelectorType.Checkbox,
                    isSelected = checked,
                    onClick = {
                        onCheckedChange.invoke(!checked)
                    },
                )
                FoundationText(
                    modifier = Modifier.weight(1f),
                    text = buildAnnotatedString {
                        append(it.first)
                        withStyle(style = SpanStyle(textDecoration = TextDecoration.Underline)) {
                            append(it.second)
                        }
                    },
                    style = FDS.Typography.bodyB2,
                    color = FDS.Colors.white,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = if (expanded) Int.MAX_VALUE else 1,
                )
            }
        }
        bottomButton()
    }
}

@Composable
private fun ApprovalHistoryTab(
    transaction: TransactionDomain,
    onRowClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
            .background(FDS.Colors.white),
    ) {
        ApprovalHistoryTabContent(
            transaction = transaction,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = FDS.Sizer.Padding.padding32)
                .safeClickable { onRowClick() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            FoundationText(
                text = stringResource(R.string.maker_transfer_account_result_check_limited_approval),
                style = FDS.Typography.bodyB2Emphasized,
                color = FDS.Colors.characterHighlighted,
            )
            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap4))
            Image(
                painter = painterResource(id = R.drawable.ic_right),
                contentDescription = null,
                modifier = Modifier.size(FDS.Sizer.Icon.icon16),
            )
        }
    }
}

@Composable
private fun TransactionHeaderInfo(
    transaction: TransactionDomain,
    modifier: Modifier = Modifier,
) {
    val amount by remember(transaction) {
        derivedStateOf {
            if (transaction.tranType == Tags.TYPE_DISBURSEMENT_ONLINE.lowercase()) {
                transaction.loanLimit
            } else {
                transaction.amount
            }
        }
    }

    val amountInWord by remember(transaction) {
        derivedStateOf {
            if (transaction.tranType == Tags.TYPE_DISBURSEMENT_ONLINE.lowercase()) {
                transaction.loanLimitInWord
            } else {
                transaction.amountInWords
            }
        }
    }

    Column(
        modifier = modifier,
    ) {
        // Amount - transaction type is now in AppBar
        FoundationText(
            text = amount ?: "",
            style = FDS.Typography.headingH1.copy(
                fontWeight = FontWeight.Bold,
            ),
            color = FDS.Colors.white,
            modifier = Modifier.fillMaxWidth(),
        )

        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))

        // Amount in words
        FoundationText(
            text = amountInWord ?: "",
            style = FDS.Typography.bodyB2,
            color = FDS.Colors.white.copy(alpha = NewUIInitTransactionConstants.AMOUNT_IN_WORDS_ALPHA),
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
private fun TermsAndConditionsCard(
    modifier: Modifier = Modifier,
    term: String,
    isAgreed: Boolean,
    onAgreementChanged: () -> Unit,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(FDS.Sizer.Radius.radius16),
        colors = CardDefaults.cardColors(
            containerColor = FDS.Colors.backgroundBgContainer.copy(
                alpha = NewUIInitTransactionConstants.CARD_BACKGROUND_ALPHA,
            ),
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(FDS.Sizer.Padding.padding16)
                .safeClickable { onAgreementChanged() },
            verticalAlignment = Alignment.Top,
        ) {
            Checkbox(
                checked = isAgreed,
                onCheckedChange = { onAgreementChanged() },
                colors = CheckboxDefaults.colors(
                    checkedColor = FDS.Colors.primary,
                    uncheckedColor = FDS.Colors.characterSecondary,
                ),
            )

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap12))

            FoundationText(
                text = term,
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterPrimary,
                modifier = Modifier.weight(1f),
            )
        }
    }
}

@Composable
private fun BatchTransactionsCard(
    isSelected: Boolean,
    transaction: BatchUiModel,
    onSelectionToggle: (String) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Gap.gap16)
            .safeClickable {
                onSelectionToggle.invoke(transaction.mtId ?: "")
            },
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
        ) {
            FoundationSelector(
                modifier = Modifier.padding(),
                boxType = SelectorType.Checkbox,
                isSelected = isSelected,
                onClick = { onSelectionToggle.invoke(transaction.mtId ?: "") },
            )

            FoundationText(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = FDS.Colors.characterSecondary)) {
                        append(stringResource(R.string.transaction_number_label))
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            color = when {
                                isSelected -> FDS.Colors.characterHighlighted
                                else -> FDS.Colors.characterPrimary
                            },
                            fontWeight = FontWeight.SemiBold,
                        ),
                    ) {
                        append(transaction.mtId)
                    }
                },
                style = FDS.Typography.captionL,
                modifier = Modifier.weight(1f),
            )
        }

        FoundationInfoHorizontal(
            title = stringResource(R.string.approve_file_amount),
            titleStyle = FDS.Typography.captionL,
            titleColor = FDS.Colors.characterSecondary,
            value = Utils.g().getDotMoneyHasCcy(
                transaction.amount ?: "",
                transaction.currency ?: MoneyCurrency.VND.value,
            ),
            valueStyle = FDS.Typography.captionLSemibold,
            valueColor = FDS.Colors.characterPrimary,
        )

        val nameDisplay = if (transaction.receiveName.isNullOrEmpty()) {
            transaction.toAccountNo ?: ""
        } else {
            transaction.receiveName.plus("\n${transaction.toAccountNo}")
        }

        FoundationInfoHorizontal(
            title = stringResource(R.string.manager_detail_transfer_to),
            titleStyle = FDS.Typography.captionL,
            titleColor = FDS.Colors.characterSecondary,
            value = nameDisplay,
            valueStyle = FDS.Typography.captionLSemibold,
            valueColor = FDS.Colors.characterPrimary,
        )

        FoundationInfoHorizontal(
            title = stringResource(R.string.maker_transfer_dashboard_content),
            titleStyle = FDS.Typography.captionL,
            titleColor = FDS.Colors.characterSecondary,
            value = transaction.remark ?: "",
            valueStyle = FDS.Typography.captionLSemibold,
            valueColor = FDS.Colors.characterPrimary,
        )
    }
}

@Composable
private fun ApprovalHistoryTabContent(
    transaction: TransactionDomain,
) {
    ApprovalHistoryTimeline(activityLogs = transaction.activityLogs)
}

@Composable
internal fun BottomActionButtons(
    isLoading: Boolean,
    multipleAction: Pair<Boolean, Boolean>,
    onRejectClick: () -> Unit,
    onApproveClick: () -> Unit,
    modifier: Modifier = Modifier,
    isAcceptTerm: Boolean = true,
) {
    if (multipleAction.first) {
        // duyet 1 giao dich => di tu luong duyet nhieu
        Box(modifier = modifier) {
            FoundationButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(R.string.common_confirm),
                onClick = if (multipleAction.second) {
                    onApproveClick
                } else {
                    onRejectClick
                },
                enabled = !isLoading && isAcceptTerm,
            )
        }
    } else {
        Row(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap12),
        ) {
            // Reject button - Dark style (isLightButton = false)
            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.transaction_button_reject),
                onClick = onRejectClick,
                enabled = !isLoading && isAcceptTerm,
                isLightButton = false, // Dark button for reject
                leadingIcon = painterResource(id = R.drawable.ic_common_banned_24),
                modifier = Modifier.weight(1f),
            )

            // Approve button - Light style (isLightButton = true)
            FoundationButton(
                text = stringResource(com.vietinbank.core_ui.R.string.transaction_button_approve),
                onClick = onApproveClick,
                enabled = !isLoading && isAcceptTerm,
                isLightButton = true, // Light button for approve
                leadingIcon = painterResource(id = R.drawable.ic_common_success_24),
                modifier = Modifier.weight(1f),
            )
        }
    }
}

// Approval history timeline moved to ApprovalHistoryTimeline.kt (reused in Success screen)

@Composable
private fun RequestTabShimmer(numberShimmer: Int = 5) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding16),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
    ) {
        repeat(numberShimmer) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
            ) {
                // Circle avatar as shown in screenshot
                com.vietinbank.core_ui.components.FoundationSkeletonCircle(
                    size = FDS.Sizer.Icon.icon40,
                )

                // Text content: 2 lines with different widths as shown in screenshot
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    // First line - long (as shown in screenshot)
                    com.vietinbank.core_ui.components.FoundationSkeletonRectangle(
                        modifier = Modifier.fillMaxWidth(0.85f),
                        height = FDS.Sizer.Icon.icon16,
                    )

                    // Second line - shorter (as shown in screenshot)
                    com.vietinbank.core_ui.components.FoundationSkeletonRectangle(
                        modifier = Modifier.fillMaxWidth(0.65f),
                        height = FDS.Sizer.Icon.icon16,
                    )
                }
            }
        }
    }
}

private fun TransactionDomain.isBatchTransaction(): Boolean {
    return tranType == "ba" || tranType == "btx"
}

@Preview(showBackground = true, backgroundColor = 0xFF002E51)
@Composable
private fun NewUIInitTransactionScreenPreview() {
    // Note: Preview doesn't use actual ViewModel due to dependency injection
    // This is just for UI preview purposes

    val mockTransaction = TransactionDomain().apply {
        tranTypeName = "Chuyển tiền nhanh 24/7"
        mtId = "************"
        amount = "1600000"
        fromAccountNo = "*********"
        toAccountNo = "************"
        receiveName = "Công ty TNHH Có phần thương mại\nMột thành viên Ban Mai Xanh"
        receiveBankName = "Techcombank"
        remark = "Chi lương T6"
        feeAmount = "0"
        feeType = "Phí ngoài"
        tranType = "np"
        status = "pending"
        statusName = "Chờ duyệt"
        currency = "VND"
        amountInWords = "Một triệu sáu trăm nghìn đồng"
        groupType = "transfer"
        serviceType = "internal"
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackgroundLevel2(),
    ) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    FoundationAppBar(
                        title = "",
                        onNavigationClick = {},
                        actions = emptyList(),
                        isLightIcon = false,
                        modifier = Modifier.fillMaxWidth(),
                    )

                    TransactionHeaderInfo(
                        transaction = mockTransaction,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .padding(bottom = FDS.Sizer.Padding.padding24),
                    )

                    FoundationTabs(
                        tabs = listOf("Yêu cầu", "Lịch sử phê duyệt"),
                        selectedIndex = 0,
                        onTabSelected = {},
                        type = TabType.Pill,
                        modifier = Modifier
                            .padding(horizontal = FDS.Sizer.Padding.padding24)
                            .padding(bottom = FDS.Sizer.Padding.padding16),
                    )
                }
            },
        ) { paddingValues ->
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentPadding = PaddingValues(
                    bottom = FDS.Sizer.Padding.padding100,
                ),
            ) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(
                            topStart = FDS.Sizer.Radius.radius32,
                            topEnd = FDS.Sizer.Radius.radius32,
                            bottomStart = FDS.Sizer.Radius.radius0,
                            bottomEnd = FDS.Sizer.Radius.radius0,
                        ),
                        colors = CardDefaults.cardColors(
                            containerColor = FDS.Colors.white,
                        ),
                        elevation = CardDefaults.cardElevation(
                            defaultElevation = FDS.Effects.elevationNone,
                        ),
                    ) {}
                }
            }
        }

        val bottomPadding =
            rememberAppNavBarDp(LocalAppConfigManager.current) ?: FDS.Sizer.Padding.padding0
        FoundationNavigationArea(
            modifier = Modifier.align(Alignment.BottomCenter),
            height = 136.dp,
            topRadius = FDS.Sizer.Radius.radius16,
            startStop = 0.5f,
        ) {
            BottomActionButtons(
                isLoading = false,
                multipleAction = Pair(false, false),
                onRejectClick = {},
                onApproveClick = {},
                isAcceptTerm = true,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(bottom = bottomPadding + FDS.Sizer.Padding.padding16, top = 16.dp),
            )
        }
    }
}

@Composable
fun NextApproveShimmer(
    nextApproveState: NextApproverState,
    onShowClick: () -> Unit,
) {
    // hien thi nguoi phe duyt tiep theo neu co
    // Khu vực chọn người phê duyệt tiếp theo (giữ nguyên từ UI mặc định)
    val showNextApproverSection = nextApproveState.allApprovers.isNotEmpty()
    AnimatedVisibility(
        visible = true,
        enter = fadeIn(
            animationSpec = tween(durationMillis = NewUIInitTransactionConstants.FADE_IN_DURATION),
        ) + expandVertically(
            animationSpec = tween(durationMillis = NewUIInitTransactionConstants.EXPAND_DURATION),
            expandFrom = Alignment.Top,
        ),
        exit = fadeOut(
            animationSpec = tween(durationMillis = NewUIInitTransactionConstants.FADE_OUT_DURATION),
        ) + shrinkVertically(
            animationSpec = tween(durationMillis = NewUIInitTransactionConstants.SHRINK_DURATION),
            shrinkTowards = Alignment.Top,
        ),
    ) {
        val isSelectedApprove = nextApproveState.selectedApprovers.isNotEmpty()
        val isSelectedAll =
            nextApproveState.selectedApprovers.size == nextApproveState.allApprovers.size
        val selectionApprove = when {
            isSelectedApprove && !isSelectedAll -> {
                stringResource(
                    R.string.next_approver_selected_count,
                    nextApproveState.selectedApprovers.size,
                )
            }

            else -> {
                stringResource(R.string.next_approver_description)
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = FDS.Sizer.Gap.gap24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
        ) {
            if (showNextApproverSection) {
                FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16))
                FoundationCategorySection(
                    title = stringResource(R.string.transaction_next_approver_title),
                    value = selectionApprove,
                    icon = R.drawable.ic_drop_down,
                    onClick = onShowClick,
                )
            }
        }
    }
}
