package com.vietinbank.feature_checker.ui.fragment

import android.content.Context
import android.content.Intent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.removeVietNam
import com.vietinbank.core_common.extensions.saveBase64File
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_data.processor.TransactionProcessorFactory
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.BatchFileDomain
import com.vietinbank.core_domain.models.checker.BatchUiModel
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.GetBatchTransactionListParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.GetExportNSNNFileTemplateParams
import com.vietinbank.core_domain.models.checker.GetTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListDomain
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.PreApproveDomain
import com.vietinbank.core_domain.models.checker.PreApproveParams
import com.vietinbank.core_domain.models.checker.PreRejectParams
import com.vietinbank.core_domain.models.checker.RejectParams
import com.vietinbank.core_domain.models.checker.SubTransactionDomain
import com.vietinbank.core_domain.models.checker.SubTransactionParams
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionHelperDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.util.TransactionProcessor
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.inits.ITransactionDetailRenderer
import com.vietinbank.feature_checker.ui.component.inits.TransactionDetailRendererFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class InitTransactionViewModel
@Inject constructor(
    val useCase: CheckerUserCase,
    override val sessionManager: ISessionManager,
    val processorFactory: TransactionProcessorFactory,
    val rendererFactory: TransactionDetailRendererFactory,
    val dataSourceProperties: DataSourceProperties,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    private val transferCacheManager: ITransferCacheManager,
    private val transferUseCase: TransferUseCase,
) : BaseViewModel() {
    companion object {
        const val MULTIPLE_NONE = "NONE"
        const val MULTIPLE_APPROVE = "APPROVE"
        const val MULTIPLE_REJECT = "REJECT"
    }

    // them dieu kien de kiem tra xem co phai di tu luong duyet nhieu hay khong
    private var multipleType: String = MULTIPLE_NONE
    fun setMultipleType(type: String) {
        multipleType = type
    }

    fun getMultipleAction(): Pair<Boolean, Boolean> =
        Pair(multipleType != MULTIPLE_NONE, multipleType == MULTIPLE_APPROVE)

    // Check if current user has Checker role
    fun isChecker(): Boolean = userProf.isChecker()

    // hien thi điều kiện phe duyet GNNT
    private val _termState = MutableStateFlow<Pair<String, String>?>(null)
    val termState = _termState.asStateFlow()
    fun getTermAndCondition(transaction: TransactionDomain?) {
        _termState.value = when (transaction?.tranType) {
            "do" -> {
                if (Tags.TYPE_DISBURSEMENT_FOREIGN == transaction.subType) {
                    Pair(
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.disbursement_term_condition),
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.disbursement_term_condition_foreign_file),
                    )
                } else {
                    Pair(
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.disbursement_term_condition),
                        resourceProvider.getString(com.vietinbank.core_ui.R.string.disbursement_term_condition_vnd_file),
                    )
                }
            }
            else -> null
        }
    }

    var isExpanded: Boolean by mutableStateOf(false)
        private set

    fun expandAll() {
        isExpanded = true
    }

    fun collapseAll() {
        isExpanded = false
    }

    private val _isAgreeDisbursement = MutableStateFlow(false)
    val isAgreeDisbursement = _isAgreeDisbursement.asStateFlow()
    fun onChangeAgreeDisbursement() {
        _isAgreeDisbursement.value = !_isAgreeDisbursement.value
    }

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private lateinit var currentProcessor: TransactionProcessor

    private val _renderer = MutableStateFlow<ITransactionDetailRenderer?>(null)
    val renderer = _renderer.asStateFlow()

    // Lưu trữ transactions từ Bundle
    var originalTransaction: TransactionListDomain? = null

    // chi tiet giao dich
    private val detailTransaction = MutableStateFlow<GetTransactionDetailDomain?>(null)
    val detailTransactionState = detailTransaction.asStateFlow()

    private val _transaction = MutableStateFlow(TransactionDomain())
    val transactionState = _transaction.asStateFlow()
    fun getTransaction(): TransactionDomain = _transaction.value

    // Selected transaction IDs for batch processing
    private val _selectedTransactions = MutableStateFlow<List<String>>(emptyList())
    val selectedTransactions = _selectedTransactions.asStateFlow()

    private val _openFileIntent = MutableSharedFlow<Intent>()
    val openFileIntent = _openFileIntent.asSharedFlow()

    private val _errorMessage = MutableSharedFlow<String>()
    val errorMessage = _errorMessage.asSharedFlow()

    private var byBin: Map<String, DataBankDomain> = emptyMap()
    private var byEbank: Map<String, DataBankDomain> = emptyMap()
    private var byName: Map<String, DataBankDomain> = emptyMap()
    private var vietinItem: DataBankDomain? = null

    // Current selected transaction type code (for compatibility)
    // sau nay can mo rong gi thi se them o day
    var transactionHelperState: StateFlow<TransactionHelperDomain?> =
        combine(_transaction, transferCacheManager.banksCacheFlow) { transaction, bankCache ->
            TransactionHelperDomain(
                bankFromUrl = deriveFromIconUrl(transaction),
                bankToUrl = deriveToIconUrl(transaction, null),
            )
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = null,
        )

    // Expose banks cache flow so UI can recompose when cache updates
    val banksFlow = transferCacheManager.banksCacheFlow

    fun ensureBankListLoaded() {
        // If cache is empty, try to load once silently
        if (true != transferCacheManager.getBankList()?.isNotEmpty()) {
            launchJobSilent {
                val res = transferUseCase.getNapasBankList(
                    NapasBankListParams(
                        username = userProf.getUserName() ?: "",
                        cifno = userProf.getCifNo() ?: "",
                    ),
                )
                when (res) {
                    is Resource.Success -> {
                        val list = res.data.dataBanks
                        transferCacheManager.saveBankList(list)
                        // reset maps to rebuild with fresh data
                        byBin = emptyMap()
                        byEbank = emptyMap()
                        byName = emptyMap()
                        vietinItem = null
                    }

                    else -> {
                        // no-op; UI will keep placeholder
                    }
                }
            }
        }
    }

    private fun ensureBankMapsBuilt() {
        if (byBin.isNotEmpty()) return
        val list = transferCacheManager.getBankList().orEmpty()
        if (list.isEmpty()) return

        byBin = list.mapNotNull {
            it.binCode?.trim()?.takeIf { c -> c.isNotEmpty() }?.let { code -> code to it }
        }.toMap()
        byEbank = list.mapNotNull {
            it.ebankCode?.trim()?.takeIf { c -> c.isNotEmpty() }?.let { code -> code to it }
        }.toMap()
        byName = list.mapNotNull { item ->
            val key = normalizeBankName(item.bankName ?: item.shortName)
            key?.takeIf { it.isNotEmpty() }?.let { it to item }
        }.toMap()

        vietinItem = list.firstOrNull { it.type == Tags.TransferType.TYPE_IN } ?: list.firstOrNull {
            normalizeBankName(it.bankName)?.contains("VIETINBANK") == true || normalizeBankName(it.shortName)?.contains(
                "VIETINBANK",
            ) == true
        }
        printLog("BankIcons: built maps — bin=${byBin.size}, ebank=${byEbank.size}, name=${byName.size}, vietin=${vietinItem?.shortName}")
    }

    private fun normalizeBankName(name: String?): String? {
        if (name.isNullOrBlank()) return null
        // Reuse existing helpers to strip Vietnamese diacritics and spaces
        val stripped = name.removeVietNam().uppercase().replace("NGAN HANG", "").replace("TMCP", "")
            .replace("NH", "").replace("  ", " ").trim()
        return stripped
    }

    /**
     * Icon for sender bank.
     * For checker flows, sender is always VietinBank (internal account),
     * use VietinBank icon from cached list when available.
     */
    fun deriveFromIconUrl(@Suppress("UNUSED_PARAMETER") transaction: TransactionDomain?): String? {
        ensureBankMapsBuilt()
        return vietinItem?.icon
    }

    /**
     * Icon for receiver bank, mapped by code when available, fallback by name.
     * - Internal transfer (tranType == "in"): VietinBank icon.
     * - External (ou/np/...): map by code (receiveBank if numeric), else by name.
     */
    fun deriveToIconUrl(
        transaction: TransactionDomain?,
        detail: GetTransactionDetailDomain?,
    ): String? {
        ensureBankMapsBuilt()
        val tranType = transaction?.tranType?.lowercase()
        if (tranType == Tags.TransferType.TYPE_IN) {
            return vietinItem?.icon
        }
        // Try to extract a code from detail->transaction.receiveBank (if numeric)
        val codeCandidate = detail?.transaction?.receiveBank?.trim()
        val code = codeCandidate?.takeIf { it.isNotEmpty() && it.all { ch -> ch.isDigit() } }
        val byCode = code?.let { byBin[it] ?: byEbank[it] }
        if (byCode != null) {
            printLog("BankIcons: matched by code=$code -> ${byCode.shortName}")
            return byCode.icon
        }

        // Fallback by name
        val nameKey = normalizeBankName(transaction?.receiveBankName)
        val byN = nameKey?.let { byName[it] }
        if (byN != null) {
            printLog("BankIcons: matched by name='${transaction?.receiveBankName}' -> ${byN.shortName}")
        } else {
            printLog("BankIcons: no match for name='${transaction?.receiveBankName}', code=$codeCandidate")
        }
        return byN?.icon
    }

    // Toggle transaction selection
    fun toggleTransactionSelection(mtId: String) {
        val currentSelections =
            _batchFileState.value.batchSelectedLst.toMutableList()
        if (currentSelections.contains(mtId)) {
            currentSelections.remove(mtId)
        } else {
            currentSelections.add(mtId)
        }
        _batchFileState.value = _batchFileState.value.copy(
            batchSelectedLst = currentSelections.toSet(),
        )
    }

    // Select all transactions in a batch
    fun toggleAllTransactions() {
        // chua call api get all
        if (_batchFileState.value.batchAllLst.isEmpty()) {
            getBatchTransaction(isAll = true)
            return
        }
        _batchFileState.value = _batchFileState.value.copy(
            batchSelectedLst = if (_batchFileState.value.batchMaximum == _batchFileState.value.batchSelectedLst?.size) {
                emptySet()
            } else {
                _batchFileState.value.batchAllLst ?: emptySet()
            },
        )
    }

    fun <T> toggleSelectAllGeneric(
        childTransactions: List<T>,
        getMtId: (T) -> String?,
    ) {
        if (_selectedTransactions.value.size == childTransactions.size) {
            _batchFileState.value.copy(
                batchSelectedLst = emptySet(),
            )
        } else {
            val allMtIds =
                childTransactions.mapNotNull { getMtId(it) }.filter { it.isNotEmpty() }.toSet()
            _batchFileState.value.copy(
                batchSelectedLst = allMtIds,
            )
        }
    }

    fun isBatchFile(tranType: String?): Boolean {
        return tranType == "ba" || tranType == "btx"
    }

    private val _errorEvent = MutableSharedFlow<String?>()
    val errorEvent = _errorEvent.asSharedFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    private val _preApprove = MutableSharedFlow<PreApproveDomain?>()
    val preApprove = _preApprove.asSharedFlow()

    private val _doRejectResponse = MutableSharedFlow<Resource<ApproveDomain>?>()
    val doRejectResponse = _doRejectResponse.asSharedFlow()

    private val _getTransactionDetail = MutableSharedFlow<Resource<GetTransactionDetailDomain>?>()
    val getTransactionDetail = _getTransactionDetail.asSharedFlow()

    // Next Approver states
    private val _isNextApproverEnabled = MutableStateFlow(false)
    val isNextApproverEnabled = _isNextApproverEnabled.asStateFlow()

    private val _nextApprovers = MutableStateFlow<List<ApproverDomains>>(emptyList())
    val nextApprovers = _nextApprovers.asStateFlow()

    private val _selectedNextApprovers = MutableStateFlow<List<ApproverDomains>>(emptyList())
    val selectedNextApprovers = _selectedNextApprovers.asStateFlow()

    private val _isNextApproverBottomSheetVisible = MutableStateFlow(false)
    val isNextApproverBottomSheetVisible = _isNextApproverBottomSheetVisible.asStateFlow()

    // Loading state + in-flight guard for next-approver fetching
    private val _isNextApproverLoading = MutableStateFlow(false)
    val isNextApproverLoading = _isNextApproverLoading.asStateFlow()

    @Volatile
    private var isFetchingNextApprovers = false

    // Toggle next approver enabled state
    fun toggleNextApproverEnabled() {
        // Turning ON
        if (!_isNextApproverEnabled.value) {
            if (_nextApprovers.value.isNotEmpty()) {
                // Data already available → open selection
                showNextApproverBottomSheet()
            } else {
                // Fetch cautiously (do not open until data is available)
                fetchNextApprovers()
            }
            return
        }

        // Turning OFF: disable and clear selection
        _isNextApproverEnabled.value = false
        _selectedNextApprovers.value = emptyList()
    }

    // Show next approver bottom sheet
    fun showNextApproverBottomSheet() {
        _isNextApproverBottomSheetVisible.value = true
    }

    // Hide next approver bottom sheet
    fun hideNextApproverBottomSheet() {
        _isNextApproverBottomSheetVisible.value = false
    }

    // Safely fetch next approvers; only open sheet when list is non-empty
    fun fetchNextApprovers() {
        if (isFetchingNextApprovers) return
        isFetchingNextApprovers = true
        _isNextApproverLoading.value = true

        launchJobSilent(
            onError = { ex ->
                // Fail quietly but inform user
                viewModelScope.launch {
                    _errorEvent.emit(ex.message ?: "Không thể tải danh sách người phê duyệt")
                }
            },
        ) {
            val res = useCase.nextStatusTransactionByRule(
                NextStatusTransactionByRuleParams(
                    amount = (_transaction.value.amount ?: "").getAmountServer(),
                    creator = userProf.getUserName() ?: "",
                    currentStatus = _transaction.value.status.toString(),
                    currentUserGroup = userProf.getGroupType() ?: "",
                    currentUserLevel = userProf.getRoleLevel().toString(),
                    customerNumber = userProf.getCifNo().toString(),
                    fromAccountNo = _transaction.value.fromAccountNo.toString(),
                    // IMPORTANT: Use originalTransaction.tranType to derive serviceCode.
                    // _transaction.value.tranType may be blank/altered (e.g. BTX case),
                    // causing wrong mapping to Napas by default.
                    serviceCode = getServiceCodeByTranType(originalTransaction?.tranType),
                    toAccountNo = _transaction.value.toAccountNo.toString(),
                    username = userProf.getUserName().toString(),
                    mtId = _transaction.value.mtId ?: "",
                ),
            )

            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    val list = data.nextApprovers ?: mutableListOf()
                    _nextApprovers.value = list
                    if (list.isNotEmpty()) {
                        // Mặc định chọn tất cả approvers
                        _selectedNextApprovers.value = list
                        _isNextApproverEnabled.value = true
                        // Open selection sheet only when we actually have data
                        _isNextApproverBottomSheetVisible.value = true
                    } else {
                        // Notify only, do not open an empty sheet
                        viewModelScope.launch { _errorEvent.emit("Không có người phê duyệt nào khả dụng") }
                    }
                },
                onError = { ex ->
                    // Fallback: notify error
                    viewModelScope.launch {
                        _errorEvent.emit(ex.message ?: "Không thể tải danh sách người phê duyệt")
                    }
                },
            )

            // Reset loading regardless of result
            _isNextApproverLoading.value = false
            isFetchingNextApprovers = false
        }
    }

    // Toggle approver selection
    fun toggleNextApprover(approver: ApproverDomains) {
        val currentList = _selectedNextApprovers.value.toMutableList()
        val index = currentList.indexOfFirst { it.id == approver.id }

        if (index >= 0) {
            // Remove if already selected
            currentList.removeAt(index)
        } else {
            // Add if not selected
            currentList.add(approver)
        }

        _selectedNextApprovers.value = currentList
    }

    // Select all approvers
    fun selectAllApprovers() {
        _selectedNextApprovers.value = _nextApprovers.value
    }

    // Clear selected approvers
    fun clearSelectedApprovers() {
        _selectedNextApprovers.value = emptyList()
    }

    // Update selected approvers from bottom sheet
    fun updateSelectedApprovers(approvers: List<ApproverDomains>) {
        _selectedNextApprovers.value = approvers
        // Enable the toggle if any approvers are selected or all selected
        _isNextApproverEnabled.value =
            approvers.isNotEmpty() || approvers.size == _nextApprovers.value.size
        hideNextApproverBottomSheet()
    }

    // Get next approvers from API (called silently in background)
    fun fetchNextApproversInBackground() {
        launchJobSilent {
            val res = useCase.nextStatusTransactionByRule(
                NextStatusTransactionByRuleParams(
                    amount = (_transaction.value.amount ?: "").getAmountServer(),
                    creator = userProf.getUserName() ?: "",
                    currentStatus = _transaction.value.status.toString(),
                    currentUserGroup = userProf.getGroupType() ?: "",
                    currentUserLevel = userProf.getRoleLevel().toString(),
                    customerNumber = userProf.getCifNo().toString(),
                    fromAccountNo = _transaction.value.fromAccountNo.toString(),
                    serviceCode = getServiceCodeByTranType(originalTransaction?.tranType),
                    toAccountNo = _transaction.value.toAccountNo.toString(),
                    username = userProf.getUserName().toString(),
                    mtId = _transaction.value.mtId ?: "",
                ),
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    printLog("InitTransactionViewModel: Fetched ${data.nextApprovers?.size} next approvers")
                    val list = data.nextApprovers ?: mutableListOf()
                    _nextApprovers.value = list
                    // Mặc định chọn tất cả nếu có data
                    if (list.isNotEmpty()) {
                        _selectedNextApprovers.value = list
                    }
                },
                onError = {
                },
            )
        }
    }

    private fun getServiceCodeByTranType(tranType: String?): String =
        when (tranType) {
            Tags.TransferType.TYPE_IN -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
            Tags.TransferType.TYPE_OUT -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
            else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
        }

    // báo lỗi call api error, xu ly chung
    private val _commonErrorEvent = MutableSharedFlow<AppException?>()
    val commonErrorEvent = _commonErrorEvent.asSharedFlow()

    // get tran detail
    private val _getTransactionList = MutableSharedFlow<Resource<GetTransactionListDomain>>()
    val getTransactionList = _getTransactionList.asSharedFlow()

    // data da duoc chuan hoa cho tung loai dien
    private suspend fun continueProcessing(transaction: TransactionListDomain) {
        // Xử lý và hiển thị giao dịch
        val transactionUI = currentProcessor.processTransaction(transaction)
        // với type tra soat
        transactionUI.apply {
            if ("tr" == tranType) {
                pref = transaction.pref?.let { pref ->
                    processorFactory.getProcessor(pref.tranType ?: "").processTransaction(pref)
                }
                tranSubTypeName = pref?.tranTypeName
            }
        }
        // them danh sach giao dich tach lenh vao TransactionDomain -> hien thi detail screen
        transactionUI.splitTransaction = subTransactionDetail?.data
        // them danh sach giao dich tach lenh vao TransactionListDomain -> hien thi confirm screen
        originalTransaction?.splitTransaction = subTransactionDetail?.data
        _transaction.value = transactionUI
        // neu can icon bank thi co the mapping o doan nay nha bro
        getTermAndCondition(transactionUI)
        printLog("Đã chuyển đổi thành công giao dịch: ${transaction.mtId}")

        // Fetch next approvers in background after transaction is loaded
        fetchNextApproversInBackground()
    }

    // Thêm event cho file permission dialog
    private val _filePermissionEvent = MutableSharedFlow<String>()
    val filePermissionEvent = _filePermissionEvent.asSharedFlow()

    /**
     * Xử lý dữ liệu giao dịch nhận được
     */
    fun processTransaction(transaction: TransactionListDomain) {
        try {
            originalTransaction = transaction
            printLog("Xử lý giao dịch: ${transaction.mtId} - trantype: ${transaction.tranType}")

            // Check if this is a batch file transaction (tranType="ba")
            val isBatchFile = transaction.tranType == "ba"
            printLog("isBatchFile: $isBatchFile, tranType: ${transaction.tranType}")

            if (isBatchFile) {
                // Process as batch file
                transaction.mtId?.let {
                    _batchFileState.value = _batchFileState.value.copy(
                        transactionId = it,
                        isBatchFile = true,
                    )
                    // goi tung page
                    getBatchTransaction()
                    // goi tat ca giao dich de lay tong so file
                    getBatchTransaction(isAll = true)
                }
                return
            } else if (transaction.tranType == "sn") {
                // goi them giao dich tach lenh
                // chuyen tien tach lenh
                transaction.mtId?.let { getSubTransactionDetail(it) }
            }

            // Lấy processor phù hợp dựa vào tranType
            currentProcessor = processorFactory.getProcessor(transaction.tranType ?: "in")
            printLog("currentProcessor: $currentProcessor")

            // Lấy renderer phù hợp
            _renderer.value = rendererFactory.getRenderer(transaction.tranType ?: "in")
            printLog("currentRenderer: ${_renderer.value}")

            if ("BTX" == transaction.serviceType) {
//                transaction.mtId = transaction.bulkID
                transaction.tranType = ""
            }
            launchJob(false) {
                // Luôn Gọi API detail
                getTransactionDetail(transaction)
                var completed = false
                getTransactionList.collect { result ->
                    if (!completed) {
                        when (result) {
                            is Resource.Success -> {
                                completed = true
                                result.data.transactions?.let { transactions ->
                                    if (transactions.isNotEmpty()) { // ← Check empty
                                        originalTransaction = transactions.first()
                                        continueProcessing(transactions.first())
                                    } else {
                                        _errorEvent.emit("Không tìm thấy thông tin giao dịch")
                                    }
                                }
                            }

                            is Resource.Error -> {
                                completed = true
                            }

                            else -> {}
                        }
                    }
                }
            }
        } catch (e: Exception) {
            printLog("Lỗi xử lý giao dịch: ${e.message}")
            e.printStackTrace()
        }

        /**
         * Hiển thị lỗi
         */
        fun showError(message: String) {
            viewModelScope.launch {
                _errorEvent.emit(message)
            }
            // update ui state
        }
    }

    private fun getTransactionDetail(transaction: TransactionListDomain) {
        launchJob(showLoading = false) {
            val res = useCase.getTransactionList(
                GetTransactionListParams(
                    mtId = transaction.mtId,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = transaction.groupType,
                    serviceType = transaction.serviceType,
                    tranType = transaction.tranType,
                    pageNum = "0",
                    pageSize = "15",
                    orderByAmount = "0", // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                    orderByApproveDate = "-1", // 1 tăng dần, -1 giảm dần, 0 là không sắp xếp
                ),
            )
            handleResource(res) { data ->
                _getTransactionList.emit(Resource.Success(data))
            }
        }
    }

    fun isSpecialTransactionType(): Boolean {
        return when (_transaction.value.tranType) {
            "ba", "btx" -> true
            else -> false
        }
    }

    fun preApprove() {
        // Nếu là giao dịch batch file, chuyển sang xử lý batch
        if (isSpecialTransactionType()) {
            // Nếu có các giao dịch đã chọn, gọi preApproveBatch thay vì xử lý ở đây
            preApproveBatch()
            return
        }

        // Xử lý các giao dịch thông thường (không phải batch)
        launchJob(showLoading = true) {
            val res = useCase.preApprove(
                PreApproveParams(
                    userName = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = _transaction.value.serviceId,
                    serviceType = _transaction.value.serviceType,
                    tranType = _transaction.value.tranType,
                    transactions = arrayListOf(_transaction.value.mtId.toString()),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = true
                _preApprove.emit(data)
                // Next approvers already fetched in background when transaction loaded
            }
        }
    }

    /**
     * Batch approve multiple transactions
     * Used for batch file approval with selected transaction IDs of child transactions
     */
    fun preApproveBatch() {
        if (_batchFileState.value.batchSelectedLst.isEmpty()) {
            viewModelScope.launch {
                _errorEvent.emit("Vui lòng chọn ít nhất một giao dịch để phê duyệt")
            }
            return
        }

        // Sử dụng mtId của các giao dịch con đã được chọn
        launchJob(showLoading = true) {
            val res = useCase.preApprove(
                PreApproveParams(
                    userName = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = _transaction.value.serviceId,
                    serviceType = _transaction.value.serviceType,
                    tranType = _transaction.value.tranType,
                    transactions = ArrayList(_batchFileState.value.batchSelectedLst),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = true
                _preApprove.emit(data)
                // Next approvers already fetched in background when transaction loaded
            }
        }
    }

    fun preReject() {
        // Nếu là giao dịch batch file, chuyển sang xử lý batch
        if (isSpecialTransactionType()) {
            // Nếu có các giao dịch đã chọn, gọi preRejectBatch thay vì xử lý ở đây
            preRejectBatch()
            return
        }

        // Xử lý các giao dịch thông thường (không phải batch)
        launchJob(showLoading = true) {
            val res = useCase.preReject(
                PreRejectParams(
                    username = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = _transaction.value.serviceId,
                    serviceType = _transaction.value.serviceType,
                    tranType = _transaction.value.tranType,
                    transactions = arrayListOf(_transaction.value.mtId.toString()),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = false
                _preApprove.emit(data)
                // Next approvers already fetched in background when transaction loaded
            }
        }
    }

    /**
     * Batch reject multiple transactions
     * Used for batch file rejection with selected transaction IDs of child transactions
     */
    fun preRejectBatch() {
        if (_batchFileState.value.batchSelectedLst.isEmpty()) {
            viewModelScope.launch {
                _errorEvent.emit("Vui lòng chọn ít nhất một giao dịch để từ chối")
            }
            return
        }

        // Sử dụng mtId của các giao dịch con đã được chọn
        launchJob(showLoading = true) {
            val res = useCase.preReject(
                PreRejectParams(
                    username = userProf.getUserName().toString(),
                    pereAppTp = arrayListOf(),
                    serviceId = _transaction.value.serviceId,
                    serviceType = _transaction.value.serviceType,
                    tranType = _transaction.value.tranType,
                    transactions = ArrayList(_batchFileState.value.batchSelectedLst),
                ),
            )
            handleResource(res) { data ->
                data.isApprove = false
                _preApprove.emit(data)
                // Next approvers already fetched in background when transaction loaded
            }
        }
    }

    /**
     * Execute reject transaction with reason
     * Called after user enters rejection reason in the dialog
     */
    fun doReject(reason: String) {
        // Get transaction IDs based on transaction type
        val transactionIds = when {
            // For batch transactions with selections
            isSpecialTransactionType() && _batchFileState.value.batchSelectedLst.isNotEmpty() -> {
                ArrayList(_batchFileState.value.batchSelectedLst)
            }
            // For regular transactions
            _transaction.value.mtId != null -> {
                arrayListOf(_transaction.value.mtId.toString())
            }

            else -> {
                viewModelScope.launch {
                    _errorEvent.emit("Không có giao dịch để từ chối")
                }
                return
            }
        }

        launchJob(showLoading = true) {
            val res = useCase.doReject(
                RejectParams(
                    username = userProf.getUserName().toString(),
                    token = "", // No token needed for direct rejection
                    softOtpTransId = "",
                    authenType = "S",
                    requestType = "0",
                    serviceType = _transaction.value.serviceType,
                    tranType = _transaction.value.tranType,
                    transactions = transactionIds,
                    transactionsTp = arrayListOf(),
                    serviceId = _transaction.value.serviceId,
                    reason = reason,
                ),
            )
            handleResource(res) { responseData ->
                _doRejectResponse.emit(Resource.Success(responseData))
            }
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        when (exception) {
            is AppException.ApiException -> {
                if (exception.requestPath == Constants.MB_PRE_APPROVE && exception.code != Tags.SESSION_EXPIRED || exception.requestPath == Constants.MB_PRE_REJECT && exception.code != Tags.SESSION_EXPIRED) {
                    // Trigger sự kiện lỗi Approve để Fragment xử lý
                    viewModelScope.launch {
                        _commonErrorEvent.emit(exception)
                    }
                } else {
                    super.onDisplayErrorMessage(exception)
                }
            }

            else -> super.onDisplayErrorMessage(exception)
        }
    }

    fun getDownloadBase64File(context: Context, mtId: String?, type: String?) {
        launchJob {
            val res = useCase.getExportNSNNFileTemplateParams(
                GetExportNSNNFileTemplateParams(
                    username = userProf.getUserName(),
                    type = type,
                    mtId = mtId,
                ),
            )
            handleResource(res) { data ->
                viewModelScope.launch {
                    if (data.file.isNullOrEmpty()) {
                        _errorMessage.emit("Không tìm thấy file đính kèm hoặc file không khả dụng")
                        return@launch
                    } else {
                        try { // Đọc data.file từ base 4 sang file
                            saveBase64File(
                                context,
                                data.file.orEmpty(),
                                data.fileName.orEmpty(),
                            ).onSuccess { file ->
                                // chuyển tu file sang uri
                                val uri = Utils.getFileUri(context, file)
                                // map định dạng file là kiểu gì để chọn app mở lên
                                val mimeType = Utils.getMimeType(file)

                                val intent = Intent(Intent.ACTION_VIEW).apply {
                                    setDataAndType(uri, mimeType)
                                    flags =
                                        Intent.FLAG_GRANT_READ_URI_PERMISSION // cấp quyền cho ứng dụng bên ngoài mo file
                                }
                                // Kiểm tra xem có ứng dụng nào trong máy có thể xử lý Intent mà đang tạo hay không.
                                if (intent.resolveActivity(context.packageManager) != null) {
                                    _openFileIntent.emit(intent)
                                } else {
                                    _errorMessage.emit("Không có ứng dụng mở file phù hợp")
                                }
                            }.onFailure { e ->
                                _errorMessage.emit(
                                    e.message ?: "Lỗi không xác định",
                                )
                            }
                        } catch (e: Exception) {
                            _errorMessage.emit("Lỗi khi lưu file: ${e.message}")
                        }
                    }
                }
            }
        }
    }

    fun getDownloadFileID(file: FileTransactionDomain? = null, isFileAttach: Boolean = false) {
        // Check permission trước khi download
        printLog("_transaction.value.isViewSlFile: ${_transaction.value.listFile2?.first()?.isViewSlFile}")
        if (file != null && !file.isViewSlFile.isNullOrEmpty() && file.isViewSlFile != "Y") {
            // Emit event để Fragment show dialog
            viewModelScope.launch {
                _filePermissionEvent.emit("Quý khách không có quyền tải xuống hay chia sẻ file lương")
            }
            return
        }

        launchJob(showLoading = true) {
            val res = useCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = getFileIDFollowTranType(
                        _transaction.value.tranType,
                        file,
                        isFileAttach,
                    ), // tam thoi dung mtid
                    mtID = getMtIdFollowTranType(_transaction.value, file),
                    tranType = _transaction.value.tranType,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    private fun getFileIDFollowTranType(
        tranType: String?,
        file: FileTransactionDomain?,
        isFileAttach: Boolean = false,
    ): String? {
        return when (tranType) {
            "go", "goc", "gor" -> {
                file?.objectId
            }

            "sl", "sx" -> {
                if (isFileAttach) {
                    // file
                    file?.mtId
                } else {
                    // ho so
                    "SL_${file?.mtId}_${file?.id}"
                }
            }

            "slo" -> {
                file?.mtId
            }

            "do" -> {
                file?.objectId
            }

            "er" -> {
                file?.id
            }

            else -> file?.mtId
        }
    }

    private fun getMtIdFollowTranType(
        transaction: TransactionDomain?,
        file: FileTransactionDomain?,
    ): String? {
        return when (transaction?.tranType ?: "") {
            "do", "go", "goc", "gor" -> {
                transaction?.mtId
            }

            else -> {
                file?.mtId ?: _transaction.value.mtId
            }
        }
    }

    private var subTransactionDetail: SubTransactionDomain? = null
    fun getSubTransactionDetail(hostMtId: String? = null) = launchJob(showLoading = true) {
        val res =
            useCase.getSubTransactionDetail(SubTransactionParams(hostMtId, userProf.getUserName()))
        handleResource(res) { data ->
            subTransactionDetail = data
            originalTransaction?.let {
                continueProcessing(it)
            }
        }
    }

    fun getListFile(type: Int): Pair<String, List<FileTransactionDomain>>? {
        val lstFile = mutableListOf<FileTransactionDomain>()
        return when (type) {
            TransactionFieldEvent.PURPOSE -> {
                _transaction.value.transferListFileVND?.let { file ->
                    lstFile.add(file.copy(fileName = "Bảng kê hóa đơn/Hợp đồng"))
                }

                _transaction.value.listDocumentFileVND?.firstOrNull()?.let { file ->
                    lstFile.add(file.copy(fileName = "Hồ sơ CMMĐSDV"))
                }
                Pair("Hồ sơ mục đích giải ngân", lstFile)
            }

            TransactionFieldEvent.FILE -> {
                _transaction.value.fileGNN?.forEach { file ->
                    lstFile.add(
                        file.copy(
                            fileName = when (file.attachmentType) {
                                "P1" -> "Đề nghị kiêm mua CTNT"
                                "D1" -> "Giấy nhận nợ"
                                else -> file.fileName
                            },
                        ),
                    )
                }
                Pair("File giải ngân", lstFile)
            }

            else -> null
        }
    }

    /**
     * Gets batch transaction list for batch files
     */

    private val _batchFileState = MutableStateFlow<BatchFileDomain>(BatchFileDomain())
    val batchFileState = _batchFileState.asStateFlow()

    fun getBatchFileSelected(): ArrayList<String> =
        ArrayList(_batchFileState.value.batchSelectedLst)

    fun getBatchTransaction(isAll: Boolean = false) {
        launchJob(showLoading = false) {
            val mtIdBatch = _batchFileState.value.transactionId ?: ""
            val res = useCase.getBathTransactionList(
                GetBatchTransactionListParams(
                    all = if (!isAll) "N" else "Y",
                    username = userProf.getUserName().toString(),
                    orderByAmount = "-1",
                    orderByApproveDate = "",
                    pageNum = "0",
                    pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                    transactionId = mtIdBatch,
                ),
            )
            handleResource(res) { data ->
                if (isAll) {
                    // tat ca file
                    _batchFileState.value = _batchFileState.value.copy(
                        batchAllLst = data.mtIds?.toSet() ?: emptySet(),
                        batchMaximum = data.countTransactionApprove?.toIntOrNull() ?: 0,
                    )
                } else {
                    // Convert batch transactions to child transactions
                    val childTransactionsField = data.transactions?.map { batchTran ->
                        BatchUiModel(
                            mtId = batchTran.mtId,
                            toAccountNo = batchTran.toAccountNo,
                            receiveName = batchTran.receiveName,
                            amount = batchTran.amount,
                            remark = batchTran.remark,
                            currency = batchTran.currency,
                        )
                    } ?: emptyList()
                    // Create parent transaction with batch file info
                    data.info?.let {
                        val batchFileTransaction = TransactionDomain().apply {
                            mtId = mtIdBatch
                            bulkID = it.bulkId
                            fileName = it.fileName
                            currency = it.currency
                            createdDate = it.processDate
                            totalTrxNo = it.total?.toString()
                            payFeeType = it.payFeeType
                            amount = Utils.g().getDotMoneyHasCcy(it.totalAmount ?: "", it.currency ?: "")
                            amountInWords = moneyHelper.convertAmountToWords(it.totalAmount ?: "", it.currency ?: "")
                            tranType = originalTransaction?.tranType
                            serviceType = originalTransaction?.serviceType
                            groupType = originalTransaction?.groupType
                            statusName = originalTransaction?.statusName
                            remark = originalTransaction?.remark
                            tranTypeName = resourceProvider.getString(com.vietinbank.core_ui.R.string.approve_file_300_title)
                        }
                        // Update UI with the batch file transaction
                        _transaction.value = batchFileTransaction
                    }
                    // Set the renderer to batch file renderer
                    _renderer.value =
                        rendererFactory.getRenderer(tranType = Tags.TYPE_GROUP_TRANSFER_CTTF300.lowercase())
                    _batchFileState.value = _batchFileState.value.copy(
                        currentPage = _batchFileState.value.currentPage + 1,
                        canLoadMore = childTransactionsField.size >= Tags.CHECKER_PAGE_SIZE,
                        batchLst = childTransactionsField,
                    )
                }
            }
        }
    }

    fun loadMoreBatchTransactions() {
        // Kiểm tra nếu đang tải hoặc không thể tải thêm thì không làm gì
        if (true == _batchFileState.value.isLoadingMore || true != _batchFileState.value.canLoadMore) return
        _batchFileState.value = _batchFileState.value.copy(isLoadingMore = true)
        val nextPage = _batchFileState.value.currentPage + 1
        launchJob(showLoading = false) {
            try {
                val res = useCase.getBathTransactionList(
                    GetBatchTransactionListParams(
                        all = "N",
                        username = userProf.getUserName().toString(),
                        orderByAmount = "-1",
                        orderByApproveDate = "",
                        pageNum = nextPage.toString(),
                        pageSize = Tags.CHECKER_PAGE_SIZE_STR,
                        transactionId = _batchFileState.value.transactionId,
                    ),
                )
                handleResource(res) { data ->
                    // Convert batch transactions to child transactions
                    val childTransactionsField = data.transactions?.map { batchTran ->
                        BatchUiModel(
                            mtId = batchTran.mtId,
                            toAccountNo = batchTran.toAccountNo,
                            receiveName = batchTran.receiveName,
                            amount = batchTran.amount,
                            remark = batchTran.remark,
                            currency = batchTran.currency,
                        )
                    } ?: emptyList()

                    val lst = _batchFileState.value.batchLst?.toMutableList() ?: mutableListOf()
                    lst.addAll(childTransactionsField)
                    _batchFileState.value = _batchFileState.value.copy(
                        currentPage = _batchFileState.value.currentPage + 1,
                        canLoadMore = childTransactionsField.size >= Tags.CHECKER_PAGE_SIZE,
                        batchLst = lst,
                    )
                }
            } finally {
                _batchFileState.value = _batchFileState.value.copy(isLoadingMore = false)
            }
        }
    }

    fun isFile3000(): Boolean {
        return _transaction.value.tranType == Tags.TYPE_GROUP_TRANSFER_CTTF300.lowercase()
    }
}
