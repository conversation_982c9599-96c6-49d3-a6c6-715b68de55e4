package com.vietinbank.feature_checker.ui.component.newui.multiple

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.MultipleTransactionUIState
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationChips
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationNavigationArea
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_checker.ui.component.approvallist.TransactionRendererFactory
import com.vietinbank.feature_checker.ui.component.newui.BottomActionButtons
import com.vietinbank.feature_checker.ui.component.newui.TransactionItem
import com.vietinbank.feature_checker.ui.component.newui.components.EmptyStateContent
import com.vietinbank.feature_checker.ui.fragment.MultipleActions
import kotlinx.coroutines.flow.distinctUntilChanged
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

/**
 * New UI Approval List Screen with Scaffold pattern
 * - Uses proper Scaffold with CenteredTitleAppBar as topBar
 * - Tabs and content in the Scaffold content area
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NewUIMultipleApprovalScreen(
    uiState: MultipleTransactionUIState,
    transactionRendererFactory: TransactionRendererFactory,
    actions: (MultipleActions) -> Unit,
) {
    val listState = rememberLazyListState()
    val appBarScrollState = rememberFoundationAppBarScrollState()

    LaunchedEffect(uiState) {
        snapshotFlow {
            val layoutInfo = listState.layoutInfo
            val totalItems = layoutInfo.totalItemsCount
            val lastVisible = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
            lastVisible >= totalItems - 3 && uiState.canLoadMore && !uiState.isLoadingMore
        }.distinctUntilChanged().collect { shouldLoad ->
            if (shouldLoad) {
                actions(MultipleActions.LoadMore)
            }
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap8, bottom = FDS.Sizer.Gap.gap16),
                ) {
                    FoundationAppBar(
                        modifier = Modifier.fillMaxWidth(),
                        title = stringResource(id = R.string.feature_checker_approve_multiple),
                        onNavigationClick = { actions(MultipleActions.BackClick) },
                        isLightIcon = false,
                        scrollState = appBarScrollState,

                    )
                }
            },
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.BottomCenter,
            ) {
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = FDS.Sizer.Radius.radius32,
                                topEnd = FDS.Sizer.Radius.radius32,
                            ),
                        ),
                ) {
                    when {
                        uiState.transactions != null -> {
                            item(key = "header") {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(FDS.Colors.white),
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(top = FDS.Sizer.Padding.padding24)
                                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically,
                                    ) {
                                        FoundationText(
                                            text = uiState.groupName ?: "",
                                            style = FDS.Typography.headingH3,
                                            color = FDS.Colors.characterHighlighted,
                                        )
                                    }

                                    // Divider
                                    FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16))

                                    LazyRow(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(top = FDS.Sizer.Gap.gap16),
                                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                                        contentPadding = PaddingValues(horizontal = FDS.Sizer.Padding.padding24),
                                    ) {
                                        itemsIndexed(
                                            uiState.subTranLst ?: emptyList(),
                                            key = { index, item -> "key_${index}_$item" },
                                        ) { index, item ->
                                            val currentTranType by remember(item) {
                                                derivedStateOf {
                                                    if (item.groupType == Tags.TYPE_DISBURSEMENT_ONLINE) {
                                                        item.tranSubType
                                                    } else {
                                                        item.tranType
                                                    }
                                                }
                                            }

                                            val currentSelected by remember(uiState.filterApprove) {
                                                derivedStateOf {
                                                    if (item.groupType == Tags.TYPE_DISBURSEMENT_ONLINE) {
                                                        uiState.filterApprove?.tranSubType
                                                    } else {
                                                        uiState.filterApprove?.tranType
                                                    }
                                                }
                                            }

                                            FoundationChips(
                                                text = item.serviceTypeName,
                                                isSelector = currentSelected == currentTranType,
                                                onClick = {
                                                    actions(
                                                        MultipleActions.FilterSelectedClick(
                                                            currentTranType,
                                                        ),
                                                    )
                                                },
                                            )
                                        }
                                    }

                                    FoundationSelector(
                                        modifier = Modifier
                                            .padding(top = FDS.Sizer.Gap.gap20)
                                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                                        boxType = SelectorType.Checkbox,
                                        title = if (uiState.isBatchFile) {
                                            stringResource(R.string.checker_multiple_selected_maximum_batch)
                                        } else {
                                            stringResource(R.string.checker_multiple_selected_maximum_transaction)
                                        },
                                        isSelected = uiState.isGlobalMaximum,
                                        onClick = { actions(MultipleActions.SelectAllClick) },
                                    )

                                    FoundationText(
                                        modifier = Modifier
                                            .padding(top = FDS.Sizer.Gap.gap8)
                                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                                        text = if (uiState.isBatchFile) {
                                            stringResource(
                                                R.string.checker_multiple_selected_batch,
                                                uiState.selectedTransactions.size,
                                                uiState.totalTransactionsCount,
                                                uiState.selectedSubTransactions.size,
                                                uiState.totalSubTransactionsCount,
                                            )
                                        } else {
                                            stringResource(
                                                R.string.checker_multiple_selected_transaction,
                                                uiState.selectedTransactions.size,
                                                uiState.totalTransactionsCount,
                                            )
                                        },
                                        style = FDS.Typography.captionCaptionL,
                                        color = FDS.Colors.characterSecondary,
                                    )

                                    FoundationDivider(
                                        modifier = Modifier
                                            .padding(top = FDS.Sizer.Gap.gap16)
                                            .padding(horizontal = FDS.Sizer.Padding.padding24),
                                    )
                                }
                            }

                            if (uiState.transactions.isNullOrEmpty()) {
                                item {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clip(
                                                RoundedCornerShape(
                                                    bottomStart = FDS.Sizer.Radius.radius32,
                                                    bottomEnd = FDS.Sizer.Radius.radius32,
                                                ),
                                            )
                                            .background(FDS.Colors.white)
                                            .padding(FDS.Sizer.Padding.padding16),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        EmptyStateContent()
                                    }
                                }
                            } else {
                                itemsIndexed(
                                    items = uiState.transactions ?: emptyList(),
                                    key = { index, item -> "${index}_${item.mtId}" },
                                ) { index, transaction ->
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(FDS.Colors.backgroundBgContainer)
                                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                                    ) {
                                        TransactionItem(
                                            transaction = transaction,
                                            isSelected = uiState.selectedTransactions.contains(
                                                transaction.mtId,
                                            ),
                                            isSelectionMode = true,
                                            onChangeSelected = {
                                                actions(
                                                    MultipleActions.TransactionSelectedClick(
                                                        transaction.mtId ?: "",
                                                    ),
                                                )
                                            },
                                            onClick = {
                                                actions(
                                                    MultipleActions.TransactionClick(
                                                        transaction.mtId ?: "",
                                                    ),
                                                )
                                            },
                                            onLongClick = {
                                                actions(
                                                    MultipleActions.TransactionClick(
                                                        transaction.mtId ?: "",
                                                    ),
                                                )
                                            },
                                            transactionRendererFactory = transactionRendererFactory,
                                        )
                                        // Divider after each item except the last
                                        if (transaction != uiState.transactions?.lastOrNull()) {
                                            FoundationDivider()
                                        }
                                    }
                                }

                                item(key = "footer") {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(
                                                FDS.Colors.white,
                                                RoundedCornerShape(
                                                    bottomStart = FDS.Sizer.Radius.radius32,
                                                    bottomEnd = FDS.Sizer.Radius.radius32,
                                                ),
                                            )
                                            .padding(FDS.Sizer.Padding.padding16),
                                        contentAlignment = Alignment.Center,
                                    ) {
                                        if (uiState.isLoadingMore) {
                                            CircularProgressIndicator(color = FDS.Colors.primary)
                                        }
                                    }
                                }

                                item {
                                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap108))
                                }
                            }
                        }
                    }
                }

                if (uiState.selectedTransactions.isNotEmpty()) {
                    FoundationNavigationArea(
                        height = FDS.Sizer.Gap.gap108,
                        topRadius = FDS.Sizer.Radius.radius0,
                        startStop = 0.5f,
                    ) {
                        BottomActionButtons(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = FDS.Sizer.Gap.gap24,
                                    vertical = FDS.Sizer.Gap.gap16,
                                ),
                            isLoading = false,
                            multipleAction = Pair(false, false),
                            onRejectClick = {
                                actions(MultipleActions.ContinueClick(false))
                            },
                            onApproveClick = {
                                actions(MultipleActions.ContinueClick(true))
                            },
                        )
                    }
                }
            }
        }
    }
}

/**
 * Actions interface for handling user interactions
 */

/**
 * Get appropriate icon for transaction type
 */
private fun getTransactionIcon(type: String): Int {
    return when (type.lowercase()) {
        "transfer", "ct", "internal" -> com.vietinbank.feature_checker.R.drawable.ic_checker_sort_arrow_16
        "payment", "tt", "external" -> R.drawable.ic_download
        "batch", "file" -> R.drawable.ic_document
        else -> com.vietinbank.feature_checker.R.drawable.ic_checker_sort_arrow_16
    }
}
