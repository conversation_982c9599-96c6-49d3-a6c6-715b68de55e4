package com.vietinbank.feature_checker.ui.component.confirmchecker

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmFieldKey
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItem
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import javax.inject.Inject

/**
 * Created by vand<PERSON> on 9/4/25.
 */

class TransferConfirmUIGenerator @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : ITransactionConfirmUIGenerator {
    override fun generateConfirmUI(
        transaction: TransactionDomain,
        confirmType: String,
        resourceProvider: IResourceProvider,
        onFieldEvent: ((TransactionFieldEvent) -> Unit)?,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Thêm header Tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin tài khoản chuyển",
            ),
        )

        // Thêm thông tin tài khoản chuyển
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Từ tài khoản",
                value = "${transaction.fromAccountNo ?: ""} - ${transaction.currency ?: "VND"}",
                subValue = transaction.fromAccountName, // Add account name as subValue
                stableKey = ConfirmFieldKey.FROM_ACCOUNT,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.SPACER,
            ),
        )

        // Thêm header Tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin tài khoản thụ hưởng",
            ),
        )

        // Thêm thông tin tài khoản thụ hưởng
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Chuyển tới",
                value = transaction.toAccountNo?.toString() ?: "",
                subValue = transaction.receiveName,
                stableKey = ConfirmFieldKey.TO_ACCOUNT,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Ngân hàng",
                value = transaction.receiveBankName ?: "",
                stableKey = ConfirmFieldKey.BANK_NAME,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Số tiền",
                value = transaction.amount ?: "",
                isHighlighted = true,
                stableKey = ConfirmFieldKey.AMOUNT,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Phí giao dịch",
                value = transaction.feeAmount ?: "",
                stableKey = ConfirmFieldKey.FEE,
            ),
        )

        // Hình thức thu phí: chuẩn hóa theo quy ước cũ ("0" => Phí ngoài, còn lại => Phí trong)
        val feeMethodText = if (transaction.feePayMethod == "0") {
            "Phí ngoài"
        } else {
            "Phí trong"
        }
        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Hình thức thu phí",
                value = feeMethodText,
                stableKey = ConfirmFieldKey.FEE_METHOD,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Nội dung",
                value = transaction.remark ?: "",
                stableKey = ConfirmFieldKey.CONTENT,
            ),
        )

        // Hình thức chuyển (Transfer method)
        if (transaction.process_time.isNullOrEmpty()) {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức chuyển",
                    value = "Chuyển ngay",
                    stableKey = ConfirmFieldKey.TRANSFER_METHOD,
                ),
            )
        } else {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Hình thức chuyển",
                    value = "Đặt lịch",
                    stableKey = ConfirmFieldKey.TRANSFER_METHOD,
                ),
            )

            items.add(
                ConfirmItem(
                    type = ConfirmItemType.KEY_VALUE,
                    label = "Ngày đặt lịch",
                    value = transaction.process_time ?: "",
                    stableKey = ConfirmFieldKey.SCHEDULE_TIME,
                ),
            )
        }

        if (transaction.tranType == "sn") {
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.SPACER,
                ),
            )

            // Thêm header Tài khoản thụ hưởng
            items.add(
                ConfirmItem(
                    type = ConfirmItemType.HEADER,
                    title = "Thông tin tách lệnh giao dịch",
                ),
            )

            transaction.splitTransaction?.forEach { subTransaction ->
                // Thêm thông tin tài khoản thụ hưởng
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số giao dịch",
                        value = subTransaction.mtId ?: "",
                        stableKey = ConfirmFieldKey.SUB_TRX_MTID,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Số tiền",
                        value = Utils.g().getDotMoneyHasCcy(subTransaction.amount ?: "", "VND"),
                        subValue = moneyHelper.convertAmountToWords(
                            subTransaction.amount ?: "",
                            "VND",
                        ),
                        stableKey = ConfirmFieldKey.SUB_TRX_AMOUNT,
                    ),
                )

                val fee = (
                    subTransaction.feeAmt?.toDoubleOrNull()
                        ?: 0.0
                    ) + (subTransaction.feeVat?.toDoubleOrNull() ?: 0.0)
                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(fee.toString(), "VND"),
                        stableKey = ConfirmFieldKey.SUB_TRX_FEE,
                    ),
                )

                items.add(
                    ConfirmItem(
                        type = ConfirmItemType.KEY_VALUE,
                        label = "Trạng thái",
                        value = subTransaction.statusName ?: "",
                        stableKey = ConfirmFieldKey.SUB_TRX_STATUS,
                    ),
                )
            }
        }
        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = listOfNotNull(transaction.mtId),
            serviceType = transaction.serviceType ?: "CT",
            tranType = transaction.tranType ?: "",
            groupType = transaction.groupType ?: "",
        )
    }

    override fun generateBatchConfirmUI(
        transactions: List<TransactionListDomain>,
        totalAmount: String,
        totalFee: String,
        confirmType: String,
        createdDate: String,
    ): ConfirmUiModel {
        val items = mutableListOf<ConfirmItem>()

        // Header info giao dịch
        items.add(
            ConfirmItem(
                type = ConfirmItemType.HEADER,
                title = "Thông tin giao dịch chuyển khoản",
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số giao dịch",
                value = transactions.size.toString(),
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng số tiền chuyển",
                value = totalAmount,
                isHighlighted = true,
            ),
        )

        items.add(
            ConfirmItem(
                type = ConfirmItemType.KEY_VALUE,
                label = "Tổng phí giao dịch",
                value = totalFee,
            ),
        )

        // Lấy danh sách ID giao dịch
        val transactionIds = transactions.mapNotNull { it.mtId }

        // Lấy service type và tran type
        val serviceType = transactions.firstOrNull()?.serviceType ?: ""
        val tranType = transactions.firstOrNull()?.tranType ?: ""
        val groupType = transactions.firstOrNull()?.groupType ?: ""

        return ConfirmUiModel(
            screenTitle = if (confirmType == Tags.APPROVE) "Duyệt" else "Từ chối",
            items = items,
            transactionIds = transactionIds,
            serviceType = serviceType,
            tranType = tranType,
            groupType = groupType,
        )
    }
}
