package com.vietinbank.feature_checker.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseOtpBoxInput
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable

/**
 * Created by vandz on 17/3/25.
 */
@Composable
fun ConfirmKeypassScreen(
    challengeCode: String = "",
    onBackClick: () -> Unit,
    onCancelClick: () -> Unit,
    onConfirmClick: (String) -> Unit,
    keypassValue: String,
    onKeypassValueChange: (String) -> Unit,
    validationError: String? = null,
) {
    // Main layout
    Column(
        modifier = Modifier
            .fillMaxSize(),
    ) {
        // App Bar
        BaseAppBar(
            title = "Xác thực",
            onBackClick = onBackClick,
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Description text
            BaseText(
                text = "Vui lòng nhập mã giao dịch $challengeCode vào thẻ Keypass để nhận chuỗi mã trả ra nhập vào ô dưới đây",
                color = AppColors.textPrimary,
                textSize = 14.sp,
                modifier = Modifier.padding(bottom = 24.dp),
            )

            // OTP Title
            BaseText(
                text = "Mã Keypass",
                color = AppColors.textPrimary,
                textSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
            )

            validationError?.let {
                BaseText(
                    text = it,
                    color = Color.White,
                    textSize = 14.sp,
                    modifier = Modifier.padding(top = 8.dp),
                )
            }

            Spacer(modifier = Modifier.width(8.dp))

            // OTP Input boxes
            BaseOtpBoxInput(
                value = keypassValue,
                onValueChange = onKeypassValueChange,
            )

            // Hướng dẫn link
            BaseText(
                text = "Hướng dẫn xác thực bằng Keypass",
                color = Color.White,
                textSize = 14.sp,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .safeClickable { /* Xử lý mở hướng dẫn */ },
            )

            // Spacer to push buttons to bottom
            Spacer(modifier = Modifier.weight(1f))
        }

        // Bottom buttons
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // Cancel button
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .border(
                        width = 0.5.dp,
                        color = AppColors.borderColor,
                        shape = RoundedCornerShape(10.dp),
                    )
                    .safeClickable { onCancelClick() },
                contentAlignment = Alignment.Center,
            ) {
                BaseText(
                    text = "Huỷ",
                    color = Color.Black,
                    textSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                )
            }

            // Confirm button
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                AppColors.gradientStart,
                                AppColors.gradientEnd,
                            ),
                        ),
                        shape = RoundedCornerShape(10.dp),
                    )
                    .safeClickable {
                        onConfirmClick(keypassValue)
                    },
                contentAlignment = Alignment.Center,
            ) {
                BaseText(
                    text = "Xác nhận",
                    color = Color.White,
                    textSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                )
            }
        }
    }
}