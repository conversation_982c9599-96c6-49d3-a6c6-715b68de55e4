package com.vietinbank.feature_checker.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.zIndex
import com.github.barteksc.pdfviewer.PDFView
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.safeClickable
import java.io.File

@Composable
fun PdfViewerDialog(
    pdfFile: File,
    title: String = "Tài liệu PDF",
    onDismiss: () -> Unit,
    confirmButtonText: String = "Đóng",
    showConfirmButton: Boolean = true,
    dialogModifier: Modifier = Modifier
        .fillMaxWidth(0.95f)
        .fillMaxHeight(0.9f),
) {
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            modifier = dialogModifier,
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                // Viewer + Title overlay
                // PDF Viewer nội dung
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                ) {
                    // AndroidView bên dưới
                    AndroidView(
                        factory = { ctx ->
                            PDFView(ctx, null)
                        },
                        update = { pdfView ->
                            pdfFile.let { bytes ->
                                pdfView.fromFile(bytes)
                                    .enableSwipe(true)
                                    .swipeHorizontal(false)
                                    .enableDoubletap(true)
                                    .load()
                            }
                        },
                        onReset = { pdfView ->
                            // Clean up resources
                            pdfView.recycle()
                            pdfView.stopFling()
                        },
                        modifier = Modifier
                            .padding(top = 48.dp)
                            .fillMaxSize()
                            .zIndex(0f), // nền PDF
                    )
                    // Phần tiêu đề overlay trên PDF
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopCenter) // phải nằm trong BoxScope mới hợp lệ!
                            .fillMaxWidth()
                            .height(48.dp)
                            .zIndex(1f)
                            .background(AppColors.grey07),
                        contentAlignment = Alignment.Center,
                    ) {
                        BaseText(
                            text = title,
                            color = Color.Black,
                            textSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
                // Nút đóng
                if (showConfirmButton) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(65.dp)
                            .padding(10.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        AppColors.primary6,
                                        AppColors.primary8,
                                    ),
                                ),
                                shape = RoundedCornerShape(5.dp),
                            )
                            .safeClickable(onSafeClick = onDismiss),
                        contentAlignment = Alignment.Center,
                    ) {
                        BaseText(
                            text = confirmButtonText,
                            color = Color.White,
                            textSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }
            }
        }
    }
}