package com.vietinbank.feature_checker.ui.compose

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.theme.FoundationDesignSystem

/**
 * Created by vandz on 27/4/25.
 */
@Composable
fun ApproverListItem(
    approver: ApproverDomains,
    isSelected: Boolean,
    onToggleSelection: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggleSelection() },
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            // Approver name
            Text(
                text = approver.username + "_" + approver.fullname,
                style = TextStyle(
                    fontFamily = FontFamily.Default,
                    fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
                    fontSize = 14.sp,
                    color = if (isSelected) Color(0xFF066099) else Color(0xFF6A8094),
                ),
            )

            // Checkbox/Radio button
            Checkbox(
                checked = isSelected,
                onCheckedChange = { onToggleSelection() },
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFF006594),
                    uncheckedColor = Color(0xFFECECEC),
                ),
            )
        }

        HorizontalDivider(
            color = FoundationDesignSystem.Colors.divider,
            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
        )
    }
}