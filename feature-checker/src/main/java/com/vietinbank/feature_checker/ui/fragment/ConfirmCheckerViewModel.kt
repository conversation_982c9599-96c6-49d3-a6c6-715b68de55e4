package com.vietinbank.feature_checker.ui.fragment

import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.getOnlyText
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.ApproveTransactionDomain
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeDomain
import com.vietinbank.core_domain.models.checker.GenKeyPassChallengeCodeParams
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeDomain
import com.vietinbank.core_domain.models.checker.GenSOTPTransCodeParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.GetTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.GetTransactionDetailParams
import com.vietinbank.core_domain.models.checker.MethodEntityDomain
import com.vietinbank.core_domain.models.checker.RejectParams
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.SuccessTitleDomain
import com.vietinbank.core_domain.models.checker.TermContent
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.maker.NextStatusTransactionByRuleParams
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTParams
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_domain.repository.CheckerRepository
import com.vietinbank.core_domain.softotp.IPinResult
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.softotp.ISoftResult
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_checker.common.KeypassConstant
import com.vietinbank.feature_checker.ui.component.confirmchecker.TransactionConfirmUIFactory
import com.vietinbank.feature_checker.ui.component.success.SuccessHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject
import kotlin.collections.forEach

@HiltViewModel
class ConfirmCheckerViewModel
@Inject
constructor(
    val useCase: CheckerUserCase,
    val smartUseCase: SmartCAUseCase,
    private val moneyHelper: MoneyHelper,
    val confirmUIFactory: TransactionConfirmUIFactory,
    val dataSourceProperties: DataSourceProperties,
    private val checkerRepository: CheckerRepository,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val softManager: ISoftManager, // Added for PIN verification and OTP generation
) : BaseViewModel() {
    fun getKeypassProfile(): String? = userProf.getKeypassProfile()
    var confirmType: String? = ""

    // Dữ liệu gốc
    private var originalTransaction: TransactionDomain? = null
    private var originalTransactions: List<TransactionListDomain> = emptyList()

    // Dữ liệu UI
    private val _uiModel = MutableStateFlow(ConfirmUiModel(items = emptyList()))
    val uiModel = _uiModel.asStateFlow()

    // Transaction ID để gọi API
    var transactionID: String? = null

    // StateFlow cho trạng thái phương thức xác thực
    private val _authMethodState = MutableStateFlow(AuthMethodState())
    val authMethodState = _authMethodState.asStateFlow()

    private val _needSyncKeypass = MutableSharedFlow<String>()
    val needSyncKeypass = _needSyncKeypass.asSharedFlow()

    private val _needReactiveSoft = MutableSharedFlow<String>()
    val needReactiveSoft = _needReactiveSoft.asSharedFlow()

    private val _needSyncSoft = MutableSharedFlow<String>()
    val needSyncSoft = _needSyncSoft.asSharedFlow()

    private val _selectedAuthMethod = MutableStateFlow("softotp")
    val selectedAuthMethod = _selectedAuthMethod.asStateFlow()

    // Events
    private val _errorEvent = SingleLiveEvent<String>()
    val errorEvent: LiveData<String> = _errorEvent

    private val _genSOTPTransCode = SingleLiveEvent<Resource<GenSOTPTransCodeDomain>>()
    val genSOTPTransCode: SingleLiveEvent<Resource<GenSOTPTransCodeDomain>> get() = _genSOTPTransCode

    private val _genKeyPassChallengeCode =
        SingleLiveEvent<Resource<GenKeyPassChallengeCodeDomain>>()
    val genKeyPassChallengeCode: SingleLiveEvent<Resource<GenKeyPassChallengeCodeDomain>> get() = _genKeyPassChallengeCode

    private val _doRejectResponse = SingleLiveEvent<Resource<ApproveDomain>>()
    val doRejectResponse: SingleLiveEvent<Resource<ApproveDomain>> get() = _doRejectResponse

    private val _hasNextApprovers = MutableStateFlow(false)
    val hasNextApprovers = _hasNextApprovers.asStateFlow()

    private val _getNextApprovers = MutableStateFlow<List<ApproverDomains>>(emptyList())
    val getNextApprovers = _getNextApprovers.asStateFlow()

    // Bottom sheet visibility state
    private val _isNextApproverBottomSheetVisible = MutableStateFlow(false)
    val isNextApproverBottomSheetVisible = _isNextApproverBottomSheetVisible.asStateFlow()

    // Selected approver
    private val _selectedNextApprover = MutableStateFlow<List<ApproverDomains>>(emptyList())
    val selectedNextApprover = _selectedNextApprover.asStateFlow()

    // Add file permission event (similar to InitTransactionViewModel)
    private val _filePermissionEvent = MutableSharedFlow<String>()
    val filePermissionEvent = _filePermissionEvent.asSharedFlow()

    // Download file
    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private val _termState = MutableStateFlow<TermContent?>(null)
    val termState = _termState.asStateFlow()

    private val _isAgreeDisbursement = MutableStateFlow(false)
    val isAgreeDisbursement = _isAgreeDisbursement.asStateFlow()
    fun onChangeAgreeDisbursement(newValue: Boolean) {
        _isAgreeDisbursement.value = newValue
    }

    // StateFlow để lưu file PDF đã download
    private val _pdfFile = MutableStateFlow<File?>(null)
    val pdfFile = _pdfFile.asStateFlow()

    // Add/remove approver from selected list
    fun toggleNextApprover(approver: ApproverDomains) {
        val currentList = _selectedNextApprover.value.toMutableList()

        // check approver is exist?
        val index = currentList.indexOfFirst { it.id == approver.id }

        if (index >= 0) {
            // if is exist, remove
            currentList.removeAt(index)
        } else {
            // not exist, add to list
            currentList.add(approver)
        }

        _selectedNextApprover.value = currentList
    }

    fun clearSelectedApprovers() {
        _selectedNextApprover.value = emptyList()
    }

    // Set selected approvers directly (used by New UI when selection happens outside this VM)
    fun setSelectedNextApprovers(approvers: List<ApproverDomains>) {
        _selectedNextApprover.value = approvers
    }

    fun isApproverSelected(approver: ApproverDomains): Boolean = _selectedNextApprover.value.any { it.id == approver.id }

    // Show/hide bottom sheet
    fun showNextApproverBottomSheet() {
        _isNextApproverBottomSheetVisible.value = true
    }

    fun hideNextApproverBottomSheet() {
        _isNextApproverBottomSheetVisible.value = false
    }

    fun getOriginalTransaction(): TransactionDomain? = originalTransaction

    fun getDownloadFileID(file: FileTransactionDomain? = null) {
        // Check permission trước khi download
        printLog("file.isViewSlFile: ${file?.isViewSlFile}")
        if (file != null && !file.isViewSlFile.isNullOrEmpty() && file.isViewSlFile != "Y") {
            // Emit event để Fragment show dialog
            viewModelScope.launch {
                _filePermissionEvent.emit("Quý khách không có quyền tải xuống hay chia sẻ file lương")
            }
            return
        }

        launchJob(showLoading = true) {
            val res =
                useCase.getDownloadFileID(
                    GetDownloadFileIDParams(
                        username = userProf.getUserName(),
                        fileId = getFileIDFollowTranType(originalTransaction?.tranType, file),
                        mtID = file?.mtId ?: originalTransaction?.mtId,
                        tranType = originalTransaction?.tranType,
                    ),
                )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl =
                            baseUrl +
                                Constants.MB_DOWNLOAD_FILE.replace(
                                    "{encryptStr}",
                                    fileId,
                                )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    fun getTermAndCondition(tranType: String?, conditionLink: String?) {
        _termState.value = when (tranType) {
            "fx", "fxr" -> TermContent(
                annotatedText = listOf(
                    "Tôi đã đọc và đồng ý với những " to null,
                    "quy định trong Mua bán ngoại tệ và chấp nhận tỷ giá tại thời điểm giao dịch của VietinBank" to conditionLink,
                ),
                fallbackMessage = "Quý khách vui lòng đồng ý với điều kiện điều khoản",
            )
            else -> null
        }
    }

    private fun getFileIDFollowTranType(tranType: String?, file: FileTransactionDomain?): String? {
        return when (tranType) {
            "sl", "sx" -> {
                "SL_${file?.mtId}_${file?.id}"
            }
            "slo" -> {
                file?.mtId
            }
            "go", "do" -> {
                "${file?.mtId}_${file?.id}"
            }
            "er" -> {
                file?.id
            }
            else -> file?.mtId
        }
    }

    fun fetchNextApprovers() {
        launchJobSilent {
            val res =
                useCase.nextStatusTransactionByRule(
                    NextStatusTransactionByRuleParams(
                        amount = (originalTransaction?.amount ?: "").getAmountServer(),
                        creator = userProf.getUserName() ?: "",
                        currentStatus = originalTransaction?.status.toString(),
                        currentUserGroup = userProf.getGroupType() ?: "",
                        currentUserLevel = userProf.getRoleLevel().toString(),
                        customerNumber = userProf.getCifNo().toString(),
                        fromAccountNo = originalTransaction?.fromAccountNo.toString(),
                        serviceCode = getServiceCodeByTranType(originalTransaction?.tranType),
                        toAccountNo = originalTransaction?.toAccountNo.toString(),
                        username = userProf.getUserName().toString(),
                        mtId = originalTransaction?.mtId ?: "",
                    ),
                )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    printLog("data.nextApprovers: ${data.nextApprovers?.size}")
                    _getNextApprovers.value = data.nextApprovers ?: mutableListOf()
                    _hasNextApprovers.value = data.nextApprovers?.isNotEmpty() == true
                },
            )
        }
    }

    private fun getServiceCodeByTranType(tranType: String?): String =
        when (tranType) {
            Tags.TransferType.TYPE_IN -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
            Tags.TransferType.TYPE_OUT -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
            else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
        }

    private fun processInputData(input: ConfirmInputData) {
        try {
            when (input) {
                is ConfirmInputData.SingleTransaction -> {
                    originalTransaction = input.transaction
                    // Logic xử lý giao dịch đơn
                    val generator = confirmUIFactory.getGenerator(input.transaction.tranType ?: "")
                    printLog("generator trantype : ${input.transaction.tranType}")
                    _uiModel.value =
                        generator.generateConfirmUI(input.transaction, confirmType ?: "", resourceProvider)
                    printLog("Đã xử lý giao dịch đơn lẻ: ${input.transaction.mtId}" + input.transaction.conditionLink)
                    getTermAndCondition(input.transaction.tranType, input.transaction.conditionLink)
                }

                is ConfirmInputData.BatchTransaction -> {
                    // Logic xử lý giao dịch batch
                    _uiModel.value = input.confirmUiModel
                    getTermAndCondition(input.confirmUiModel.tranType, "")
                }
            }
        } catch (e: Exception) {
            _errorEvent.postValue("Lỗi xử lý dữ liệu: ${e.message}")
        }
    }

    fun parseAndProcessData(
        jsonData: String,
        dataType: String?,
    ) {
        try {
            val gson = Utils.g().provideGson()
            val input =
                when (dataType) {
                    Tags.UI_MODEL -> {
                        val transaction: TransactionDomain =
                            gson
                                .fromJson(jsonData, TransactionDomain::class.java)
                        ConfirmInputData.SingleTransaction(transaction)
                    }

                    Tags.UI_BATCH -> {
                        val uiModel = gson.fromJson(jsonData, ConfirmUiModel::class.java)
                        ConfirmInputData.BatchTransaction(uiModel)
                    }

                    else -> {
                        // Tự động phát hiện loại dữ liệu
                        when {
                            jsonData.contains("\"mtId\"") -> {
                                val transaction =
                                    gson
                                        .fromJson(jsonData, TransactionDomain::class.java)
                                ConfirmInputData.SingleTransaction(transaction)
                            }

                            jsonData.contains("\"items\"") -> {
                                val uiModel = gson.fromJson(jsonData, ConfirmUiModel::class.java)
                                ConfirmInputData.BatchTransaction(uiModel)
                            }

                            else -> {
                                _errorEvent.postValue("Không thể xử lý dữ liệu không xác định")
                                throw IllegalArgumentException("Không thể xử lý dữ liệu không xác định")
                            }
                        }
                    }
                }
            processInputData(input)
        } catch (e: Exception) {
            _errorEvent.postValue("Lỗi xử lý dữ liệu: ${e.message}")
        }
    }

    /**
     * Tính tổng số tiền
     */
    private fun calculateTotalAmount(transactions: List<TransactionListDomain>): Double =
        transactions.sumOf {
            try {
                (it.amount?.replace(",", "")?.toDoubleOrNull() ?: 0.0)
            } catch (e: Exception) {
                0.0
            }
        }

    /**
     * Tính tổng phí
     */
    private fun calculateTotalFee(transactions: List<TransactionListDomain>): Double =
        transactions.sumOf {
            try {
                (it.feeAmount?.replace(",", "")?.toDoubleOrNull() ?: 0.0)
            } catch (e: Exception) {
                0.0
            }
        }

    /**
     * Hàm hỗ trợ lấy transactionIds từ model hiện tại
     * Hữu ích cho các API call
     */
    fun getTransactionIds(): List<String> = _uiModel.value.transactionIds

    /**
     * Hàm hỗ trợ lấy serviceType từ model hiện tại
     */
    fun getServiceType(): String = _uiModel.value.serviceType

    /**
     * Hàm hỗ trợ lấy transactionType từ model hiện tại
     */
    fun getTransactionType(): String = _uiModel.value.tranType

    /**
     * Xử lý phương thức xác thực
     */
    fun processAuthMethods(methods: List<MethodEntityDomain>) {
        var hasSoftOtp = false
        var hasKeypass = false
        var hasSign = false

        methods.forEach { method ->
            when (method.method?.lowercase()) {
                "vnptsmartca" -> {
                    hasSign = true
                }

                "softotp" -> {
                    hasSoftOtp = true
                }

                "keypass" -> {
                    hasKeypass = true
                }

                else -> {}
            }
        }

        val selectedMethod =
            when {
                hasSoftOtp && isAllowUseSotp() -> "softotp"
                hasKeypass -> "keypass"
                hasSign -> "sign"
                else -> ""
            }

        // Cập nhật state với đối tượng mới
        _authMethodState.value =
            AuthMethodState(
                hasSoftOTP = hasSoftOtp,
                hasKeypass = hasKeypass,
                hasSign = hasSign,
                selectedMethod = selectedMethod,
            )
        printLog("Has SOTP: $hasSoftOtp, Has Keypass: $hasKeypass, Has SmartCA $hasSign")
    }

    /**
     * Kiểm tra xem có thể sử dụng Soft OTP không
     */
    fun isAllowUseSotp(): Boolean = Tags.ACCEPTABLE_REGISTER_SOTP == userProf.getKeypassSoftotp()

    /**
     * Hiển thị lỗi
     */
    fun showError(message: String) {
        _errorEvent.postValue(message)
    }

    fun updateSelectedAuthMethod(method: String) {
        _authMethodState.value =
            _authMethodState.value.copy(
                selectedMethod = method,
            )
    }

    /**
     * Tạo OTP Transaction Code
     */
    fun genSOTPTransCode() {
        val transactionIds = _uiModel.value.transactionIds
        if (transactionIds.isEmpty()) {
            showError("Không có ID giao dịch để xác nhận")
            return
        }

        launchJob(showLoading = true) {
            val res =
                useCase.genSOTPTransCode(
                    GenSOTPTransCodeParams(
                        cifno = userProf.getCifNo(),
                        userName = userProf.getUserName().toString(),
                        actionId = "approve",
                        groupType = _uiModel.value.groupType,
                        mtIds = transactionIds.joinToString(","),
                        reason = "",
                        transactionData = "1",
                        trxType = _uiModel.value.tranType,
                    ),
                )
            handleResource(res) { responseData ->
                _genSOTPTransCode.postValue(Resource.Success(responseData))
            }
        }
    }

    /**
     * Tạo Keypass Challenge Code
     */
    fun genKeyPassChallengeCode() {
        launchJob(showLoading = true) {
            val res =
                useCase.genKeyPassChallengeCode(
                    GenKeyPassChallengeCodeParams(
                        username = userProf.getUserName(),
                    ),
                )
            handleResource(res) { data ->
                _genKeyPassChallengeCode.postValue(Resource.Success(data))
            }
        }
    }

    // ===== SOFT OTP PIN VERIFICATION & OTP GENERATION =====
    // Following Clean Architecture - business logic in ViewModel, not in Dialog

    private val _pinVerificationResult = MutableStateFlow<Resource<Boolean>?>(null)
    val pinVerificationResult = _pinVerificationResult

    private val _otpGenerationResult = MutableStateFlow<Resource<String>?>(null)
    val otpGenerationResult = _otpGenerationResult

    /**
     * Verify PIN for Soft OTP
     * Called when user enters PIN in SimplePinInputDialog
     */
    fun verifySoftOtpPin(pin: String) {
        printLog("ConfirmCheckerViewModel: Verifying PIN")
        printLog("ConfirmCheckerViewModel: Current transactionID before PIN verify = $transactionID")
        printLog("ConfirmCheckerViewModel: Current uiModel.transactionIds = ${_uiModel.value.transactionIds}")
        // Clear previous result
        _pinVerificationResult.value = null

        // Set up PIN result listener
        softManager.setPinResultListener(object : IPinResult {
            override fun onSuccess() {
                printLog("ConfirmCheckerViewModel: PIN verified successfully")
                printLog("ConfirmCheckerViewModel: transactionID after PIN success = $transactionID")
                _pinVerificationResult.value = Resource.Success(true)
                // After PIN success, generate OTP
                generateSoftOtp()
            }

            override fun onError(message: String?, code: Int?) {
                printLog("ConfirmCheckerViewModel: PIN verification failed - $message")
                _pinVerificationResult.value = Resource.Error(
                    message = message ?: "Mã PIN không đúng",
                    code = code?.toString() ?: "PIN_ERROR",
                )
            }
        })

        // Verify PIN
        softManager.verifyPin(pin)
    }

    /**
     * Generate Soft OTP after PIN verification
     * Called automatically after successful PIN verification
     */
    private fun generateSoftOtp() {
        printLog("ConfirmCheckerViewModel: Generating Soft OTP")
        // Clear previous result
        _otpGenerationResult.value = null

        // Set up OTP result listener BEFORE calling genSoftOtp
        softManager.setSoftResultListener(object : ISoftResult {
            override fun onSuccess(otpCode: String?, timeCount: Int?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.onSuccess called - otpCode=$otpCode, timeCount=$timeCount")
                viewModelScope.launch {
                    if (!otpCode.isNullOrEmpty()) {
                        printLog("ConfirmCheckerViewModel: Emitting OTP success to Fragment")
                        _otpGenerationResult.value = Resource.Success(otpCode)
                        // Store OTP for later use in doApprove
                        tempOtpCode = otpCode
                    } else {
                        printLog("ConfirmCheckerViewModel: OTP is empty or null")
                        _otpGenerationResult.value = Resource.Error(
                            message = "Không thể tạo mã OTP",
                            code = "OTP_EMPTY",
                        )
                    }
                }
            }

            override fun isActive(isActive: Boolean?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.isActive called - isActive=$isActive")
            }

            override fun onError(message: String?, code: Int?) {
                printLog("ConfirmCheckerViewModel: ISoftResult.onError called - message=$message, code=$code")
                viewModelScope.launch {
                    _otpGenerationResult.value = Resource.Error(
                        message = message ?: "Không thể tạo mã OTP",
                        code = code?.toString() ?: "OTP_ERROR",
                    )
                }
            }
        })

        // Generate OTP with transaction data
        // Using same params as old ConfirmCheckerFragment
        val userId = getKeypassProfile() ?: userProf.getUserName()
        val transactionId = transactionID // Already set from genSOTPTransCode
        val messageId = "" // Empty string like in old flow

        printLog("ConfirmCheckerViewModel: Calling softManager.genSoftOtp with params:")
        printLog("  - userId=$userId")
        printLog("  - transactionId=$transactionId")
        printLog("  - messageId=$messageId (empty like old flow)")

        // Call genSoftOtp with same params as old flow
        softManager.genSoftOtp(
            userId = userId,
            transactionId = transactionId,
            messageId = messageId,
        )

        printLog("ConfirmCheckerViewModel: softManager.genSoftOtp() called, waiting for callback...")
    }

    // Temporary storage for OTP code
    private var tempOtpCode: String? = null

    /**
     * Clear PIN and OTP states
     */
    fun clearPinOtpStates() {
        _pinVerificationResult.value = null
        _otpGenerationResult.value = null
        tempOtpCode = null
    }

    /**
     * Xử lý từ chối giao dịch
     */
    fun doReject(
        otpValue: String,
        reason: String = "",
    ) {
        val transactionIds = _uiModel.value.transactionIds
        if (transactionIds.isEmpty()) {
            showError("Không có ID giao dịch để từ chối")
            return
        }

        launchJob(showLoading = true) {
            val res =
                useCase.doReject(
                    RejectParams(
                        username = userProf.getUserName().toString(),
                        token = otpValue,
                        softOtpTransId = transactionID,
                        authenType = "S",
                        requestType = "0",
                        serviceType = _uiModel.value.serviceType,
                        tranType = _uiModel.value.tranType,
                        transactions = ArrayList(transactionIds),
                        transactionsTp = arrayListOf(),
                        serviceId = "",
                        reason = reason,
                    ),
                )
            handleResource(res) { responseData ->
                // Capture tranDate from API response
                transactionDate = responseData.tranDate
                // Note: hostMtId is typically not available for rejected transactions
                _doRejectResponse.postValue(Resource.Success(responseData))
            }
        }
    }

    /**
     * Lấy tiêu đề cho màn hình thành công
     */
    fun getSuccessTitle(
        trans: ApproveTransactionDomain?,
        message: String,
    ): SuccessTitleDomain {
        // Tìm item số tiền được highlight
        val highlightedItem =
            _uiModel.value.items.find {
                it.type == ConfirmItemType.KEY_VALUE && it.isHighlighted
            }

        // Lấy giá trị amount, đảm bảo không null
        val amount = highlightedItem?.value ?: ""

        // Lấy giá trị subValue (số tiền bằng chữ) đã được tạo trước đó
        var amountText = highlightedItem?.subValue ?: ""

        // Xử lý trường hợp truyền số tiền bằng chữ không thành công
        if (amountText.isEmpty() && amount.isNotEmpty()) {
            // Thử tạo lại amountText từ amount
            try {
                // Nếu là nhiều dòng (nhiều loại tiền), xử lý riêng từng loại tiền
                if (amount.contains("\n")) {
                    val lines = amount.split("\n")
                    val resultBuilder = StringBuilder()

                    lines.forEachIndexed { index, line ->
                        // Tìm loại tiền tệ từ dòng
                        val currency = extractCurrency(line)

                        // Lấy số tiền sạch
                        val cleanAmount = cleanAmountString(line, currency)

                        // Chuyển đổi thành chữ
                        val lineInWords = moneyHelper.convertAmountToWords(cleanAmount, currency)
                        resultBuilder.append(lineInWords)

                        // Thêm "và" giữa các dòng (nếu không phải dòng cuối)
                        if (index < lines.size - 1) {
                            resultBuilder.append(" và ")
                        }
                    }
                    amountText = resultBuilder.toString()
                } else {
                    // Trường hợp đơn tiền tệ
                    val currency = extractCurrency(amount)
                    val cleanAmount = cleanAmountString(amount, currency)
                    amountText = moneyHelper.convertAmountToWords(cleanAmount, currency)
                }
            } catch (e: Exception) {
                printLog("Lỗi khi tạo lại amountText: ${e.message}")
            }
        }

        return SuccessTitleDomain(
            title = message,
            status = trans?.status,
            statusTrans = trans?.statusTrans,
            amountText = amountText,
            amountTitle = amount,
        )
    }

    /**
     * Trích xuất loại tiền tệ từ chuỗi số tiền
     */
    private fun extractCurrency(amountString: String): String {
        return amountString.getOnlyText()
    }

    /**
     * Làm sạch chuỗi số tiền, loại bỏ định dạng và đơn vị tiền tệ
     */
    private fun cleanAmountString(amountString: String, currency: String): String {
        return amountString.replace(" $currency", "").replace(",", "").trim()
    }

    // Store the transaction date from API response
    private var transactionDate: String? = null

    // Store the host mtId from API response
    private var hostMtId: String? = null

    /**
     * Lấy danh sách mục thông tin cho màn hình thành công
     * Tùy vào loại giao dịch mà tạo danh sách khác nhau
     */
    fun getSuccessList(): List<SuccessItemDomain> {
        // Lấy mtID từ danh sách transactionIds
        val mtId = _uiModel.value.transactionIds.firstOrNull() ?: ""

        // Xác định đây có phải là giao dịch batch không
        // Giao dịch batch có nhiều ID giao dịch và thường có ít item hiển thị hơn
        val isBatch = _uiModel.value.transactionIds.size > 1 ||
            (_uiModel.value.items.any { it.label == "Tổng số giao dịch" })

        // Sử dụng Helper để tạo danh sách theo tranType, với items là nguồn dữ liệu chính
        return SuccessHelper
            .createSuccessList(
                tranType = _uiModel.value.tranType,
                mtId = mtId,
                items = _uiModel.value.items,
                isBatch = isBatch,
                tranDate = transactionDate,
                fileAttachments = null,
                nextApprovers = null,
                hostMtId = hostMtId,
            )
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath to exception.code) {
                Constants.MB_GEN_KEYPASS_CHALLENGE_CDE to Tags.NEED_SYNC_KEYPASS -> {
                    vmScope.launch {
                        _needSyncKeypass.emit(exception.message ?: "")
                    }
                    return
                }

                Constants.MB_GEN_SOTP_TRANS_CODE to Tags.NEED_REACTIVE_SOTP -> {
                    vmScope.launch {
                        _needReactiveSoft.emit(exception.message ?: "")
                    }
                    return
                }

                Constants.MB_GEN_SOTP_TRANS_CODE to Tags.NEED_SYNC_SOTP -> {
                    vmScope.launch {
                        _needSyncSoft.emit(exception.message ?: "")
                    }
                    return
                }
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    private val _signFlowState = MutableSharedFlow<SignCallState>()
    val signFlowState = _signFlowState.asSharedFlow()
    fun changeSignState(state: SignCallState) {
        viewModelScope.launch {
            _signFlowState.emit(state)
        }
    }

    // lay danh sach cks
    private val _lstCertState = MutableSharedFlow<List<SmartCAEsignRegisterDomain>>()
    val lstCertState = _lstCertState.asSharedFlow()
    fun getCertVNPT() {
        launchJob {
            val params = SmartCACertVNPTParams(
                userProf.getIdNumber(),
                userProf.getRoleId(),
                userProf.getUserName(),
            )
            val res = smartUseCase.getCertVNPT(params)
            handleResource(res) { data ->
                _lstCertState.emit(data.esignRegisterList ?: emptyList())
            }
        }
    }

    // xac nhan phe duyet giao dich -> goi trong truong hop phe duyet gd bang ky so
    var expiredCount: Int = 300
    private val _doSignApproveState = MutableSharedFlow<ApproveDomain>()
    val doSignApproveState: SharedFlow<ApproveDomain> = _doSignApproveState.asSharedFlow()
    fun doSignApprove(
        currentTransaction: TransactionDomain? = null,
        approveLst: List<ApproverDomains>? = null,
        transactionLst: List<String>? = null,
    ) = launchJob {
        val nextApproveLst = ArrayList<ApproverDomains>()
        approveLst?.forEach { approver ->
            val updatedApprove = approver.copy()
            updatedApprove.isSelected = true
            nextApproveLst.add(updatedApprove)
        }
        val res = useCase.doApprove(
            ApproveParams(
                username = userProf.getUserName().toString(),
                token = "",
                softOtpTransId = "",
                authenType = "VNPTSMARTCA",
                requestType = "",
                serviceType = currentTransaction?.serviceType,
                tranType = currentTransaction?.tranType,
                transactions = ArrayList(transactionLst ?: emptyList()),
                transactionsTp = arrayListOf(),
                nextApprovers = nextApproveLst,
            ),
        )
        handleResource(res) { data ->
            val firstTransaction = data.transactions?.firstOrNull()
            transactionID = firstTransaction?.mtId ?: ""
            // Capture tranDate and hostMtId from API response
            transactionDate = data.tranDate
            // Try to get hostMtId from transaction - need to check if this field exists
            // hostMtId = firstTransaction?.hostMtId
            data.verifySignInfo?.expiredIn?.let {
                expiredCount = it.toIntOrNull() ?: 300
            }
            _doSignApproveState.emit(data)
        }
    }

    // Approve with Soft OTP
    private val _doApproveResponse = MutableSharedFlow<ApproveDomain>()
    val doApproveResponse: SharedFlow<ApproveDomain> = _doApproveResponse.asSharedFlow()
    fun doApprove(confirmType: String, objType: String = "", otpCode: String) = launchJob {
        printLog("ConfirmCheckerViewModel: doApprove called - confirmType=$confirmType, otpCode=$otpCode")
        printLog("ConfirmCheckerViewModel: doApprove - transactionID=$transactionID")
        printLog("ConfirmCheckerViewModel: doApprove - uiModel.transactionIds=${_uiModel.value.transactionIds}")
        printLog("ConfirmCheckerViewModel: doApprove - uiModel.tranType=${_uiModel.value.tranType}, serviceType=${_uiModel.value.serviceType}")

        val nextApproversList = ArrayList<ApproverDomains>()
        selectedNextApprover.value.forEach { approver ->
            val updatedApprove = approver.copy()
            updatedApprove.isSelected = true
            nextApproversList.add(updatedApprove)
        }

        val res = useCase.doApprove(
            ApproveParams(
                username = userProf.getUserName().toString(),
                token = otpCode,
                softOtpTransId = transactionID ?: "",
                authenType = "S", // S for Soft OTP
                requestType = "0",
                serviceType = _uiModel.value.serviceType,
                tranType = _uiModel.value.tranType,
                transactions = ArrayList(_uiModel.value.transactionIds),
                transactionsTp = arrayListOf(),
                nextApprovers = nextApproversList,
            ),
        )
        handleResource(res) { data ->
            val firstTransaction = data.transactions?.firstOrNull()
            transactionID = firstTransaction?.mtId ?: ""
            // Capture tranDate and hostMtId from API response
            transactionDate = data.tranDate
            // Try to get hostMtId from transaction - need to check if this field exists
            // hostMtId = firstTransaction?.hostMtId
            _doApproveResponse.emit(data)
        }
    }

    fun verifyKeypass(otpValue: String) {
        launchJob(showLoading = true) {
            /*
            Viewmodel này có thể không phải viewmodel chính của fragment nên phải send event
            riêng để view handle các response case thay vì để base handle
             */
            sendEvent(KeypassAuthEvent.OnVerifyingKeypass(false))
            val nextApproversList = selectedNextApprover.value.map {
                it.copy(isSelected = true)
            }
            val res = useCase.doApprove(
                ApproveParams(
                    username = userProf.getUserName().toString(),
                    token = otpValue,
                    softOtpTransId = "",
                    authenType = KeypassConstant.AUTH_TYPE,
                    requestType = KeypassConstant.REQUEST_TYPE,
                    serviceType = _uiModel.value.serviceType,
                    tranType = _uiModel.value.tranType,
                    transactions = ArrayList(_uiModel.value.transactionIds),
                    transactionsTp = arrayListOf(),
                    nextApprovers = ArrayList(nextApproversList),
                ),
            )
            sendEvent(KeypassAuthEvent.OnVerifyingKeypass(true))
            when (res) {
                is Resource.Error -> {
                    (res.exception as? AppException.ApiException)?.let { exception ->
                        if (exception.requestPath == Constants.MB_APPROVE &&
                            exception.code == Tags.NEED_SYNC_KEYPASS
                        ) {
                            sendEvent(KeypassAuthEvent.OnReachMaxFail)
                        } else {
                            sendEvent(
                                KeypassAuthEvent.OnVerifyFail(
                                    exception.message ?: "",
                                ),
                            )
                        }
                    }
                }
                is Resource.Success -> {
                    val firstTransaction = res.data.transactions?.firstOrNull()
                    transactionID = firstTransaction?.mtId ?: ""
                    // Capture tranDate and hostMtId from API response
                    transactionDate = res.data.tranDate
                    sendEvent(
                        KeypassAuthEvent.OnVerifyKeypassSuccess(res.data),
                    )
                }
            }
        }
    }

    // kiem tra trang thai cua giao dich khi phe duyet bang chu ky so
    private val _transactionDetailState = MutableSharedFlow<GetTransactionDetailDomain>()
    val transactionDetailState = _transactionDetailState.asSharedFlow()
    fun getTransactionDetail(onError: ((AppException) -> Unit)? = null) = launchJobSilent {
        val res = useCase.getTransactionDetail(
            GetTransactionDetailParams(
                mtId = transactionID,
                tranType = _uiModel.value.tranType,
                serviceType = _uiModel.value.serviceType,
                username = userProf.getUserName(),
                signType = "1",
            ),
        )
        handleResourceSilent(
            resource = res,
            onSuccess = { data -> _transactionDetailState.emit(data) },
            onError = onError,
        )
    }

    /**
     * Download PDF file từ URL
     * @param pdfUrl URL của file PDF cần download
     */
    fun downloadPdf(pdfUrl: String) = launchJob {
        handleResource(checkerRepository.downloadPdfFile(pdfUrl)) { file ->
            _pdfFile.value = file
            printLog("PDF downloaded successfully: ${file.name}")
        }
    }

    data class AuthMethodState(
        val hasSoftOTP: Boolean = false,
        val hasKeypass: Boolean = false,
        val hasSign: Boolean = false,
        val selectedMethod: String = "",
    )

    sealed class ConfirmInputData {
        data class SingleTransaction(
            val transaction: TransactionDomain,
        ) : ConfirmInputData()

        data class BatchTransaction(
            val confirmUiModel: ConfirmUiModel,
        ) : ConfirmInputData()
    }
}

sealed interface KeypassAuthEvent : OneTimeEvent {
    object OnReachMaxFail : KeypassAuthEvent
    data class OnVerifyFail(val message: String) : KeypassAuthEvent
    data class OnVerifyKeypassSuccess(val approveData: ApproveDomain) : KeypassAuthEvent
    data class OnVerifyingKeypass(val isDone: Boolean) : KeypassAuthEvent
}
