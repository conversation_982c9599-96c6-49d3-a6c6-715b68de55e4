package com.vietinbank.feature_checker.ui.adapter

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Filter
import android.widget.Filterable
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.vietinbank.core_common.exception.setThrottleClickListener
import com.vietinbank.core_common.extensions.appendText
import com.vietinbank.core_common.extensions.removeVietNam
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_ui.R
import com.vietinbank.feature_checker.databinding.ItemCertBinding
import com.vietinbank.feature_checker.databinding.ItemFileBinding

class CertAdapter : ListAdapter<Any, RecyclerView.ViewHolder>(DIFF_CALLBACK), Filterable {
    private var dataSet: MutableList<Any> = mutableListOf()
    private var selectedId: String? = null
    fun getSelectedId() = selectedId
    private var onItemClick: ((Any) -> Unit)? = null
    fun setOnItemClick(callback: ((Any) -> Unit)? = null) {
        onItemClick = callback
    }

    fun setData(data: List<Any>) {
        dataSet.clear()
        dataSet.addAll(data)
        submitList(dataSet)
    }

    override fun getItemViewType(position: Int): Int {
        return when (currentList[position]) {
            is FileTransactionDomain -> FILE
            else -> DEFAULT
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            FILE -> FileVH(
                ItemFileBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )

            else -> CertVH(
                ItemCertBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false,
                ),
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is FileVH -> holder.bindData(getItem(position))
            is CertVH -> holder.bindData(getItem(position))
            else -> {}
        }
    }

    inner class CertVH(val binding: ItemCertBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setThrottleClickListener {
                try {
                    if (currentList[adapterPosition] is SmartCAEsignRegisterDomain) {
                        selectedId =
                            (currentList[adapterPosition] as SmartCAEsignRegisterDomain).serial
                        notifyDataSetChanged()
                    }
                } catch (_: Exception) {
                }
            }
        }

        fun bindData(item: Any) {
            with(binding) {
                if (item is SmartCAEsignRegisterDomain) {
                    tvSerial.text = item.serial ?: ""
                    tvExpireDate.text = item.endDate ?: ""
                    tvService.text = item.serviceType ?: ""
                    tvEsignFrom.text = item.esignFrom ?: ""
                    if (!TextUtils.isEmpty(selectedId) && TextUtils.equals(
                            item.serial, selectedId,
                        )
                    ) {
                        txtSerial.setTextColor(
                            ContextCompat.getColor(
                                root.context,
                                R.color.text_blue_02,
                            ),
                        )
                        tvSerial.setTextColor(
                            ContextCompat.getColor(
                                root.context,
                                R.color.text_blue_02,
                            ),
                        )
                        ivStatus.setImageResource(R.drawable.ic_radio_check)
                    } else {
                        txtSerial.setTextColor(
                            ContextCompat.getColor(
                                root.context,
                                R.color.text_blue_07,
                            ),
                        )
                        tvSerial.setTextColor(
                            ContextCompat.getColor(
                                root.context,
                                R.color.text_gray_description,
                            ),
                        )
                        ivStatus.setImageResource(R.drawable.ic_radio_uncheck)
                    }
                }
            }
        }
    }

    inner class FileVH(val binding: ItemFileBinding) : RecyclerView.ViewHolder(binding.root) {
        init {
            binding.root.setThrottleClickListener {
                onItemClick?.invoke(getItem(adapterPosition))
            }
        }

        fun bindData(item: Any) {
            with(binding) {
                if (item is FileTransactionDomain) {
                    tvName.appendText(text = item.fileName ?: "", underline = true)
                }
            }
        }
    }

    companion object {
        const val DEFAULT = 0
        const val FILE = 1
        val DIFF_CALLBACK: DiffUtil.ItemCallback<Any> = object : DiffUtil.ItemCallback<Any>() {
            override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
                return oldItem === newItem
            }

            override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
                return if (oldItem is SmartCAEsignRegisterDomain && newItem is SmartCAEsignRegisterDomain) {
                    oldItem.mtId == newItem.mtId
                } else if (oldItem is FileTransactionDomain && newItem is FileTransactionDomain) {
                    oldItem.mtId == newItem.mtId
                } else {
                    false
                }
            }
        }
    }

    override fun getFilter(): Filter {
        return object : Filter() {
            override fun performFiltering(constraintFilter: CharSequence?): FilterResults {
                val filter = FilterResults()
                if (constraintFilter.isNullOrEmpty()) {
                    filter.values = dataSet
                } else {
                    val constraint = constraintFilter.toString().removeVietNam(true)
                    val lstFilter = mutableListOf<Any>()
                    lstFilter.addAll(
                        dataSet.filter { item ->
                            (
                                item is SmartCAEsignRegisterDomain && (
                                    item.name.removeVietNam(true)
                                        .contains(constraint) || true == item.serial?.contains(
                                        constraint,
                                    )
                                    )
                                ) || (
                                item is FileTransactionDomain && item.fileName.removeVietNam(
                                    true,
                                ).contains(constraint)
                                )
                        },
                    )
                    filter.values = lstFilter
                }
                return filter
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                if (results != null) {
                    submitList(results.values as MutableList<*>)
                }
            }
        }
    }
}