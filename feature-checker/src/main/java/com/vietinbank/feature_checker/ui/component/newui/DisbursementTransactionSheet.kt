package com.vietinbank.feature_checker.ui.component.newui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.feature_checker.R
import com.vietinbank.feature_checker.ui.component.approvallist.DisbursementTransactionInfo
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun DisbursementTransactionSheet(
    transaction: TransactionListDomain,
    visible: Boolean,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    onNext: () -> Unit,
) {
    BaseBottomSheet(
        visible = visible,
        onDismissRequest = onBack,
        allowTouchDismiss = true,
        secureFlag = true,
    ) {
        Column(
            modifier = modifier.commonRoundedCornerCard(),
        ) {
            FoundationStatus(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                statusMessage = transaction.statusName
                    ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
                statusCode = when (transaction.statusCode) {
                    Tags.STATUS_SUCCESS -> Status.Success
                    Tags.STATUS_USER_REJECT -> Status.Fail
                    Tags.STATUS_AWAITING_APPROVAL -> Status.Pending
                    else -> Status.Pending
                },
            )
            FoundationDivider(modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding16))
            DisbursementTransactionInfo(
                transaction,
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                allowCopy = false,
            )
            FoundationDivider(
                modifier = Modifier.padding(
                    top = FDS.Sizer.Padding.padding32,
                    bottom = FDS.Sizer.Padding.padding16,
                ),
            )

            Row(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_transaction_approve),
                    contentDescription = null,
                )

                FoundationText(
                    text = stringResource(CoreR.string.common_appro),
                    modifier = Modifier.padding(start = FDS.Sizer.Padding.padding8),
                )
            }

            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

            Row(
                modifier = Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(R.drawable.ic_transaction_reject),
                    contentDescription = null,
                )

                FoundationText(
                    text = stringResource(CoreR.string.common_reject),
                    modifier = Modifier.padding(start = FDS.Sizer.Padding.padding8),
                )
            }
        }

        Row(
            modifier = Modifier.padding(
                top = FDS.Sizer.Padding.padding16,
                start = FDS.Sizer.Padding.padding24,
                end = FDS.Sizer.Padding.padding24,
                bottom = FDS.Sizer.Padding.padding16,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            FoundationButton(
                modifier = Modifier.weight(1f),
                text = stringResource(com.vietinbank.core_ui.R.string.common_back),
                onClick = onBack,
                isLightButton = false,
            )

            Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

            FoundationButton(
                modifier = Modifier.weight(1f),
                text = stringResource(com.vietinbank.core_ui.R.string.common_detail),
                onClick = onNext,
            )
        }
    }
}