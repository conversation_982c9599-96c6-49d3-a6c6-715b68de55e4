package com.vietinbank.feature_checker.ui.component.newui

import com.vietinbank.core_domain.models.maker.ApproverDomains

/**
 * Actions for Init Transaction screen using new UI
 */
data class InitTransactionActions(
    val onBackClick: () -> Unit = {},
    val onApproveClick: () -> Unit = {},
    val onRejectClick: () -> Unit = {},
    val onBatchApproveClick: () -> Unit = {},
    val onBatchRejectClick: () -> Unit = {},
    val onDetailFileClick: (type: Int) -> Unit = {},
    val onAgreeDisbursementChanged: () -> Unit = {},
    val onTabSelected: (index: Int) -> Unit = {},
    val onManageTransactionClick: () -> Unit = {},
    val onToggleNextApprover: () -> Unit = {},
    val onShowNextApproverSelection: () -> Unit = {},
    val onShowUpdateDialog: () -> Unit = {},

    // file
    val onLoadMoreBatch: () -> Unit = {},
    val onToggleBatchAllClick: () -> Unit = {},
    val onToggleBatch: (mtId: String) -> Unit = {},
)

/**
 * State for next approver selection
 */
data class NextApproverState(
    val isEnabled: Boolean = false,
    val allApprovers: List<ApproverDomains> = emptyList(),
    val selectedApprovers: List<ApproverDomains> = emptyList(),
    val isBottomSheetVisible: Boolean = false,
)
