package com.vietinbank.feature_checker.ui.component.newui

sealed interface ITransactionInforAction {
    data object OnDismiss : ITransactionInforAction
    data object OnRejectClick : ITransactionInforAction
    data object OnApproveClick : ITransactionInforAction
    data class OnOpenDetail(val mtID: String, val tranType: String) : ITransactionInforAction
    data class OnDownloadCustomer(val mtID: String, val tranType: String) : ITransactionInforAction
    data class OnDownloadDelegate(val mtID: String, val tranType: String) : ITransactionInforAction
    data class OnDownloadSignature(val mtID: String, val tranType: String) : ITransactionInforAction
}