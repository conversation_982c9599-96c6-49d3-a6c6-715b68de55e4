package com.vietinbank.feature_checker.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.ConfirmOTPActions
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_checker.ui.screen.ConfirmOTPScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ConfirmOTPFragment : BaseFragment<ConfirmOTPViewModel>() {
    override val viewModel: ConfirmOTPViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Composable
    override fun ComposeScreen() {
        // collect state from ViewModel
        val isProcessing by viewModel.isProcessing.collectAsState()
        // create trigger actions
        val actions = ConfirmOTPActions(
            onBackClick = { appNavigator.popBackStack() },
            onCancelClick = { appNavigator.popBackStack() },
            onConfirmClick = { otpValue ->
                when (viewModel.confirmType) {
                    Tags.APPROVE -> viewModel.doApprove(otpValue)
                    Tags.UPDATE, Tags.REGISTER_EKYC -> viewModel.updateEkyc(otpValue)
                }
            },
            onTimeoutReached = {
                viewModel.handleTimeout()
            },
        )

        AppTheme {
            // Hiển thị loading indicator nếu cần
            if (isProcessing) {
                // LoadingIndicator()
            }
            ConfirmOTPScreen(
                viewModel = viewModel,
                actions = actions,
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.onScreenVisible()
        setupObserver()
        // Lấy dữ liệu transaction từ Bundle
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                printLog("OTP transaction data: $jsonData")

                // Kiểm tra xem dữ liệu có chứa items (đặc trưng của ConfirmUiModel)
                if (jsonData.contains("\"items\"")) {
                    // Parse dữ liệu theo ConfirmUiModel
                    val uiModel =
                        Utils.g().provideGson().fromJson(jsonData, ConfirmUiModel::class.java)
                    viewModel.processConfirmUiModel(uiModel)
                    printLog("Đã xử lý dữ liệu theo ConfirmUiModel: ${Utils.g().provideGson().toJson(uiModel)}")
                } else {
                    // Parse theo TransactionListDomain
                    val transaction = Utils.g().provideGson()
                        .fromJson(jsonData, TransactionListDomain::class.java)
                    viewModel.processTransaction(transaction)
                    printLog("Đã xử lý dữ liệu theo TransactionListDomain")
                }
            } catch (e: Exception) {
                printLog("Lỗi parse transaction JSON: ${e.message}")
                e.printStackTrace()
                viewModel.showError("Không thể xử lý dữ liệu giao dịch: ${e.message}")
            }
        }
        arguments?.getString(Tags.ORIGINAL_TRANSACTION_BUNDLE)?.let { jsonData ->
            if (jsonData.isNotEmpty()) {
                try {
                    val originalTrans = Utils.g().provideGson()
                        .fromJson(jsonData, TransactionListDomain::class.java)
                    viewModel.originalTransaction = originalTrans
                    printLog("OTP - originalTransaction parsed: ${originalTrans.mtId}, files: ${originalTrans.listFile?.size}")
                } catch (e: Exception) {
                    printLog("OTP - Error parsing originalTransaction: ${e.message}")
                }
            }
        }
        arguments?.getString(Tags.NEXT_APPROVER_BUNDLE)?.let { jsonData ->
            try {
                val typeToken = object : TypeToken<List<ApproverDomains>>() {}.type
                viewModel.selectedNextApprover =
                    Utils.g().provideGson().fromJson<List<ApproverDomains>>(jsonData, typeToken)
            } catch (e: Exception) {
                printLog("Lỗi parse transaction JSON: ${e.message}")
                e.printStackTrace()
            }
        }
        printLog("selectedNextApprover first: ${viewModel.selectedNextApprover?.firstOrNull()}")

        // Lấy token và step từ Bundle
        val token = arguments?.getString(Tags.TOKEN_BUNDLE) ?: ""
        viewModel.transactionID = arguments?.getString(Tags.TRANSACTION_ID_BUNDLE) ?: ""
        viewModel.transactionMtID = arguments?.getString(Tags.MT_ID) ?: ""

        val step = arguments?.getInt(Tags.STEP_BUNDLE, 60) ?: 60
        viewModel.updateOTPInfo(token, step)
        viewModel.confirmType = arguments?.getString(Tags.CONFIRM_TYPE_BUNDLE)
        viewModel.registerEkycID = arguments?.getString(Tags.REGISTER_EKYC_ID) ?: ""
    }

    private fun setupObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.apply {
                    launch {
                        doApproveResponse.collect { success ->
                            when (success) {
                                is Resource.Success -> {
                                    // chuyen man hinh
                                    val successList =
                                        Utils.g().provideGson().toJson(viewModel.getSuccessList())
                                    val successTitle = Utils.g().provideGson().toJson(
                                        viewModel.getSuccessTitle(
                                            success.data.transactions?.firstOrNull(),
                                            success.data.status?.message.toString(),
                                        ),
                                    )
//                                    appNavigator.gotoSuccessChecker(successList, successTitle, viewModel.confirmType ?: "")
                                    viewModel.resetDoApproveResponse()
                                }

                                else -> {}
                            }
                        }
                    }

                    launch {
                        errorEvent.collect { errorMessage ->
                            // Hiển thị thông báo lỗi
                            showNoticeDialog(errorMessage)
                        }
                    }

                    launch {
                        approveErrorEvent.collect { exception ->
                            if (exception.message != null) {
                                showNoticeDialog(
                                    message = exception.message.toString(),
                                    positiveAction = {
                                        appNavigator.popToApprovalList()
                                    },
                                )
                            }
                        }
                    }

                    launch {
                        viewModel.apiEvent.collect {
                            when (it) {
                                is ApiEvent.Success -> {
                                    appNavigator.goToConfirmUpdateEkycScreen()
                                }
                                is ApiEvent.ShowError -> {}
                            }
                        }
                    }
                }
            }
        }
    }
}