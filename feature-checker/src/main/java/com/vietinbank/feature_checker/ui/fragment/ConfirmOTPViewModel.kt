package com.vietinbank.feature_checker.ui.fragment

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.ApproveTransactionDomain
import com.vietinbank.core_domain.models.checker.NextApproversListDomain
import com.vietinbank.core_domain.models.checker.SuccessItemDomain
import com.vietinbank.core_domain.models.checker.SuccessTitleDomain
import com.vietinbank.core_domain.models.checker.TransactionListDomain
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmItemType
import com.vietinbank.core_domain.models.checker.maping.confirm.ConfirmUiModel
import com.vietinbank.core_domain.models.home.UpdateEkycParams
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_checker.ui.component.success.SuccessHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ConfirmOTPViewModel @Inject constructor(
    val useCase: CheckerUserCase,
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val moneyHelper: MoneyHelper,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
) : BaseViewModel() {
    var confirmType: String? = null
    var registerEkycID = ""

    // Lưu trữ transaction từ Bundle
    var originalTransaction: TransactionListDomain? = null

    // luu tru transaction ID, tam thoi dung de sau khi verify pin thanh cong
    var transactionID: String? = null
    var transactionMtID: String? = null

    // OTP token từ Bundle
    private val _token = MutableStateFlow("")
    val token = _token.asStateFlow()

    // Thời gian đếm ngược (giây)
    private val _timeoutSeconds = MutableStateFlow(60)
    val timeoutSeconds = _timeoutSeconds.asStateFlow()

    // Trạng thái đang xử lý
    private val _isProcessing = MutableStateFlow(false)
    val isProcessing = _isProcessing.asStateFlow()

    // Preapprove code
    private var _preapproveCode: String? = null

    private val _errorEvent = MutableSharedFlow<String>()
    val errorEvent = _errorEvent.asSharedFlow()

    // Cờ để kiểm soát việc load dữ liệu khi back về từ màn hình khác
    private var initialLoadDone = false

    private val _doApproveResponse = MutableStateFlow<Resource<ApproveDomain>?>(null)
    val doApproveResponse = _doApproveResponse.asStateFlow()

    private val _approveErrorEvent = MutableSharedFlow<AppException>()
    val approveErrorEvent = _approveErrorEvent.asSharedFlow()

    private val _otpValue = MutableStateFlow("")
    val otpValue = _otpValue.asStateFlow()

    private var confirmUiModel: ConfirmUiModel? = null

    // Selected approver
    var selectedNextApprover: List<ApproverDomains>? = emptyList()

    private val _apiEvent = Channel<ApiEvent>(Channel.UNLIMITED)
    val apiEvent = _apiEvent.receiveAsFlow()

    fun updateOTPValue(value: String) {
        _otpValue.value = value
    }

    // reset state
    fun resetDoApproveResponse() {
        _doApproveResponse.value = null
    }

    fun resetOtpValue() {
        _otpValue.value = ""
    }

    /**
     * Được gọi khi Fragment onViewCreated để đảm bảo chỉ load dữ liệu khi cần
     */
    fun onScreenVisible() {
        if (!initialLoadDone) {
            initialLoadDone = true
        }
    }

    /**
     * Xử lý dữ liệu ConfirmUiModel
     */
    fun processConfirmUiModel(uiModel: ConfirmUiModel) {
        confirmUiModel = uiModel
        printLog("Đã xử lý ConfirmUiModel với ${uiModel.items.size} items")
    }

    /**
     * Phương thức để xác định loại dữ liệu hiện tại
     */
    private fun isUsingConfirmUiModel(): Boolean {
        return confirmUiModel != null
    }

    /**
     * Xử lý dữ liệu giao dịch
     */
    fun processTransaction(transDomain: TransactionListDomain) {
        originalTransaction = transDomain
        try {
            printLog("Xử lý giao dịch trong OTP screen: ${transDomain.mtId}")

            // Thêm logic xử lý dữ liệu nếu cần
        } catch (e: Exception) {
            printLog("Lỗi xử lý giao dịch: ${e.message}")
            e.printStackTrace()
            showError("Không thể xử lý dữ liệu giao dịch: ${e.message}")
        }
    }

    /**
     * Cập nhật token, thời gian đếm ngược và preapprove code
     */
    fun updateOTPInfo(token: String, timeoutSeconds: Int) {
        _token.value = token
        _otpValue.value = token.take(6).padEnd(6, ' ')
        _timeoutSeconds.value = timeoutSeconds
        printLog("Đã cập nhật token: $token, thời gian: $timeoutSeconds giây")
    }

    /**
     * Xử lý khi hết thời gian
     */
    fun handleTimeout() {
        printLog("Hết thời gian chờ OTP, yêu cầu tạo mới")
        regenerateOTP()
    }

    /**
     * Tạo mới OTP
     */
    fun regenerateOTP() {
        _isProcessing.value = true

        // Gọi API để tạo mới OTP
        originalTransaction?.let { transaction ->
            _preapproveCode?.let { preapproveCode ->
                // Mock response
                _isProcessing.value = false
                _token.value = "123456"
                _timeoutSeconds.value = 60
            } ?: run {
                _isProcessing.value = false
                showError("Không có preapprove code để tạo mới OTP")
            }
        } ?: run {
            _isProcessing.value = false
            showError("Không có thông tin giao dịch để tạo mới OTP")
        }
    }

    /**
     * doApprove
     */
    fun doApprove(otpValue: String) {
        _isProcessing.value = true

        confirmUiModel?.let { uiModel ->
            launchJob(showLoading = true) {
                val nextApproversList = ArrayList<ApproverDomains>()
                selectedNextApprover?.forEach { approver ->
                    val updatedApprover = approver.copy()
                    updatedApprover.isSelected = true
                    nextApproversList.add(updatedApprover)
                }
                val res = useCase.doApprove(
                    ApproveParams(
                        username = userProf.getUserName().toString(),
                        token = otpValue,
                        softOtpTransId = transactionID,
                        authenType = "S",
                        requestType = "0",
                        serviceType = uiModel.serviceType, // "CT",
                        tranType = uiModel.tranType,
                        transactions = ArrayList(uiModel.transactionIds),
                        transactionsTp = arrayListOf(),
                        nextApprovers = nextApproversList,
                    ),
                )
                handleResource(res) { data ->
                    // Capture tranDate and hostMtId from API response
                    transactionDate = data.tranDate
                    val firstTransaction = data.transactions?.firstOrNull()
                    // Try to get hostMtId from transaction - need to check if this field exists
                    // hostMtId = firstTransaction?.hostMtId
                    nextApproversListDomain = data.nextApprovers
                    _doApproveResponse.value = Resource.Success(data)
                }
            }
        } ?: run {
            _isProcessing.value = false
            showError("Không có thông tin giao dịch để xác nhận OTP")
        }
    }

    fun updateEkyc(otpValue: String) {
        launchJob {
            val params = createEkycParams(otpValue)
            val res = homeUseCase.updateEkyc(params)

            handleResource(res) { data ->
                _apiEvent.send(ApiEvent.Success)
            }
        }
    }

    private fun createEkycParams(otpValue: String): UpdateEkycParams {
        val isUpdate = confirmType == Tags.UPDATE
        return UpdateEkycParams(
            username = userProf.getUserName().toString(),
            authenType = "S",
            softOtpTransId = transactionID,
            token = otpValue,
            tranType = if (isUpdate) "UTH" else "NEW_STH",
            serviceType = if (isUpdate) "UTH" else "NEW_STH",
            transactions = listOf(if (isUpdate) transactionMtID ?: "" else registerEkycID),
        )
    }

    /**
     * Hiển thị lỗi
     */
    fun showError(message: String) {
        viewModelScope.launch {
            _errorEvent.emit(message)
        }
    }

    fun getSuccessTitle(trans: ApproveTransactionDomain?, message: String): SuccessTitleDomain {
        // Tìm item số tiền được highlight từ ConfirmUiModel
        val highlightedItem = confirmUiModel?.items?.find {
            it.type == ConfirmItemType.KEY_VALUE && it.isHighlighted
        }

        // Lấy giá trị amount, đảm bảo không null
        val amount = highlightedItem?.value ?: ""

        // Lấy giá trị subValue (số tiền bằng chữ) đã được tạo trước đó
        var amountText = highlightedItem?.subValue ?: ""

        // Xử lý trường hợp truyền số tiền bằng chữ không thành công
        if (amountText.isEmpty() && amount.isNotEmpty()) {
            // Thử tạo lại amountText từ amount
            try {
                // Nếu là nhiều dòng (nhiều loại tiền), xử lý riêng từng loại tiền
                if (amount.contains("\n")) {
                    val lines = amount.split("\n")
                    val resultBuilder = StringBuilder()

                    lines.forEachIndexed { index, line ->
                        // Tìm loại tiền tệ từ dòng
                        val currency = extractCurrency(line)

                        // Lấy số tiền sạch
                        val cleanAmount = cleanAmountString(line, currency)

                        // Chuyển đổi thành chữ
                        val lineInWords = moneyHelper.convertAmountToWords(cleanAmount, currency)
                        resultBuilder.append(lineInWords)

                        // Thêm "và" giữa các dòng (nếu không phải dòng cuối)
                        if (index < lines.size - 1) {
                            resultBuilder.append(" và ")
                        }
                    }
                    amountText = resultBuilder.toString()
                } else {
                    // Trường hợp đơn tiền tệ
                    val currency = extractCurrency(amount)
                    val cleanAmount = cleanAmountString(amount, currency)
                    amountText = moneyHelper.convertAmountToWords(cleanAmount, currency)
                }
            } catch (e: Exception) {
                printLog("Lỗi khi convert amount: ${e.message}")
            }
        }

        return SuccessTitleDomain(
            title = message,
            status = trans?.status,
            statusTrans = trans?.statusTrans,
            amountText = amountText,
            amountTitle = amount,
        )
    }

    /**
     * Trích xuất loại tiền tệ từ chuỗi số tiền
     */
    private fun extractCurrency(amountString: String): String {
        return when {
            amountString.contains(" VND") -> "VND"
            amountString.contains(" USD") -> "USD"
            amountString.contains(" EUR") -> "EUR"
            amountString.contains(" GBP") -> "GBP"
            amountString.contains(" JPY") -> "JPY"
            amountString.contains(" CAD") -> "CAD"
            amountString.contains(" AUD") -> "AUD"
            amountString.contains(" SGD") -> "SGD"
            amountString.contains(" HKD") -> "HKD"
            amountString.contains(" CNY") -> "CNY"
            else -> "VND" // Mặc định là VND
        }
    }

    /**
     * Làm sạch chuỗi số tiền, loại bỏ định dạng và đơn vị tiền tệ
     */
    private fun cleanAmountString(amountString: String, currency: String): String {
        return amountString.replace(" $currency", "").replace(",", "").trim()
    }

    // Store the transaction date from API response
    private var transactionDate: String? = null

    // Store the host mtId from API response
    private var hostMtId: String? = null

    private var nextApproversListDomain: ArrayList<NextApproversListDomain>? = null

    /**
     * Lấy danh sách mục thông tin cho màn hình thành công
     * Tùy vào loại giao dịch mà tạo danh sách khác nhau
     */
    fun getSuccessList(): List<SuccessItemDomain> {
        // Nếu không có confirmUiModel, trả về danh sách rỗng
        confirmUiModel?.let { uiModel ->
            // Lấy mtID từ danh sách transactionIds
            val mtId = uiModel.transactionIds.firstOrNull() ?: ""

            val isBatch = uiModel.transactionIds.size > 1

            val fileAttachments = if (uiModel.tranType in listOf("sl", "slo", "sx")) {
                originalTransaction?.listFile ?: emptyList()
            } else if (uiModel.tranType == "gor") {
                // giai toa bl
                originalTransaction?.ibFile?.filter { "GDN" == it.attachmentType } ?: emptyList()
            } else if (uiModel.tranType == "goc") {
                // sua doi bl
                originalTransaction?.listFiles?.filter { "GDN" == it.attachmentType } ?: emptyList()
            } else {
                null
            }

            return SuccessHelper
                .createSuccessList(
                    tranType = uiModel.tranType,
                    mtId = mtId,
                    items = uiModel.items,
                    isBatch = isBatch,
                    tranDate = transactionDate,
                    fileAttachments = fileAttachments,
                    nextApprovers = nextApproversListDomain,
                    hostMtId = hostMtId,
                )
        } ?: return emptyList()
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_APPROVE ||
            exception is AppException.ApiException && exception.requestPath == Constants.MB_REJECT
        ) {
            viewModelScope.launch {
                _approveErrorEvent.emit(exception)
            }
        } else {
            super.onDisplayErrorMessage(exception)
        }
    }
}

sealed class ApiEvent {
    data object Success : ApiEvent()
    data class ShowError(val message: String) : ApiEvent()
}