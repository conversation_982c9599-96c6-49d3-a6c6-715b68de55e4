package com.vietinbank.feature_home.ui.view.home_card

import com.vietinbank.core_common.constants.FunctionItemType
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R

enum class ItemAction {
    NoAction,
    Move,
    Remove,
    Add,
}

sealed class FunctionNavigationEvent : OneTimeEvent {
    data object NavigateToTransfer : FunctionNavigationEvent()
    data object NotYetImplement : FunctionNavigationEvent()
}

fun String.sendNavigate(): FunctionNavigationEvent {
    return when (this) {
        FunctionItemType.GroupConstants.TRANSFER -> FunctionNavigationEvent.NavigateToTransfer
        else -> FunctionNavigationEvent.NotYetImplement
    }
}

// fun FunctionItemData.withAction(action: ItemAction): FunctionItemData =
//    this.copy(itemAction = action)

fun defaultFunc(financialPackage: String, roleLevel: String): List<FunctionItemData> {
    return when (financialPackage) {
        Tags.ROLE_ACCOUNT_INQUIRY -> {
            listOf(
                FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_SETTING, null),
                FunctionItemData(
                    R.drawable.ic_home_transfer,
                    Tags.HOME_FUNC_EFAST_MANAGE,
                    null,
                ),
                FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_EINVOICE, null),
                FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_ACCOUNT, null),
            )
        }

        Tags.ROLE_INVOICE -> {
            listOf(
                FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_SETTING, null),
                FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_EINVOICE, null),
            )
        }

        else -> {
            when (roleLevel) {
                Tags.ROLE_MAKER -> {
                    listOf(
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_SETTING,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_EFAST_MANAGE,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_TRANSFER,
                            null,
                        ),
                        FunctionItemData(R.drawable.ic_home_transfer, Tags.HOME_FUNC_QR, null),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_ACCOUNT,
                            null,
                        ),
                    )
                }

                else -> {
                    listOf(
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_SETTING,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_MANAGE,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_TAX,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_SALARY,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_TRANSFER,
                            null,
                        ),
                        FunctionItemData(
                            R.drawable.ic_home_transfer,
                            Tags.HOME_FUNC_ACCOUNT,
                            null,
                        ),
                    )
                }
            }
        }
    }
}