package com.vietinbank.feature_home.ui.screen.home_new_ui.account_home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.components.UserProfileCard
import com.vietinbank.core_ui.components.radialGradientBackground
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.gradientBorder
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home_new_ui.home_page.BottomGuideAction
import com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut.ShortCutScreen
import com.vietinbank.feature_home.ui.view.AssetCard
import com.vietinbank.feature_home.ui.view.NavigationBar
import com.vietinbank.feature_home.ui.view.home_card.BannerCard
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun AccountScreen(
    uiState: AccountUiState,
    onAction: (AccountAction) -> Unit,
) {
    val listState = rememberLazyListState()
    var bottomSize by remember { mutableStateOf(IntSize.Zero) }

    Box {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackground()
                .systemBarsPadding()
                .padding(top = FDS.Sizer.Padding.padding8)
                .padding(horizontal = FDS.Sizer.Padding.padding8)
                .clip(
                    RoundedCornerShape(
                        bottomEnd = FDS.Sizer.Padding.padding32,
                        bottomStart = FDS.Sizer.Padding.padding32,
                    ),
                ),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
            horizontalAlignment = Alignment.CenterHorizontally,
            state = listState,
        ) {
            item {
                UserProfileCard(
                    title = uiState.userName,
                    content = stringResource(uiState.roleName, uiState.companyName),
                    icon = painterResource(com.vietinbank.core_ui.R.drawable.ic_profile_card_notification),
                    onClickAvatar = {
                        onAction(AccountAction.OnClickAvatar)
                    },
                    onClickIcon = {
                        onAction(AccountAction.OnClickNotification)
                    },
                )
            }
            item {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(FDS.Sizer.Padding.padding16),
                    verticalArrangement = Arrangement.spacedBy(
                        FDS.Sizer.Padding.padding8,
                        Alignment.CenterVertically,
                    ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    FoundationText(
                        text = stringResource(R.string.home_account_smart_insight),
                        color = FDS.Colors.characterHighlightedLighter,
                        style = FDS.Typography.captionCaptionLBold,
                    )
                    DotIndicatorTabs()
                }
            }
//            item {
//                FoundationTabs(
//                    tabs = listOf(
//                        stringResource(R.string.account_home_tab_all),
//                        stringResource(R.string.account_home_cash_flow),
//                        stringResource(R.string.account_home_efficiency),
//                    ),
//                    selectedIndex = uiState.tabSelected,
//                    onTabSelected = {
//                        onAction(AccountAction.OnTabSelected(it))
//                    },
//                    type = TabType.Pill,
//                )
//            }
            item {
                AssetCard(
//                    uiModel = uiState.assetState,
//                    buttonAction = { onAction(AccountAction.ClickInquiryAccount) },
                    paymentUiModel = uiState.payment,
                    savingUiModel = uiState.saving,
                    loanUiModel = uiState.loan,
                    paymentAction = { onAction(AccountAction.CLickPayment) },
                    savingAction = { onAction(AccountAction.CLickSaving) },
                    loanAction = { onAction(AccountAction.CLickLoan) },
                )
            }
            item {
                BannerCard(
                    onClickBanner = { onAction(AccountAction.TestTest) },
                )
            }

            item {
                BottomGuideAction(
                    modifier = Modifier
                        .padding(bottom = (bottomSize.height / 2).dp),
                    onClickFindBankBranch = { onAction(AccountAction.TestTest) },
                    onClickHelp = { onAction(AccountAction.TestTest) },
                    onClickContact = { onAction(AccountAction.TestTest) },
                )
            }
        }

        Box(
            modifier = Modifier.align(Alignment.BottomCenter),
        ) {
            NavigationBar(
                selectedIndex = uiState.bottomNavIndex,
                onTabSelected = {
                    onAction(AccountAction.OnNavigationTabSelected(it))
                },
                onClickShortcut = {
                    onAction(AccountAction.ClickShortcut)
                },
                onSizeChanged = {
                    bottomSize = it
                },
            )
        }

        if (uiState.isShowShortcut) {
            ShortCutScreen(
                onClickShortcut = { onAction(AccountAction.ClickShortcut) },
                list = uiState.listFuncShortcut,
                onClickEdit = { onAction(AccountAction.ClickEditShortcut) },
                onClickMyRequestItem = {},
            )
        }
    }
}

@Composable
fun DotIndicatorTabs(
    pageCount: Int = 5,
) {
    val pagerState = rememberPagerState(pageCount = { pageCount }) // số trang

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Pager
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = FDS.Sizer.Padding.padding12),
        ) { page ->
            Box(
                contentAlignment = Alignment.Center,
            ) {
                FoundationText(
                    text = stringResource(R.string.home_account_test_insight),
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    style = FDS.Typography.bodyB1Emphasized,
                )
            }
        }

        // Indicator chấm tròn
        Row(
            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8, alignment = Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            repeat(pagerState.pageCount) { index ->
                val isSelected = pagerState.currentPage == index
                GradientDot(isSelected = isSelected)
            }
        }
    }
}

@Composable
fun GradientDot(
    isSelected: Boolean = false,
) {
    Box(
        modifier = Modifier
            .size(FDS.Sizer.Padding.padding8)
            .clip(CircleShape)
            .then(
                if (isSelected) {
                    Modifier.background(Color.White)
                } else {
                    Modifier.radialGradientBackground(
                        colors = listOf(
                            FDS.Colors.darkButtonEnableState.copy(alpha = 0.2F),
                            Color.White.copy(alpha = 0F),
                        ),
                    )
                },
            )
            .gradientBorder(),
    )
}