package com.vietinbank.feature_home.ui.screen.setting.newui.fragment

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.setting.SettingEvent
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingItemDestination
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingUiState
import com.vietinbank.feature_home.ui.screen.setting.newui.defaultCheckerSettingItems
import com.vietinbank.feature_home.ui.screen.setting.newui.defaultMakerSettingItems
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class NewUISettingViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    val transferCacheManager: ITransferCacheManager,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(SettingUiState())
    val uiState = _uiState.asStateFlow()

    init {
        _uiState.update {
            it.copy(
                userName = userProf.getFullName() ?: "",
                companyName = userProf.getCorpName() ?: " ",
                role = when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> R.string.account_card_company_name_maker
                    else -> R.string.account_card_company_name_checker
                },
                settingSections = when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> defaultMakerSettingItems
                    else -> defaultCheckerSettingItems
                },
            )
        }
    }

    fun onAction(action: SettingItemDestination) {
        when (action) {
            SettingItemDestination.AccountInfo -> {}
            SettingItemDestination.AuthenticationMethods -> {}
            SettingItemDestination.BillContacts -> {}
            SettingItemDestination.BiometricUpdate -> {}
            SettingItemDestination.ChangeAvatar -> {}
            SettingItemDestination.ChangeLanguage -> {}
            SettingItemDestination.ChangePassword -> {}
            SettingItemDestination.ChangeThemeAndFont -> {}
            SettingItemDestination.CustomerSupport -> {}
            SettingItemDestination.Faq -> {}
            SettingItemDestination.Logout -> {}
            SettingItemDestination.ManageDevices -> {}
            SettingItemDestination.ManageNotification -> {
                sendEvent(SettingEvent.OttServiceEvent)
            }
            SettingItemDestination.ShareApp -> {}
            SettingItemDestination.TransactionLimit -> {}
            SettingItemDestination.TransactionLocations -> {}
            SettingItemDestination.UserGuide -> {}
        }
    }
    fun clearSession() = sessionManager.clearSession()

    fun onFaceLoginChange(value: Boolean) {
        _uiState.update {
            it.copy(
                isFaceLogin = value,
            )
        }
    }
}