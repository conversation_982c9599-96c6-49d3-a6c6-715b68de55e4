package com.vietinbank.feature_home.ui.screen.ott_setting.detail_fee

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.activityViewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.ui.navigation.OttServiceNavigator
import com.vietinbank.feature_home.ui.screen.ott_setting.list_account.ListAccountViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DetailsFeeFragment : BaseFragment<ListAccountViewModel>() {
    override val viewModel: ListAccountViewModel by activityViewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ottServiceNavigator: OttServiceNavigator

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        DetailsFeeScreen(
            uiState = uiState,
            onBackClick = {
                ottServiceNavigator.popBackStack()
            },
        )
    }
}