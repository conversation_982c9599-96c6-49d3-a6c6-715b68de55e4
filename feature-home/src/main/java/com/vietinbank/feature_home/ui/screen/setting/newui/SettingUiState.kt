package com.vietinbank.feature_home.ui.screen.setting.newui

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.R as CoreR

@Stable
data class SettingUiState(
    val userName: String = "",
    val role: Int = R.string.account_card_company_name_maker,
    val companyName: String = "",
    val isFaceLogin: Boolean = false,
    val settingSections: List<SettingSection> = defaultCheckerSettingItems,
)

data class SettingSection(
    @StringRes val titleRes: Int,
    val items: List<SettingItem>,
)

sealed class SettingItem(
    @DrawableRes val iconRes: Int,
    @StringRes val titleRes: Int,
) {
    // Item fix cứng giá trị
    class StaticItem(
        iconRes: Int,
        titleRes: Int,
        val action: SettingItemDestination,
    ) : SettingItem(iconRes, titleRes)

    // Item động
    class FaceLoginItem(
        iconRes: Int,
        titleRes: Int,
    ) : SettingItem(iconRes, titleRes)
}

// Action cho setting cho item static
sealed interface SettingItemDestination {
    // Quản lý chung
    data object AccountInfo : SettingItemDestination
    data object ChangeAvatar : SettingItemDestination
    data object ChangeThemeAndFont : SettingItemDestination
    data object ChangeLanguage : SettingItemDestination
    data object ManageNotification : SettingItemDestination
    data object BillContacts : SettingItemDestination
    data object TransactionLimit : SettingItemDestination

    // Bảo mật
    data object ChangePassword : SettingItemDestination
    data object AuthenticationMethods : SettingItemDestination
    data object BiometricUpdate : SettingItemDestination
    data object ManageDevices : SettingItemDestination

    // Hỗ trợ & Liên hệ
    data object CustomerSupport : SettingItemDestination
    data object TransactionLocations : SettingItemDestination
    data object Faq : SettingItemDestination
    data object ShareApp : SettingItemDestination
    data object UserGuide : SettingItemDestination
    data object Logout : SettingItemDestination
}

val defaultCheckerSettingItems = listOf(
    SettingSection(
        titleRes = CoreR.string.setting_common_manage_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_common,
                titleRes = CoreR.string.setting_button_common_info,
                action = SettingItemDestination.AccountInfo,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_user,
                titleRes = CoreR.string.setting_button_change_avatar,
                action = SettingItemDestination.ChangeAvatar,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_theme,
                titleRes = CoreR.string.setting_button_change_theme_and_font,
                action = SettingItemDestination.ChangeThemeAndFont,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_language,
                titleRes = CoreR.string.setting_button_change_language,
                action = SettingItemDestination.ChangeLanguage,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_noti,
                titleRes = CoreR.string.setting_button_manage_notifications,
                action = SettingItemDestination.ManageNotification,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_receipt,
                titleRes = CoreR.string.setting_button_bill_contacts,
                action = SettingItemDestination.BillContacts,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_trans_budget,
                titleRes = CoreR.string.setting_button_set_transaction_limit,
                action = SettingItemDestination.TransactionLimit,
            ),
        ),
    ),
    SettingSection(
        titleRes = CoreR.string.setting_security_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_password,
                titleRes = CoreR.string.setting_button_change_password,
                action = SettingItemDestination.ChangePassword,
            ),
            SettingItem.FaceLoginItem(
                iconRes = R.drawable.ic_setting_ekyc,
                titleRes = CoreR.string.setting_button_login_with_face,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_auth,
                titleRes = CoreR.string.setting_button_authentication_methods,
                action = SettingItemDestination.AuthenticationMethods,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_biometric,
                titleRes = CoreR.string.setting_button_biometric_update,
                action = SettingItemDestination.BiometricUpdate,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_device,
                titleRes = CoreR.string.setting_button_manage_devices,
                action = SettingItemDestination.ManageDevices,
            ),
        ),
    ),
    SettingSection(
        titleRes = CoreR.string.setting_support_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_support,
                titleRes = CoreR.string.setting_button_customer_support,
                action = SettingItemDestination.CustomerSupport,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_location,
                titleRes = CoreR.string.setting_button_transaction_locations,
                action = SettingItemDestination.TransactionLocations,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_faq,
                titleRes = CoreR.string.setting_button_faq,
                action = SettingItemDestination.Faq,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_share,
                titleRes = CoreR.string.setting_button_share_app,
                action = SettingItemDestination.ShareApp,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_guide,
                titleRes = CoreR.string.setting_button_user_guide,
                action = SettingItemDestination.UserGuide,
            ),
        ),
    ),
)

val defaultMakerSettingItems = listOf(
    SettingSection(
        titleRes = CoreR.string.setting_common_manage_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_common,
                titleRes = CoreR.string.setting_button_common_info,
                action = SettingItemDestination.AccountInfo,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_user,
                titleRes = CoreR.string.setting_button_change_avatar,
                action = SettingItemDestination.ChangeAvatar,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_theme,
                titleRes = CoreR.string.setting_button_change_theme_and_font,
                action = SettingItemDestination.ChangeThemeAndFont,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_language,
                titleRes = CoreR.string.setting_button_change_language,
                action = SettingItemDestination.ChangeLanguage,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_noti,
                titleRes = CoreR.string.setting_button_manage_notifications,
                action = SettingItemDestination.ManageNotification,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_receipt,
                titleRes = CoreR.string.setting_button_bill_contacts,
                action = SettingItemDestination.BillContacts,
            ),
        ),
    ),
    SettingSection(
        titleRes = CoreR.string.setting_security_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_password,
                titleRes = CoreR.string.setting_button_change_password,
                action = SettingItemDestination.ChangePassword,
            ),
            SettingItem.FaceLoginItem(
                iconRes = R.drawable.ic_setting_ekyc,
                titleRes = CoreR.string.setting_button_login_with_face,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_device,
                titleRes = CoreR.string.setting_button_manage_devices,
                action = SettingItemDestination.ManageDevices,
            ),
        ),
    ),
    SettingSection(
        titleRes = CoreR.string.setting_support_section_title,
        items = listOf(
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_support,
                titleRes = CoreR.string.setting_button_customer_support,
                action = SettingItemDestination.CustomerSupport,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_location,
                titleRes = CoreR.string.setting_button_transaction_locations,
                action = SettingItemDestination.TransactionLocations,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_faq,
                titleRes = CoreR.string.setting_button_faq,
                action = SettingItemDestination.Faq,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_share,
                titleRes = CoreR.string.setting_button_share_app,
                action = SettingItemDestination.ShareApp,
            ),
            SettingItem.StaticItem(
                iconRes = R.drawable.ic_setting_guide,
                titleRes = CoreR.string.setting_button_user_guide,
                action = SettingItemDestination.UserGuide,
            ),
        ),
    ),
)
