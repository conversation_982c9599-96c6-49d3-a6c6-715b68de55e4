package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun OttServiceBottomSheet(
    visible: Boolean,
    accounts: List<Triple<String, Int?, Int?>>,
    selectedItem: String,
    onDismiss: () -> Unit,
    onPicked: (String) -> Unit,
) {
    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = { picked ->
            onPicked(picked)
            // Auto-dismiss handled by BaseBottomSheet
        },
        secureFlag = false, // Set to true if showing sensitive account data
    ) { onResult ->
        Column(
            Modifier
                .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                .background(Color.White)
                .padding(vertical = FDS.Sizer.Padding.padding24),
        ) {
            // Title
            FoundationText(
                text = stringResource(R.string.ott_service_title),
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = FDS.Sizer.Padding.padding16),
            )

            HorizontalDivider(
                color = FDS.Colors.strokeDivider,
                thickness = FDS.Sizer.Stroke.stroke1,
                modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
            )

            accounts.forEach { account ->
                // Account list
                AlertItem(
                    title = account.second,
                    description = account.third,
                    isSelected = account.first == selectedItem,
                    onCheckedChange = {
                        onResult(account.first) // This will trigger onPicked and auto-dismiss
                    },
                )
            }
        }
    }
}

@Composable
fun AlertItem(
    title: Int?,
    description: Int?,
    isSelected: Boolean,
    onCheckedChange: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(FDS.Sizer.Padding.padding8)
            .padding(horizontal = FDS.Sizer.Padding.padding8)
            .dismissRippleClickable {
                onCheckedChange()
            },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(1f),
        ) {
            if (title != null) {
                FoundationText(
                    text = stringResource(title),
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterHighlighted,
                )
            }
            Spacer(modifier = Modifier.height(FDS.Sizer.Padding.padding4))
            if (description != null) {
                FoundationText(
                    text = stringResource(description),
                    style = FDS.Typography.captionL,
                    color = FDS.Colors.characterSecondary,
                )
            }
        }

        FoundationSelector(
            boxType = SelectorType.Radio,
            isSelected = isSelected,
            onClick = {},
        )
    }
}