package com.vietinbank.feature_home.ui.navigation

interface OttServiceNavigator {
    fun goToOttService(serviceType: String, newRegister: Boolean = false)
    fun goToListAccountFragment(
        serviceType: String,
        aliasName: String,
        data: String,
        cifNo: String,
        phoneNum: String,
        feeSMS: Int,
        accountSMS: Int,
        feeAmount: String,
        alertType: String,
    )
    fun popToOttManage()
    fun goToDetailFee()
    fun popBackStack()
    fun popBackGraph()
}