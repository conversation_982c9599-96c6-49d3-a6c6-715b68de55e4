package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_manage

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui.OttServiceViewModel.Companion.IS_VACC_N
import com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui.OttServiceViewModel.Companion.IS_VACC_Y
import com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui.OttServiceViewModel.Companion.TYPE_CHECK_PARAM
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class OttServiceManageViewModel @Inject constructor(
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(OttServiceManageUiState())
    val uiState: StateFlow<OttServiceManageUiState> = _uiState.asStateFlow()

    init {
        listReg()
    }

    fun onAction(action: OttServiceManageAction) {
        when (action) {
            OttServiceManageAction.OnClickBack -> {
                sendEvent(OttServiceManageEvent.NavigateBack)
            }
            OttServiceManageAction.OnClickOttSetting -> {
                sendEvent(OttServiceManageEvent.NavigateSignUpOtt(false))
            }
            OttServiceManageAction.OnClickOttSwitch -> {
                sendEvent(OttServiceManageEvent.NavigateSwitchSms)
            }
            OttServiceManageAction.OnClickOttSignUp -> {
                sendEvent(OttServiceManageEvent.NavigateSignUpOtt(_uiState.value.isNewRegis))
            }
        }
    }

    private fun listReg() {
        launchJob(showLoading = true) {
            val res = ottUseCase.listReg(
                ListRegRequestParams(
                    // change when keypass method valid
                    alertMethod = Tags.OTT_SERVICE_TAG_ALL,
                    isVacct = IS_VACC_N,
                    mobileNumber = userProf.getPhoneNo() ?: "",
                    roleId = "",
                    tranId = "",
                    typeCheck = IS_VACC_Y,
                    type = TYPE_CHECK_PARAM,
                    username = userProf.getUserName().orEmpty(),
                ),
            )

            handleResource(
                resource = res,
                onSuccess = { data ->
                    val filteredList = data.results?.filter {
                        it.alertType == Tags.OTT_TAG_159
                    }.orEmpty()
                    _uiState.update {
                        it.copy(
                            isNewRegis = filteredList.isEmpty(),
                        )
                    }
                },
                handleError = {
                },
            )
        }
    }
}

data class OttServiceManageUiState(
    val isNewRegis: Boolean = false,
)

sealed class OttServiceManageEvent : OneTimeEvent {
    data object NavigateBack : OneTimeEvent
    data class NavigateSignUpOtt(val newRegister: Boolean) : OneTimeEvent
    data object NavigateSwitchSms : OneTimeEvent
}

sealed interface OttServiceManageAction {
    data object OnClickBack : OttServiceManageAction
    data object OnClickOttSignUp : OttServiceManageAction // test
    data object OnClickOttSetting : OttServiceManageAction
    data object OnClickOttSwitch : OttServiceManageAction
}