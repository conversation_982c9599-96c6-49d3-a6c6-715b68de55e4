package com.vietinbank.feature_home.ui.view.home_card

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.ShimmerLoading
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class RecommendedUiModelState(
    val updatedTime: String = "",
    val listSuggestion: List<FunctionItemData>? = listOf(),
    val pendingRequest: String = "",
    val rejectRequest: String = "",
    val approveRequest: String = "",
    val isLoading: Boolean = false,
    val isLoadingRecommended: Boolean = false,
    val isEnableLoading: Boolean = true,
)

@Composable
fun RecommendedCard(
    modifier: Modifier = Modifier,
    uiModel: RecommendedUiModelState,
    onClickLoading: () -> Unit,
    onClickSeeAll: () -> Unit = {},
    onClickItem: (String) -> Unit,
    onClickAllFeature: () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .background(Color.White),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = FDS.Sizer.Padding.padding24)
                .padding(top = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationText(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.home_rcm_card_title),
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.tabTextInactive,
                )
                FoundationText(
                    modifier = Modifier.dismissRippleClickable {
                        onClickAllFeature()
                    },
                    text = stringResource(R.string.home_all_feature),
                    style = FDS.Typography.interactionSmallButton,
                    color = FDS.Colors.tabTextActiveInline,
                )
                Icon(
                    modifier = Modifier
                        .size(FDS.Sizer.Padding.padding24)
                        .dismissRippleClickable {
                            onClickAllFeature()
                        },
                    painter = painterResource(R.drawable.ic_home_more),
                    contentDescription = "",
                    tint = FDS.Colors.tabTextActiveInline,
                )
            }

            if (uiModel.isLoadingRecommended) {
                ShimmerLoading()
                ShimmerLoading()
            } else {
                if (uiModel.listSuggestion.isNullOrEmpty()) {
                    FoundationText(
                        text = stringResource(R.string.home_error_balance),
                        style = FDS.Typography.bodyB2Emphasized,
                    )
                } else {
                    uiModel.listSuggestion.forEachIndexed { index, item ->
                        MyRequestItem(
                            itemData = item,
                            isHavePadding = false,
                            onClickItem = {
                                onClickItem(it)
                            },
                        )
                    }
                }
            }
        }

        StrokeLine()

        Column(
            modifier = Modifier
                .padding(horizontal = FDS.Sizer.Padding.padding24)
                .padding(bottom = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
        ) {
            LoadingText(
                title = stringResource(R.string.home_recommended_request),
                isLoading = uiModel.isLoading,
                isEnableLoading = uiModel.isEnableLoading,
                onClickLoading = onClickLoading,
                updatedTime = uiModel.updatedTime,
            )

            if (uiModel.isLoading) {
                ShimmerLoading()
            } else {
                val isEmptyRequest =
                    uiModel.approveRequest.isEmpty() && uiModel.rejectRequest.isEmpty() && uiModel.pendingRequest.isEmpty()
                if (isEmptyRequest) {
                    FoundationText(
                        text = stringResource(R.string.home_recommended_card_empty_request),
                        style = FDS.Typography.bodyB2Emphasized,
                    )
                } else {
                    ListRequest(
                        rejectRequest = uiModel.rejectRequest,
                        pendingRequest = uiModel.pendingRequest,
                        approveRequest = uiModel.pendingRequest,
                    )
                }
            }

            FooterInsight(
                guideText = stringResource(R.string.home_rcm_card_guide_see_all),
                buttonText = stringResource(R.string.home_see_all),
                buttonAction = onClickSeeAll,
            )
        }
    }
}

@Composable
private fun ListRequest(
    rejectRequest: String,
    pendingRequest: String,
    approveRequest: String,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding8,
                alignment = Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(R.drawable.ic_home_reject),
                contentDescription = "",
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding4),
            ) {
                FoundationText(
                    text = rejectRequest,
                    style = FDS.Typography.bodyB2Emphasized,
                )
                FoundationText(
                    text = stringResource(R.string.home_recommend_card_reject),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterPrimary,
                )
            }
        }

        Row(
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding8,
                alignment = Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(R.drawable.ic_home_pending),
                contentDescription = "",
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding4),
            ) {
                FoundationText(
                    text = pendingRequest,
                    style = FDS.Typography.bodyB2Emphasized,
                )
                FoundationText(
                    text = stringResource(R.string.home_recommended_card_pending),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterPrimary,
                )
            }
        }

        Row(
            horizontalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding8,
                alignment = Alignment.CenterHorizontally,
            ),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(R.drawable.ic_home_approve),
                contentDescription = "",
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding4),
            ) {
                FoundationText(
                    text = approveRequest,
                    style = FDS.Typography.bodyB2Emphasized,
                )
                FoundationText(
                    text = stringResource(R.string.home_recommended_card_approve),
                    style = FDS.Typography.captionCaptionL,
                    color = FDS.Colors.characterPrimary,
                )
            }
        }
    }
}