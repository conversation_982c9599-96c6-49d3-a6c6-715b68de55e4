package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.formatAsMoney
import com.vietinbank.core_common.extensions.maskEmail
import com.vietinbank.core_common.extensions.maskPhoneNumber
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.CountPendingParams
import com.vietinbank.core_domain.models.checker.GetTransactionListParams
import com.vietinbank.core_domain.models.checker.MobileConfigLastParams
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_domain.models.home.CheckUserEkycParams
import com.vietinbank.core_domain.models.home.CountTransGroupParams
import com.vietinbank.core_domain.models.home.DataRetail
import com.vietinbank.core_domain.models.home.DifferentData
import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_domain.models.home.GenerateOtpParam
import com.vietinbank.core_domain.models.home.GetBiometricFaceParams
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ImageUpdate
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.models.home.ListKeyParams
import com.vietinbank.core_domain.models.home.ListLatestParams
import com.vietinbank.core_domain.models.home.VerifyOtpEkycParam
import com.vietinbank.core_domain.models.home.toUserInfo
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home.CheckUserEkycEvent
import com.vietinbank.feature_home.ui.screen.home_new_ui.account_home.AccountType
import com.vietinbank.feature_home.ui.view.home_card.MyRequestUiState
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionUiModel
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionUiState
import com.vietinbank.feature_home.ui.view.home_card.RecommendedUiModelState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

@HiltViewModel
class HomePageViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(HomePageUiState())
    val uiState: StateFlow<HomePageUiState> = _uiState.asStateFlow()

    private val _sectionsHome = MutableStateFlow<List<HomeCardItem>>(emptyList())
    val sections: StateFlow<List<HomeCardItem>> = _sectionsHome.asStateFlow()

    private val formatTimeString = SimpleDateFormat(Tags.HOME_DATE_PATTERN, Locale.getDefault())

    private var timerRcm: Job? = null
    private var timerRequest: Job? = null

    private var retailData: String? = null
    private var nationData: String? = null

    private var isVerifySuccess: Boolean = false

    private var successCreateSmsTime: Long = 0L

    // Store full countPending response for all transaction types
    private var countTransactionList: List<SubTranTypeListDomain> = emptyList()

    // Current transaction being processed
    private var currentTransaction: SubTranTypeListDomain? = null

    // Store transaction list JSON for navigation
    private var transactionListJson: String = ""
    private var transactionListCountTransaction: String? = null

    fun getTransactionListJson(): String = transactionListJson

    fun getCountTransaction(): String? = transactionListCountTransaction

    fun getCountTransactionListJson(): String = Utils.g().provideGson().toJson(countTransactionList)

    fun getCurrentTransaction(): SubTranTypeListDomain? = currentTransaction

    init {
        _uiState.update {
            it.copy(
                userName = userProf.getFullName() ?: "",
                companyName = userProf.getCorpName() ?: " ",
                roleName = when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> R.string.account_card_company_name_maker
                    else -> R.string.account_card_company_name_checker
                },
                phoneNum = userProf.getPhoneNo().maskPhoneNumber(),
                email = userProf.getEmail().maskEmail(),
            )
        }

        setCardBaseOnRole()

        if (appConfig.isTimerConfig()) {
            getMobileConfigLast()
        }

        if (prefs.getBoolean(Tags.CHECKEKYC)) {
            checkUserEkyc()
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    private fun getBiometricFace(biometricFace: String) {
        launchJobSilent {
            val res = homeUseCase.getBiometricFace(
                GetBiometricFaceParams(
                    biometricId = biometricFace,
                    username = userProf.getUserName().toString(),
                    roleId = "",
                ),
            )
            printLog("ekyc getBiometricFace: $res")
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    viewModelScope.launch(Dispatchers.Default) {
                        val biometricImage = vmScope.async {
                            Base64.Default.decode(data.image ?: "")
                        }
                        UserInfoDataHolder.saveBiometricImage(
                            biometricImage.await(),
                        )
                    }
                },
            )
        }
    }

    private fun checkUserEkyc() {
        prefs.setBoolean(Tags.CHECKEKYC, false)
        launchJobSilent {
            val res = homeUseCase.checkUserEkyc(
                CheckUserEkycParams(
                    userProf.getUserName().toString(),
                ),
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    if (data.bioStat == "0" && data.statusAccountHolder == "1") {
                        sendEvent(HomePageEvent.ShowUpdateEkycDialog)
                    }
                    getBiometricFace(data.biometricFace ?: "")
                    retailData = data.retail
                    nationData = data.national
                },
            )
        }
    }

    // check ekyc cua user khi click "Dong y"
    fun checkEkycEvent() {
        val checkUserEkycEvent = when {
            retailData == "0" && nationData == "VN" -> CheckUserEkycEvent.NewEkyc
            retailData == "0" && nationData != "VN" -> CheckUserEkycEvent.ShowGuildToCounter
            retailData == "1" -> CheckUserEkycEvent.UpdateEkyc
            else -> CheckUserEkycEvent.Nothing
        }
        sendEvent(checkUserEkycEvent)
    }

    fun checkNational() {
        val checkUserEkycEvent = when {
            nationData == "VN" -> CheckUserEkycEvent.NewEkyc
            nationData != "VN" -> CheckUserEkycEvent.ShowGuildToCounter
            else -> CheckUserEkycEvent.Nothing
        }
        sendEvent(checkUserEkycEvent)
    }

    private var efastId: String? = null

    fun generateOtpRetail(isResend: Boolean = false) {
        launchJob {
            val res = homeUseCase.generateOtpRetail(
                GenerateOtpParam(
                    userProf.getUserName().toString(),
                    userProf.getPhoneNo().toString(),
                ),
            )
            handleResource(
                res,
                onSuccess = { data ->
                    isVerifySuccess = true
                    successCreateSmsTime = System.currentTimeMillis()
                    sendEvent(HomePageEvent.GenOtpEkycSuccess(isResend))
                },
                handleError = {
                    sendEvent(HomePageEvent.GenOtpEkycFail)
                },
            )
        }
    }

    fun verifyOtpEkycRetail(otp: String) {
        launchJob(showLoading = true) {
            val res = homeUseCase.verifyOtpRetail(
                VerifyOtpEkycParam(
                    username = userProf.getUserName().toString(),
                    efastId = efastId ?: "",
                    otp = otp,
                ),
            )
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    handleDataProcessing(
                        differentData = data.differentData,
                        imageUpdate = data.imageUpdate,
                        fileGTDTT = data.fileGTDTT?.file,
                        dataRetail = data.dataRetail,
                        isTypeIdCard = "05" == data.dataRetail?.idType,
                        isFromRegisterScreen = false,
                    )
                    sendEvent(HomePageEvent.VerifyOtpEkycSuccess)
                },
                onError = {
                    sendEvent(HomePageEvent.VerifyOtpEkycFail)
                },
            )
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    private suspend fun handleDataProcessing(
        differentData: DifferentData?,
        imageUpdate: List<ImageUpdate?>?,
        fileGTDTT: String?,
        dataRetail: DataRetail?,
        isTypeIdCard: Boolean,
        isFromRegisterScreen: Boolean = false,
    ) {
        val diff = differentData ?: DifferentData.empty()
        withContext(Dispatchers.Default) {
            val listImageDeferred = async {
                imageUpdate?.mapNotNull { image ->
                    image?.file?.let { Base64.Default.decode(it) }
                }?.toList() ?: emptyList()
            }
            UserInfoDataHolder.saveUserInfoByteArrayData(
                listImageDeferred.await(),
                Base64.Default.decode(fileGTDTT ?: ""),
            )
            UserInfoDataHolder.setFromRegisterScreen(isFromRegisterScreen)
        }
        UserInfoDataHolder.saveUserInfoData(
            newList = dataRetail?.toUserInfo(diff) ?: emptyList(),
            newTypeIdCard = isTypeIdCard,
        )
        UserInfoDataHolder.isCalculating
            .first { calculating -> !calculating }
    }

    fun updateVisibleBalance() {
        _uiState.update {
            it.copy(
                isVisibleBalance = false,
            )
        }
    }

    private fun setCardBaseOnRole() {
        // maker 0
        when (userProf.getRoleLevel()) {
            Tags.ROLE_MAKER -> {
                _sectionsHome.value = listOf(
                    HomeCardItem.RecommendedCard(
                        RecommendedUiModelState(),
                    ),
                    HomeCardItem.BannerCard,
                    HomeCardItem.RecentTransactionCard(RecentTransactionUiState.Loading),
                )
                countTransGroup()
                listLatest()
                listKey()
            }

            else -> {
                _sectionsHome.value = listOf(
                    HomeCardItem.MyRequestCard(
                        MyRequestUiState.Loading,
                    ),
                    HomeCardItem.BannerCard,
                )
                countPending()
            }
        }
    }

    fun onAction(action: HomePageAction) {
        when (action) {
            HomePageAction.OnClickExploreButton -> {}
            is HomePageAction.OnTabSelected -> {
                when (action.index) {
                    1 -> {
                        sendEvent(HomePageEvent.NavigateToAccount)
                    }

                    2 -> sendEvent(HomePageEvent.NavigateToExplore)
                    else -> {}
                }
            }

            HomePageAction.OnClickVisibleButton -> {
                if (_uiState.value.isVisibleBalance) {
                    _uiState.update {
                        it.copy(
                            isVisibleBalance = !it.isVisibleBalance,
                        )
                    }
                } else {
                    homeAccountList()
                }
            }

            HomePageAction.OnClickAvatar -> {
                sendEvent(HomePageEvent.NavigateToSetting)
            }

            HomePageAction.OnClickNotification -> {
                sendEvent(HomePageEvent.NavigateToOttScreen)
            }

            HomePageAction.OnClickSearch -> {
                sendEvent(HomePageEvent.NotYetImplement)
            }

            HomePageAction.OnClickLoadingMyRequest -> {
                countPending()
            }

            HomePageAction.NotYetImplemented -> {
                sendEvent(HomePageEvent.NotYetImplement)
            }

            HomePageAction.OnClickLoadingRcmCard -> {
                countTransGroup()
            }

            HomePageAction.ClickShortcut -> {
                if (_uiState.value.isShowShortcut) {
                    _uiState.update {
                        it.copy(
                            isShowShortcut = false,
                        )
                    }
                } else {
                    getFavouriteList()
                }
            }

            HomePageAction.NavigateCt -> sendEvent(HomePageEvent.NavigateToCt)
            HomePageAction.ClickEditShortcut -> {
                sendEvent(HomePageEvent.NavigateToEditShortcut)
                _uiState.update {
                    it.copy(
                        isShowShortcut = false,
                    )
                }
            }

            HomePageAction.OnClickAllTransaction -> {
                sendEvent(HomePageEvent.NavigateToManager)
            }

            is HomePageAction.OnCLickItem -> {
                when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> {
                        when (action.id) {
                            Tags.TYPE_GROUP_TRANSFER.trim().lowercase() -> {
                                sendEvent(HomePageEvent.NavigateToCt)
                            }
                        }
                    }

                    else -> {
                        // Find the corresponding transaction data by functionId
                        val transaction =
                            countTransactionList.firstOrNull { it.functionId == action.id }
                        if (transaction != null) {
                            // Call getTransactionList for any item with valid transaction data
                            getTransactionListForItem(transaction)
                        }
                    }
                }
            }

            is HomePageAction.OnClickAllFeature -> {
                sendEvent(HomePageEvent.OnClickAllFeature)
            }

            is HomePageAction.ClickMyRequestItem -> {
                when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> {
                        when (action.functionItemData.functionId) {
                            "rp" -> { // Doan nay dang conflig voi TYPE_GROUP_RESET_PASSWORD can confirm lai
                                // Quản lý Giao dịch
                            }
                            Tags.TYPE_GROUP_SETTING.trim().lowercase() -> {
                                // Cài đặt
                            }
                            Tags.TYPE_GROUP_TRANSFER.trim().lowercase() -> {
                                // Chuyển tiền
                                sendEvent(HomePageEvent.NavigateToCt)
                            }
                            Tags.TYPE_GROUP_LOOKUP.trim().lowercase() -> {
                                // Tra soát
                                sendEvent(HomePageEvent.NavigateToTracePayment)
                            }
                            Tags.TYPE_GROUP_TRANSFER_QR.trim().lowercase() -> {
                                // Quét QR
                            }
                            Tags.TYPE_GROUP_PAYMENT.trim().lowercase() -> {
                                // Lệnh chi
                                sendEvent(HomePageEvent.NavigateToPaymentOrder)
                            }
                        }
                    }
                    else -> {
                        // checker feature hereeeeeee
                    }
                }
            }
        }
    }

    private fun getTransactionListForItem(transaction: SubTranTypeListDomain) {
        // Store current transaction for navigation
        currentTransaction = transaction
        var tranType = transaction.tranType
        // neu co subTranTypeList => lấy tranType, servicetype, groupType của item đầu tiên subTranTypeList
        if (!transaction.subTranTypeList.isNullOrEmpty()) {
            tranType = transaction.subTranTypeList?.firstOrNull()?.tranType.orEmpty()
        }
        launchJob(showLoading = true) {
            val res = homeUseCase.getTranList(
                GetTransactionListParams(
                    queryType = Tags.QUERY_TYPE_PENDING,
                    cifNo = userProf.getCifNo().toString(),
                    username = userProf.getUserName().toString(),
                    groupType = getSafeGroupType(tranType, transaction.groupType),
                    serviceType = transaction.servicetype,
                    tranType = getSafeTranType(tranType),
                    pageNum = "0",
                    pageSize = "15",
                    orderByAmount = "0",
                    orderByApproveDate = "-1",
                ),
            )
            handleResource(res) { data ->
                // Store transaction list as JSON for navigation
                transactionListJson = Utils.g().provideGson().toJson(data.transactions)
                // Store the total count from API response
                transactionListCountTransaction = data.countTransaction
                printLog("HomePageViewModel: API returned countTransaction = ${data.countTransaction}, transactions.size = ${data.transactions?.size}")
                // Navigate to approval list with transaction data
                sendEvent(HomePageEvent.NavigateToApprovalList)
            }
        }
    }

    private fun getSafeGroupType(tranType: String?, groupType: String?): String? {
        return if (tranType == "fx" || tranType == "fxt") {
            ""
        } else {
            groupType
        }
    }

    private fun getSafeTranType(tranType: String?): String? {
        return if (tranType == "tp") {
            ""
        } else {
            tranType
        }
    }

    fun clearSession() {
        sessionManager.clearSession()
//        transferCacheManager.clearTransferALL()
        ottRegistrationRepository.clearAllOttCache()
    }

//    override fun onDisplayErrorMessage(exception: AppException) {
//        if (exception is AppException.ApiException) {
//            val handled = when (exception.requestPath) {
//                Constants.MB_COUNT_PENDING -> {
//                    val res = Utils.g().provideGson()
//                        .fromJson(exception.rawResponseJson, CountPendingDomain::class.java)
//                    if (res.status?.code != Tags.SESSION_EXPIRED) {
//                        val currentTime = formatTimeString.format(Calendar.getInstance().time)
//                        updateMyRequestCardState(
//                            MyRequestUiState.LoadingFail(
//                                currentTime,
//                            ),
//                        )
//                        true
//                    } else {
//                        false
//                    }
//                }
//
//                Constants.MB_LIST_LATEST -> {
//                    val res = Utils.g().provideGson()
//                        .fromJson(exception.rawResponseJson, ListLatestDomain::class.java)
//                    if (res.status?.code != Tags.SESSION_EXPIRED) {
//                        _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
//                            card.copy(state = RecentTransactionUiState.LoadingFail)
//                        }
//                        true
//                    } else {
//                        false
//                    }
//                }
//
//                Constants.MB_LIST_KEY -> {
//                    val res = Utils.g().provideGson()
//                        .fromJson(exception.rawResponseJson, ListKeyDomain::class.java)
//                    if (res.status?.code != Tags.SESSION_EXPIRED) {
//                        _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
//                            card.copy(
//                                state = card.state.copy(
//                                    isLoadingRecommended = false,
//                                    listSuggestion = listRcm,
//                                ),
//                            )
//                        }
//                        true
//                    } else {
//                        false
//                    }
//                }
//
//                Constants.MB_HOME_ACCOUNT_LIST -> {
//                    val res = Utils.g().provideGson()
//                        .fromJson(exception.rawResponseJson, HomeAccountListDomain::class.java)
//                    if (res.status?.code != Tags.SESSION_EXPIRED) {
//                        _uiState.update {
//                            it.copy(
//                                totalBalance = null,
//                                isBalanceLoading = false,
//                                isVisibleBalance = true,
//                            )
//                        }
//                        true
//                    } else {
//                        false
//                    }
//                }
//
//                else -> false
//            }
//
//            if (!handled) super.onDisplayErrorMessage(exception)
//        } else {
//            super.onDisplayErrorMessage(exception)
//        }
//    }

    private fun listFavoriteFunction() {
        launchJob(showLoading = true) {
            val res = homeUseCase.listFavoriteFunction(
                ListFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                    packageId = userProf.getFinancialPackage(),
                ),
            )
            handleResource(res) { data ->
                val func = data.data ?: listOf()
                val newList = func.map {
                    FunctionItemData(
                        iconRes = R.drawable.ic_home_transfer,
                        functionName = it?.functionName ?: "",
                        functionId = it?.functionId ?: "",
                        groupName = it?.groupName ?: "",
                        groupId = it?.groupId ?: "",
                        orderNumber = it?.orderNumber?.toInt() ?: 0,
                        valueRes = null,
                    )
                }.sortedBy {
                    it.orderNumber
                }
                prefs.setString(Tags.LIST_FAVOURITE, Utils.g().provideGson().toJson(newList))
                _uiState.update {
                    it.copy(
                        isShowShortcut = true,
                        listFuncShortcut = newList,
                    )
                }
            }
        }
    }

    private fun getFavouriteList() {
        val data = prefs.getString(Tags.LIST_FAVOURITE)
        val type = object : TypeToken<List<FunctionItemData>>() {}.type
        if (!data.isNullOrEmpty()) {
            val list: List<FunctionItemData> = Utils.g().provideGson().fromJson(data, type)
            val listFav = list.map {
                it.copy(
                    iconRes = R.drawable.ic_home_transfer,
                    functionName = it.functionName ?: "",
                    functionId = it.functionId ?: "",
                    groupName = it.groupName ?: "",
                    groupId = it.groupId ?: "",
                    orderNumber = it.orderNumber,
                )
            }
            _uiState.update {
                it.copy(
                    isShowShortcut = true,
                    listFuncShortcut = listFav,
                )
            }
        } else {
            listFavoriteFunction()
        }
    }

    // MyRequestCard
    private fun countPending() {
        launchJob(showLoading = false) {
            updateMyRequestCardState(MyRequestUiState.Loading)

            val res = homeUseCase.countPending(
                CountPendingParams(
                    cifNo = userProf.getCifNo().orEmpty(),
                    userName = userProf.getUserName().orEmpty(),
                    role = userProf.getRoleId() ?: "",
                ),
            )
            if (res is Resource.Error) {
                val currentTime = formatTimeString.format(Calendar.getInstance().time)
                updateMyRequestCardState(
                    MyRequestUiState.Empty(
                        currentTime,
                        isEnableLoading = false,
                    ),
                )
            }
            timerRequest?.cancel()
            timerRequest = startCountdown(
                action = {
                    updateMyRequestEnableLoading(true)
                },
                totalMillis = 60 * 1000L,
            )
            handleResource(
                res,
                onSuccess = { data ->
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)

                    val countList = data.countTransactionList.orEmpty()

                    if (countList.isEmpty()) {
                        updateMyRequestCardState(
                            MyRequestUiState.Empty(
                                currentTime,
                                isEnableLoading = false,
                            ),
                        )
                        return@handleResource
                    }
                    // Store full countTransactionList for all transaction types
                    countTransactionList = data.countTransactionList ?: emptyList()

                    val newList = countList.take(3).map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it.servicetypename.orEmpty(),
                            valueRes = it.count_transaction,
                            functionId = it.functionId,
                        )
                    }

                    updateMyRequestCardState(
                        MyRequestUiState.Success(
                            items = newList,
                            totalTransaction = data.total_count.orEmpty(),
                            isEnableLoading = false,
                            updatedTime = currentTime,
                        ),
                    )
                },
                handleError = {
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)
                    updateMyRequestCardState(
                        MyRequestUiState.LoadingFail(
                            currentTime,
                            isEnableLoading = false,
                        ),
                    )
                },
            )
        }
    }

    private fun updateMyRequestCardState(newState: MyRequestUiState) {
        _sectionsHome.updateCard<HomeCardItem.MyRequestCard> { card ->
            card.copy(state = newState)
        }
    }

    private fun updateMyRequestEnableLoading(enable: Boolean) {
        _sectionsHome.updateCard<HomeCardItem.MyRequestCard> { card ->
            val newState = when (val state = card.state) {
                is MyRequestUiState.Empty -> state.copy(isEnableLoading = enable)
                is MyRequestUiState.Success -> state.copy(isEnableLoading = enable)
                is MyRequestUiState.Loading -> state
                is MyRequestUiState.LoadingFail -> state.copy(isEnableLoading = enable)
            }
            card.copy(state = newState)
        }
    }

    private inline fun <reified T : HomeCardItem> MutableStateFlow<List<HomeCardItem>>.updateCard(
        crossinline transform: (T) -> HomeCardItem,
    ) {
        this.update { list ->
            list.map { item ->
                if (item is T) transform(item) else item
            }
        }
    }

    private fun countTransGroup() {
        launchJob(showLoading = false) {
            // set loading true
            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                card.copy(
                    state = card.state.copy(
                        isLoading = true,
                        isEnableLoading = false,
                    ),
                )
            }

            val res = homeUseCase.countTransGroup(
                CountTransGroupParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    type = Tags.TYPE_COUNT_TRANS_GROUP,
                ),
            )

            handleResource(
                res,
                onSuccess = { data ->
                    val currentTime = formatTimeString.format(Calendar.getInstance().time)
                    // delay for smooth skeleton
                    delay(500)
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        // parse dữ liệu từ backend
                        val pending =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_PENDING }?.count
                                ?: "0"
                        val approved =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_APPROVED }?.count
                                ?: "0"
                        val rejected =
                            data.transGroup?.firstOrNull { it.code == Tags.HOME_REJECTED }?.count
                                ?: "0"

                        card.copy(
                            state = card.state.copy(
                                isLoading = false,
                                isEnableLoading = false,
                                updatedTime = currentTime,
                                pendingRequest = pending,
                                approveRequest = approved,
                                rejectRequest = rejected,
                            ),
                        )
                    }

                    // setup timer
                    timerRcm?.cancel()
                    timerRcm = startCountdown(
                        action = {
                            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                                card.copy(
                                    state = card.state.copy(isEnableLoading = true),
                                )
                            }
                        },
                    )
                },
                handleError = {
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                isLoading = false,
                                isEnableLoading = true,
                            ),
                        )
                    }
                },
            )
        }
    }

    private fun homeAccountList() {
        prefs.setBoolean(Tags.IS_RELOAD_ACCOUNT, true)
        launchJob(showLoading = false) {
            _uiState.update {
                it.copy(
                    isBalanceLoading = true,
                )
            }
            val res = homeUseCase.homeAccountList(
                HomeAccountListParams(
                    username = userProf.getUserName() ?: "",
                    isBalance = Tags.IS_BALANCE,
                    accountType = AccountType.Payment.code,
                ),
            )
            handleResource(
                res,
                onSuccess = { data ->
                    _uiState.update {
                        it.copy(
                            totalBalance = data.assetAmount?.formatAsMoney() ?: "",
                            isBalanceLoading = false,
                            isVisibleBalance = true,
                        )
                    }
                },
                handleError = {
                    _uiState.update {
                        it.copy(
                            totalBalance = null,
                            isBalanceLoading = false,
                            isVisibleBalance = true,
                        )
                    }
                },
            )
        }
    }

    // RecentCard
    private fun listLatest() {
        launchJob(showLoading = false) {
            _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                card.copy(state = RecentTransactionUiState.Loading)
            }
            val res = homeUseCase.listLatest(
                ListLatestParams(
                    username = userProf.getUserName() ?: "",
                    role = userProf.getRoleId() ?: "",
                    status = Tags.HOME_LIST_LATEST_PARAMS_STATUS,
                    pageSize = Tags.HOME_LIST_LATEST_PARAMS_PAGE_SIZE,
                    pageNum = Tags.HOME_LIST_LATEST_PARAMS_PAGE_NUM,
                ),
            )
            handleResource(
                res,
                onSuccess = { data ->
                    delay(500)
                    val transactionInfoList = data.transactionInfoList.orEmpty()

                    if (transactionInfoList.isEmpty()) {
                        _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                            card.copy(state = RecentTransactionUiState.Empty)
                        }
                        return@handleResource
                    }

                    val newList = transactionInfoList.take(3).map {
                        RecentTransactionUiModel(
                            logoBank = it.iconUrl ?: "",
                            title = it.tranTypeName ?: "",
                            content = it.toAccountName ?: "",
                            secondaryContent = (
                                it.amount?.formatAsMoney()
                                    ?: ""
                                ) + " ${it.currency}",
                        )
                    }

                    _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                        card.copy(
                            state = RecentTransactionUiState.Success(newList),
                        )
                    }
                },
                handleError = {
                    _sectionsHome.updateCard<HomeCardItem.RecentTransactionCard> { card ->
                        card.copy(state = RecentTransactionUiState.LoadingFail)
                    }
                },
            )
        }
    }

    private var listRcm: List<FunctionItemData>? = null

    // recommended
    private fun listKey() {
        launchJob(showLoading = false) {
            _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                card.copy(state = card.state.copy(isLoadingRecommended = true))
            }
            val res = homeUseCase.listKey(
                ListKeyParams(
                    username = userProf.getUserName() ?: "",
                    type = Tags.HOME_LIST_KEY_PARAM_R,
                    role = userProf.getRoleId() ?: "",
                ),
            )
            handleResource(
                res,
                onSuccess = { data ->
                    delay(500)
                    val list = data.data.orEmpty()
                    listRcm = list.take(3).map {
                        FunctionItemData(
                            iconRes = R.drawable.ic_home_transfer,
                            functionName = it?.key ?: "",
                            functionId = it?.functionId ?: "",
                            valueRes = null,
                        )
                    }
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                listSuggestion = listRcm,
                                isLoadingRecommended = false,
                            ),
                        )
                    }
                },
                handleError = {
                    _sectionsHome.updateCard<HomeCardItem.RecommendedCard> { card ->
                        card.copy(
                            state = card.state.copy(
                                isLoadingRecommended = false,
                                listSuggestion = listRcm,
                            ),
                        )
                    }
                },
            )
        }
    }

    private fun startCountdown(action: () -> Unit, totalMillis: Long = 5 * 60 * 1000L): Job {
        return viewModelScope.launch {
            var remaining = totalMillis
            while (remaining > 0) {
                delay(1000L)
                remaining -= 1000L
            }
            // Khi hết giờ
            action()
        }
    }

    // Call API check hết phiên client
    private fun getMobileConfigLast() {
        launchJobSilent {
            val res = homeUseCase.mobileConfigLast(
                MobileConfigLastParams(
                    username = userProf.getUserName() ?: "",
                    lastModified = appConfig.getTotalLastModified()
                        .toString(),
                ),
            )
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    // set lại giá trị để chỉ gọi 1 lần API khi login vào
                    appConfig.setTimerConfig(false)
                    // Nếu configList rỗng thì giữ nguyên như cũ không thay đổi
                    if (!data.configList.isNullOrEmpty()) {
                        appConfig.updateTotalLastModified(data.totalLastModified!!)
                        // Lưu lại list configList để sau dùng
                        prefs.setString(
                            Tags.TIMECONFINGLIST,
                            Utils.g().provideGson().toJson(data.configList),
                        )
                    }
                },
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        timerRcm?.cancel()
        timerRequest?.cancel()
    }
}
