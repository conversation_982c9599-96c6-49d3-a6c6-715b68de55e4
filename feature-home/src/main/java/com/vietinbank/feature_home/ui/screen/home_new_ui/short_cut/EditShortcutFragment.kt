package com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class EditShortcutFragment : BaseFragment<EditShortcutViewModel>() {
    override val viewModel: EditShortcutViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                ShortcutEvent.PopBackStack -> {
                    appNavigator.popBackStack()
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        val listFunc by viewModel.listFunc
        EditShortcutScreen(
            listFavoriteFunc = viewModel.favouriteItems,
            listFunc = listFunc,
            listSearch = viewModel.listSearch,
            onAction = viewModel::onAction,
            uiState = uiState,
        )
    }
}
