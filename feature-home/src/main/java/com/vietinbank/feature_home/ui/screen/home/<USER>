package com.vietinbank.feature_home.ui.screen.home

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.ListFunctionId
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.BottomNavigationActions
import com.vietinbank.core_domain.models.home.HomeFeatureActions
import com.vietinbank.core_domain.models.home.HomeOtherActions
import com.vietinbank.core_domain.models.home.TuNA5Actions
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.UpdateEkycDialog
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class HomeFragment : BaseFragment<HomeViewModel>() {

    override val viewModel: HomeViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var softManager: ISoftManager

    @Composable
    override fun ComposeScreen() {
        // Collect state from ViewModel
        val uiState by viewModel.uiState.collectAsState()

        val featureActions = HomeFeatureActions(
            onBhxhClick = {
                /* Chức năng bảo hiểm xã hội */
            },
            onTransferClick = {
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_M_TRANSFER_CT) {
                    activity?.viewModelStore?.clear()
//                    appNavigator.goToViewPagerTransferFragment(bundleOf())
                    appNavigator.goToMakerTransfer(bundleOf())
                }
            },
            onTaxClick = { /* Chức năng nộp thuế */ },
            onForexClick = { /* Chức năng ngoại tệ */ },
            onLatestTransactionClick = { /* Xem giao dịch mới nhất */ },
            onOfferClick = { /* Xem ưu đãi */ },
            onInquiryClick = {
                /* Tra soat */
                viewModel.handleForceUpdateOrNavigate(ListFunctionId.FORCE_UPDATE_M_TRANSFER_TR) {
                    appNavigator.goToTracePaymentFragment(bundleOf())
                }
            },
            onPaymentOrderClick = {
                /* Lenh chi */
                activity?.viewModelStore?.clear()
                appNavigator.goToPaymentOrderFragment()
            },
        )

        val bottomNavActions = BottomNavigationActions(
            onHomeClick = { },
            onNotificationClick = {
                appNavigator.goToOttDashboard()
            },
            onReportClick = {
                appNavigator.goToDashBoardReportFragment(bundleOf())
            },
            onMarketClick = {
            },
        )

        val otherActions = HomeOtherActions(
            onNotifyClick = { },
            onApprovalItemClick = { item ->
                // Xử lý khi click vào item phê duyệt
            },
            onTransactionItemClick = { transaction ->
                viewModel.getTransactionList(transaction)
            },
        )

        AppTheme {
            HomeScreen(
                featureActions = featureActions,
                bottomNavActions = bottomNavActions,
                otherActions = otherActions,
                uiState = uiState,
                tuNA5Actions = TuNA5Actions(
                    onSoftClick = {
                        if (!softManager.isAllowSoft()) {
                            showNoticeDialog("Quý khách chưa đăng ký phương thức xác thực giao dịch. Quý khách vui lòng liên hệ CN/PGD VietinBank gần nhất để được hỗ trợ.")
                        } else if (softManager.isUserActive) {
                            // user da duoc kich hoat tren thiet bi
                            appNavigator.gotoHomeSoft()
                        } else if (softManager.isAppActive) {
                            // device da kich hoat soft nhung user thi chua
                            appNavigator.goToEnterPIN(VSoftConstants.VtpOTPFlowType.ACTIVE.name)
                        } else {
                            // device chua kich hoat soft
                            appNavigator.gotoActivSoft()
                        }
                    },
                    onKeypassClick = {
                        appNavigator.gotoKeypassMain()
                    },
                    onManageTransaction = {
                        appNavigator.gotoFilterTransaction()
                    },
                    onInquiryClick = {
                        appNavigator.goToInquiryAccount("DDA")
                    },
                    onSmartCAClick = {
                        appNavigator.goToListSmartCA()
                    },
                    onRegiterOttClick = {
                        appNavigator.goToDashboardFeatureOTT(flowSetting = "Y")
                    },
                    onRegisterEKYC = { appNavigator.goToRegisterEkycScreen() },

                ),
                onClickAvatar = {
                    appNavigator.goToSetting()
                },
            )
        }
    }

//    override fun onBackPressed(): Boolean {
//        showConfirmDialog(
//            message = "Bạn có chắc muốn thoát màn hình đăng nhập?",
//            positiveAction = {
//                inactivityManager.stopMonitoring()
//                // user confirm => pop
//                viewModel.clearSession()
//                viewModel.transferCacheManager.clearTransferALL()
//                viewModel.ottRegistrationRepository.clearAllOttCache()
//                appNavigator.goToHomePreLoginAndPopAll()
//            },
//            negativeAction = {
//                // user cancel => do nothing
//            },
//        )
//        return true
//    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (viewModel.isChecker()) {
            viewModel.countPending()
        }
        if (viewModel.checkShowPopupEkyc()) {
            viewModel.checkUserEkyc()
        }
        initObserver()

        // chi load 1 lan duy nhat
        if (viewModel.isChecker() && !viewModel.isKeepLoadConfigSoft()) {
            softManager.loadSoftStatus()
            viewModel.setKeepLoadConfigSoft(true)
        }
        viewModel.listReg()

        // chỉ call 1 lần sau khi login vào
        if (viewModel.isTimerConfig()) {
            viewModel.getMobileConfigLast()
        }

        // Sync OTT messages đã được chuyển vào HomeViewModel.init
        // để đảm bảo chỉ sync 1 lần khi OTT initialized
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                // Collect SharedFlow
                launch {
                    viewModel.getTransactionList.collect { data ->
                        when (data) {
                            is Resource.Success -> {
                                viewModel.handleNavigateWithForceUpdate(viewModel.currentTransType.toString()) {
                                    appNavigator.goToApprovalList(
                                        transaction = Utils.g().provideGson()
                                            .toJson(data.data.transactions),
                                        tranType = viewModel.currentTransType ?: "",
                                        groupType = viewModel.currentGroupType ?: "",
                                        serviceType = viewModel.currentServiceType ?: "",
                                        isBatchFileMode = false,
                                    )
                                }
                            }

                            else -> {}
                        }
                    }
                }

                launch {
                    viewModel.checkUserEkyc.collect { values ->
                        if (values) showUpdateEkycDialog()
                    }
                }

                launch {
                    viewModel.checkEkycEvent.collect { event ->
                        when (event) {
                            CheckUserEkycEvent.NewEkyc -> { appNavigator.goToRegisterEkycScreen() }
                            CheckUserEkycEvent.ShowGuildToCounter -> showGuideToCounterDialog()
                            CheckUserEkycEvent.UpdateEkyc -> appNavigator.gotoUpdateEkyc()
                            CheckUserEkycEvent.Nothing -> {}
                        }
                    }
                }

                launch {
                    viewModel.listRegState.observe(viewLifecycleOwner) { data ->
                        showConfirmDialog(
                            message = "VietinBank cung cấp dịch vụ gửi thông báo biến động số dư trên ứng dụng eFAST với ưu điểm vượt trội và tiết kiệm hơn so với nhận thông báo BĐSD qua SMS. Vui lòng nhấn “Cài đặt” sử dụng dịch vụ",
                            positiveButtonText = "Cài đặt",
                            negativeButtonText = "Đóng",
                            positiveAction = {
                                appNavigator.goToRegisterOTT(tag = "REG_OTT", flowSetting = "N")
                            },
                            negativeAction = {
                                dismissNoticeDialog()
                            },
                        )
                    }
                }
            }
        }
    }

    private fun showUpdateEkycDialog() {
        val dialog = UpdateEkycDialog.Companion.newInstance(
            title = "Thông báo",
            message = "Theo quy định của Ngân hàng Nhà nước Việt Nam, người đại diện hợp pháp của doanh nghiệp cần thu thập sinh trắc học để tiếp tục sử dụng tài khoản. Xin vui lòng cho phép VietinBank thực hiện thu thập khuôn mặt, giấy tờ tùy thân để xác thực thông tin khách hàng. Trân trọng cảm ơn Quý khách!",
            buttonText = "Đăng ký sinh trắc học",
            showImage = true,
        )
        dialog.setOnConfirmListener {
            viewModel.checkEkycEvent()
            dialog.dismiss()
        }
        dialog.show(childFragmentManager, "updateEkycDialog")
    }

    private fun showGuideToCounterDialog() {
        val dialog = UpdateEkycDialog.Companion.newInstance(
            title = "Thông báo",
            message = "Quý khách vui lòng thực hiện thu thập và đối chiếu sinh trắc học tai các Chi nhánh/ Phòng giao dịch VietinBank gần nhất",
            buttonText = "Đóng",
            showImage = false,
        )
        dialog.setOnConfirmListener {
            dialog.dismiss()
        }
        dialog.show(childFragmentManager, "GuideToCounterDialog")
    }
}
