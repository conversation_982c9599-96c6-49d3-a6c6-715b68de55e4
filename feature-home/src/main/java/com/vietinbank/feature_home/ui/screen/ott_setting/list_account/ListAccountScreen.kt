package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.formatAsMoney
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ListAccountScreen(
    uiState: ListAccountUiState = ListAccountUiState(),
    onAction: (ListAccountAction) -> Unit = {},
    onBackClick: () -> Unit = {},
    listItem: List<AccountUiModel>,
    listSearch: List<AccountUiModel>,
) {
    var showFilter by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()

    val itemsToShow = if (uiState.search.isNotEmpty() || uiState.currencySelected.isNotEmpty()) {
        listSearch
    } else {
        listItem
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        FoundationAppBar(
            modifier = Modifier.padding(FDS.Sizer.Padding.padding8),
            title = stringResource(uiState.title),
            titleStyle = FDS.Typography.headingH2,
            isLightIcon = false,
            onNavigationClick = {
                onBackClick()
            },
            isCustomActionUseSafeClick = false,
        )
        Row(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
        ) {
            FoundationText(
                text = stringResource(uiState.description),
                textAlign = TextAlign.Left,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterTertiary,
            )
        }

        Column(
            Modifier
                .weight(1f)
                .padding(top = FDS.Sizer.Padding.padding16)
                .clip(
                    RoundedCornerShape(
                        topStart = FDS.Sizer.Padding.padding32,
                        topEnd = FDS.Sizer.Padding.padding32,
                    ),
                )
                .background(FDS.Colors.white),
        ) {
            Column(
                Modifier
                    .padding(horizontal = FDS.Sizer.Padding.padding24)
                    .padding(top = FDS.Sizer.Padding.padding24),
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Box(
                        Modifier
                            .weight(1f)
                            .padding(bottom = FDS.Sizer.Padding.padding8)
                            .background(
                                FDS.Colors.homeBackgroundIcon,
                                RoundedCornerShape(FDS.Sizer.Padding.padding32),
                            )
                            .then(
                                if (uiState.search.isEmpty()) {
                                    Modifier
                                } else {
                                    Modifier
                                        .border(
                                            1.dp,
                                            FDS.Colors.borderGradientStart,
                                            RoundedCornerShape(FDS.Sizer.Padding.padding32),
                                        )
                                },
                            ),
                        contentAlignment = Alignment.Center,
                    ) {
                        FoundationFieldType(
                            value = uiState.search,
                            onValueChange = {
                                onAction(ListAccountAction.SearchItem(it))
                            },
                            placeholder = stringResource(R.string.list_account_seacrch),
                            isHaveClearIcon = true,
                            clearValue = {
                                onAction(ListAccountAction.SearchItem(""))
                            },
                            leadingIcon = {
                                Icon(
                                    painter = painterResource(
                                        id = R.drawable.ic_home_search,
                                    ),
                                    contentDescription = "",
                                    tint = if (uiState.search.isEmpty()) {
                                        FDS.Colors.characterTertiary
                                    } else {
                                        FDS.Colors.tabTextActiveInline
                                    },
                                    modifier = Modifier
                                        .size(FDS.Sizer.Icon.icon16),
                                )
                            },
                        )
                    }

                    FoundationText(
                        modifier = Modifier
                            .padding(start = FDS.Sizer.Padding.padding16)
                            .dismissRippleClickable {
                                showFilter = true
                            },
                        text = stringResource(R.string.list_account_filter),
                        style = FDS.Typography.interactionLink,
                        color = FDS.Colors.characterHighlighted,
                    )
                    Image(
                        // update icon filter
                        painter = painterResource(R.drawable.ic_list_acc_filter),
                        contentDescription = "",
                        modifier = Modifier
                            .size(FDS.Sizer.Padding.padding24)
                            .dismissRippleClickable {
                                showFilter = true
                            },
                    )
                    if (uiState.currencySelected.isNotEmpty()) {
                        Box(
                            modifier = Modifier
                                .size(FoundationDesignSystem.Sizer.Padding.padding24)
                                .clip(CircleShape)
                                .background(FoundationDesignSystem.Colors.stateBadgeCounting),
                            contentAlignment = Alignment.Center,
                        ) {
                            FoundationText(
                                text = uiState.currencySelected.size.toString(),
                                color = Color.White,
                                style = FoundationDesignSystem.Typography.material3Typography.labelSmall,
                            )
                        }
                    }
                }
                Spacer(Modifier.size(FDS.Sizer.Padding.padding16))
                Row {
                    FoundationSelector(
                        boxType = SelectorType.Checkbox,
                        isSelected = uiState.selectAll,
                        isEnable = true,
                        onClick = {
                            onAction(ListAccountAction.SelectAll)
                        },
                        title = stringResource(R.string.list_account_selected_all, itemsToShow.size),
                    )
                    Spacer(Modifier.weight(1f))
                    FoundationText(
                        text = stringResource(
                            R.string.list_account_selected_account,
                            uiState.selectedAccount,
                        ),

                    )
                }
            }
            StrokeLine(Modifier.padding(vertical = FDS.Sizer.Padding.padding16))

            LazyColumn(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                items(itemsToShow) { account ->
                    Row(
                        Modifier
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationSelector(
                            boxType = SelectorType.Checkbox,
                            isSelected = account.isSelected,
                            onClick = {
                                onAction(ListAccountAction.SelectItem(account.domain.accountNumber ?: ""))
                            },
                        )
                        FoundationText(
                            text = stringResource(
                                R.string.list_account_item_value,
                                account.domain.accountNumber ?: "",
                                account.domain.currency ?: "",
                            ),
                        )
                    }
                    StrokeLine(Modifier.padding(vertical = FDS.Sizer.Padding.padding16))
                }
            }
            Column(
                Modifier
                    .fillMaxWidth()
                    .clip(
                        RoundedCornerShape(
                            topStart = FDS.Sizer.Padding.padding32,
                            topEnd = FDS.Sizer.Padding.padding32,
                        ),
                    )
                    .background(FDS.Colors.backgroundBgScreen)
                    .padding(top = FDS.Sizer.Padding.padding16)
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationSelector(
                        boxType = SelectorType.OutlinedCheckbox,
                        isSelected = uiState.confirmSelected,
                        onClick = {
                            onAction(ListAccountAction.OnClickConfirmTerm)
                        },
                    )
                    TermsAndConditionsText(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = FDS.Sizer.Padding.padding8),
                        onClick = {
                            // click link text
                        },
                    )
                }
                Spacer(Modifier.size(FDS.Sizer.Padding.padding16))
                Row {
                    Column(
                        Modifier.padding(end = FDS.Sizer.Padding.padding16),
                        verticalArrangement = Arrangement.Center,
                    ) {
                        FoundationText(
                            text = stringResource(uiState.feeDescription),
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.captionL,
                        )
                        Spacer(Modifier.size(FDS.Sizer.Padding.padding4))
                        Row {
                            FoundationText(
                                text = stringResource(
                                    R.string.details_screen_vnd,
                                    uiState.selectedFee.toString().formatAsMoney(),
                                ),
                                color = FDS.Colors.white,
                                style = FDS.Typography.bodyB2Emphasized,
                            )
                            FoundationText(
                                text = stringResource(R.string.list_account_selected_details),
                                style = FDS.Typography.bodyB2,
                                color = FDS.Colors.characterHighlightSoft,
                                textDecoration = TextDecoration.Underline,
                                modifier = Modifier
                                    .padding(start = FDS.Sizer.Padding.padding8)
                                    .dismissRippleClickable {
                                        onAction(ListAccountAction.ShowDetail)
                                    },
                            )
                        }
                    }
                    FoundationButton(
                        text = stringResource(R.string.list_account_selected_confirm),
                        onClick = {
                            onAction(ListAccountAction.OnCLickSignUp)
                        },
                        enabled = uiState.confirmSelected,
                        modifier = Modifier.weight(1f),

                    )
                }
            }
        }
    }
    CurrencyBottomSheet(
        visible = showFilter,
        currencies = uiState.listCurrency,
        selectedCurrencies = uiState.currencySelected,
        onDismiss = {
            scope.launch {
                delay(200) // đợi 200ms
                showFilter = false
            }
        },
        onPicked = {
            onAction(ListAccountAction.SelectCurrency(it))
        },
    )
}

@Composable
fun TermsAndConditionsText(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    val annotatedText = buildAnnotatedString {
        append(stringResource(R.string.list_account_screen_terms_1))

        withLink(
            LinkAnnotation.Clickable(
                tag = "",
                styles = TextLinkStyles(
                    style = SpanStyle(
                        color = FDS.Colors.characterHighlightSoft,
                        textDecoration = TextDecoration.Underline,
                    ),
                ),
                linkInteractionListener = { link ->
                    when (link) {
                        is LinkAnnotation.Clickable -> {
                            onClick()
                        }
                    }
                },
            ),
        ) {
            append(stringResource(R.string.list_account_screen_terms_2))
        }

        append(stringResource(R.string.list_account_screen_terms_3))
    }

    FoundationText(
        text = annotatedText,
        style = FDS.Typography.captionM,
        color = FDS.Colors.white,
        textAlign = TextAlign.Left,
        modifier = modifier,

    )
}
