package com.vietinbank.feature_home.ui.screen.setting

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.utils.eFastBackgroundLevel2
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home.UserInfoCard
import com.vietinbank.feature_home.ui.view.ItemViewType

@Composable
fun SettingScreen(
    uiState: SettingUiState,
    onAction: (SettingItemAction) -> Unit,
    goToTestOTP: () -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxSize()
            .eFastBackgroundLevel2()
            .systemBarsPadding(),
    ) {
        BaseAppBar(
            title = "VietinBank eFAST",
            onBackClick = {
                onAction(SettingItemAction.NavigateBack)
            },
            customActions = {
                Column(
                    modifier = Modifier
                        .size(height = 24.dp, width = 88.dp)
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .safeClickable {
                            onAction(SettingItemAction.OnClickLogout)
                        },
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    BaseText(
                        text = "Đăng xuất",
                        color = Color(0xFF062A46),
                        textSize = 12.sp,
                        fontCus = 2,
                    )
                }
            },
        )
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(top = 15.dp)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(15.dp),
        ) {
            UserInfoCard(
                fullName = uiState.fullName,
                modifier = Modifier
                    .fillMaxWidth(),
                onClickAvatar = {},
            )
            BaseText(
                text = "Update thông tin sinh trắc học",
                color = Color(0xFFD6F4FF),
            )

            ItemViewType(
                itemTitle = "Click to update sth",
                isShowDivideLine = false,
                shape = RoundedCornerShape(2.dp),
                onClickItem = { onAction(SettingItemAction.UpdateEkycAction) },
            )

            BaseText(
                text = "Cài đặt thông báo",
                color = Color(0xFFD6F4FF),
            )

            Column {
                ItemViewType(
                    itemIcon = R.drawable.ic_account_balance,
                    itemTitle = "Biến động số dư",
                    isShowDivideLine = true,
                    shape = RoundedCornerShape(topEnd = 2.dp, topStart = 2.dp),
                    onClickItem = { onAction(SettingItemAction.OttServiceAction) },
                )

                ItemViewType(
                    itemIcon = R.drawable.ic_account_balance,
                    itemTitle = "Trạng thái giao dịch",
                    isShowDivideLine = true,
                    shape = RoundedCornerShape(0.dp),
                    onClickItem = { onAction(SettingItemAction.TransactionStatusAction) },
                )

                ItemViewType(
                    itemIcon = R.drawable.ic_account_balance,
                    itemTitle = "Nhắc nợ vay",
                    isShowDivideLine = true,
                    shape = RoundedCornerShape(bottomEnd = 2.dp, bottomStart = 2.dp),
                    onClickItem = { onAction(SettingItemAction.LoanAccountAction) },
                )

                ItemViewType(
                    itemIcon = R.drawable.ic_account_balance,
                    itemTitle = "Luồng Soft OTP",
                    isShowDivideLine = true,
                    shape = RoundedCornerShape(bottomEnd = 2.dp, bottomStart = 2.dp),
                    onClickItem = goToTestOTP,
                )
            }
        }
    }
}
