package com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.CircularIconButton
import com.vietinbank.core_ui.components.CircularIconButtonSize
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.home_card.MyRequestItem
import com.vietinbank.feature_home.ui.view.paddingNavigationBar
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun ShortCutScreen(
    onClickShortcut: () -> Unit,
    onClickEdit: () -> Unit,
    onClickMyRequestItem: (FunctionItemData) -> Unit,
    list: List<FunctionItemData>,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(FDS.Colors.dialogBackground.copy(0.52f))
            .dismissRippleClickable {
                onClickShortcut()
            },
        contentAlignment = Alignment.BottomEnd,
    ) {
        Column(
            modifier = Modifier
                .paddingNavigationBar()
                .dismissRippleClickable(
                    enabled = false,
                    onClick = {},
                ),
            verticalArrangement = Arrangement.spacedBy(
                FDS.Sizer.Padding.padding16,
                Alignment.Bottom,
            ),
            horizontalAlignment = Alignment.End,
        ) {
            Column(
                modifier = Modifier
                    .width(FDS.Sizer.Padding.padding260)
                    .background(Color.White, RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .padding(vertical = FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
            ) {
                Row(
                    Modifier.padding(horizontal = FDS.Sizer.Padding.padding24)
                        .dismissRippleClickable {
                            onClickEdit()
                        },
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding12, Alignment.CenterHorizontally),
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_home_add_short_cut),
                        contentDescription = "",
                    )

                    FoundationText(
                        text = stringResource(R.string.home_add_short_cut),
                    )
                }

                StrokeLine()

                Column(
                    Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
                    verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
                ) {
                    list.reversed().forEach { item ->
                        MyRequestItem(
                            itemData = item,
                            isHavePadding = false,
                            onClickItem = { onClickMyRequestItem(item) },
                        )
                    }
                }
            }
            CircularIconButton(
                icon = painterResource(R.drawable.ic_home_short_cut),
                isLightIcon = false,
                onClick = onClickShortcut,
                size = CircularIconButtonSize.Medium,
            )
        }
    }
}