package com.vietinbank.feature_home.ui.screen.ott_setting

import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.constants.Tags.OTT_TAG_169
import com.vietinbank.core_common.constants.Tags.OTT_TAG_189
import com.vietinbank.feature_home.R

enum class OttServiceType(val codeType: String, val title: Int, val description: Int) {
    SWITCH_SERVICE(
        codeType = "SWITCH_SERVICE",
        title = R.string.ott_switch_service_title,
        description = R.string.ott_switch_service_description,
    ),
    SIGN_UP_SERVICE(
        codeType = "SIGN_UP_SERVICE",
        title = R.string.ott_sign_up_service_title,
        description = R.string.ott_sign_up_service_description,
    ),
    ;

    companion object {
        fun fromCodeType(codeType: String?): OttServiceType {
            return OttServiceType.entries.find { it.codeType == codeType } ?: SIGN_UP_SERVICE
        }
    }
}

enum class ServiceAccountType(val codeType: String, val title: Int?) {
    PAYMENT(codeType = "PAYMENT", title = R.string.service_account_payment_title),
    LOAN(codeType = "LOAN", title = R.string.service_account_loan_title),
    IDENTITY(codeType = "IDENTITY", title = R.string.service_account_identity_title),
    NONE(codeType = "", title = null),
    ;

    companion object {
        fun fromCodeType(codeType: String): ServiceAccountType {
            return ServiceAccountType.entries.find { it.codeType == codeType } ?: NONE
        }
    }
}

enum class NotificationType(val alertType: String, val title: Int?, val description: Int?) {
    Alert89(
        alertType = Tags.OTT_TAG_89,
        title = R.string.notification_title_alert_89,
        description = R.string.alert_type_89_description,
    ),
    Alert189(
        alertType = OTT_TAG_189,
        title = R.string.notification_title_alert_189,
        description = R.string.alert_type_189_description,
    ),
    Alert169(
        alertType = OTT_TAG_169,
        title = R.string.notification_title_alert_169,
        description = R.string.alert_type_169_description,
    ),
    NONE(
        alertType = "",
        title = null,
        description = null,
    ),
    ;

    companion object {
        fun fromAlertType(alertType: String): NotificationType {
            return entries.find { it.alertType == alertType } ?: NONE
        }
    }
}