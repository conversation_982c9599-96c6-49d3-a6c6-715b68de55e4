package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.ott_feature.CancelRecord
import com.vietinbank.core_domain.models.ott_feature.ConfirmVacctParams
import com.vietinbank.core_domain.models.ott_feature.OttRegisterRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut.normalize
import com.vietinbank.feature_home.ui.screen.ott_setting.NotificationType
import com.vietinbank.feature_home.ui.screen.ott_setting.OttServiceType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ListAccountViewModel @Inject constructor(
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(ListAccountUiState())
    val uiState: StateFlow<ListAccountUiState> = _uiState.asStateFlow()

    private var _listItem = mutableStateListOf<AccountUiModel>()
    val listItem: SnapshotStateList<AccountUiModel> = _listItem

    var filteredList: SnapshotStateList<AccountUiModel> = mutableStateListOf()

    fun onAction(action: ListAccountAction) {
        when (action) {
            ListAccountAction.OnClickConfirmTerm -> {
                _uiState.update {
                    it.copy(
                        confirmSelected = !it.confirmSelected,
                    )
                }
            }

            is ListAccountAction.SearchItem -> {
                _uiState.update { it.copy(search = action.search, selectAll = false) }
                filterList()
            }

            ListAccountAction.SelectAll -> {
                _uiState.update { it.copy(selectAll = !it.selectAll) }
                selectAllFilter()
                setSelectedAmount()
            }

            ListAccountAction.ShowDetail -> {
                setupDetailsData()
                sendEvent(ListAccountEvent.NavigateToDetails)
            }
            ListAccountAction.OnCLickSignUp -> {
                ottRegisterRequest()
            }

            is ListAccountAction.SelectItem -> {
                _uiState.update { it.copy(selectAll = false) }
                selectItem(action.accountNumber)
                filterList()
                setSelectedAmount()
            }

            is ListAccountAction.SelectCurrency -> {
                _uiState.update { it.copy(currencySelected = action.listCurrency, selectAll = false) }
                filterList()
            }
        }
    }

    fun setAccountsState(
        serviceType: String,
        data: String?,
        phoneNum: String,
        cifNo: String,
        feeSMS: Int,
        accountSMS: Int,
        feeAmount: String,
        alertType: String,
    ) {
        data?.let {
            val type = object : TypeToken<List<ResultItemDomains>>() {}.type
            val allItems = Utils.g().provideGson().fromJson<List<ResultItemDomains>>(data, type)
            _listItem.clear()
            _listItem.addAll(allItems.map { AccountUiModel(it) })
        }
        val listCurrency = _listItem.map {
            it.domain.currency ?: ""
        }.distinct().sorted()
        _uiState.update {
            it.copy(
                serviceType = OttServiceType.fromCodeType(serviceType),
                listCurrency = listCurrency,
                cifNo = cifNo,
                phoneNum = phoneNum,
                smsFee = feeSMS,
                accountSMS = accountSMS,
                feeAmount = feeAmount.toInt(),
                alertType = NotificationType.fromAlertType(alertType),
            )
        }
        if (_uiState.value.serviceType == OttServiceType.SWITCH_SERVICE) {
            _uiState.update {
                it.copy(
                    title = R.string.list_account_screen_title_switch_sms,
                    description = R.string.list_account_screen_description_switch_sms,
                    feeDescription = R.string.list_account_fee_description_switch_sms,
                )
            }
        }
    }

    private fun setSelectedAmount() {
        val selectedItems = _listItem.filter { it.isSelected }
        val totalSelected = when (_uiState.value.serviceType) {
            OttServiceType.SIGN_UP_SERVICE ->
                selectedItems.sumOf { it.domain.feeAmount?.toInt() ?: 0 }
            else ->
                selectedItems.size * _uiState.value.feeAmount
        }

        _uiState.update {
            it.copy(
                selectedAccount = selectedItems.size,
                selectedFee = totalSelected,
            )
        }
    }

    private fun filterList() {
        filteredList.clear()
        filteredList.addAll(
            _listItem.filter { item ->
                val search = _uiState.value.search.isBlank() ||
                    item.domain.accountNumber?.normalize()?.contains(_uiState.value.search) == true

                val currency = _uiState.value.currencySelected.isEmpty() ||
                    _uiState.value.currencySelected.contains(item.domain.currency)

                search && currency
            },
        )
    }

    private fun selectAllFilter() {
        if (_uiState.value.search.isBlank() && _uiState.value.currencySelected.isEmpty()) {
            _listItem.replaceAll { it.copy(isSelected = _uiState.value.selectAll) }
        } else {
            val filteredMap = filteredList.associateBy { it.domain.accountNumber }

            _listItem.replaceAll { item ->
                val match = filteredMap[item.domain.accountNumber]
                if (match != null) {
                    item.copy(isSelected = _uiState.value.selectAll) // đồng bộ state
                } else {
                    item
                }
            }
            filterList()
        }
    }

    private fun selectItem(accountNumber: String) {
        val index = _listItem.indexOfFirst { it.domain.accountNumber == accountNumber }
        if (index != -1) {
            val current = _listItem[index]
            _listItem[index] = current.copy(isSelected = !current.isSelected) // toggle
        }
    }

    private fun setupDetailsData() {
        val totalSelected = _listItem
            .filter { it.isSelected }
        when (_uiState.value.serviceType) {
            OttServiceType.SWITCH_SERVICE -> {
                _uiState.update {
                    it.copy(
                        accountOTT = _listItem.size,
                        finalFee = it.smsFee + it.selectedFee,
                        finalAccountSMS = it.accountSMS + it.selectedAccount,
                        finalAccountOTT = _listItem.size - it.selectedAccount,
                    )
                }
            }
            OttServiceType.SIGN_UP_SERVICE -> {
                _uiState.update {
                    it.copy(
                        finalFee = it.smsFee - it.selectedFee,
                        finalAccountSMS = it.accountSMS - totalSelected.size,
                        finalAccountOTT = it.accountOTT + it.selectedAccount,
                    )
                }
            }
        }
    }
    private fun ResultItemDomains.toActionItem(
        action: String,
        alertType: String,
        alertMethod: String,
    ): ResultItemDomains {
        return this.copy(
            alertType = alertType,
            alertMethod = alertMethod,
            action = action,
            isSelected = false, // luôn false khi gửi request
        )
    }

    private fun registerIdentity() {
        val selectedDomains = _listItem
            .filter { it.isSelected }
            .map { it.domain }
        launchJob(showLoading = true) {
            val selectedItems = selectedDomains.map {
                CancelRecord(
                    username = it.username,
                    virAcctNo = it.accountNumber,
                    virAcctName = it.username,
                    virPhoneNo = it.phonenumber,
                )
            }
            val res = ottUseCase.confirmVacct(
                ConfirmVacctParams(
                    phoneNo = userProf.getPhoneNo(),
                    records = selectedItems,
                    userAction = "",
                    userRole = "",
                    username = userProf.getUserName(),
                ),
            )
            handleResource(res) {
//                isSetupPin()
            }
        }
    }

//    private fun isSetupPin() {
//        val cifNo = userProf.getCifNo()
//        val key = "IS_ENABLE_PIN_$cifNo"
//        val state = prefs.getString(key, encrypted = false)
//        when (state) {
//            "ENABLE" -> sendEvent(ConfirmSmsEvent.ConfirmRegisIdentityEvent)
//            else -> sendEvent(ConfirmSmsEvent.ConfirmSetupPinEvent)
//        }
//    }

    private fun ottRegisterRequest() {
        val regStatus = if (_uiState.value.serviceType == OttServiceType.SWITCH_SERVICE) Tags.OTT_SERVICE_TAG_SMS else Tags.OTT_SERVICE_TAG_OTT
        val regType = if (_uiState.value.serviceType == OttServiceType.SWITCH_SERVICE) _uiState.value.alertType.alertType else Tags.OTT_TAG_159
        val unRegStatus = if (_uiState.value.serviceType == OttServiceType.SWITCH_SERVICE) Tags.OTT_SERVICE_TAG_OTT else Tags.OTT_SERVICE_TAG_SMS

        launchJob(showLoading = true) {
            val selectedDomains = _listItem
                .filter { it.isSelected }
                .map { it.domain }

            val regItems = selectedDomains.map { it.toActionItem(Tags.OTT_TAG_REG, regType, regStatus) }
            val unRegItems = selectedDomains.map { it.toActionItem(Tags.OTT_TAG_UN_REG, regType, unRegStatus) }

            val requestItems = (regItems + unRegItems).toMutableList()

            val res = ottUseCase.registerOTT(
                OttRegisterRequestParams(
                    isVacct = "",
                    mobileNumber = _uiState.value.cifNo,
                    roleId = "",
                    tranId = "",
                    cifno = _uiState.value.cifNo,
                    username = userProf.getUserName() ?: "",
                    recordsParams = requestItems,
                ),
            )
            handleResource(res) {
                sendEvent(ListAccountEvent.UpdateSuccess)
            }
        }
    }
}

// UI Model
data class AccountUiModel(
    val domain: ResultItemDomains,
    val isSelected: Boolean = false,
)

data class ListAccountUiState(
    val serviceType: OttServiceType = OttServiceType.SIGN_UP_SERVICE,
    val cifNo: String = "",
    val phoneNum: String = "",
    val feeAmount: Int = 0,
    val alertType: NotificationType = NotificationType.NONE,
    val title: Int = R.string.list_account_screen_title_sign_up_ott,
    val description: Int = R.string.list_account_screen_description_sign_up_ott,
    val feeDescription: Int = R.string.list_account_fee_description_sign_up,
    val selectAll: Boolean = false,
    val confirmSelected: Boolean = false,
    val search: String = "",
    val currencySelected: List<String> = emptyList(),
    val listCurrency: List<String> = emptyList(),
    val accountSMS: Int = 0,
    val finalAccountSMS: Int = 0,
    val selectedAccount: Int = 0,
    val accountOTT: Int = 0,
    val finalAccountOTT: Int = 0,
    val smsFee: Int = 0,
    val selectedFee: Int = 0,
    val finalFee: Int = 0,
)

sealed class ListAccountEvent : OneTimeEvent {
    data object UpdateSuccess : OneTimeEvent
    data object NavigateToDetails : OneTimeEvent
}

sealed interface ListAccountAction {
    data object SelectAll : ListAccountAction
    data object OnClickConfirmTerm : ListAccountAction
    data object ShowDetail : ListAccountAction
    data object OnCLickSignUp : ListAccountAction
    data class SelectCurrency(val listCurrency: List<String>) : ListAccountAction
    data class SelectItem(val accountNumber: String) : ListAccountAction
    data class SearchItem(val search: String) : ListAccountAction
}