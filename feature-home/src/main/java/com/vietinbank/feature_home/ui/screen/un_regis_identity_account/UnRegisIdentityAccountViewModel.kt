package com.vietinbank.feature_home.ui.screen.un_regis_identity_account

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.CancelRecord
import com.vietinbank.core_domain.models.ott_feature.CancelVacctOttParams
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class UnRegisIdentityAccountViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val sessionManager: ISessionManager,
    override val ottSetupService: IOttSetupService,
    private val ottUseCase: OttFeatureUseCase,
    private val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(UnRegIdentityAccountUiState())
    val uiState: StateFlow<UnRegIdentityAccountUiState> = _uiState.asStateFlow()

    init {
        getListAccNumber()
        _uiState.update {
            it.copy(
                phoneNum = userProf.getPhoneNo() ?: "",
                corpName = userProf.getCorpName() ?: "",
            )
        }
    }

    fun onAction(action: UnRegIdentityAccountAction) {
        when (action) {
            UnRegIdentityAccountAction.OnClickNavigateBack -> sendEvent(UnRegIdentityAccountEvent.NavigateBackEvent)
            is UnRegIdentityAccountAction.OnClickConfirm -> {
                if (action.listSelected.isEmpty()) {
                    sendEvent(UnRegIdentityAccountEvent.ShowEmptyListErrorDialog)
                } else {
                    val listSelected = _uiState.value.listUnReg.filter {
                        action.listSelected.contains(it.accountNumber)
                    }
                    unRegisterIdentity(listSelected)
                }
            }
        }
    }

    private fun getListAccNumber() {
        launchJob() {
            val params = ListRegRequestParams(
                alertMethod = "ALL",
                isVacct = "",
                mobileNumber = userProf.getPhoneNo() ?: "",
                roleId = "",
                tranId = "",
                typeCheck = "Y",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.listReg(
                params,
            )
            handleResource(
                resource = res,
                onSuccess = { data ->
                    _uiState.update { state ->
                        state.copy(
                            listUnReg = data.results?.filter { it.status == "A" } ?: listOf(),
                        )
                    }
                },
            )
        }
    }

    private fun unRegisterIdentity(listSelected: List<ResultItemDomains>) {
        launchJob(showLoading = true) {
            val selectedItems = listSelected.map {
                CancelRecord(
                    username = it.username,
                    virAcctNo = it.accountNumber,
                    virAcctName = it.username,
                    virPhoneNo = it.phonenumber,
                )
            }
            val cancelParams = CancelVacctOttParams(
                username = userProf.getUserName(),
                phoneNo = userProf.getPhoneNo(),
                record = selectedItems,
            )
            val cancelRes = ottUseCase.cancelVacct(
                cancelParams,
            )
            handleResource(cancelRes) {
                sendEvent(UnRegIdentityAccountEvent.ConfirmEvent)
            }
        }
    }
}

data class UnRegIdentityAccountUiState(
    val phoneNum: String = "",
    val corpName: String = "",
    val listUnReg: List<ResultItemDomains> = listOf(),
)

sealed class UnRegIdentityAccountEvent : OneTimeEvent {
    data object NavigateBackEvent : OneTimeEvent
    data object ConfirmEvent : OneTimeEvent
    data object ShowEmptyListErrorDialog : OneTimeEvent
}

sealed interface UnRegIdentityAccountAction {
    data object OnClickNavigateBack : UnRegIdentityAccountAction
    data class OnClickConfirm(val listSelected: List<String>) : UnRegIdentityAccountAction
}