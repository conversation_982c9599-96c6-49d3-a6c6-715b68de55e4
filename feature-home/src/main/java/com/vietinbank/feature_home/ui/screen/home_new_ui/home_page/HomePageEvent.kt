package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.home_card.MyRequestUiState
import com.vietinbank.feature_home.ui.view.home_card.RecentTransactionUiState
import com.vietinbank.feature_home.ui.view.home_card.RecommendedUiModelState
import java.time.LocalDate
import java.time.format.DateTimeFormatter

data class HomePageUiState(
    val approveLever: String = "",
    val userName: String = "",
    val companyName: String = "",
    val phoneNum: String = "",
    val email: String = "",
    val roleName: Int = R.string.account_card_company_name_maker,
    val isShowShortcut: Boolean = false,
    val bottomNavIndex: Int = 0,
    val isVisibleBalance: Boolean = false,
    val totalBalance: String? = "",
    val isBalanceLoading: Boolean = false,
    val listFuncShortcut: List<FunctionItemData> = listOf(),
)

sealed class HomePageEvent : OneTimeEvent {
    data object NavigateToSetting : HomePageEvent()
    data object NavigateToOttScreen : HomePageEvent()
    data object NavigateToAccount : HomePageEvent()
    data object NavigateToExplore : HomePageEvent()
    data object NavigateToCt : HomePageEvent()
    data object NavigateToEditShortcut : HomePageEvent()
    data object NavigateToManager : HomePageEvent()
    data object NavigateToApprovalList : HomePageEvent()
    data object NotYetImplement : HomePageEvent()
    data object ShowUpdateEkycDialog : HomePageEvent()
    data class GenOtpEkycSuccess(val isResend: Boolean) : HomePageEvent()
    data object GenOtpEkycFail : HomePageEvent()
    data object VerifyOtpEkycSuccess : HomePageEvent()
    data object VerifyOtpEkycFail : HomePageEvent()
    data object OnClickAllFeature : HomePageEvent()
    data object NavigateToTracePayment : HomePageEvent()
    data object NavigateToPaymentOrder : HomePageEvent()
}

sealed interface HomePageAction {
    data object OnClickAvatar : HomePageAction
    data object OnClickExploreButton : HomePageAction
    data object OnClickVisibleButton : HomePageAction
    data object OnClickNotification : HomePageAction
    data class OnTabSelected(val index: Int) : HomePageAction
    data object OnClickSearch : HomePageAction
    data object OnClickLoadingMyRequest : HomePageAction
    data object OnClickLoadingRcmCard : HomePageAction
    data object NavigateCt : HomePageAction
    data object ClickShortcut : HomePageAction
    data object ClickEditShortcut : HomePageAction
    data object OnClickAllTransaction : HomePageAction
    data class OnCLickItem(val id: String) : HomePageAction
    data object NotYetImplemented : HomePageAction
    data object OnClickAllFeature : HomePageAction
    data class ClickMyRequestItem(val functionItemData: FunctionItemData) : HomePageAction
}

sealed class HomeCardItem {
    data class RecommendedCard(val state: RecommendedUiModelState) : HomeCardItem()
    data class RecentTransactionCard(val state: RecentTransactionUiState) :
        HomeCardItem()

    data class MyRequestCard(val state: MyRequestUiState) : HomeCardItem()
    data object BannerCard : HomeCardItem()
}

fun formatDate(input: String): String {
    val inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    val outputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
    val date = LocalDate.parse(input, inputFormatter)
    return date.format(outputFormatter)
}