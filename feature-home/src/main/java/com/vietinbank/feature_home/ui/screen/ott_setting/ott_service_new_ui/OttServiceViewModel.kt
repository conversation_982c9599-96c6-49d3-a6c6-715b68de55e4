package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.getRemainingSeconds
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.models.ott_feature.SmsOtpCreateRequestParams
import com.vietinbank.core_domain.models.ott_feature.SmsOtpVerifyRequestParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.ui.screen.ott_setting.NotificationType
import com.vietinbank.feature_home.ui.screen.ott_setting.OttServiceType
import com.vietinbank.feature_home.ui.screen.ott_setting.ServiceAccountType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class OttServiceViewModel @Inject constructor(
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {

    companion object {
        const val VALID_TIME = 300
        const val RESEND_TIME = 60
        const val TYPE_CHECK_PARAM = "1"
        const val IS_VACC_N = "N"
        const val IS_VACC_Y = "Y"
        const val DEFAULT_FEE_AMOUNT = "0"
    }

    private val _uiState = MutableStateFlow(OttServiceUiState())
    val uiState: StateFlow<OttServiceUiState> = _uiState.asStateFlow()

    private var tempData: UserDataOtp = UserDataOtp()

    private var isVerifySuccess: Boolean = false

    private var successCreateSmsTime: Long = 0L

    fun onAction(action: OttServiceAction) {
        when (action) {
            is OttServiceAction.OnCifNoChange -> {
                _uiState.update {
                    it.copy(
                        cifNo = action.value,
                    )
                }
            }

            is OttServiceAction.OnPhoneNumChange -> {
                _uiState.update {
                    it.copy(
                        phoneNum = action.value,
                    )
                }
            }

            is OttServiceAction.OnPickAccountType -> {
                _uiState.update {
                    it.copy(
                        accountType = ServiceAccountType.fromCodeType(action.value),
                    )
                }
            }

            is OttServiceAction.OnPickNotificationType -> {
                _uiState.update {
                    it.copy(
                        alertType = NotificationType.fromAlertType(action.value),
                    )
                }
            }

            OttServiceAction.OnConfirmClick -> {
                val validTime = getRemainingSeconds(5, successCreateSmsTime)
                if (tempData.isSameAs(_uiState.value) && isVerifySuccess && validTime > 0) {
                    sendEvent(
                        OttServiceEvent.ShowOtpDialog(
                            validTime,
                            getRemainingSeconds(1, successCreateSmsTime),
                            false,
                        ),
                    )
                } else {
                    smsOtpCreate()
                }
            }
        }
    }

    fun setupData(type: String, newRegister: Boolean) {
        _uiState.update {
            it.copy(
                serviceType = OttServiceType.fromCodeType(type),
                isEnableEditCif = !newRegister,
                cifNo = if (newRegister) userProf.getCifNo() ?: "" else "",
            )
        }
    }

    fun smsOtpCreate(isResend: Boolean = false) {
        tempData = UserDataOtp(
            phoneNum = _uiState.value.phoneNum,
            cifNo = _uiState.value.cifNo,
            accountType = _uiState.value.accountType,
            notificationType = _uiState.value.alertType,
        )

        launchJob(showLoading = true) {
            val res = ottUseCase.smsOtpCreate(
                SmsOtpCreateRequestParams(
                    accountNumber = "",
                    isVacct = if (_uiState.value.accountType == ServiceAccountType.PAYMENT) {
                        IS_VACC_N
                    } else {
                        IS_VACC_Y
                    },
                    phoneNo = _uiState.value.phoneNum.replace(" ", ""),
                    roleId = "",
                    type = "",
                    username = userProf.getUserName() ?: "",
                    cifno = _uiState.value.cifNo.replace(" ", ""),
                ),
            )
            handleResource(
                res,
                onSuccess = {
                    isVerifySuccess = true
                    successCreateSmsTime = System.currentTimeMillis()
                    sendEvent(OttServiceEvent.ShowOtpDialog(VALID_TIME, RESEND_TIME, isResend))
                },
                handleError = {
                    isVerifySuccess = false
                },

            )
        }
    }

    fun smsOtpVerify(otp: String) {
        launchJob(showLoading = true) {
            val res = ottUseCase.smsOtpVerify(
                SmsOtpVerifyRequestParams(
                    isVacct = "",
                    otpNumber = otp,
                    phoneNo = _uiState.value.phoneNum.replace(" ", ""),
                    roleId = "",
                    typeCheck = TYPE_CHECK_PARAM,
                    username = userProf.getUserName() ?: "",
                    cifno = _uiState.value.cifNo.replace(" ", ""),
                ),
            )
            handleResourceSilent(
                res,
                onSuccess = { data ->
                    _uiState.update {
                        it.copy(
                            otpFailCount = 0,
                        )
                    }
                    listReg()
//                    sendEvent(OttServiceEvent.VerifyOtpSuccess)
                },
                onError = {
                    val failCount = _uiState.value.otpFailCount + 1
                    if (failCount >= 5) {
                        sendEvent(OttServiceEvent.ShowLockOtp)
                        return@handleResourceSilent
                    }
                    _uiState.update {
                        it.copy(
                            otpFailCount = failCount,
                        )
                    }
                    sendEvent(OttServiceEvent.VerifyOtpFail)
                },
            )
        }
    }

    private fun listReg() {
        launchJob(showLoading = true) {
            val res = ottUseCase.listReg(
                ListRegRequestParams(
                    // change when keypass method valid
                    alertMethod = Tags.OTT_SERVICE_TAG_ALL,
                    isVacct = if (_uiState.value.accountType == ServiceAccountType.PAYMENT) {
                        IS_VACC_N
                    } else {
                        IS_VACC_Y // IDENTITY ACCOUNT
                    },
                    mobileNumber = _uiState.value.phoneNum.replace(" ", ""),
                    roleId = "",
                    tranId = "",
                    typeCheck = IS_VACC_Y,
                    type = TYPE_CHECK_PARAM,
                    username = userProf.getUserName().orEmpty(),
                    cifno = _uiState.value.cifNo.replace(" ", ""),
                ),
            )

            handleResource(
                resource = res,
                onSuccess = { data ->
                    val results = data.results.orEmpty()
                    val signUpTags = setOf(Tags.OTT_TAG_89, Tags.OTT_TAG_169, Tags.OTT_TAG_189)

                    val filteredList = results.filter {
                        when (_uiState.value.serviceType) {
                            OttServiceType.SIGN_UP_SERVICE -> it.alertType in signUpTags
                            OttServiceType.SWITCH_SERVICE -> it.alertType == Tags.OTT_TAG_159
                        }
                    }

                    if (filteredList.isEmpty()) {
                        when (_uiState.value.serviceType) {
                            OttServiceType.SIGN_UP_SERVICE -> sendEvent(OttServiceEvent.EmptyListDataSignUp)
                            OttServiceType.SWITCH_SERVICE -> sendEvent(OttServiceEvent.EmptyListDataSwitch)
                        }
                    } else {
                        val listSMS = results.filter { it.alertType in signUpTags }
                        val totalFeeSms = listSMS.sumOf { it.feeAmount?.toIntOrNull() ?: 0 }
                        val feeAmount = data.fees
                            ?.firstOrNull { it.alertType == _uiState.value.alertType.alertType }
                            ?.feeAmount ?: DEFAULT_FEE_AMOUNT

                        sendEvent(
                            OttServiceEvent.NavigateToListAccountFragment(
                                filteredList,
                                totalFeeSms,
                                listSMS.size,
                                feeAmount,
                            ),
                        )
                    }
                },
                handleError = {
                },
            )
        }
    }
}

data class UserDataOtp(
    val phoneNum: String = "",
    val cifNo: String = "",
    val accountType: ServiceAccountType = ServiceAccountType.NONE,
    val notificationType: NotificationType = NotificationType.NONE,
)

fun UserDataOtp.isSameAs(state: OttServiceUiState): Boolean {
    return phoneNum == state.phoneNum &&
        cifNo == state.cifNo &&
        accountType == state.accountType &&
        notificationType == state.alertType
}

data class OttServiceUiState(
    val serviceType: OttServiceType = OttServiceType.SIGN_UP_SERVICE,
    val isEnableEditCif: Boolean = true,
    val phoneNum: String = "",
    val cifNo: String = "",
    val aliasName: String = "",
    val accountType: ServiceAccountType = ServiceAccountType.NONE,
    val alertType: NotificationType = NotificationType.NONE,
    val otpFailCount: Int = 0,
)

sealed class OttServiceEvent : OneTimeEvent {
    data class ShowOtpDialog(val validTime: Int, val resendTime: Int, val isResend: Boolean) : OneTimeEvent
    data object VerifyOtpFail : OneTimeEvent
    data object ShowLockOtp : OneTimeEvent
    data object EmptyListDataSignUp : OneTimeEvent
    data object EmptyListDataSwitch : OneTimeEvent
    data class NavigateToListAccountFragment(val data: List<ResultItemDomains>, val feeSMS: Int, val accountSMS: Int, val feeAmount: String) : OneTimeEvent
}

sealed interface OttServiceAction {
    data object OnConfirmClick : OttServiceAction
    data class OnPickAccountType(val value: String) : OttServiceAction
    data class OnPickNotificationType(val value: String) : OttServiceAction
    data class OnCifNoChange(val value: String) : OttServiceAction
    data class OnPhoneNumChange(val value: String) : OttServiceAction
}