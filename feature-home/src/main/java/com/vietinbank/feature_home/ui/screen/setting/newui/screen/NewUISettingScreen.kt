package com.vietinbank.feature_home.ui.screen.setting.newui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.UserProfileCard
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingItem
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingItemDestination
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingSection
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingUiState
import com.vietinbank.core_ui.R as CoreR
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun NewUISettingScreen(
    modifier: Modifier = Modifier,
    uiState: SettingUiState,
    onStaticItemClicked: (SettingItemDestination) -> Unit,
    onFaceLoginCheckedChange: (Boolean) -> Unit,
    onLogout: () -> Unit,
    onBack: () -> Unit,
) {
    val lazyListState = rememberLazyListState()

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
        state = lazyListState,
    ) {
        item {
            FoundationAppBar(
                isLightIcon = false,
                title = stringResource(CoreR.string.setting_title),
                onNavigationClick = onBack,
            )
        }

        item {
            UserProfileCard(
                modifier = Modifier.padding(
                    vertical = FDS.Sizer.Padding.padding24,
                    horizontal = FDS.Sizer.Padding.padding32,
                ),
                title = uiState.userName,
                content = stringResource(uiState.role, uiState.companyName),
                isLightCard = false,
                onClickAvatar = {},
                onClickIcon = {},
            )
        }

        uiState.settingSections.forEachIndexed { index, section ->
            settingSection(section) { item ->
                when (item) {
                    is SettingItem.FaceLoginItem -> {
                        // Prevent frozen capture in onClick
                        val isFaceLogin by rememberUpdatedState(
                            uiState.isFaceLogin,
                        )
                        SettingItemRow(
                            item = item,
                            trailingIcon = {
                                FoundationSelector(
                                    boxType = SelectorType.Switch,
                                    isSelected = uiState.isFaceLogin,
                                    onClick = {
                                        onFaceLoginCheckedChange.invoke(isFaceLogin.not())
                                    },
                                )
                            },
                        )
                    }

                    is SettingItem.StaticItem -> {
                        SettingItemRow(
                            item = item,
                            onClick = {
                                onStaticItemClicked.invoke(item.action)
                            },
                        )
                    }
                }
            }

            if (index != uiState.settingSections.lastIndex) {
                item {
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                }
            }
        }

        item {
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
        }

//        item {
//            Box(
//                modifier = Modifier
//                    .commonRoundedCornerCard()
//                    .padding(horizontal = FDS.Sizer.Padding.padding24)
//            ) {
//                CsatView {  }
//            }
//        }

        item {
            Box(
                modifier = Modifier
                    .commonRoundedCornerCard()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                SettingItemRow(
                    item = SettingItem.StaticItem(
                        iconRes = R.drawable.ic_setting_logout,
                        titleRes = CoreR.string.setting_button_logout,
                        action = SettingItemDestination.Logout,
                    ),
                    textColor = FDS.Colors.stateError,
                    iconColor = FDS.Colors.stateError,
                    // TODO: Global define color code
                    iconBackgroundColor = Color(0xFFFFBCCD),
                    onClick = onLogout,
                )
            }
        }

        item {
            Column(
                modifier = Modifier
                    .padding(FDS.Sizer.Padding.padding24),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                FoundationText(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(CoreR.string.setting_footer_version, "1.0.0"),
                    style = FDS.Typography.captionMBold,
                    color = FDS.Colors.characterSecondary,
                    textAlign = TextAlign.Center,
                )
                FoundationText(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(CoreR.string.setting_footer_copy_right),
                    style = FDS.Typography.captionM,
                    color = FDS.Colors.characterTertiary,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

// Fake card ui để giữ section.items recycle
fun LazyListScope.settingSection(
    section: SettingSection,
    item: @Composable (SettingItem) -> Unit,
) {
    item {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(FDS.Sizer.Padding.padding24)
                .clip(
                    RoundedCornerShape(
                        topStart = FDS.Sizer.Radius.radius32,
                        topEnd = FDS.Sizer.Radius.radius32,
                    ),
                )
                .background(FDS.Colors.background),
        )
    }

    item {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(FDS.Colors.background)
                .padding(horizontal = FDS.Sizer.Padding.padding24),
        ) {
            FoundationText(
                text = stringResource(section.titleRes),
                style = FDS.Typography.captionM,
                color = FDS.Colors.characterSecondary,
            )
            Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
        }
    }

    itemsIndexed(
        items = section.items,
        key = { _, item -> item.titleRes },
    ) { index, item ->
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(FDS.Colors.background)
                .padding(horizontal = FDS.Sizer.Padding.padding24),
        ) {
            item(item)
        }

        if (index != section.items.lastIndex) {
            Spacer(
                modifier = Modifier
                    .background(FDS.Colors.background)
                    .fillMaxWidth()
                    .height(FDS.Sizer.Gap.gap16),
            )
        }
    }

    item {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(FDS.Sizer.Padding.padding24)
                .clip(
                    RoundedCornerShape(
                        bottomStart = FDS.Sizer.Radius.radius32,
                        bottomEnd = FDS.Sizer.Radius.radius32,
                    ),
                )
                .background(FDS.Colors.background),
        )
    }
}

@Composable
private fun SettingItemRow(
    modifier: Modifier = Modifier,
    item: SettingItem,
    onClick: (() -> Unit)? = null,
    textColor: Color = FDS.Colors.characterHighlighted,
    iconColor: Color = FDS.Colors.characterHighlighted,
    iconBackgroundColor: Color = FDS.Colors.backgroundBgHighlightSoft,
    trailingIcon: @Composable (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .safeClickable { onClick?.invoke() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Icon with background
        Box(
            modifier = Modifier
                .size(FDS.Sizer.Icon.icon32)
                .clip(CircleShape)
                .background(iconBackgroundColor),
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                painter = painterResource(item.iconRes),
                contentDescription = null,
                tint = iconColor,
            )
        }

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

        // Title
        FoundationText(
            modifier = Modifier.weight(1f),
            text = stringResource(item.titleRes),
            style = FDS.Typography.captionLSemibold,
            color = textColor,
        )

        Spacer(modifier = Modifier.width(FDS.Sizer.Gap.gap8))

        trailingIcon?.invoke() ?: Icon(
            painter = painterResource(R.drawable.ic_home_more),
            contentDescription = null,
            tint = FDS.Colors.characterSecondary,
            modifier = Modifier.size(FDS.Sizer.Icon.icon16),
        )
    }
}

@Preview
@Composable
private fun PreviewNewUISettingScreen() {
    var isFaceLogin by remember { mutableStateOf(false) }
    val uiState = remember(isFaceLogin) {
        SettingUiState(
            isFaceLogin = isFaceLogin,
        )
    }
    AppTheme {
        NewUISettingScreen(
            modifier = Modifier
                .fillMaxSize()
                .eFastBackground(),
            uiState = uiState,
            onBack = {},
            onStaticItemClicked = {},
            onLogout = {},
            onFaceLoginCheckedChange = {
                isFaceLogin = it
            },
        )
    }
}
