package com.vietinbank.feature_home.ui.screen.setting

import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.UserInfoDataHolder
import com.vietinbank.core_domain.models.home.CheckUserEkycParams
import com.vietinbank.core_domain.models.home.GetBiometricFaceParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.ui.screen.home.CheckUserEkycEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

@HiltViewModel
class SettingViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    val transferCacheManager: ITransferCacheManager,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {
    private val _settingUiState = MutableStateFlow(SettingUiState())
    val settingUiState: StateFlow<SettingUiState> = _settingUiState.asStateFlow()

    fun clearSession() = sessionManager.clearSession()
    init {
        viewModelScope.launch {
            userProf.userProfFlow.collect {
                _settingUiState.update {
                    it.copy(
                        fullName = userProf.getFullName().toString(),
                    )
                }
            }
        }
    }

    fun onAction(action: SettingItemAction) {
        when (action) {
            SettingItemAction.UpdateEkycAction -> {
                checkUserEkyc()
            }

            SettingItemAction.AccountBalanceAction -> {
                sendEvent(SettingEvent.AccountBalanceEvent)
            }

            SettingItemAction.TransactionStatusAction -> {
                sendEvent(SettingEvent.TransactionStatusEvent)
            }

            SettingItemAction.NavigateBack -> sendEvent(SettingEvent.NavigateBackEvent)
            SettingItemAction.OnClickLogout -> sendEvent(SettingEvent.LogOutEvent)
            SettingItemAction.LoanAccountAction -> sendEvent(SettingEvent.LoanAccountEvent)
            SettingItemAction.OttServiceAction -> sendEvent(
                SettingEvent.OttServiceEvent,
            )
        }
    }

    private fun checkUserEkyc() {
        launchJob {
            val res = homeUseCase.checkUserEkyc(
                CheckUserEkycParams(
                    userProf.getUserName().toString(),
                ),
            )
            printLog("ekyc checkUserEkyc: $res")
            handleResource(
                resource = res,
                onSuccess = { data ->
                    if (data.bioStat == "0" && data.statusAccountHolder == "1") {
                        sendEvent(
                            when {
                                data.retail == "0" && data.national == "VN" -> CheckUserEkycEvent.NewEkyc
                                data.retail == "0" && data.national != "VN" -> CheckUserEkycEvent.ShowGuildToCounter
                                data.retail == "1" -> {
                                    getBiometricFace(data.biometricFace ?: "")
                                    CheckUserEkycEvent.UpdateEkyc
                                }

                                else -> CheckUserEkycEvent.Nothing
                            },
                        )
                    } else {
                        sendEvent(CheckUserEkycEvent.Nothing)
                    }
                },
            )
        }
    }

    @OptIn(ExperimentalEncodingApi::class)
    private fun getBiometricFace(biometricFace: String) {
        launchJobSilent {
            val res = homeUseCase.getBiometricFace(
                GetBiometricFaceParams(
                    biometricId = biometricFace,
                    username = userProf.getUserName().toString(),
                    roleId = "",
                ),
            )
            printLog("ekyc getBiometricFace: $res")
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    viewModelScope.launch(Dispatchers.Default) {
                        val biometricImage = vmScope.async {
                            Base64.Default.decode(data.image ?: "")
                        }
                        UserInfoDataHolder.saveBiometricImage(
                            biometricImage.await(),
                        )
                    }
                },
            )
        }
    }
}

sealed class SettingEvent : OneTimeEvent {
    data object AccountBalanceEvent : SettingEvent()
    data object OttServiceEvent : SettingEvent()
    data object TransactionStatusEvent : SettingEvent()
    data object LoanAccountEvent : SettingEvent()
    data object NavigateBackEvent : SettingEvent()
    data object LogOutEvent : SettingEvent()
}