package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_manage

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun OttServiceManageScreen(
    uiState: OttServiceManageUiState,
    onAction: (OttServiceManageAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(FDS.Sizer.Padding.padding8),
    ) {
        FoundationAppBar(
            title = stringResource(R.string.ott_manage_title),
            titleStyle = FDS.Typography.headingH2,
            isLightIcon = false,
            onNavigationClick = {
                onAction(OttServiceManageAction.OnClickBack)
            },
            isSingleLineAppBar = true,
            isCustomActionUseSafeClick = false,
        )
        Spacer(Modifier.size(FDS.Sizer.Padding.padding24))
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
        ) {
            Column(
                modifier = Modifier
                    .commonRoundedCornerCard()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
            ) {
                OttManageItem(
                    icon = R.drawable.ic_ott_manage_setting,
                    leadingText = stringResource(R.string.ott_setting_title),
                    trailingText = stringResource(R.string.ott_setting_description),
                    onClick = {
                        onAction(OttServiceManageAction.OnClickOttSetting)
                    },
                )
                StrokeLine()
                OttManageItem(
                    leadingText = stringResource(R.string.ott_sign_up_service_title),
                    trailingText = stringResource(R.string.ott_sign_up_service_title),
                    onClick = {
                        onAction(OttServiceManageAction.OnClickOttSignUp)
                    },
                )
                OttManageItem(
                    leadingText = stringResource(R.string.ott_switch_title),
                    trailingText = stringResource(R.string.ott_switch_description),
                    onClick = {
                        onAction(OttServiceManageAction.OnClickOttSwitch)
                    },
                )
                if (!uiState.isNewRegis) {
                    OttManageItem(
                        leadingText = stringResource(R.string.ott_pin_setup_title),
                        trailingText = stringResource(R.string.ott_pin_setup_description),
                    )
                }
            }

            Column(
                modifier = Modifier
                    .commonRoundedCornerCard()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                OttManageItem(
                    icon = R.drawable.ic_transaction_status,
                    leadingText = stringResource(R.string.transaction_status_title),
                    trailingText = stringResource(R.string.transaction_status_description),
                )
            }

            Column(
                modifier = Modifier
                    .commonRoundedCornerCard()
                    .padding(horizontal = FDS.Sizer.Padding.padding24),
            ) {
                OttManageItem(
                    icon = R.drawable.ic_ott_promotion,
                    leadingText = stringResource(R.string.ott_promotion_title),
                    trailingText = stringResource(R.string.ott_promotion_description),
                )
            }
        }
    }
}

@Composable
private fun OttManageItem(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int? = null,
    leadingText: String,
    trailingText: String,
    isUseSwitch: Boolean = false,
    onClick: () -> Unit = {},
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .dismissRippleClickable {
                onClick()
            },
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (icon != null) {
            Image(
                painter = painterResource(icon),
                contentDescription = "",
                modifier = Modifier.size(FDS.Sizer.Padding.padding32),
            )
        } else {
            Spacer(Modifier.size(FDS.Sizer.Padding.padding32))
        }
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding4),
        ) {
            FoundationText(
                text = leadingText,
                color = FDS.Colors.characterHighlighted,
                style = FDS.Typography.captionLSemibold,
                textAlign = TextAlign.Left,
            )
            FoundationText(
                text = trailingText,
                color = FDS.Colors.characterSecondary,
                style = FDS.Typography.captionL,
                textAlign = TextAlign.Left,
            )
        }

        if (isUseSwitch) {
            // to do
        } else {
            Icon(
                painter = painterResource(R.drawable.ic_home_more),
                contentDescription = "",
                modifier = Modifier.size(FDS.Sizer.Padding.padding24),
                tint = FDS.Colors.characterPrimary,
            )
        }
    }
}