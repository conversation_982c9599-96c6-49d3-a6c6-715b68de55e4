package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun CurrencyBottomSheet(
    visible: Boolean = true,
    currencies: List<String>,
    selectedCurrencies: List<String>,
    onDismiss: () -> Unit = {},
    onPicked: (List<String>) -> Unit = {},
) {
    val selectedItems = remember { mutableStateListOf<String>() }
    LaunchedEffect(Unit) {
        selectedItems.addAll(selectedCurrencies)
    }

    BaseBottomSheet<String>(
        visible = visible,
        onDismissRequest = onDismiss,
        onResult = {
//            onPicked(selectedItems)
            // Auto-dismiss handled by BaseBottomSheet
        },
        secureFlag = false, // Set to true if showing sensitive account data
    ) {
        Column {
            Column(
                Modifier
                    .padding(bottom = FDS.Sizer.Padding.padding16)
                    .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
                    .background(Color.White)
                    .padding(vertical = FDS.Sizer.Padding.padding24),
            ) {
                // Title
                FoundationText(
                    text = stringResource(R.string.ott_service_title),
                    style = FDS.Typography.headingH3,
                    color = FDS.Colors.characterHighlighted,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = FDS.Sizer.Padding.padding16),
                )

                HorizontalDivider(
                    color = FDS.Colors.strokeDivider,
                    thickness = FDS.Sizer.Stroke.stroke1,
                )

                val state = rememberLazyListState()

                LazyColumn(
                    state = state,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(FDS.Sizer.Padding.padding24),
                ) {
                    item {
                        FoundationText(
                            stringResource(R.string.currency_bottom_sheet_title),
                            color = Color.Gray,
                            modifier = Modifier.padding(vertical = 8.dp),
                        )
                    }

                    items(currencies) { currency ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .dismissRippleClickable {
                                    if (selectedItems.contains(currency)) {
                                        selectedItems.remove(currency)
                                    } else {
                                        selectedItems.add(currency)
                                    }
                                }
                                .padding(vertical = FDS.Sizer.Padding.padding8),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                text = currency,
                                modifier = Modifier.weight(1f),
                            )
                            FoundationSelector(
                                boxType = SelectorType.Checkbox,
                                isSelected = selectedItems.contains(currency),
                                onClick = {
                                    if (!selectedItems.contains(currency)) {
                                        selectedItems.add(currency)
                                    } else {
                                        selectedItems.remove(currency)
                                    }
                                },
                            )
                        }
                    }
                }
            }

            Row(
                Modifier
                    .padding(horizontal = FDS.Sizer.Padding.padding16),
            ) {
                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.currency_botton_sheet_negative_button),
                    isLightButton = false,
                    onClick = {
                        onDismiss()
                    },
                )
                Spacer(Modifier.size(FDS.Sizer.Padding.padding8))
                FoundationButton(
                    modifier = Modifier.weight(1f),
                    text = stringResource(R.string.currency_botton_sheet_positive_button),
                    onClick = {
                        onDismiss()
                        onPicked(selectedItems)
                    },
                )
            }
        }
    }
}
