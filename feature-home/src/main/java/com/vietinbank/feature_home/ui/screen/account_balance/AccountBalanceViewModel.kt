package com.vietinbank.feature_home.ui.screen.account_balance

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.biometric.IBiometricManager
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.ott_feature.ListRegRequestParams
import com.vietinbank.core_domain.models.ott_feature.ResultItemDomains
import com.vietinbank.core_domain.usecase.ott_feature.OttFeatureUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.utils.AccountType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class AccountBalanceViewModel @Inject constructor(
    private val biometricManager: IBiometricManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    private val prefs: CustomEncryptedPrefs,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val ottUseCase: OttFeatureUseCase,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(AccountBalanceUiState())
    val uiState: StateFlow<AccountBalanceUiState> = _uiState.asStateFlow()

    private var accountTag = ""
    private var listAccount: List<ResultItemDomains> = listOf()

    init {
        getListAccNumber()
    }

    fun onAction(action: AccountBalanceAction) {
        when (action) {
            AccountBalanceAction.PinSetupAction -> {
                if (_uiState.value.setupPinState) {
                    checkBiometricAvailable()
                } else {
                    sendEvent(AccountBalanceEvent.PinSetupEvent)
                }
            }

            AccountBalanceAction.NotificationAction -> {
                sendEvent(AccountBalanceEvent.NotificationEvent)
            }

            AccountBalanceAction.ChangePinAction -> {
                sendEvent(AccountBalanceEvent.ChangePinEvent)
            }

            AccountBalanceAction.ForgetPinAction -> {
                sendEvent(AccountBalanceEvent.PinSetupEvent)
            }

            AccountBalanceAction.NavigateBack -> {
                sendEvent(AccountBalanceEvent.NavigateBackEvent)
            }

            AccountBalanceAction.NotYetImplemented -> {
                sendEvent(AccountBalanceEvent.NotYetImplemented)
            }

            AccountBalanceAction.GotoRegisterLoanAccountAction -> {
                sendEvent(AccountBalanceEvent.GotoRegisterLoanAccountAction)
            }

            is AccountBalanceAction.SelectedTabIndex -> {
                _uiState.update {
                    it.copy(
                        tag = AccountType.fromValue(action.tab),
                    )
                }
                updateListRegisAccNumber()
                accountTag = when (_uiState.value.tag) {
                    AccountType.UnDefine -> ""
                    AccountType.PaymentAccount -> Tags.LIST_PAYMENT_ACC_NUMBER
                    AccountType.IdentityAccount -> Tags.LIST_IDENTITY_ACC_NUMBER
                    AccountType.LoanAccount -> Tags.LIST_LOAN_ACC_NUMBER
                }
            }
        }
    }

    fun getListAccNumber() {
        launchJob() {
            val params = ListRegRequestParams(
                alertMethod = "ALL",
                isVacct = "Y",
                mobileNumber = userProf.getPhoneNo() ?: "",
                roleId = "",
                tranId = "",
                typeCheck = "Y",
                username = userProf.getUserName() ?: "",
            )
            val res = ottUseCase.listReg(
                params,
            )
            handleResource(
                resource = res,
                onSuccess = { data ->
                    listAccount = data.results ?: listOf()
                    mergeWithNewKeys()
                    updateListRegisAccNumber()
                },
            )
        }
    }

    private fun mergeWithNewKeys() {
        val newKeys: List<String> = listAccount.map {
            it.accountNumber ?: ""
        }
        val oldMap = prefs.getString(accountTag + userProf.getCifNo())
        val type = object : TypeToken<Map<String, Boolean>>() {}.type
        val currentMap: MutableMap<String, Boolean> = if (!oldMap.isNullOrEmpty()) {
            Utils.g().provideGson().fromJson(oldMap, type)
        } else {
            mutableMapOf()
        }
        newKeys.forEach { key ->
            if (!currentMap.containsKey(key)) {
                currentMap[key] = true
            }
        }
        val updatedJson = Utils.g().provideGson().toJson(currentMap)
        prefs.setString(accountTag + userProf.getCifNo(), updatedJson)
    }

    fun updateListRegisAccNumber() {
        _uiState.update {
            it.copy(
                listAccNumber = listAccount.filter { item ->
                    when (_uiState.value.tag) {
                        AccountType.IdentityAccount -> item.status == "A"
                        else -> {
                            item.alertType == "159"
                        }
                    }
                }.map { account ->
                    account.accountNumber ?: ""
                },
            )
        }
    }

    fun getListRegisAccNumber() {
        val oldMap = prefs.getString(accountTag + userProf.getCifNo())
        val type = object : TypeToken<Map<String, Boolean>>() {}.type
        val currentMap: MutableMap<String, Boolean> = if (!oldMap.isNullOrEmpty()) {
            Utils.g().provideGson().fromJson(oldMap, type)
        } else {
            mutableMapOf()
        }
        _uiState.update { state ->
            state.copy(
                listRegisAccNumber = currentMap.filter { it.value }.map { it.key },
            )
        }
    }

    fun isSetupPin() {
        val cifNo = userProf.getCifNo()
        val key = "IS_ENABLE_PIN_$cifNo"
        val state = prefs.getString(key, encrypted = false)
        _uiState.update {
            it.copy(
                setupPinState = when (state) {
                    "ENABLE" -> true
                    else -> false
                },
            )
        }
    }

    private fun setStateSetupPin(isEnable: Boolean) {
        val cifNo = userProf.getCifNo()
        val key = "IS_ENABLE_PIN_$cifNo"
        val state = if (isEnable) "ENABLE" else "DISABLE"
        prefs.setString(key, state, encrypted = false)
        _uiState.update {
            it.copy(setupPinState = isEnable)
        }
    }

    private fun checkBiometricAvailable() {
        if (biometricManager.canAuthenticate()) {
            // Save PIN temporarily and request biometric authentication
            sendEvent(AccountBalanceEvent.ShowBiometricPrompt)
        } else {
            sendEvent(AccountBalanceEvent.ShowBiometricSetupRequired)
        }
    }

    fun onBiometricError(errorMessage: String) {
        sendEvent(AccountBalanceEvent.ShowError(errorMessage))
    }

    fun onBiometricSuccess() {
        setStateSetupPin(false)
    }
}

data class AccountBalanceUiState(
    val tag: AccountType = AccountType.PaymentAccount,
    val listAccNumber: List<String> = listOf(),
    val listRegisAccNumber: List<String> = listOf(),
    val setupPinState: Boolean = false,
    val biometricAvailable: Boolean = false,
)

sealed interface AccountBalanceEvent : OneTimeEvent {
    data object NavigateBackEvent : AccountBalanceEvent
    data object NotificationEvent : AccountBalanceEvent
    data object PinSetupEvent : AccountBalanceEvent
    data object ChangePinEvent : AccountBalanceEvent
    data object ShowBiometricPrompt : AccountBalanceEvent
    data object ShowBiometricSetupRequired : AccountBalanceEvent
    data object NotYetImplemented : AccountBalanceEvent
    data object GotoRegisterLoanAccountAction : AccountBalanceEvent
    data class ShowError(val message: String) : AccountBalanceEvent
}
