package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_manage

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.ui.navigation.OttServiceNavigator
import com.vietinbank.feature_home.ui.screen.ott_setting.OttServiceType
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OttServiceManageFragment : BaseFragment<OttServiceManageViewModel>() {
    override val viewModel: OttServiceManageViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ottServiceNavigator: OttServiceNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                OttServiceManageEvent.NavigateBack -> {
                    appNavigator.popBackStack()
                }
                is OttServiceManageEvent.NavigateSignUpOtt -> {
                    ottServiceNavigator.goToOttService(OttServiceType.SIGN_UP_SERVICE.codeType, event.newRegister)
                }
                OttServiceManageEvent.NavigateSwitchSms -> {
                    ottServiceNavigator.goToOttService(OttServiceType.SWITCH_SERVICE.codeType)
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        OttServiceManageScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }
}