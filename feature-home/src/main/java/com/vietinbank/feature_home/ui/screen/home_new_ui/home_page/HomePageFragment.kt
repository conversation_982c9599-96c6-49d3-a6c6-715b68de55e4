package com.vietinbank.feature_home.ui.screen.home_new_ui.home_page

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.base.dialog.examples.ConfirmationDialog
import com.vietinbank.core_ui.base.dialog.examples.ConfirmationResult
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpDialog
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpResult
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.dialog.SyncBiometricDialog
import com.vietinbank.feature_home.ui.dialog.SyncBiometricResult
import com.vietinbank.feature_home.ui.screen.home.CheckUserEkycEvent
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HomePageFragment : BaseFragment<HomePageViewModel>() {
    override val viewModel: HomePageViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var imageLoader: CoilImageLoader

    override fun onResume() {
        super.onResume()
        viewModel.updateVisibleBalance()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                HomePageEvent.NavigateToSetting -> {
                    appNavigator.goToSetting()
                }

                HomePageEvent.NavigateToOttScreen -> {
                    appNavigator.goToOttDashboard()
                }

                HomePageEvent.NavigateToAccount -> {
                    appNavigator.goToAccountNoAnm()
                }

                HomePageEvent.NavigateToExplore -> {
                    appNavigator.goToListSmartCA()
//                    appNavigator.goToRegisterSmartCA()
//
//                    val encodedItem = URLEncoder.encode("{\"requestId\":\"38TYM6JB\",\"sessionId\":\"CNT2ENWTNKMJ0WEGRWG80G10S1L3640X\",\"status\":{\"code\":\"1\",\"message\":\"Giao dịch ER25091885075 được chuyển đến ngân hàng thành công.\",\"subCode\":\"\"},\"data\":{\"transTime\":\"\",\"transId\":\"\",\"addinfo\":\"\",\"custCode\":\"\",\"addInfoDetail\":[]},\"esignRegister\":{\"mtId\":\"ER25091885075\",\"creator\":\"bym.ctk\",\"createDate\":\"2025-09-18T08:12:38.000+0000\",\"manager\":null,\"verifiedDate\":null,\"cifNo\":\"*********\",\"userName\":\"bym.ctk\",\"esignType\":\"1\",\"serviceType\":\"2\",\"role\":\"6\",\"name\":\"Phan Ha Bym\",\"no\":\"************\",\"serial\":\"540101013054f34adf6782543c36954b\",\"startDate\":null,\"endDate\":\"01/11/2025 02:58:00\",\"esignFrom\":\"VNPT\",\"nameEsign\":\"Phan Hà Phương - TEST Test\",\"idEsign\":\"************\",\"branch\":\"12498\",\"statusCode\":\"04\",\"message\":null,\"statusName\":\"\",\"taxNumber\":\"\",\"hostId\":\"124982025091815123800000077998\",\"statusDsm\":\"00\",\"messError\":\"\",\"chanOrgName\":\"\",\"serviceTypeName\":\"Dịch vụ tín dụng/ tài trợ thương mại/ bảo lãnh\",\"roleName\":\"Người đại diện trước pháp luật\",\"branchName\":\"CN BA DINH HOI SO\",\"provider\":null,\"files\":[{\"id\":\"134497\",\"mtId\":\"ER25091885075\",\"fileName\":\"Đăng kí kiêm cam kết sử dụng CKS.pdf\",\"fileAttach\":null,\"createDate\":\"2025-09-18 15:12:35\",\"creator\":\"bym.ctk\",\"fileType\":null,\"fileContent\":null,\"objectId\":\"0902852080840724\"}],\"createDateDesc\":\"18-09-2025 15:12:38\",\"verifiedDateDesc\":\"\",\"esignTypeName\":\"Cá nhân\",\"startDateDesc\":\"\",\"endDateDesc\":\"\"},\"toaDo\":{\"fileId\":\"134497\",\"pageNo\":0,\"x\":0.0,\"y\":0.0,\"pageHeight\":0.0,\"pageWidth\":0.0,\"file\":null,\"fileByte\":null,\"fileName\":\"Đăng kí kiêm cam kết sử dụng CKS.pdf\",\"keyValue\":\"\"},\"androidKey\":\"\",\"iosKey\":\"\"}", "UTF8")
//                    appNavigator.goToResultSmartCA(
//                        transactionBundle = encodedItem,
//                    )
                }

                HomePageEvent.NotYetImplement -> {
                    showNoticeDialog(
                        getString(R.string.home_update_text),
                    )
                }

                HomePageEvent.NavigateToCt -> {
                    appNavigator.goToMakerTransfer(bundleOf())
                }

                HomePageEvent.NavigateToEditShortcut -> {
                    appNavigator.goToEditShortcut()
                }

                HomePageEvent.NavigateToManager -> {
                    appNavigator.gotoTransactionManager(
                        bundleOf().apply {
                            putString(
                                Tags.COUNT_TRANSACTION_BUNDLE,
                                viewModel.getCountTransactionListJson(),
                            )
                        },
                    )
                }

                HomePageEvent.NavigateToApprovalList -> {
                    // Navigate to Checker Approval List with transaction data
                    // API getTransactionList already called in ViewModel
                    val transaction = viewModel.getCurrentTransaction()
                    if (transaction != null) {
                        // Pass full countTransactionList as JSON for filter options
                        val countTransactionListJson = viewModel.getCountTransactionListJson()
                        printLog("HomePageFragment: Navigating with countTransactionList")
                        appNavigator.goToApprovalList(
                            transaction = viewModel.getTransactionListJson(),
                            tranType = transaction.tranType ?: "",
                            groupType = transaction.groupType ?: "",
                            serviceType = transaction.servicetype ?: "",
                            isBatchFileMode = false,
                            countTransaction = countTransactionListJson,
                        )
                    }
                }

                HomePageEvent.OnClickAllFeature -> {
                }
                HomePageEvent.NavigateToTracePayment -> {
                    appNavigator.goToTracePaymentFragment(bundleOf())
                }
                HomePageEvent.NavigateToPaymentOrder -> {
                    appNavigator.goToMakerPaymentOrder(bundleOf())
                }

                HomePageEvent.ShowUpdateEkycDialog -> {
                    showUpdateEkycDialog()
                }

                CheckUserEkycEvent.NewEkyc -> {
                    appNavigator.goToRegisterEkycScreen()
                }

                CheckUserEkycEvent.ShowGuildToCounter -> showGuideToCounterDialog()
                CheckUserEkycEvent.UpdateEkyc -> {
                    showInfoEkycDialog()
                }

                CheckUserEkycEvent.Nothing -> {}
                is HomePageEvent.GenOtpEkycSuccess -> {
                    if (event.isResend) {
                        (childFragmentManager.findFragmentByTag(VerifyBiometricOtpDialog::class.java.name) as? VerifyBiometricOtpDialog)?.apply {
                            afterCallDone(true, isResend = true)
                        }
                    } else {
                        showOtpDialog()
                    }
                }

                HomePageEvent.VerifyOtpEkycSuccess -> {
                    (childFragmentManager.findFragmentByTag(VerifyBiometricOtpDialog::class.java.name) as? VerifyBiometricOtpDialog)?.apply {
                        afterCallDone(true)
                        dismiss()
                    }
                    appNavigator.gotoUpdateEkycUserInfo()
                }

                HomePageEvent.VerifyOtpEkycFail -> {
                    (
                        childFragmentManager.findFragmentByTag(
                            VerifyBiometricOtpDialog::class.java.name,
                        ) as? VerifyBiometricOtpDialog
                        )?.afterCallDone(false)
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        val sections by viewModel.sections.collectAsState()

        HomePageScreen(
            uiState = uiState,
            sections = sections,
            onAction = viewModel::onAction,
            imageLoader = imageLoader,
        )
    }

    override fun onBackPressed(): Boolean {
        showConfirmDialog(
            message = getString(R.string.home_log_out_guide),
            positiveAction = {
                inactivityManager.stopMonitoring()
                // user confirm => pop
                viewModel.clearSession()
                appNavigator.goToHomePreLoginAndPopAll()
            },
            negativeAction = {
                // user cancel => do nothing
            },
        )
        return true
    }

    private fun showUpdateEkycDialog() {
        ConfirmationDialog.Companion.show(
            fragmentManager = childFragmentManager,
            title = getString(R.string.home_title_notice),
            message = getString(R.string.home_update_ekyc_dialog_guide),
            positiveText = getString(R.string.home_button_positive_ekyc),
            negativeText = getString(R.string.home_button_negative_ekyc),
            iconRes = R.drawable.ic_home_update_ekyc,
        )
        childFragmentManager.setFragmentResultListener(
            ConfirmationDialog.KEY_CONFIRMATION,
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<ConfirmationResult>("key_result")
                result?.let {
                    viewModel.checkEkycEvent()
                }
            }
        }
    }

    private fun showInfoEkycDialog() {
        SyncBiometricDialog.show(
            fragmentManager = childFragmentManager,
        )
        childFragmentManager.setFragmentResultListener(
            SyncBiometricDialog.KEY_SYNC_BIOMETRIC_RESULT,
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<SyncBiometricResult>("key_result")
                result?.let {
                    viewModel.generateOtpRetail()
                }
            } else {
                viewModel.checkNational()
            }
        }
    }

    private fun showGuideToCounterDialog() {
        ConfirmationDialog.Companion.show(
            fragmentManager = childFragmentManager,
            iconRes = R.drawable.home_empty_icon,
            title = getString(R.string.home_title_notice),
            message = getString(R.string.home_show_ekyc_guide_to_counter),
            positiveText = getString(R.string.home_close_text),
        )
    }

    private fun showOtpDialog() {
        val dialog = VerifyBiometricOtpDialog.newInstance(
            phoneNum = viewModel.uiState.value.phoneNum,
            email = viewModel.uiState.value.email,
        )
        dialog.show(
            childFragmentManager,
            VerifyBiometricOtpDialog::class.java.name,
        )

        childFragmentManager.setFragmentResultListener(
            VerifyBiometricOtpDialog.BIOMETRIC_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<VerifyBiometricOtpResult>("key_result")
                result?.let {
                    if (it.isResend) {
                        viewModel.generateOtpRetail(isResend = true)
                    } else {
                        viewModel.verifyOtpEkycRetail(it.otpValue)
                    }
                }
            } else {
                // user cancel
            }
        }
    }
}
