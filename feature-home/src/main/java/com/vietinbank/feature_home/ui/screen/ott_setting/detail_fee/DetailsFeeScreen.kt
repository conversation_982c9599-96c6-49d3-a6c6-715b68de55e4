package com.vietinbank.feature_home.ui.screen.ott_setting.detail_fee

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.extensions.formatAsMoney
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.commonRoundedCornerCard
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.screen.ott_setting.OttServiceType
import com.vietinbank.feature_home.ui.screen.ott_setting.list_account.ListAccountUiState
import kotlin.math.abs
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun DetailsFeeScreen(
    uiState: ListAccountUiState = ListAccountUiState(),
    onBackClick: () -> Unit,
) {
    val (bgColor, textColor) = when {
        (uiState.serviceType == OttServiceType.SIGN_UP_SERVICE && uiState.selectedFee > 0) -> FDS.Colors.paletteGreen50 to FDS.Colors.stateSuccess
        (uiState.serviceType == OttServiceType.SWITCH_SERVICE && uiState.selectedFee > 0) -> FDS.Colors.paletteRed50 to FDS.Colors.stateError
        else -> FDS.Colors.paletteNeutral50 to FDS.Colors.characterPrimary
    }

    val diffFeeTitle =
        if (uiState.serviceType == OttServiceType.SIGN_UP_SERVICE) R.string.details_screen_saving else R.string.details_screen_switch_fee

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(horizontal = FDS.Sizer.Padding.padding8),
    ) {
        FoundationAppBar(
            modifier = Modifier.padding(vertical = FDS.Sizer.Padding.padding8),
            title = stringResource(R.string.detail_fee_title),
            titleStyle = FDS.Typography.headingH2,
            isLightIcon = false,
            onNavigationClick = {
                onBackClick()
            },
            isCustomActionUseSafeClick = false,
        )
        Row(
            Modifier
                .padding(horizontal = FDS.Sizer.Padding.padding16)
                .padding(bottom = FDS.Sizer.Padding.padding16),
        ) {
            FoundationText(
                text = stringResource(R.string.details_screen_guide),
                textAlign = TextAlign.Left,
                style = FDS.Typography.captionCaptionL,
                color = FDS.Colors.characterTertiary,
            )
        }

        Column(
            Modifier
                .commonRoundedCornerCard()
                .padding(horizontal = FDS.Sizer.Padding.padding24),
            Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
        ) {
            Box {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    Column(
                        Modifier
                            .weight(1f)
                            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding16))
                            .border(
                                width = FDS.Sizer.Stroke.stroke105,
                                color = FDS.Colors.paletteNeutral100,
                                shape = RoundedCornerShape(FDS.Sizer.Padding.padding16),
                            )
                            .padding(FDS.Sizer.Padding.padding16),
                        Arrangement.Center,
                        Alignment.CenterHorizontally,
                    ) {
                        FoundationText(
                            stringResource(R.string.details_screen_before_sign_up),
                            style = FDS.Typography.captionL,
                        )
                        Spacer(Modifier.size(FDS.Sizer.Padding.padding4))
                        FoundationText(
                            stringResource(
                                R.string.details_screen_vnd,
                                uiState.smsFee.toString().formatAsMoney(),
                            ),
                            style = FDS.Typography.bodyB2,
                        )
                    }
                    Spacer(Modifier.size(FDS.Sizer.Padding.padding8))
                    Column(
                        Modifier
                            .weight(1f)
                            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding16))
                            .background(bgColor)
                            .border(
                                width = FDS.Sizer.Stroke.stroke105,
                                color = textColor,
                                shape = RoundedCornerShape(FDS.Sizer.Padding.padding16),
                            )
                            .padding(FDS.Sizer.Padding.padding16),
                        Arrangement.Center,
                        Alignment.CenterHorizontally,
                    ) {
                        FoundationText(
                            stringResource(R.string.details_screen_after_sign_up),
                            style = FDS.Typography.captionL,
                            color = textColor,
                        )
                        Spacer(Modifier.size(FDS.Sizer.Padding.padding4))
                        FoundationText(
                            stringResource(
                                R.string.details_screen_vnd,
                                uiState.finalFee.toString().formatAsMoney(),
                            ),
                            style = FDS.Typography.bodyB2,
                            color = textColor,
                        )
                    }
                }
                Box(
                    modifier = Modifier
                        .matchParentSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        modifier = Modifier
                            .size(FDS.Sizer.Padding.padding32)
                            .background(FDS.Colors.white, CircleShape)
                            .padding(FDS.Sizer.Padding.padding4),
                        painter = painterResource(R.drawable.ic_detail_fee_arrow),
                        contentDescription = "",
                        tint = textColor,
                    )
                }
            }
            DetailComponent(
                title = stringResource(R.string.details_screen_before_sign_up),
                smsAmount = uiState.accountSMS.toString(),
                ottAmount = uiState.accountOTT.toString(),
                fee = uiState.smsFee.toString().formatAsMoney(),
            )
            DetailComponent(
                title = stringResource(R.string.details_screen_after_sign_up),
                smsAmount = uiState.finalAccountSMS.toString(),
                ottAmount = uiState.finalAccountOTT.toString(),
                difFee = abs(uiState.selectedFee).toString().formatAsMoney(),
                fee = uiState.finalFee.toString().formatAsMoney(),
                color = textColor,
                diffFeeTitle = diffFeeTitle,
            )
        }
    }
}

@Composable
private fun DetailComponent(
    modifier: Modifier = Modifier,
    title: String,
    smsAmount: String,
    ottAmount: String,
    fee: String,
    difFee: String? = null,
    color: Color = FDS.Colors.stateSuccess,
    diffFeeTitle: Int = R.string.details_screen_saving,
) {
    Column(
        modifier,
        Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Row(
            Modifier,
            Arrangement.Center,
            Alignment.CenterVertically,
        ) {
            FoundationText(
                title,
                color = FDS.Colors.characterTertiary,
                style = FDS.Typography.captionL,
            )
            Box(
                modifier = Modifier
                    .padding(start = FDS.Sizer.Padding.padding8)
                    .weight(1f)
                    .height(FoundationDesignSystem.Sizer.Stroke.stroke1)
                    .background(FoundationDesignSystem.Colors.strokeDivider),
            )
        }
        Row(
            Modifier,
            Arrangement.Center,
            Alignment.CenterVertically,
        ) {
            FoundationText(
                stringResource(R.string.details_screen_sms_title),
                modifier = Modifier.weight(1f),
                color = FDS.Colors.characterSecondary,
                style = FDS.Typography.captionL,
            )
            FoundationText(
                stringResource(R.string.details_screen_account, smsAmount),
                modifier = Modifier.weight(1f),
                style = FDS.Typography.captionLSemibold,
            )
        }
        Row(
            Modifier,
            Arrangement.Center,
            Alignment.CenterVertically,
        ) {
            FoundationText(
                stringResource(R.string.details_screen_title_2),
                modifier = Modifier.weight(1f),
                color = FDS.Colors.characterSecondary,
                style = FDS.Typography.captionL,
            )
            FoundationText(
                stringResource(R.string.details_screen_account, ottAmount),
                modifier = Modifier.weight(1f),
                style = FDS.Typography.captionLSemibold,
            )
        }
        if (!difFee.isNullOrBlank()) {
            Row(
                Modifier,
                Arrangement.Center,
                Alignment.CenterVertically,
            ) {
                FoundationText(
                    stringResource(diffFeeTitle),
                    modifier = Modifier.weight(1f),
                    color = FDS.Colors.characterSecondary,
                    style = FDS.Typography.captionL,
                )
                FoundationText(
                    stringResource(R.string.details_screen_vnd_month, difFee),
                    modifier = Modifier.weight(1f),
                    style = FDS.Typography.captionLBold,
                    color = color,
                )
            }
        }
        Row(
            Modifier,
            Arrangement.Center,
            Alignment.CenterVertically,
        ) {
            FoundationText(
                stringResource(R.string.details_screen_total_fee),
                modifier = Modifier.weight(1f),
                color = FDS.Colors.characterSecondary,
                style = FDS.Typography.captionL,
            )
            FoundationText(
                stringResource(R.string.details_screen_vnd_month, fee),
                modifier = Modifier.weight(1f),
                style = FDS.Typography.captionLBold,
                color = FDS.Colors.characterHighlighted,
            )
        }
    }
}
