package com.vietinbank.feature_home.ui.navigation

import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.feature_home.R

class OttServiceNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : OttServiceNavigator {

    private fun createSlideNavOptions(): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)

        return builder.build()
    }

    override fun goToOttService(serviceType: String, newRegister: Boolean) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.OTT_SERVICE_TYPE, serviceType)
            putBoolean(Tags.OTT_SERVICE_NEW_REGISTER, newRegister)
        }
        navController.navigate(R.id.ottServiceFragment, bundle, navOptions)
    }

    override fun goToListAccountFragment(
        serviceType: String,
        aliasName: String,
        data: String,
        cifNo: String,
        phoneNum: String,
        feeSMS: Int,
        accountSMS: Int,
        feeAmount: String,
        alertType: String,
    ) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.OTT_SERVICE_TYPE, serviceType)
            putString(Tags.OTT_SERVICE_RESULT_DATA, data)
            putString(Tags.OTT_SERVICE_RESULT_CIF, cifNo)
            putString(Tags.OTT_SERVICE_RESULT_PHONE, phoneNum)
            putInt(Tags.OTT_SERVICE_RESULT_FEE_SMS, feeSMS)
            putInt(Tags.OTT_SERVICE_RESULT_ACCOUNT_SMS, accountSMS)
            putString(Tags.OTT_SERVICE_RESULT_ACCOUNT_FEE_AMOUNT, feeAmount)
            putString(Tags.OTT_SERVICE_RESULT_ACCOUNT_ALERT_TYPE, alertType)
        }
        navController.navigate(R.id.listAccountFragment, bundle, navOptions)
    }

    override fun popToOttManage() {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.ottServiceManageFragment, true) // xóa toàn bộ back stack
            .build()
        navController.navigate(R.id.ottServiceManageFragment, null, navOptions)
    }

    override fun goToDetailFee() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.detailsFeeFragment, null, navOptions)
    }

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun popBackGraph() {
        navController.popBackStack(R.id.ott_service_nav_graph, false)
    }
}