package com.vietinbank.feature_home.ui.screen.ott_setting.ott_service_new_ui

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.maskPhoneNumber
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpDialog
import com.vietinbank.core_ui.components.dialog.VerifyBiometricOtpResult
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.navigation.OttServiceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class OttServiceFragment : BaseFragment<OttServiceViewModel>() {
    override val viewModel: OttServiceViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ottServiceNavigator: OttServiceNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.setupData(
            arguments?.getString(Tags.OTT_SERVICE_TYPE) ?: "",
            arguments?.getBoolean(Tags.OTT_SERVICE_NEW_REGISTER) ?: false,
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                is OttServiceEvent.ShowOtpDialog -> {
                    if (event.isResend) {
                        (childFragmentManager.findFragmentByTag(VerifyBiometricOtpDialog::class.java.name) as? VerifyBiometricOtpDialog)?.apply {
                            afterCallDone(true, isResend = true)
                        }
                    } else {
                        showOtpDialog(
                            validTime = event.validTime,
                            resendTime = event.resendTime,
                        )
                    }
                }

                OttServiceEvent.VerifyOtpFail -> {
                    (
                        childFragmentManager.findFragmentByTag(
                            VerifyBiometricOtpDialog::class.java.name,
                        ) as? VerifyBiometricOtpDialog
                        )?.afterCallDone(false)
                }

                OttServiceEvent.EmptyListDataSignUp -> {
                    dismissAllDialogs()
                    showNoticeDialog(
                        getString(R.string.ott_service_empty_list_sign_up),
                    )
                }

                OttServiceEvent.EmptyListDataSwitch -> {
                    dismissAllDialogs()
                    showNoticeDialog(
                        getString(R.string.ott_service_empty_list_switch),
                    )
                }

                OttServiceEvent.ShowLockOtp -> {
                    dismissAllDialogs()
                    showNoticeDialog(
                        getString(R.string.ott_service_lock_otp),
                    )
                }

                is OttServiceEvent.NavigateToListAccountFragment -> {
                    dismissAllDialogs()
                    ottServiceNavigator.goToListAccountFragment(
                        serviceType = viewModel.uiState.value.serviceType.codeType,
                        data = Utils.g().provideGson().toJson(event.data),
                        cifNo = viewModel.uiState.value.cifNo,
                        aliasName = viewModel.uiState.value.aliasName,
                        phoneNum = viewModel.uiState.value.phoneNum,
                        feeSMS = event.feeSMS,
                        accountSMS = event.accountSMS,
                        alertType = viewModel.uiState.value.alertType.alertType,
                        feeAmount = event.feeAmount,
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        OttServiceScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
            onBackClick = { appNavigator.popBackStack() },
        )
    }

    private fun showOtpDialog(validTime: Int, resendTime: Int) {
        val dialog = VerifyBiometricOtpDialog.newInstance(
            otpValiditySeconds = validTime,
            resendTime = resendTime,
            phoneNum = viewModel.uiState.value.phoneNum.maskPhoneNumber(),
        )
        dialog.show(
            childFragmentManager,
            VerifyBiometricOtpDialog::class.java.name,
        )

        childFragmentManager.setFragmentResultListener(
            VerifyBiometricOtpDialog.BIOMETRIC_KEY,
            viewLifecycleOwner,
        ) { _, bundle ->
            val isCancelled = bundle.getBoolean("key_cancelled", false)
            if (!isCancelled) {
                val result = bundle.getParcelable<VerifyBiometricOtpResult>("key_result")
                result?.let {
                    if (it.isResend) {
                        viewModel.smsOtpCreate(isResend = true)
                    } else {
                        viewModel.smsOtpVerify(it.otpValue)
                    }
                }
            } else {
                // user cancel
            }
        }
    }
}