package com.vietinbank.feature_home.ui.screen.setting.newui.fragment

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.feature_home.ui.screen.setting.newui.SettingItemDestination
import com.vietinbank.feature_home.ui.screen.setting.newui.screen.NewUISettingScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class NewUISettingFragment : BaseFragment<NewUISettingViewModel>() {
    override val viewModel: NewUISettingViewModel by viewModels()

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override val useCompose: Boolean = true

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsStateWithLifecycle()
        AppTheme {
            NewUISettingScreen(
                modifier = Modifier
                    .fillMaxSize()
                    .eFastBackground()
                    .systemBarsPadding()
                    .imePadding(),
                uiState = uiState,
                onStaticItemClicked = {
                    when (it) {
                        SettingItemDestination.AccountInfo -> {}
                        SettingItemDestination.AuthenticationMethods -> {
                            appNavigator.goToEnterPIN("test")
                        }
                        SettingItemDestination.BillContacts -> {}
                        SettingItemDestination.BiometricUpdate -> {}
                        SettingItemDestination.ChangeAvatar -> {}
                        SettingItemDestination.ChangeLanguage -> {}
                        SettingItemDestination.ChangePassword -> {}
                        SettingItemDestination.ChangeThemeAndFont -> {}
                        SettingItemDestination.CustomerSupport -> {}
                        SettingItemDestination.Faq -> {}
                        SettingItemDestination.Logout -> {}
                        SettingItemDestination.ManageDevices -> {}
                        SettingItemDestination.ManageNotification -> {
                            appNavigator.goToOttServiceManage()
                        }
                        SettingItemDestination.ShareApp -> {}
                        SettingItemDestination.TransactionLimit -> {}
                        SettingItemDestination.TransactionLocations -> {}
                        SettingItemDestination.UserGuide -> {}
                    }
                },
                onFaceLoginCheckedChange = viewModel::onFaceLoginChange,
                onLogout = {
                    showConfirmDialog(
                        message = "Bạn có chắc muốn logout?",
                        positiveAction = {
                            // Stop InactivityManager monitoring khi logout
                            inactivityManager.stopMonitoring()
                            viewModel.clearSession()
                            viewModel.transferCacheManager.clearTransferALL()
                            viewModel.ottRegistrationRepository.clearAllOttCache()
                            appNavigator.goToHomePreLoginAndPopAll()
                        },
                        negativeAction = {
                            // user cancel => do nothing
                        },
                    )
                },
                onBack = {
                    appNavigator.popBackStack()
                },
            )
        }
    }
}