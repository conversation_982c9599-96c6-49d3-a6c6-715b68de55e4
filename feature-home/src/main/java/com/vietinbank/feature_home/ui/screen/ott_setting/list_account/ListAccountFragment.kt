package com.vietinbank.feature_home.ui.screen.ott_setting.list_account

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.activityViewModels
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.navigation.OttServiceNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ListAccountFragment : BaseFragment<ListAccountViewModel>() {
    override val viewModel: ListAccountViewModel by activityViewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var ottServiceNavigator: OttServiceNavigator
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val listJson = arguments?.getString(Tags.OTT_SERVICE_RESULT_DATA)
        viewModel.setAccountsState(
            serviceType = arguments?.getString(Tags.OTT_SERVICE_TYPE) ?: "",
            data = listJson,
            cifNo = arguments?.getString(Tags.OTT_SERVICE_RESULT_CIF) ?: "",
            phoneNum = arguments?.getString(Tags.OTT_SERVICE_RESULT_PHONE) ?: "",
            feeSMS = arguments?.getInt(Tags.OTT_SERVICE_RESULT_FEE_SMS) ?: 0,
            accountSMS = arguments?.getInt(Tags.OTT_SERVICE_RESULT_ACCOUNT_SMS) ?: 0,
            alertType = arguments?.getString(Tags.OTT_SERVICE_RESULT_ACCOUNT_ALERT_TYPE) ?: "",
            feeAmount = arguments?.getString(Tags.OTT_SERVICE_RESULT_ACCOUNT_FEE_AMOUNT) ?: "0",
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                ListAccountEvent.NavigateToDetails -> {
                    ottServiceNavigator.goToDetailFee()
                }
                ListAccountEvent.UpdateSuccess -> {
                    // check pin and show dialog
                    showNoticeDialog(
                        getString(R.string.list_account_update_success),
                        icon = R.drawable.ic_home_update_success,
                        positiveAction = {
                            ottServiceNavigator.popToOttManage()
                        },
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()
        ListAccountScreen(
            uiState = uiState,
            onBackClick = {
                ottServiceNavigator.popBackStack()
            },
            onAction = viewModel::onAction,
            listItem = viewModel.listItem,
            listSearch = viewModel.filteredList,
        )
    }
}