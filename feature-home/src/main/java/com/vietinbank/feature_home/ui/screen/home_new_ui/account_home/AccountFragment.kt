package com.vietinbank.feature_home.ui.screen.home_new_ui.account_home

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.feature_home.R
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AccountFragment : BaseFragment<AccountViewModel>() {
    override val viewModel: AccountViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.isReloadingData()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleSingleEvent { event ->
            when (event) {
                AccountEvent.NotYetImplement -> {
                    showNoticeDialog(
                        getString(R.string.home_update_text),
                    )
                }
                AccountEvent.NavigateToHome -> {
                    appNavigator.popToHomeFromAccount()
                }
                AccountEvent.NavigateToSetting -> {
                    appNavigator.goToSetting()
                }
                AccountEvent.NavigateToOttScreen -> {
                    appNavigator.goToOttDashboard()
                }
                AccountEvent.NavigateToExplore -> {
                    appNavigator.goToMakerTransfer(bundleOf())
                }
                AccountEvent.NavigateToEditShortcut -> {
                    appNavigator.goToEditShortcut()
                }
                AccountEvent.NavigateToPayment -> {
                    appNavigator.goToInquiryAccount("DDA")
                }
                AccountEvent.NavigateToLoan -> {
                    appNavigator.goToInquiryAccount("LN")
                }
                AccountEvent.NavigateToSaving -> {
                    appNavigator.goToInquiryAccount("CD")
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.uiState.collectAsState()

        AccountScreen(
            uiState = uiState,
            onAction = viewModel::onAction,
        )
    }

    override fun onBackPressed(): Boolean {
        appNavigator.popToHomeFromAccount()
        return true
    }
}