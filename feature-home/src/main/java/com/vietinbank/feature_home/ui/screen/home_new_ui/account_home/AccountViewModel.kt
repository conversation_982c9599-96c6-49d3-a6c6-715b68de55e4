package com.vietinbank.feature_home.ui.screen.home_new_ui.account_home

import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.formatAsMoney
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.CustomEncryptedPrefs
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.home.FunctionItemData
import com.vietinbank.core_domain.models.home.HomeAccountListDomain
import com.vietinbank.core_domain.models.home.HomeAccountListParams
import com.vietinbank.core_domain.models.home.ListFunctionParams
import com.vietinbank.core_domain.repository.cache.IOttRegistrationRepository
import com.vietinbank.core_domain.usecase.home.HomeUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.OneTimeEvent
import com.vietinbank.feature_home.R
import com.vietinbank.feature_home.ui.view.LoanUiModel
import com.vietinbank.feature_home.ui.view.PaymentUiModel
import com.vietinbank.feature_home.ui.view.SavingUiModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class AccountViewModel @Inject constructor(
    private val homeUseCase: HomeUseCase,
    override val sessionManager: ISessionManager,
    private val prefs: CustomEncryptedPrefs,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    val ottRegistrationRepository: IOttRegistrationRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(AccountUiState())
    val uiState: StateFlow<AccountUiState> = _uiState.asStateFlow()

    val listAccountType = listOf(AccountType.Payment, AccountType.Saving, AccountType.Loan)

    init {
        _uiState.update {
            it.copy(
                userName = userProf.getFullName() ?: "",
                companyName = userProf.getCorpName() ?: " ",
                roleName = when (userProf.getRoleLevel()) {
                    Tags.ROLE_MAKER -> R.string.account_card_company_name_maker
                    else -> R.string.account_card_company_name_checker
                },
            )
        }
    }

    fun isReloadingData() {
        if (prefs.getBoolean(Tags.IS_RELOAD_ACCOUNT)) {
            listAccountType.forEach { homeAccountList(it) }
            return
        }

        listAccountType.forEach { type ->
            val cached = prefs.getString(getAccountKeyByType(type))
            if (cached != null) {
                _uiState.update {
                    it.updateAccount(
                        type = type,
                        total = cached,
                        loading = false,
                    )
                }
            } else {
                homeAccountList(type)
            }
        }
    }

    fun onAction(action: AccountAction) {
        when (action) {
            is AccountAction.OnNavigationTabSelected -> {
                when (action.index) {
                    0 -> {
                        sendEvent(AccountEvent.NavigateToHome)
                    }

                    2 -> {
                        sendEvent(AccountEvent.NavigateToExplore)
                    }

                    else -> {}
                }
            }

            is AccountAction.OnTabSelected -> {
                _uiState.update {
                    it.copy(
                        tabSelected = action.index,
                    )
                }
            }

            AccountAction.TestTest -> sendEvent(AccountEvent.NotYetImplement)
            AccountAction.OnClickAvatar -> {
                sendEvent(AccountEvent.NavigateToSetting)
            }

            AccountAction.OnClickNotification -> {
                sendEvent(AccountEvent.NavigateToOttScreen)
            }

            AccountAction.ClickShortcut -> {
                if (_uiState.value.isShowShortcut) {
                    _uiState.update {
                        it.copy(
                            isShowShortcut = false,
                        )
                    }
                } else {
                    getFavouriteList()
                }
            }

            AccountAction.ClickEditShortcut -> {
                sendEvent(AccountEvent.NavigateToEditShortcut)
                _uiState.update {
                    it.copy(
                        isShowShortcut = false,
                    )
                }
            }
            AccountAction.CLickLoan -> sendEvent(AccountEvent.NavigateToLoan)
            AccountAction.CLickPayment -> sendEvent(AccountEvent.NavigateToPayment)
            AccountAction.CLickSaving -> sendEvent(AccountEvent.NavigateToSaving)
        }
    }

    private fun homeAccountList(type: AccountType) {
        prefs.setBoolean(Tags.IS_RELOAD_ACCOUNT, false)
        launchJob(showLoading = false) {
            // Set loading
            _uiState.update { it.updateAccount(type, loading = true) }

            val res = homeUseCase.homeAccountList(
                HomeAccountListParams(
                    username = userProf.getUserName().orEmpty(),
                    accountType = type.code,
                    isBalance = Tags.IS_BALANCE,
                ),
            )

            handleResource(
                res,
                onSuccess = { data ->
                    val total = data.toTotal(type)
                    _uiState.update {
                        it.updateAccount(
                            type = type,
                            total = total,
                            loading = false,
                        )
                    }
                    prefs.setString(getAccountKeyByType(type), total)
                },
                handleError = {
                    _uiState.update {
                        it.updateAccount(type, loading = false)
                    }
                },
            )
        }
    }

    /** Extension để rút gọn update theo AccountType */
    private fun AccountUiState.updateAccount(
        type: AccountType,
        total: String? = null,
        loading: Boolean,
    ): AccountUiState = when (type) {
        AccountType.Saving -> copy(
            saving = SavingUiModel(total = total ?: saving.total, loading = loading),
        )
        AccountType.Payment -> copy(
            payment = PaymentUiModel(total = total ?: payment.total, loading = loading),
        )
        AccountType.Loan -> copy(
            loan = LoanUiModel(total = total ?: loan.total, loading = loading),
        )
    }

    private fun HomeAccountListDomain.toTotal(type: AccountType): String = when (type) {
        AccountType.Saving -> cdSumAmount?.formatAsMoney() ?: "0"
        AccountType.Payment -> assetAmount?.formatAsMoney() ?: "0"
        AccountType.Loan -> lnSumAmount?.formatAsMoney() ?: "0"
    }

    private fun getAccountKeyByType(type: AccountType): String = when (type) {
        AccountType.Saving -> Tags.SAVING_HOME_ACCOUNT
        AccountType.Payment -> Tags.PAYMENT_HOME_ACCOUNT
        AccountType.Loan -> Tags.LOAN_HOME_ACCOUNT
    }

    private fun listFavoriteFunction() {
        launchJob(showLoading = true) {
            val res = homeUseCase.listFavoriteFunction(
                ListFunctionParams(
                    username = userProf.getUserName().orEmpty(),
                    roleId = userProf.getRoleId().toString(),
                    role = userProf.getRoleId().toString(),
                    packageId = userProf.getFinancialPackage(),
                ),
            )
            handleResource(res) { data ->
                val func = data.data ?: listOf()
                val newList = func.map {
                    FunctionItemData(
                        iconRes = R.drawable.ic_home_transfer,
                        functionName = it?.functionName ?: "",
                        functionId = it?.functionId ?: "",
                        groupName = it?.groupName ?: "",
                        groupId = it?.groupId ?: "",
                        orderNumber = it?.orderNumber?.toInt() ?: 0,
                    )
                }.sortedBy {
                    it.orderNumber
                }

//                    if (func.isEmpty()) {
//                    defaultFunc(
//                        financialPackage = userProf.getFinancialPackage() ?: "",
//                        roleLevel = userProf.getRoleLevel() ?: "",
//                    )
//                } else {
//
//                }
                prefs.setString(Tags.LIST_FAVOURITE, Utils.g().provideGson().toJson(newList))
                _uiState.update {
                    it.copy(
                        isShowShortcut = true,
                        listFuncShortcut = newList,
                    )
                }
            }
        }
    }

    private fun getFavouriteList() {
        val data = prefs.getString(Tags.LIST_FAVOURITE)
        val type = object : TypeToken<List<FunctionItemData>>() {}.type
        if (!data.isNullOrEmpty()) {
            val list: List<FunctionItemData> = Utils.g().provideGson().fromJson(data, type)
            val listFav = list.map {
                it.copy(
                    iconRes = R.drawable.ic_home_transfer,
                    functionName = it.functionName ?: "",
                    functionId = it.functionId ?: "",
                    groupName = it.groupName ?: "",
                    groupId = it.groupId ?: "",
                    orderNumber = it.orderNumber,
                )
            }
            _uiState.update {
                it.copy(
                    isShowShortcut = true,
                    listFuncShortcut = listFav,
                )
            }
        } else {
            listFavoriteFunction()
        }
    }
}

enum class AccountType(val code: String) {
    Saving("CD"),
    Payment("DDA"),
    Loan("LN"),
}

data class AccountUiState(
    val tabSelected: Int = 0,
    val userName: String = "",
    val companyName: String = "",
    val bottomNavIndex: Int = 1,
    val isShowShortcut: Boolean = false,
    val payment: PaymentUiModel = PaymentUiModel(),
    val saving: SavingUiModel = SavingUiModel(),
    val loan: LoanUiModel = LoanUiModel(),
//    val creditState: CreditUiModel? = null,
    val listFuncShortcut: List<FunctionItemData> = listOf(),
    val roleName: Int = R.string.account_card_company_name_maker,
)

sealed class AccountEvent : OneTimeEvent {
    data object NotYetImplement : AccountEvent()
    data object NavigateToOttScreen : AccountEvent()
    data object NavigateToSetting : AccountEvent()
    data object NavigateToExplore : AccountEvent()
    data object NavigateToHome : AccountEvent()
    data object NavigateToEditShortcut : AccountEvent()
    data object NavigateToPayment : AccountEvent()
    data object NavigateToLoan : AccountEvent()
    data object NavigateToSaving : AccountEvent()
}

sealed interface AccountAction {
    data class OnTabSelected(val index: Int) : AccountAction
    data class OnNavigationTabSelected(val index: Int) : AccountAction
    data object OnClickAvatar : AccountAction
    data object OnClickNotification : AccountAction
    data object ClickShortcut : AccountAction
    data object ClickEditShortcut : AccountAction
    data object CLickPayment : AccountAction
    data object CLickLoan : AccountAction
    data object CLickSaving : AccountAction
    data object TestTest : AccountAction
}