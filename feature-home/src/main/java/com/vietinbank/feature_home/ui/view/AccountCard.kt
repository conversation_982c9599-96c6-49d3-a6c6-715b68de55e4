package com.vietinbank.feature_home.ui.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.base.views.StrokeLine
import com.vietinbank.core_ui.components.ShimmerBlock
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.dismissRippleClickable
import com.vietinbank.feature_home.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

data class PaymentUiModel(
    val loading: Boolean = false,
    val total: String = "",
)

data class SavingUiModel(
    val loading: Boolean = false,
    val total: String = "",
)

data class LoanUiModel(
    val loading: Boolean = false,
    val total: String = "",
)

@Composable
fun AssetCard(
    paymentUiModel: PaymentUiModel,
    savingUiModel: SavingUiModel,
    loanUiModel: LoanUiModel,
    paymentAction: () -> Unit,
    savingAction: () -> Unit,
    loanAction: () -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Padding.padding32))
            .background(Color.White)
            .padding(vertical = FDS.Sizer.Padding.padding24),
        verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),
    ) {
        Column(
            Modifier.padding(horizontal = FDS.Sizer.Padding.padding24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding16),

        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
            ) {
                FoundationText(
                    text = stringResource(R.string.home_asset_card_title),
                    color = FDS.Colors.characterHighlighted,
                    style = FDS.Typography.headingH2,
                )
                FoundationText(
                    text = stringResource(R.string.home_account_description),
                    color = FDS.Colors.characterSecondary,
                    style = FDS.Typography.headingH6,
                )
            }

            AssetItem(
                isLoading = paymentUiModel.loading,
                total = paymentUiModel.total,
                title = stringResource(R.string.home_asset_payment_title),
                onClick = paymentAction,
            )

            AssetItem(
                isLoading = savingUiModel.loading,
                total = savingUiModel.total,
                title = stringResource(R.string.home_asset_saving_title),
                onClick = savingAction,
            )

            AssetItem(
                isLoading = loanUiModel.loading,
                total = loanUiModel.total,
                title = stringResource(R.string.home_credit_card_title_total),
                onClick = loanAction,
            )
        }
        StrokeLine()
        FoundationText(
            text = stringResource(R.string.home_account_note),
            modifier = Modifier
                .padding(horizontal = FDS.Sizer.Padding.padding24),
            color = FDS.Colors.characterPrimary,
            style = FDS.Typography.captionCaptionL,
        )
    }
}

@Composable
private fun AssetItem(
    isLoading: Boolean,
    total: String,
    title: String,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier.dismissRippleClickable {
            onClick()
        },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Column(
            Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Padding.padding8),
        ) {
            FoundationText(
                text = title,
                color = FDS.Colors.tabTextInactive,
                style = FDS.Typography.captionCaptionL,
            )
            when {
                isLoading -> {
                    ShimmerBlock(
                        modifier = Modifier
                            .padding(end = FDS.Sizer.Padding.padding16)
                            .fillMaxWidth()
                            .height(FoundationDesignSystem.Sizer.Padding.padding24),
                    )
                }

                total.isBlank() -> {
                    FoundationText(
                        text = "Không có dữ liệu",
                        color = FDS.Colors.stateError,
                        style = FDS.Typography.captionCaptionL,
                    )
                }

                else -> {
                    FoundationText(
                        text = stringResource(R.string.home_account_vnd, total),
                        color = FDS.Colors.characterHighlighted,
                        style = FDS.Typography.bodyB1Emphasized,
                    )
                }
            }
        }
        Image(
            painter = painterResource(R.drawable.ic_home_account_arrow_right),
            contentDescription = "",
        )
    }
}
