package com.vietinbank.feature_transaction_manage.di

import com.vietinbank.feature_transaction_manage.fragment.render.Bulk300ManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.Bulk5000ManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.CustomsInlandManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.DefaultManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.DisbursementManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.FileNSNNManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.GuaranteeAmendManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.GuaranteeIssueManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.GuaranteeManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.InfrastructureManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.LookupManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.PaymentOrderManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.RenderManagerFactory
import com.vietinbank.feature_transaction_manage.fragment.render.SalaryManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.TransferForeignManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.TransferManagerImpl
import com.vietinbank.feature_transaction_manage.fragment.render.UnLockUserManagerImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object MRenderModule {
    @Provides
    @Singleton
    fun provideRenderManagerFactory(
        defaultManagerImpl: DefaultManagerImpl,
        transferManagerImpl: TransferManagerImpl,
        disbursementManagerImpl: DisbursementManagerImpl,
        lookupManagerImpl: LookupManagerImpl,
        salaryManagerImpl: SalaryManagerImpl,
        guaranteeManagerImpl: GuaranteeManagerImpl,
        guaranteeAmendManagerImpl: GuaranteeAmendManagerImpl,
        guaranteeIssueManagerImpl: GuaranteeIssueManagerImpl,
        customsInlandManagerImpl: CustomsInlandManagerImpl,
        fileNSNNManagerImpl: FileNSNNManagerImpl,
        infrastructureManagerImpl: InfrastructureManagerImpl,
        transferForeignManagerImpl: TransferForeignManagerImpl,
        unLockUserManagerImpl: UnLockUserManagerImpl,
        bulkManagerImpl: Bulk300ManagerImpl,
        orderManagerImpl: PaymentOrderManagerImpl,
        bulk5000ManagerImpl: Bulk5000ManagerImpl,
    ): RenderManagerFactory = RenderManagerFactory(
        defaultManagerImpl,
        transferManagerImpl,
        disbursementManagerImpl,
        lookupManagerImpl,
        salaryManagerImpl,
        guaranteeManagerImpl,
        guaranteeAmendManagerImpl,
        guaranteeIssueManagerImpl,
        customsInlandManagerImpl,
        fileNSNNManagerImpl,
        infrastructureManagerImpl,
        transferForeignManagerImpl,
        unLockUserManagerImpl,
        bulkManagerImpl,
        orderManagerImpl,
        bulk5000ManagerImpl,
    )
}