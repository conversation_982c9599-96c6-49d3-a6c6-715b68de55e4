package com.vietinbank.feature_transaction_manage.di

import android.os.Bundle

/**
 * Created by vandz on 27/2/25.
 */
interface IMTransactionNavigator {
    fun popBackStack() // optional
    fun setFragmentResult(requestKey: String, result: Bundle)

    fun goToDetailsTransaction(vararg pair: Pair<String, Any?>)
    fun goToNewDetailsTransaction(vararg pair: Pair<String, Any?>)
    fun goToListTransaction(vararg pair: Pair<String, Any?>)
}