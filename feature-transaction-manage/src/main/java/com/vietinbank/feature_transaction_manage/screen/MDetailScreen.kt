package com.vietinbank.feature_transaction_manage.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransactionDetailActions
import com.vietinbank.core_ui.base.compose.BaseAppBar
import com.vietinbank.core_ui.base.compose.BaseButton
import com.vietinbank.core_ui.base.compose.BaseText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel

@Composable
fun MDetailScreen(
    viewModel: DetailManagerViewModel,
    actions: TransactionDetailActions? = null,
) {
    val detailTrans by viewModel.detailTransState.collectAsState()
    val renderer by viewModel.renderer.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .background(AppColors.itemBackground),
    ) {
        val appBarActions = mutableListOf<AppBarAction>()
        appBarActions.add(
            AppBarAction(
                icon = com.vietinbank.core_ui.R.drawable.ic_home,
                contentDescription = "HOME",
                tint = Color.White,
                onClick = actions?.onHomeClick ?: {},
            ),
        )
        // AppBar
        BaseAppBar(
            title = "Thông tin chi tiết",
            onBackClick = actions?.onBackClick ?: {},
            actions = appBarActions,
        )

        // Content
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f),
        ) {
            renderer?.RenderViewDetail(
                transaction = detailTrans,
                onFieldClick = { event ->
                    when (event) {
                        is TransactionFieldEvent.FileAttachmentClick -> {
                            viewModel.getDownloadFileID(event.file)
                        }

                        is TransactionFieldEvent.ProfileAttachmentClick -> {
                            viewModel.getDownloadFileID(event.file)
                        }
                        is TransactionFieldEvent.ListAttachmentClick -> {
                        }
                        is TransactionFieldEvent.FileBaseAttachmentClick -> {
                        }

                        TransactionFieldEvent.ViewAttachmentDocument -> {
                        }
                    }
                },
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (viewModel.isHasUNC(detailTrans?.tranType, detailTrans?.status)) {
                BaseText(
                    text = "Tải UNC",
                    leftDrawable = com.vietinbank.core_ui.R.drawable.ic_download_20,
                    textSize = 14.sp,
                    color = AppColors.blue02,
                    fontCus = 5,
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .background(AppColors.white, RoundedCornerShape(20.dp))
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    onClick = {
                        actions?.onUncClick?.invoke(detailTrans!!)
                    },
                )
            }

            if (viewModel.isHasBM01C102(detailTrans?.tranType)) {
                BaseText(
                    text = "BM 01",
                    leftDrawable = com.vietinbank.core_ui.R.drawable.ic_download_20,
                    textSize = 14.sp,
                    color = AppColors.blue02,
                    fontCus = 5,
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .background(AppColors.white, RoundedCornerShape(20.dp))
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    onClick = {
                        actions?.onBM01Click?.invoke(detailTrans!!)
                    },
                )
            }

            if (viewModel.isHasBM01C102(detailTrans?.tranType)) {
                BaseText(
                    text = "C1-02",
                    leftDrawable = com.vietinbank.core_ui.R.drawable.ic_download_20,
                    textSize = 14.sp,
                    color = AppColors.blue02,
                    fontCus = 5,
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .background(AppColors.white, RoundedCornerShape(20.dp))
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    onClick = {
                        actions?.onC102Click?.invoke(detailTrans!!)
                    },
                )
            }

            if (viewModel.isHasC102COCKS(detailTrans?.tranType, detailTrans?.status)) {
                BaseText(
                    text = "C102 CÓ CKS",
                    leftDrawable = com.vietinbank.core_ui.R.drawable.ic_download_20,
                    textSize = 14.sp,
                    color = AppColors.blue02,
                    fontCus = 5,
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .background(AppColors.white, RoundedCornerShape(20.dp))
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    onClick = {
                        actions?.onC102COCKSClick?.invoke(detailTrans!!)
                    },
                )
            }

            if (viewModel.isHasUNCCKS(detailTrans?.tranType, detailTrans?.status)) {
                BaseText(
                    text = "Tải UNC có CKS",
                    leftDrawable = com.vietinbank.core_ui.R.drawable.ic_download_20,
                    textSize = 14.sp,
                    color = AppColors.blue02,
                    fontCus = 5,
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .background(AppColors.white, RoundedCornerShape(20.dp))
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    onClick = {
                        actions?.onUncCKSClick?.invoke(detailTrans!!)
                    },
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))
        if (renderer?.isWaitingApproval(detailTrans?.tranType, detailTrans?.status) == true) {
            Spacer(modifier = Modifier.height(8.dp))
            BaseButton(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 4.dp)
                    .safeClickable { actions?.onCheckApprovedClick?.invoke() },
                "Kiểm tra danh sách cấp phê duyệt",
            )
        }

        if (viewModel.isHasTrace(detailTrans?.tranType, detailTrans?.status)) {
            Spacer(modifier = Modifier.height(8.dp))
            BaseButton(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 4.dp)
                    .safeClickable { actions?.onLookupClick?.invoke(detailTrans?.mtId ?: "") },
                "Tra soát",
            )
        }

        if (viewModel.isHasCopy(detailTrans?.tranType, detailTrans?.status)) {
            Spacer(modifier = Modifier.height(8.dp))
            BaseButton(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 4.dp)
                    .safeClickable { actions?.onCopyClick?.invoke(detailTrans!!) },
                "Sao chép",
            )
        }

        if (viewModel.isHasDelete(detailTrans?.tranType, detailTrans?.status)) {
            Spacer(modifier = Modifier.height(8.dp))
            BaseButton(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 4.dp)
                    .safeClickable { actions?.onDeleteClick?.invoke(detailTrans!!) },
                "Xóa",
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
    }
}