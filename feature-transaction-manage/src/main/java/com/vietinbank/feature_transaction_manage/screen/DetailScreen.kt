package com.vietinbank.feature_transaction_manage.screen

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.trace_payment.VerifiedByEntity
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationNavigationArea
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel.Companion.ERROR
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel.Companion.PENDING
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel.Companion.SUCCESS
import com.vietinbank.feature_transaction_manage.ManagerDetailAction
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun DetailManagerScreen(
    viewModel: DetailManagerViewModel,
    onAction: (ManagerDetailAction) -> Unit,
) {
    val renderer by viewModel.renderer.collectAsState()
    val detailState by viewModel.detailState.collectAsState()

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
        containerColor = Color.Transparent, // Keep gradient background from MainActivity
        topBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8),
            ) {
                FoundationAppBar(
                    title = "",
                    isLightIcon = false,
                    isSingleLineAppBar = true,
                    onNavigationClick = { onAction(ManagerDetailAction.TapBack) },
                    actions = if (!detailState.fileLst.isNullOrEmpty()) {
                        listOf(
                            AppBarAction(
                                icon = R.drawable.ic_manage_download_24,
                                contentDescription = "",
                                onClick = {
                                    onAction.invoke(ManagerDetailAction.TapDownloadFile(true))
                                },
                            ),
                        )
                    } else {
                        emptyList()
                    },
                )

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentSize(Alignment.TopEnd),
                ) {
                    DropdownMenu(
                        offset = DpOffset(-FDS.Sizer.Gap.gap16, FDS.Sizer.Gap.gap16),
                        shape = RoundedCornerShape(FDS.Sizer.Radius.radius32),
                        containerColor = FDS.Colors.backgroundBgContainer,
                        modifier = Modifier
                            .padding(vertical = FDS.Sizer.Gap.gap24)
                            .padding(horizontal = FDS.Sizer.Gap.gap16),
                        expanded = detailState.isShowDownload,
                        onDismissRequest = {
                            onAction.invoke(ManagerDetailAction.TapDownloadFile(false))
                        },
                    ) {
                        detailState.fileLst?.forEachIndexed { index, item ->
                            Row(
                                modifier = Modifier.safeClickable {
                                    onAction.invoke(ManagerDetailAction.TapDownloadFile(false))
                                    onAction.invoke(
                                        ManagerDetailAction.TapSecondAction(
                                            item.actionType,
                                            detailState.detailModel,
                                        ),
                                    )
                                },
                                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Image(
                                    painter = painterResource(com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_download_32),
                                    contentDescription = "",
                                )

                                FoundationText(
                                    text = item.actionName ?: "",
                                    style = FDS.Typography.interactionButton,
                                    color = FDS.Colors.characterHighlighted,
                                )
                            }
                            if (index != detailState.fileLst?.lastIndex) {
                                Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                            }
                        }
                    }
                }

                renderer?.RenderTitleHeader(detailState.detailModel)

                if (false == renderer?.isLockTabs()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                vertical = FDS.Sizer.Gap.gap16,
                                horizontal = FDS.Sizer.Gap.gap24,
                            ),
                    ) {
                        FoundationTabs(
                            tabs = listOf(
                                stringResource(R.string.transaction_tab_request),
                                stringResource(R.string.transaction_tab_history),
                            ),
                            selectedIndex = detailState.tabSelected,
                            onTabSelected = {
                                onAction(ManagerDetailAction.TapOnTab(it))
                            },
                            type = TabType.Pill,
                        )
                    }
                }
            }
        },
    ) { paddingValues ->

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
            ) {
                when (detailState.tabSelected) {
                    0 -> {
                        item {
                            renderer?.RenderViewDetail(
                                transaction = detailState.detailModel,
                                onFieldClick = { event ->
                                    when (event) {
                                        is TransactionFieldEvent.FileAttachmentClick -> {
                                            viewModel.getDownloadFileID(event.file)
                                        }

                                        is TransactionFieldEvent.ProfileAttachmentClick -> {
                                            viewModel.getDownloadFileID(event.file)
                                        }

                                        is TransactionFieldEvent.ListAttachmentClick -> {
                                        }

                                        is TransactionFieldEvent.FileBaseAttachmentClick -> {
                                        }

                                        TransactionFieldEvent.ViewAttachmentDocument -> {
                                        }
                                    }
                                },
                            )
                        }
                    }

                    else -> {
                        item {
                            detailState.approveLst?.let { lst ->
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                                        .background(FDS.Colors.white)
                                        .padding(FDS.Sizer.Gap.gap24),
                                ) {
                                    detailState.approveLst?.forEachIndexed { index, user ->
                                        ItemApproveLevel(
                                            user,
                                            if (index < lst.size - 1) {
                                                detailState.approveLst?.get(index + 1)
                                            } else {
                                                null
                                            },
                                        )
                                    }

                                    if (detailState.isCheckLimit) {
                                        FoundationIconText(
                                            modifier = Modifier
                                                .padding(start = FDS.Sizer.Gap.gap32)
                                                .safeClickable {
                                                    onAction.invoke(ManagerDetailAction.TapCheckLimit)
                                                },
                                            text = stringResource(R.string.maker_transfer_account_result_check_limited_approval),
                                            style = FDS.Typography.interactionSmallButton,
                                            color = FDS.Colors.characterHighlighted,
                                            icons = mapOf(
                                                IconPosition.RIGHT to IconConfig(
                                                    icon = R.drawable.ic_right,
                                                    size = FDS.Sizer.Icon.icon24,
                                                ),
                                            ),
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (detailState.isCanCopy || detailState.isCanTrace) {
                FoundationNavigationArea(
                    height = FDS.Sizer.Gap.gap108,
                    topRadius = FDS.Sizer.Radius.radius0,
                    startStop = 0.5f,
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = FDS.Sizer.Gap.gap16)
                            .padding(horizontal = FDS.Sizer.Gap.gap24),
                        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                    ) {
                        if (detailState.isCanCopy) {
                            FoundationButton(
                                modifier = Modifier.weight(1f),
                                text = stringResource(R.string.manager_detail_copy),
                                isLightButton = !detailState.isCanTrace,
                                onClick = {
                                    onAction.invoke(
                                        ManagerDetailAction.TapSecondAction(
                                            DetailManagerViewModel.COPY,
                                            detailState.detailModel,
                                        ),
                                    )
                                },
                                leadingIcon = painterResource(R.drawable.ic_account_copy_24),
                            )
                        }

                        if (detailState.isCanTrace) {
                            FoundationButton(
                                modifier = Modifier.weight(1f),
                                text = stringResource(R.string.manager_detail_trace),
                                onClick = {
                                    onAction.invoke(
                                        ManagerDetailAction.TapSecondAction(
                                            DetailManagerViewModel.LOOKUP,
                                            detailState.detailModel,
                                        ),
                                    )
                                },
                                leadingIcon = painterResource(R.drawable.ic_common_search_24),
                            )
                        }
                    }
                }
            }
        }
    }
}

// phai biet trang thai cua 2 cap lien nhau
@Preview
@Composable
fun ItemApproveLevel(
    approvedUser: VerifiedByEntity? = null,
    nextUser: VerifiedByEntity? = null,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(
                painter = painterResource(
                    when (approvedUser?.status) {
                        PENDING -> R.drawable.ic_commom_pending_24
                        ERROR -> R.drawable.ic_commom_fail_24
                        else -> R.drawable.ic_commom_success_24
                    },
                ),
                contentDescription = "",
            )

            val status = when {
                approvedUser?.status == SUCCESS && nextUser?.status == SUCCESS -> StatusLine.SUCCESS
                approvedUser?.status == SUCCESS && nextUser?.status == PENDING -> StatusLine.PENDING
                approvedUser?.status == SUCCESS && nextUser?.status == ERROR -> StatusLine.ERROR
                else -> StatusLine.NONE
            }

            if (nextUser != null) {
                DashLine(modifier = Modifier.height(FDS.Sizer.Gap.gap40), status = status)
            }
        }

        Column(verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4)) {
            FoundationText(
                text = approvedUser?.userfullname ?: "",
                color = FDS.Colors.characterPrimary,
                style = FDS.Typography.bodyB1,
                maxLines = 1,
            )
            FoundationText(
                text = approvedUser?.username ?: "",
                color = FDS.Colors.gray600,
                style = FDS.Typography.captionL,
                maxLines = 1,
            )
            FoundationText(
                text = approvedUser?.description ?: "",
                color = FDS.Colors.gray600,
                style = FDS.Typography.captionL,
                maxLines = 1,
            )

            approvedUser?.messageError?.let {
                FoundationText(
                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
                    text = it,
                    color = FDS.Colors.stateError,
                    style = FDS.Typography.captionM,
                )
            }
        }
    }
}

enum class StatusLine {
    SUCCESS, PENDING, ERROR, NONE
}

// success -> success
// success -> pending
// success -> fail
@Composable
fun DashLine(
    status: StatusLine = StatusLine.SUCCESS,
    strokeWidth: Dp = FDS.Sizer.Stroke.stroke2,
    dashGap: Float = 8f, // khoảng trống
    dashWidth: Float = 8f, // độ dài nét dứt
    modifier: Modifier = Modifier,
) {
    val brushLine = when (status) {
        StatusLine.SUCCESS -> Brush.linearGradient(
            colors = listOf(
                FDS.Colors.stateSuccessLighter,
                FDS.Colors.stateSuccessLighter,
            ),
        )

        StatusLine.PENDING -> Brush.linearGradient(
            colors = listOf(
                FDS.Colors.stateSuccessLighter,
                FDS.Colors.stateWarningLighter,
            ),
        )

        StatusLine.ERROR -> Brush.linearGradient(
            colors = listOf(
                FDS.Colors.stateSuccessLighter,
                FDS.Colors.stateErrorLighter,
            ),
        )

        else -> Brush.linearGradient(
            colors = listOf(
                FDS.Colors.white,
                FDS.Colors.white,
            ),
        )
    }

    val dashGapFinal = if (status != StatusLine.PENDING) 0f else dashGap
    Canvas(modifier = modifier.width(strokeWidth)) {
        drawLine(
            brush = brushLine,
            start = Offset(strokeWidth.toPx() / 2, 0f),
            end = Offset(strokeWidth.toPx() / 2, size.height),
            strokeWidth = strokeWidth.toPx(),
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(dashWidth, dashGapFinal), 0f),
        )
    }
}

@Preview
@Composable
fun DashLinePreview() {
    DashLine(
        modifier = Modifier.height(50.dp),
    )
}
