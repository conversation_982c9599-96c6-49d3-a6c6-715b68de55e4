package com.vietinbank.feature_transaction_manage.screen

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_domain.models.checker.SubTranTypeListDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.components.date.FoundationDateRangePicker
import com.vietinbank.core_ui.components.rememberFoundationAppBarScrollState
import com.vietinbank.core_ui.components.text.foundation.FoundationCounterText
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.FilterAction
import com.vietinbank.feature_transaction_manage.IManage
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import com.vietinbank.feature_transaction_manage.bottomSheet.FillListDataBottomSheet
import com.vietinbank.feature_transaction_manage.bottomSheet.FilterManagerSheet
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ManagerScreenFinal(
    viewModel: ManagerViewModel,
    imageLoader: CoilImageLoader,
    onAction: (IManage) -> Unit,
) {
    val appBarScrollState = rememberFoundationAppBarScrollState()
    val filterModel by viewModel.filterModel.collectAsState()
    LaunchedEffect(Unit) {
        viewModel.observeListScroll()
    }

    // show filter sheet
    FilterManagerSheet(
        visible = filterModel.isShowFilter && null == filterModel.fillSheet && !filterModel.isShowPicker,
        filterModel = filterModel.filterChange,
        onAction = onAction,
    )

    // show selector data sheet
    FillListDataBottomSheet(
        visible = null != filterModel.fillSheet,
        sheet = filterModel.fillSheet,
        imageLoader = imageLoader,
        onDismissRequest = { onAction.invoke(FilterAction.TapFillSheet(null)) },
        onResult = { onAction.invoke(FilterAction.TapOnFillData(it)) },
    )

    // date picker
    if (filterModel.isShowPicker) {
        FoundationDateRangePicker(
            startDate = filterModel.filterChange?.startDate,
            endDate = filterModel.filterChange?.endDate,
            rangeValidate = filterModel.filterChange?.dateOverError ?: 0,
            onDismiss = { onAction.invoke(FilterAction.TapDatePicker(false)) },
            onSelected = { startDate, endDate ->
                onAction.invoke(FilterAction.TapRangeDate(startDate, endDate))
            },
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .dismissKeyboardOnClickOutside()
            .nestedScroll(appBarScrollState.nestedScrollConnection),
    ) {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent,
            topBar = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap8),
                ) {
                    FoundationAppBar(
                        title = stringResource(R.string.manager_transaction),
                        isLightIcon = false,
                        onNavigationClick = { onAction.invoke(ManagerAction.OnBackPress) },
                        scrollState = appBarScrollState,
                    )

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(FDS.Sizer.Gap.gap16),
                    ) {
                        FoundationTabs(
                            tabs = listOf(
                                stringResource(R.string.manager_transaction_me),
                                stringResource(R.string.manager_transaction_all),
                            ),
                            selectedIndex = filterModel.tabManager,
                            onTabSelected = {
                                onAction(ManagerAction.OnTabChanged(it))
                            },
                            type = TabType.Pill,
                        )
                    }
                }
            },
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .clip(
                        RoundedCornerShape(
                            topStart = FDS.Sizer.Radius.radius32,
                            topEnd = FDS.Sizer.Radius.radius32,
                        ),
                    )
                    .background(FDS.Colors.backgroundBgContainer)
                    .padding(FDS.Sizer.Gap.gap24),
            ) {
                when {
                    // tab yeu cau => khong co dien
                    filterModel.tabManager == 0 && filterModel.countList?.isEmpty() == true -> {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            Image(
                                painter = painterResource(com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_empty_pending_96),
                                contentDescription = null,
                            )

                            FoundationText(
                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                                text = stringResource(R.string.manager_request_empty),
                                color = FDS.Colors.characterHighlighted,
                                style = FDS.Typography.headingH3,
                            )

                            FoundationText(
                                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                text = stringResource(R.string.manager_request_empty_note),
                                color = FDS.Colors.characterPrimary,
                                style = FDS.Typography.bodyB2,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }
                    // tab yeu cau  => co dien
                    filterModel.tabManager == 0 && filterModel.countList?.isNotEmpty() == true -> {
                        FoundationText(
                            text = if (filterModel.isChecker) {
                                stringResource(R.string.manager_filter_date_nearest)
                            } else {
                                stringResource(R.string.manager_filter_date_30_nearest)
                            },
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.bodyB2Emphasized,
                        )

                        LazyVerticalGrid(
                            columns = GridCells.Fixed(2),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap16),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            itemsIndexed(
                                filterModel.countList ?: emptyList(),
                                key = { index, item ->
                                    "body_${item.groupType}_$index"
                                },
                            ) { index, item ->

                                ItemTransaction(item, filterModel.isChecker, onAction)
                            }
                        }
                    }

                    // tab tat ca giao dich
                    filterModel.tabManager == 1 -> {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.End,
                        ) {
                            FoundationIconText(
                                modifier = Modifier.safeClickable {
                                    onAction(FilterAction.TapShowFilter(true))
                                },
                                text = stringResource(R.string.manager_filter_option),
                                icons = mapOf(
                                    IconPosition.RIGHT to IconConfig(
                                        icon = R.drawable.ic_common_sort_24,
                                        size = FDS.Sizer.Icon.icon24,
                                    ),
                                ),
                                style = FDS.Typography.interactionSmallButton,
                                color = FDS.Colors.characterHighlighted,
                                textAlign = TextAlign.End,
                                horizontalAlignment = CenterHorizontally,
                            )
                        }

                        filterModel.reportList?.let { lst ->
                            if (lst.isEmpty()) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = FDS.Sizer.Gap.gap48),
                                    horizontalAlignment = CenterHorizontally,
                                ) {
                                    Image(
                                        painter = painterResource(R.drawable.ic_common_empty_search_96),
                                        contentDescription = null,
                                    )

                                    FoundationText(
                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24),
                                        text = stringResource(R.string.manager_filter_empty_transaction),
                                        color = FDS.Colors.characterHighlighted,
                                        style = FDS.Typography.headingH3,
                                    )

                                    FoundationText(
                                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                        text = stringResource(R.string.manager_filter_reset_pls),
                                        color = FDS.Colors.characterPrimary,
                                        style = FDS.Typography.bodyB2,
                                    )
                                }
                            } else {
                                LazyColumn(state = viewModel.lazyDetailState) {
                                    filterModel.reportList?.let {
                                        itemsIndexed(
                                            it,
                                            key = { index, item -> "report_${index}_${item.mtId}" },
                                        ) { index, item ->
                                            viewModel.renderUI(item.tranType)
                                                ?.RenderViewItemManager(item, onAction)

                                            if (index != it.lastIndex) {
                                                FoundationDivider()
                                            }
                                        }
                                    }

                                    if (filterModel.isLoadingMore) {
                                        item {
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .background(FDS.Colors.backgroundBgContainer)
                                                    .padding(FDS.Sizer.Gap.gap16),
                                                contentAlignment = Alignment.Center,
                                            ) {
                                                CircularProgressIndicator(
                                                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                                                    color = FDS.Colors.blue900,
                                                    strokeWidth = FDS.Sizer.Stroke.stroke2,
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        } ?: run {
                            // hien thi shimmerLoading
                        }
                    }
                    // default
                    else -> {
                    }
                }
            }
        }
    }
}

@Composable
fun ItemTransaction(
    item: SubTranTypeListDomain,
    isChecker: Boolean,
    onAction: (IManage) -> Unit,
) {
    val colorList = listOf(
        FDS.Colors.buttonGradientPrimary.copy(alpha = 0.2f),
        FDS.Colors.buttonGradientPrimaryTransparent,
    )

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius16))
            .border(
                FDS.Sizer.Stroke.stroke1,
                FDS.Colors.blue100,
                RoundedCornerShape(FDS.Sizer.Radius.radius16),
            )
            .drawBehind {
                val radius = size.height
                val gradient = Brush.radialGradient(
                    colors = colorList,
                    center = Offset(size.width, size.height),
                    radius = radius,
                )
                drawRect(brush = gradient, size = size)
            }
            .padding(
                horizontal = FDS.Sizer.Gap.gap16,
                vertical = FDS.Sizer.Gap.gap8,
            )
            .safeClickable { onAction(ManagerAction.OnMultipleApproval(item)) },
    ) {
        Column(
            modifier = Modifier.padding(
                top = FDS.Sizer.Gap.gap8,
            ),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_common_transfer_32),
                contentDescription = "",
            )

            FoundationText(
                text = item.servicetypename ?: "",
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.characterHighlighted,
                minLines = 2,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
            )
        }

        if (isChecker) {
            FoundationCounterText(
                modifier = Modifier.padding(start = FDS.Sizer.Gap.gap24),
                counter = item.count_transaction?.toIntOrNull() ?: 0,
            )
        }
    }
}
