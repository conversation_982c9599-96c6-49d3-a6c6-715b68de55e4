package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.runtime.Composable
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.feature_transaction_manage.ManagerAction

interface IRenderManager {

    @Composable
    fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    )

    @Composable
    fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    )

    fun isWaitingApproval(tranType: String? = null, statusCode: String? = null): Boolean =
        statusCode?.contains(Tags.REGEX_AWAITING_APPROVAL) == true && when (tranType?.uppercase()) {
            Tags.TYPE_GROUP_TRANSFER_IN, Tags.TYPE_GROUP_TRANSFER_OUT, Tags.TYPE_GROUP_TRANSFER_NAPAS, Tags.TYPE_GROUP_PAYMENT, Tags.TYPE_GROUP_TRANSFER_CTTF5000, Tags.TYPE_GROUP_TRANSFER_CTTF300 -> true
            else -> false
        }

    @Composable
    fun RenderTitleHeader(transaction: TransDetailDomain?) {}

    fun isGetBankList() = false

    fun isLockTabs() = false

    fun isNewUI() = false
}