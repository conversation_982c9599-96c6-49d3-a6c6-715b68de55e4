package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.vietinbank.core_common.constants.StatusType
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_SSS
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.models.TransferResult
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.components.text.foundation.appendSemiBold
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class TransferManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
    private val imageLoader: CoilImageLoader,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        detailDomain: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailDomain?.let { transaction ->
            val contentLst = mutableListOf(
                ItemResult(
                    stringResource(R.string.transaction_number_label),
                    transaction.mtId ?: "",
                ),
                ItemResult(
                    stringResource(R.string.transaction_label_content),
                    transaction.remark ?: "",
                ),
                ItemResult(
                    stringResource(R.string.transaction_label_fee),
                    Utils.g().getDotMoneyHasCcy(
                        transaction.feeAmount ?: "",
                        transaction.debitCurrency ?: "",
                    ),
                ),
                ItemResult(
                    stringResource(R.string.transaction_label_fee_method),
                    transaction.feePayMethodDesc ?: "",
                ),
                ItemResult(
                    stringResource(R.string.transaction_label_transfer_type),
                    if (transaction.process_time.isNullOrEmpty()) {
                        stringResource(R.string.transaction_transfer_immediate)
                    } else {
                        stringResource(R.string.transaction_transfer_scheduled)
                    },
                ),
            )

            if (!transaction.process_time.isNullOrEmpty()) {
                contentLst.add(
                    ItemResult(
                        stringResource(R.string.manager_detail_appointment_date),
                        transaction.process_time ?: "",
                        valueColor = if (transaction.isOverProcess()) {
                            FDS.Colors.stateError
                        } else {
                            FDS.Colors.characterPrimary
                        },
                    ),
                )
            }
            contentLst.add(
                ItemResult(
                    stringResource(R.string.manager_detail_status),
                    transaction.statusName ?: "",
                    valueColor = transaction.status.getColorStatus(),
                ),
            )

            if (transaction.status == StatusType.SUCCESS.value) {
                contentLst.add(
                    ItemResult(
                        stringResource(R.string.manager_detail_ref_no),
                        transaction.host_mtid ?: "",
                    ),
                )
            }

            FoundationTransfer(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FDS.Sizer.Gap.gap8),
                accountFrom = TransferResult(
                    bankIconURL = transaction.fromBankUrl,
                    bankName = transaction.branchName,
                    accountName = transaction.fromAccountName,
                    accountNo = transaction.fromAccountNo,
                ),
                accountTo = TransferResult(
                    bankIconURL = transaction.toBankUrl,
                    bankName = transaction.receiveBankName,
                    accountName = transaction.receiveName,
                    accountNo = transaction.toAccountNo,
                ),
                lstContent = contentLst,
                imageLoader = imageLoader,
            )
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationRichText(
                    modifier = Modifier.weight(1f),
                    style = FDS.Typography.captionL,
                ) {
                    appendColored(
                        resourceProvider.getString(R.string.transaction_number_label),
                        resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                    )
                    append(" ")
                    appendSemiBold(
                        transaction.mtId ?: "",
                        resourceProvider.getComposeColor(R.color.foundation_character_primary),
                    )
                }

                if (transaction.isTranTypeSN()) {
                    FoundationText(
                        modifier = Modifier
                            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                            .border(
                                FDS.Sizer.Stroke.stroke1,
                                FDS.Colors.backgroundBgHighlight,
                                RoundedCornerShape(FDS.Sizer.Radius.radius32),
                            )
                            .background(FDS.Colors.blue50)
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                                vertical = FDS.Sizer.Gap.gap4,
                            ),
                        text = stringResource(R.string.manager_detail_separation),
                        color = FDS.Colors.characterHighlighted,
                        style = FDS.Typography.captionCaptionMBold,
                    )
                }

                if (!transaction.process_time.isNullOrEmpty()) {
                    FoundationText(
                        modifier = Modifier
                            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                            .border(
                                FDS.Sizer.Stroke.stroke1,
                                FDS.Colors.backgroundBgHighlight,
                                RoundedCornerShape(FDS.Sizer.Radius.radius32),
                            )
                            .background(FDS.Colors.blue50)
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                                vertical = FDS.Sizer.Gap.gap4,
                            ),
                        text = stringResource(R.string.transaction_transfer_scheduled),
                        color = FDS.Colors.characterHighlighted,
                        style = FDS.Typography.captionCaptionMBold,
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    modifier = Modifier.weight(1f),
                    text = transaction.tranTypeName ?: "",
                    iconTextGap = FDS.Sizer.Gap.gap4,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_transfer_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                )

                FoundationText(
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_transfer_to),
                value = "${transaction.receiveName ?: ""} - ${transaction.receiveBankName ?: ""} - ${transaction.toAccountNo ?: ""}",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.maker_transfer_dashboard_content),
                value = transaction.remark ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate?.getDateToFormat(
                    dd_MM_yyyy_HH_mm_ss_SSS,
                    dd_MM_yyyy_HH_mm_ss,
                ),
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            if (!transaction.process_time.isNullOrEmpty()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap8),
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.manager_detail_appointment_date),
                        style = FDS.Typography.captionL,
                        color = FDS.Colors.characterSecondary,
                    )
                    if (transaction.isOverProcess) {
                        Image(
                            modifier = Modifier.safeClickable {},
                            painter = painterResource(R.drawable.ic_common_info_16),
                            contentDescription = "",
                        )
                    }

                    FoundationText(
                        modifier = Modifier.weight(1f),
                        text = transaction.process_time ?: "",
                        style = FDS.Typography.captionLSemibold,
                        color = if (transaction.isOverProcess) {
                            FDS.Colors.stateError
                        } else {
                            FDS.Colors.characterPrimary
                        },
                        textAlign = TextAlign.End,
                    )
                }
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }

    @Composable
    override fun RenderTitleHeader(transaction: TransDetailDomain?) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = FDS.Sizer.Gap.gap16)
                .padding(horizontal = FDS.Sizer.Gap.gap24),
        ) {
            FoundationText(
                text = "Chuyển tiền\n${
                    Utils.g().getDotMoneyHasCcy(
                        transaction?.amount ?: "",
                        transaction?.currency ?: "",
                    )
                }",
                style = FDS.Typography.headingH2,
                color = FDS.Colors.white,
            )

            FoundationText(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
                text = moneyHelper.convertAmountToWords(
                    transaction?.amount ?: "",
                    transaction?.currency ?: "",
                ),
                style = FDS.Typography.bodyB2,
                color = FDS.Colors.gray200,
            )
        }
    }

    override fun isGetBankList(): Boolean {
        return true
    }

    override fun isNewUI(): Boolean {
        return true
    }
}