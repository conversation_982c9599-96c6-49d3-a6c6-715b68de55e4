package com.vietinbank.feature_transaction_manage.fragment.inquiry_approver_list

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.manage.InquiryApproverDomains
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class InquiryApproverListViewModel @Inject constructor(
    private val transferUseCase: TransactionManageUseCase,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    var inquiryApproverListAdapter = InquiryApproverListAdapter()
    val _inquiryApproverState = MutableLiveData<InquiryApproverDomains?>()
    val inquiryApproverState: LiveData<InquiryApproverDomains?> get() = _inquiryApproverState
    fun getInquiryApproverState(mtId: String, accountNumber: String, serviceId: String) {
//        launchJob(showLoading = true) {
//            val params = InquiryApproverParams(
//                username = userProf.getUserName() ?: "",
//                mtId = mtId,
//                accountNumber = accountNumber,
//                serviceId = serviceId,
//            )
//            val res = transferUseCase.getInquiryApprover(params)
//            handleResource(res) { data ->
//                data.usersList?.let { inquiryApproverListAdapter.setData(data = it.toMutableList()) }
//                _inquiryApproverState.postValue(data)
//            }
//        }
    }

    fun getServiceID(tranType: String) = when (tranType) {
        Tags.TransferType.TYPE_IN -> Tags.TransferType.SERVICE_TYPE_TRANSFER_IN
        Tags.TransferType.TYPE_OUT -> Tags.TransferType.SERVICE_TYPE_TRANSFER_OUT
        else -> Tags.TransferType.SERVICE_TYPE_TRANSFER_NAPAS_CARD
    }
}