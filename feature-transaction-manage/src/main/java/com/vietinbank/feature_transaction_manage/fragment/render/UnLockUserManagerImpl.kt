package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendBold
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class UnLockUserManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {

    @Composable
    override fun RenderTitleHeader(transaction: TransDetailDomain?) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16)
                .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
        ) {
            FoundationText(
                text = transaction?.tranTypeName ?: "",
                style = FoundationDesignSystem.Typography.headingH2,
                color = FoundationDesignSystem.Colors.white,
            )
        }
    }

    @Composable
    override fun RenderViewDetail(
        detailDomain: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailDomain?.let { transaction ->
            val contentLst = mutableListOf(
                ItemResult(
                    stringResource(R.string.transaction_number_label),
                    transaction.mtId ?: "",
                ),
                ItemResult(
                    stringResource(R.string.lock_init_cif_mst),
                    transaction.cifno ?: "",
                ),

                ItemResult(
                    stringResource(R.string.lock_init_name),
                    transaction.creator ?: "",
                ),
                ItemResult(
                    stringResource(R.string.lock_init_cccd),
                    transaction.idCard ?: "",
                ),

                ItemResult(
                    stringResource(R.string.lock_init_account),
                    transaction.paymentAccount ?: "",
                ),

                ItemResult(
                    stringResource(R.string.lock_confirm_method_pass),
                    transaction.typeSend ?: "",
                ),

                ItemResult(
                    stringResource(R.string.manager_detail_status),
                    transaction.statusName ?: "",
                    valueColor = transaction.status.getColorStatus(),
                ),
            )

            FoundationTransfer(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8),
                lstContent = contentLst,
            )
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationRichText(
                    modifier = Modifier.weight(1f),
                    style = FoundationDesignSystem.Typography.captionCaptionL,
                ) {
                    appendColored(
                        resourceProvider.getString(R.string.transaction_number_label),
                        resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                    )
                    append(" ")
                    appendBold(
                        transaction.mtId ?: "",
                        resourceProvider.getComposeColor(R.color.foundation_character_primary),
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    modifier = Modifier.weight(1f),
                    text = transaction.tranTypeName ?: "",
                    iconTextGap = FoundationDesignSystem.Sizer.Gap.gap4,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_transfer_24,
                            size = FoundationDesignSystem.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FoundationDesignSystem.Typography.captionCaptionLBold,
                    color = FoundationDesignSystem.Colors.characterPrimary,
                )

                FoundationText(
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    style = FoundationDesignSystem.Typography.captionCaptionLBold,
                    color = FoundationDesignSystem.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(com.vietinbank.core_ui.R.string.lock_init_name),
                value = transaction.creator ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }

    override fun isLockTabs(): Boolean {
        return true
    }

    override fun isNewUI(): Boolean {
        return true
    }
}