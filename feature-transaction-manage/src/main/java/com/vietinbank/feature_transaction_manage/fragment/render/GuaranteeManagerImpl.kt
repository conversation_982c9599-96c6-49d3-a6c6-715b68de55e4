package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_SSS
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

// GTBL

class GuaranteeManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction != null) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Loại giao dịch",
                        value = transaction.tranTypeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Người khởi tạo",
                        value = "${transaction.creator} - ${transaction.createdDate}",
                    )

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }
                Text(
                    text = "Thông tin khoản bảo lãnh",
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Số bảo lãnh",
                        value = transaction.host_mtid ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày phát hành",
                        value = transaction.releaseDate ?: "",
                    )

                    // so tien de nghi phat hanh bao lanh
                    val amountDesc = StringBuilder()

                    transaction.amount?.getAmountServer()?.let {
                        if (it.isNotEmpty()) {
                            amountDesc.append(
                                "${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amount ?: "",
                                        transaction.currency ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amount,
                                        transaction.currency,
                                    )
                                }",
                            )
                        }
                    }
                    transaction.amount2?.getAmountServer()?.let {
                        if (it.isNotEmpty() && "0" != it) {
                            amountDesc.append(
                                "\n${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amount2 ?: "",
                                        transaction.currency2 ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amount2,
                                        transaction.currency2,
                                    )
                                }",
                            )
                        }
                    }
                    transaction.amount3?.getAmountServer()?.let {
                        if (it.isNotEmpty() && "0" != it) {
                            amountDesc.append(
                                "\n${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amount3 ?: "",
                                        transaction.currency3 ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amount3,
                                        transaction.currency3,
                                    )
                                }",
                            )
                        }
                    }
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền bảo lãnh",
                        value = amountDesc.toString(),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày/sự kiện bắt đầu hiệu lực",
                        value = transaction.effectiveStartDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày/sự kiện kết thúc hiệu lực",
                        value = transaction.effectiveEndDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số HĐ đã ký kết với NH",
                        value = transaction.contractNo ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày hợp đồng",
                        value = transaction.contractDate ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Chi nhánh VietinBank",
                        value = "${transaction.branch} - ${transaction.branchName}",
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Thông tin khách hàng",
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Tên khách hàng",
                        value = transaction.cifName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số ĐKKD",
                        value = transaction.guaranteeCode ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngày cấp",
                        value = transaction.dateRange ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nơi cấp",
                        value = transaction.issuesBy ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Tên người đại diện/Ủy quyền",
                        value = transaction.representativeName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ủy quyền số",
                        value = transaction.authority ?: "",
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Nội dung đề nghị giải tỏa/giảm trừ",
                    modifier = Modifier.padding(vertical = 8.dp),
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    val amountChangeDesc = StringBuilder()

                    transaction.amountChange?.getAmountServer()?.let {
                        if (it.isNotEmpty()) {
                            amountChangeDesc.append(
                                "${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amountChange ?: "",
                                        transaction.currency ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amountChange,
                                        transaction.currency,
                                    )
                                }",
                            )
                        }
                    }
                    transaction.amountChange2?.getAmountServer()?.let {
                        if (it.isNotEmpty() && "0" != it) {
                            amountChangeDesc.append(
                                "\n${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amountChange2 ?: "",
                                        transaction.currency2 ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amountChange2,
                                        transaction.currency2,
                                    )
                                }",
                            )
                        }
                    }
                    transaction.amountChange3?.getAmountServer()?.let {
                        if (it.isNotEmpty() && "0" != it) {
                            amountChangeDesc.append(
                                "\n${
                                    Utils.g().getDotMoneyHasCcy(
                                        transaction.amountChange3 ?: "",
                                        transaction.currency3 ?: "",
                                    )
                                }\n${
                                    moneyHelper.convertAmountToWords(
                                        transaction.amountChange3,
                                        transaction.currency3,
                                    )
                                }",
                            )
                        }
                    }
                    InfoHorizontalView(
                        title = "Loại giải tỏa",
                        value = when (transaction.typeChange) {
                            "10" -> "Giảm trừ/Giải tỏa từng phần"
                            "11" -> "Giải tỏa toàn bộ"
                            else -> ""
                        },
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Giá trị bảo lãnh được giải tỏa/giảm trừ",
                        value = amountChangeDesc.toString(),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Lý do Giải tỏa/giảm trừ",
                        value = transaction.reasonChange ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giảm trừ/giải tỏa trước hạn bảo lãnh",
                        value = when (transaction.feeDesc) {
                            "10" -> "Theo biểu phí NHCT"
                            else -> "Thỏa thuận"
                        },
                    )
                    if ("10" != transaction.feeDesc) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Cụ thể",
                            value = transaction.feeDesc ?: "",
                        )
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Chỉ dẫn hoàn ký quỹ",
                        value = "${transaction.depositAccount ?: ""} - ${
                            Utils.g().getDotMoney(transaction.depositAmount ?: "")
                        } - ${transaction.depositCurrency ?: ""}",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phương thức phát hành",
                        value = if (!transaction.message.isNullOrEmpty()) {
                            transaction.message ?: ""
                        } else {
                            when (transaction.messageType) {
                                "1" -> "Bản giấy"
                                "2" -> "Bản điện tử ký số"
                                "3" -> "SWIFT"
                                else -> ""
                            }
                        },
                    )
                }

                if (!transaction.ibFile.isNullOrEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Thông tin hồ sơ đính kèm",
                        modifier = Modifier.padding(vertical = 8.dp),
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 12.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        transaction.ibFile?.forEachIndexed { index, file ->
                            InfoHorizontalView(
                                modifier = Modifier.padding(
                                    top = if (index == 0) {
                                        0.dp
                                    } else {
                                        14.dp
                                    },
                                ),
                                title = when (file.attachmentType) {
                                    "GDN" -> "Giấy đề nghị giải tỏa/giảm trừ bảo lãnh"
                                    "13" -> "Biên bản chấp thuận/Thông báo chấp nhận hình thức bảo đảm khác thay cho bảo lãnh khác"
                                    "11" -> "Biên bản thanh lý hợp đồng/Biên bản làm việc"
                                    else -> ""
                                },
                                value = file.fileName ?: "",
                            ) {
                                onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        val amountDesc = StringBuilder()
        try {
            transaction.amountChange?.getAmountServer()?.let {
                if (it.isNotEmpty()) {
                    amountDesc.append(
                        Utils.g().getDotMoneyHasCcy(
                            transaction.amountChange ?: "",
                            transaction.currency ?: "",
                        ),
                    )
                }
            }
            transaction.amountChange2?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amountChange2 ?: "",
                                transaction.currency2 ?: "",
                            )
                        }",
                    )
                }
            }
            transaction.amountChange3?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction.amountChange3 ?: "",
                                transaction.currency3 ?: "",
                            )
                        }",
                    )
                }
            }
        } catch (_: Exception) {
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                verticalAlignment = Alignment.Bottom,
            ) {
                FoundationText(
                    text = stringResource(id = com.vietinbank.core_ui.R.string.account_transaction_no),
                    color = FoundationDesignSystem.Colors.characterSecondary,
                    style = FoundationDesignSystem.Typography.captionL,
                )
                FoundationText(
                    text = transaction.mtId ?: "",
                    modifier = Modifier.padding(start = 4.dp),
                    color = FoundationDesignSystem.Colors.characterPrimary,
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_switch_vertical_24),
                        contentDescription = "Switch icon vertically",
                        tint = AppColors.characterPrimary,
                        modifier = Modifier
                            .padding(end = 4.dp)
                            .safeClickable {
                                // TODO: switch vertical action
                            },
                    )

                    FoundationText(
                        text = transaction.tranTypeName?.trim() ?: "",
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        style = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                }

                val formattedAmount = remember(transaction.amount, transaction.currency) {
                    try {
                        val cleanAmount = transaction.amount?.getAmountServer()
                        Utils.g().getDotMoneyHasCcy(cleanAmount ?: "", transaction.currency ?: "")
                    } catch (_: Exception) {
                        "${transaction.amount} ${transaction.currency}"
                    }
                }
                FoundationText(
                    text = formattedAmount,
                    modifier = Modifier.align(Alignment.Top),
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.feature_transaction_manage.R.string.feature_manage_guaruatee_receiver),
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                value = transaction.addressOfReceiver ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = 8.dp),
                title = stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_documentType),
                value = when (transaction.tranType) {
                    "1" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_1)
                    "2" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_2)
                    "3" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
                    "4" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_4)
                    "5" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_5)
                    "6" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_6)
                    "7" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_7)
                    "8" -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_8)
                    else -> ""
                },
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.feature_transaction_manage.R.string.feature_manage_creator),
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                value = transaction.creator ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate?.getDateToFormat(
                    dd_MM_yyyy_HH_mm_ss_SSS,
                    dd_MM_yyyy_HH_mm_ss,
                ),
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                valueColor = transaction.statusCode?.getColorStatus(),
            )
        }
    }
}
