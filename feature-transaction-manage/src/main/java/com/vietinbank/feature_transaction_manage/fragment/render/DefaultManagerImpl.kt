package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendBold
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class DefaultManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        detailDomain: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailDomain?.let { transaction ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }
                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }

                Text(
                    text = "Thông tin tài khoản chuyển",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Từ tài khoản",
                        value = "${transaction.fromAccountNo} - VND - ${transaction.fromAccountName}",
                    )
                    if ("sn" == transaction.tranType) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Chi nhánh xử lý",
                            value = "${transaction.branchId} - ${transaction.branchName}",
                        )
                    }
                }

                Text(
                    text = "Thông tin tài khoản thụ hưởng",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Tới tài khoản",
                        value = "${transaction.toAccountNo ?: ""}\n${transaction.receiveName ?: ""}",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Ngân hàng",
                        value = transaction.receiveBankName ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                        valueFont = 5,
                        valueColor = AppColors.blue02,
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.debitCurrency,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Hình thức thu phí",
                        value = transaction.feePayMethodDesc ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nội dung",
                        value = transaction.remark ?: "",
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Thời gian chuyển",
                        value = transaction.createdDate ?: "",
                    )
                }

                // giao dich tach lenh
                if (transaction.tranType == "sn") {
                    Text(
                        text = "Thông tin tách lệnh giao dịch",
                        style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    )

                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 12.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color.White)
                            .padding(vertical = 20.dp, horizontal = 16.dp),
                    ) {
                        transaction.splitTransaction?.forEach { subTransaction ->
                            InfoHorizontalView(
                                title = "Số giao dịch",
                                value = subTransaction.mtId,
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Số tiền",
                                value = Utils.g()
                                    .getDotMoneyHasCcy(subTransaction.amount ?: "", "VND"),
                                valueTwo = moneyHelper.convertAmountToWords(
                                    subTransaction.amount ?: "",
                                    "VND",
                                ),
                            )

                            val fee = (
                                subTransaction.feeAmt?.toDoubleOrNull()
                                    ?: 0.0
                                ) + (subTransaction.feeVat?.toDoubleOrNull() ?: 0.0)
                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Phí giao dịch",
                                value = Utils.g().getDotMoneyHasCcy(fee.toString(), "VND"),
                            )

                            InfoHorizontalView(
                                modifier = Modifier.padding(top = 14.dp),
                                title = "Trạng thái",
                                value = subTransaction.statusName ?: "",
                            )

                            Spacer(
                                modifier = Modifier
                                    .padding(top = 8.dp)
                                    .fillMaxWidth()
                                    .height(1.dp)
                                    .background(AppColors.lineColor),
                            )
                        }

                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Hình thức thu phí",
                            value = transaction.feePayMethodDesc ?: "",
                        )
                    }
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationRichText(
                    modifier = Modifier.weight(1f),
                    style = FDS.Typography.captionCaptionL,
                ) {
                    appendColored(
                        resourceProvider.getString(R.string.transaction_number_label),
                        resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                    )
                    append(" ")
                    appendBold(
                        transaction.mtId ?: "",
                        resourceProvider.getComposeColor(R.color.foundation_character_primary),
                    )
                }

                if (transaction.isTranTypeSN()) {
                    FoundationText(
                        modifier = Modifier
                            .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                            .border(FDS.Sizer.Stroke.stroke1, FDS.Colors.backgroundBgHighlight)
                            .background(FDS.Colors.blue50)
                            .padding(
                                horizontal = FDS.Sizer.Gap.gap8,
                                vertical = FDS.Sizer.Gap.gap4,
                            ),
                        text = stringResource(R.string.manager_detail_separation),
                        color = FDS.Colors.characterHighlighted,
                        style = FDS.Typography.captionCaptionMBold,
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FDS.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    modifier = Modifier.weight(1f),
                    text = transaction.tranTypeName ?: "",
                    iconTextGap = FDS.Sizer.Gap.gap4,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_transfer_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.characterPrimary,
                )

                FoundationText(
                    text = Utils.getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    style = FDS.Typography.captionCaptionLBold,
                    color = FDS.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_transfer_to),
                value = "${transaction.receiveName ?: ""} - ${transaction.receiveBankName ?: ""} - ${transaction.toAccountNo ?: ""}",
                titleColor = FDS.Colors.characterSecondary,
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionCaptionLBold,
                valueColor = FDS.Colors.characterPrimary,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.maker_transfer_dashboard_content),
                value = transaction.remark ?: "",
                titleColor = FDS.Colors.characterSecondary,
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionCaptionLBold,
                valueColor = FDS.Colors.characterPrimary,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleColor = FDS.Colors.characterSecondary,
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionCaptionLBold,
                valueColor = FDS.Colors.characterPrimary,
            )

            if (!transaction.process_time.isNullOrEmpty()) {
                FoundationInfoHorizontal(
                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                    title = stringResource(R.string.manager_detail_appointment_date),
                    value = transaction.process_time ?: "",
                    titleColor = FDS.Colors.characterSecondary,
                    titleStyle = FDS.Typography.captionCaptionL,
                    valueStyle = FDS.Typography.captionCaptionLBold,
                    valueColor = FDS.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FDS.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleColor = FDS.Colors.characterSecondary,
                titleStyle = FDS.Typography.captionCaptionL,
                valueStyle = FDS.Typography.captionCaptionLBold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }
}