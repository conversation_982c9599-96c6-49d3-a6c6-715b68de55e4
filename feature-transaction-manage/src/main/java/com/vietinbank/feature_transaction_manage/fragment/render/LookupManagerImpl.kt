package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.models.ItemResult
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.ItemSubTransactionDetailMangerDomain
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendBold
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject

class LookupManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {

    @Composable
    override fun RenderTitleHeader(transaction: TransDetailDomain?) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16)
                .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
        ) {
            FoundationText(
                text = transaction?.tranTypeName ?: "",
                style = FoundationDesignSystem.Typography.headingH2,
                color = FoundationDesignSystem.Colors.white,
            )
        }
    }

    @Composable
    override fun RenderViewDetail(
        detailDomain: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailDomain?.let { transaction ->
            val contentLst = mutableListOf(
                ItemResult(
                    stringResource(R.string.transaction_label_content),
                    transaction.remark ?: "",
                ),
                if (transaction.feeAmount.isNullOrEmpty() || transaction.feeAmount == "0") {
                    ItemResult(
                        stringResource(R.string.checker_trace_init_feeAmount),
                        stringResource(R.string.checker_trace_content_trace),
                    )
                } else {
                    ItemResult(
                        stringResource(R.string.maker_transfer_dashboard_main_account_type_D),
                        transaction.feeAccountNo ?: "",
                    )
                    ItemResult(
                        stringResource(R.string.checker_trace_init_feeAmount),
                        Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.currency ?: "",
                        ),
                    )
                },

                ItemResult(
                    stringResource(R.string.manager_detail_create_time),
                    transaction.createdDate ?: "",
                ),
            )

            AppendPrefSection(contentLst, transaction)

            FoundationTransfer(
                contentTop = {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            text = stringResource(R.string.checker_trace_init_title),
                            color = FoundationDesignSystem.Colors.characterHighlighted,
                            style = FoundationDesignSystem.Typography.bodyB2,
                        )
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8),
                lstContent = contentLst,
            )
        }
    }

    @Composable
    fun DefaultDetail(
        transaction: TransDetailDomain,
    ): List<ItemResult> {
        val results = mutableListOf<ItemResult>()

        results.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
                    value = "${transaction.fromAccountNo.orEmpty()} - ${transaction.currency.orEmpty()} - ${transaction.fromAccountName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_to_account),
                    value = "${transaction.toAccountNo.orEmpty()}\n${transaction.receiveName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
                    value = transaction.receiveBankName.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                    value = Utils.g().getDotMoneyHasCcy(
                        transaction.amount.orEmpty(),
                        transaction.currency.orEmpty(),
                    ),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
                    value = Utils.g().getDotMoneyHasCcy(
                        transaction.feeAmount.orEmpty(),
                        transaction.currency.orEmpty(),
                    ),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                    value = transaction.feePayMethodDesc.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
                    value = transaction.remark.orEmpty(),
                ),
            ),
        )

        // File đính kèm đầu tiên (nếu có)
        transaction.listFile?.firstOrNull()?.let { file ->
            results.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                    value = file.fileName.orEmpty(),
                ),
            )
        }

        // Ngày tạo / process time
        results.add(
            ItemResult(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
                value = transaction.createdDate.orEmpty(),
            ),
        )

        return results
    }

    @Composable
    fun buildTaxItemResults(
        item: ItemSubTransactionDetailMangerDomain,
    ): List<ItemResult> {
        return listOf(
            ItemResult(
                title = stringResource(
                    R.string.checker_trace_init_customs_inland_detail_content,
                ),
                value = item.content.orEmpty(),
            ),
            ItemResult(
                title = stringResource(
                    R.string.checker_trace_init_customs_inland_detail_sit,
                ),
                value = item.sitCode.orEmpty(),
            ),
            ItemResult(
                title = stringResource(
                    R.string.checker_trace_init_customs_inland_detail_tax,
                ),
                value = item.taxPeriod.orEmpty(),
            ),
            ItemResult(
                title = stringResource(
                    com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount,
                ),
                value = Utils.g().getDotMoneyHasCcy(
                    item.amount.orEmpty(),
                    item.currency.orEmpty(),
                ),
            ),
        )
    }

    @Composable
    fun TransferForeignDetail(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        contentLst.apply {
            // Tên người chuyển (fromAccountName)
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_name),
                    value = transaction.fromAccountName.orEmpty(),
                ),
            )

            // Thông tin Remitter
            if (!transaction.remitterName.isNullOrEmpty()) {
                addAll(
                    listOf(
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_name_transfer),
                            value = transaction.fromAccountName.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                            value = transaction.remitterCountry.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                            value = transaction.remitterDistrict.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                            value = transaction.remitterWard.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                            value = transaction.remitterStreet.orEmpty(),
                        ),
                    ),
                )
            }

            // --- Divider + Header: "Thông tin" ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )

            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_introducer),
                        value = transaction.introducer.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_branch),
                        value = transaction.branchName.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_total_amount),
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.currency ?: "",
                        ),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.common_content),
                        value = transaction.remark.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_note),
                        value = transaction.note.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_date),
                        value = transaction.valueDate.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_purpose),
                        value = transaction.purposeTransferName.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return),
                        value = if (!transaction.lcReturnDate.isNullOrEmpty()) {
                            stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return_after)
                        } else {
                            stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_return_befor)
                        },
                    ),
                ),
            )

            if (!transaction.lcReturnDate.isNullOrEmpty()) {
                add(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_date_return),
                        value = transaction.lcReturnDate.orEmpty(),
                    ),
                )
            }

            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_total),
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.currency ?: "",
                        ),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_feetype),
                        value = transaction.feeType.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_feeaccount),
                        value = transaction.feeAccountNo.orEmpty(),
                    ),
                ),
            )

            // --- Divider + Header: "Tài khoản" ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_account_number),
                        value = transaction.fromAccountNo.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debit),
                        value = transaction.debitAmt1.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debitAmt),
                        value = transaction.debitAmtByRate1.orEmpty(),
                    ),
                ),
            )

            // --- Divider + Header: "Tài khoản số ..." ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_no),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_account_number),
                        value = transaction.debitAccountNo2.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debit),
                        value = transaction.debitAmt2.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_account_debitAmt),
                        value = transaction.debitAmtByRate2.orEmpty(),
                    ),
                ),
            )

            // --- Divider + Header: "Người thụ hưởng" ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_ficiary),
                        value = transaction.beneficiaryAccount.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_name),
                        value = transaction.benName.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                        value = transaction.benCountry.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                        value = transaction.benDistrict.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                        value = transaction.benWard.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                        value = transaction.benStreet.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter),
                        value = if (!transaction.remitterName.isNullOrEmpty()) {
                            stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter_yes)
                        } else {
                            stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_remitter_no)
                        },
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_ben_final),
                        value = transaction.finalBenName.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                        value = transaction.finalBenCountry.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                        value = transaction.finalBenDistrict.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                        value = transaction.finalBenWard.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                        value = transaction.finalBenStreet.orEmpty(),
                    ),
                ),
            )

            // --- Divider + Header: "Ngân hàng thụ hưởng" ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_bic),
                        value = transaction.receiveBank.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_address),
                        value = transaction.benBankName.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                        value = transaction.benBankCountry.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                        value = transaction.benBankDistrict.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                        value = transaction.benBankWard.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                        value = transaction.benBankStreet.orEmpty(),
                    ),
                ),
            )

            // --- Divider + Header: "Ngân hàng trung gian" ---
            add(ItemResult())
            add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_banks),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_bic),
                        value = transaction.midBankCode.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_address),
                        value = transaction.midBankName2.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_country),
                        value = transaction.midBankCountry.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_district),
                        value = transaction.midBankDistrict.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_ward),
                        value = transaction.midBankWard.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_foreign_street),
                        value = transaction.midBankStreet.orEmpty(),
                    ),
                ),
            )

            // --- Files (nếu có) ---
            if (!transaction.listFile.isNullOrEmpty()) {
                add(ItemResult())
                add(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                        titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                    ),
                )

                transaction.listFile?.firstOrNull()?.let { file ->
                    add(
                        ItemResult(
                            title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_transfer_detail_file),
                            value = file.fileName.orEmpty(),
                        ),
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                FoundationRichText(
                    modifier = Modifier.weight(1f),
                    style = FoundationDesignSystem.Typography.captionCaptionL,
                ) {
                    appendColored(
                        resourceProvider.getString(R.string.transaction_number_label),
                        resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                    )
                    append(" ")
                    appendBold(
                        transaction.mtId ?: "",
                        resourceProvider.getComposeColor(R.color.foundation_character_primary),
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FoundationDesignSystem.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    modifier = Modifier.weight(1f),
                    text = transaction.tranTypeName ?: "",
                    iconTextGap = FoundationDesignSystem.Sizer.Gap.gap4,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = R.drawable.ic_trasoat,
                            size = FoundationDesignSystem.Sizer.Icon.icon24,
                        ),
                    ),
                    style = FoundationDesignSystem.Typography.captionCaptionLBold,
                    color = FoundationDesignSystem.Colors.characterPrimary,
                )
            }

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_list_transaction_number),
                value = transaction.tracerMtId ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.account_detail_transaction_type),
                value = transaction.tranSubTypeName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.transaction_label_content),
                value = transaction.tracerContent ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )
            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.activityLogs?.createdBy?.processDate ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionLBold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }

    @Composable
    fun AppendPrefSection(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        val pref = transaction.pref ?: return

        // Tiêu đề chung
        contentLst.add(
            ItemResult(
                title = stringResource(R.string.checker_trace_init_title_trace),
                titleColor = FoundationDesignSystem.Colors.characterHighlighted,
            ),
        )

        when (pref.tranType) {
            Tags.CHECKER_TRANTYPE_CT,
            Tags.CHECKER_TRANTYPE_HU,
            Tags.CHECKER_TRANTYPE_BA,
            -> {
                TransferCT_HU_BA(contentLst, pref)
            }

            Tags.CHECKER_TRANTYPE_SX,
            Tags.CHECKER_TRANTYPE_SL,
            Tags.CHECKER_TRANTYPE_SLO,
            -> {
                TransferSX_SL_SLO(contentLst, pref)
            }

            Tags.CHECKER_TRANTYPE_TX,
            Tags.CHECKER_TRANTYPE_BTX,
            -> {
                TransferTX_BTX(contentLst, pref)
            }

            Tags.CHECKER_TRANTYPE_IF -> {
                TransferIF(contentLst, pref)
            }

            Tags.CHECKER_TRANTYPE_FX,
            Tags.CHECKER_TRANTYPE_FXR,
            -> {
                TransferForeignDetail(contentLst, pref)
            }

            else -> {
                contentLst.addAll(DefaultDetail(pref))
            }
        }
    }

    // ==== CT / HU / BA ====
    @Composable
    fun TransferCT_HU_BA(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        contentLst.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
                    value = "${transaction.fromAccountNo.orEmpty()} - ${transaction.currency.orEmpty()} - ${transaction.fromAccountName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_to_account),
                    value = "${transaction.toAccountNo.orEmpty()}\n${transaction.receiveName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_beneficiary_bank),
                    value = transaction.receiveBankName.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                    value = Utils.g().getDotMoneyHasCcy(transaction.amount.orEmpty(), transaction.currency.orEmpty()),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
                    value = Utils.g().getDotMoneyHasCcy(transaction.feeAmount.orEmpty(), transaction.currency.orEmpty()),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                    value = transaction.feePayMethodDesc.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
                    value = transaction.remark.orEmpty(),
                ),
            ),
        )

        transaction.listFile?.firstOrNull()?.let { file ->
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee_method),
                    value = file.fileName.orEmpty(),
                ),
            )
        }

        contentLst.add(
            ItemResult(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
                value = transaction.createdDate.orEmpty(),
            ),
        )

        transaction.process_time?.takeIf { it.isNotEmpty() }?.let {
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_transfer_date_schedule),
                    value = it,
                ),
            )
        }
    }

    // ==== SX / SL / SLO ====
    @Composable
    fun TransferSX_SL_SLO(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        contentLst.add(
            ItemResult(
                title = stringResource(com.vietinbank.core_ui.R.string.maker_transfer_dashboard_amount),
                value = Utils.g().getDotMoneyHasCcy(transaction.amount.orEmpty(), transaction.currency.orEmpty()),
            ),
        )

        if (transaction.tranType == Tags.CHECKER_TRANTYPE_SX) {
            transaction.exchangeRate?.firstOrNull()?.let { ex ->
                contentLst.add(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_salary_detail),
                        value = ex.toString(),
                    ),
                )
                contentLst.add(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_salary_detail_debit_amount),
                        value = ex.toString(),
                    ),
                )
            }
        }

        contentLst.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
                    value = Utils.g().getDotMoneyHasCcy(transaction.feeAmount.orEmpty(), transaction.currency.orEmpty()),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_content),
                    value = transaction.remark.orEmpty(),
                ),
            ),
        )

        transaction.listFile?.firstOrNull()?.let { file ->
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.maker_payment_order_dashboard_upload_file_name_title),
                    value = file.fileName.orEmpty(),
                ),
            )
        }
        transaction.listFile2?.firstOrNull()?.let { file ->
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_file),
                    value = file.fileName.orEmpty(),
                ),
            )
        }

        contentLst.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_process_time),
                    value = transaction.createdDate.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_transfer_time),
                    value = if (transaction.process_time.isNullOrEmpty()) {
                        stringResource(com.vietinbank.core_ui.R.string.transaction_transfer_immediate)
                    } else {
                        stringResource(com.vietinbank.core_ui.R.string.transaction_transfer_scheduled)
                    },
                ),
            ),
        )

        transaction.process_time?.takeIf { it.isNotEmpty() }?.let {
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_appointment_date),
                    value = it,
                ),
            )
        }
    }

    // ==== TX / BTX (Thuế nội địa) ====
    @Composable
    fun TransferTX_BTX(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        contentLst.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax),
                    value = transaction.taxMethodName.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_name),
                    value = transaction.payname.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_tax_number),
                    value = transaction.paycode.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_address),
                    value = transaction.payadd.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.manager_filter_from_account),
                    value = transaction.fromAccountNo.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.feature_checker_bank),
                    value = transaction.branchName.orEmpty(),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_province),
                    value = "${transaction.provinceCode.orEmpty()} - ${transaction.provinceName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_area),
                    value = "${transaction.areaCode.orEmpty()} - ${transaction.areaName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_treasury),
                    value = "${transaction.treasuryCode.orEmpty()} - ${transaction.treasuryName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collection),
                    value = "${transaction.collectionAccountNo.orEmpty()} - ${transaction.collectionAccountName.orEmpty()}",
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_collect),
                    value = "${transaction.collectAgencyCode.orEmpty()} - ${transaction.collectAgencyName.orEmpty()}",
                ),
            ),
        )

        if (Tags.NSNN_CHECKER_TRANTYPE_ND == transaction.taxType) {
            contentLst.addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_bu),
                        value = "${transaction.bucode.orEmpty()} - ${transaction.buname.orEmpty()}",
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_ofi),
                        value = "${transaction.oficode.orEmpty()} - ${transaction.ofiname.orEmpty()}",
                    ),
                ),
            )
        }

        contentLst.add(
            ItemResult(
                title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_chap),
                value = "${transaction.chapCode.orEmpty()} - ${transaction.chapName.orEmpty()}",
            ),
        )

        transaction.declareNumber?.takeIf { it.isNotEmpty() }?.let {
            contentLst.add(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_custom),
                    value = it,
                ),
            )
        }

        if (Tags.NSNN_CHECKER_TRANTYPE_ND == transaction.taxType) {
            contentLst.addAll(
                listOf(
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_custom_date),
                        value = transaction.declareDate.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_ie),
                        value = "${transaction.ieType.orEmpty()} - ${transaction.ieName.orEmpty()}",
                    ),
                ),
            )
        }

        // Detail
        contentLst.addAll(
            listOf(
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.checker_trace_init_customs_inland_detail_total),
                    value = Utils.g().getDotMoneyHasCcy(transaction.amount.orEmpty(), transaction.currency.orEmpty()),
                ),
                ItemResult(
                    title = stringResource(com.vietinbank.core_ui.R.string.transaction_label_fee),
                    value = Utils.g().getDotMoneyHasCcy(transaction.feeAmount.orEmpty(), transaction.currency.orEmpty()),
                ),
            ),
        )

        // Nếu có danh sách item thuế chi tiết
        transaction.items?.let { items ->
            items.forEachIndexed { index, item ->
                contentLst.addAll(buildTaxItemResults(item))
                if (items.size > 1 && index < items.lastIndex) {
                    contentLst.add(ItemResult()) // divider marker
                }
            }
        }
    }

    // ==== IF (Hạ tầng) ====
    @Composable
    fun TransferIF(
        contentLst: MutableList<ItemResult>,
        transaction: TransDetailDomain,
    ) {
        transaction.providerCode?.let { providerCode ->

            if (Tags.NSNN_CHECKER_TRANTYPE_IN == providerCode) {
                contentLst.add(
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure),
                        value = (transaction.providerCode.orEmpty() + transaction.providerName.orEmpty()),
                    ),
                )
            }

            // tax method
            contentLst.add(
                ItemResult(
                    title = stringResource(R.string.checker_trace_init_customs_inland_tax),
                    value = transaction.taxMethodName.orEmpty(),
                ),
            )

            // pay info
            contentLst.addAll(
                listOf(
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_customs_inland_tax_name),
                        value = transaction.payname.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_customs_inland_tax_number),
                        value = transaction.paycode.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_customs_inland_address),
                        value = transaction.payadd.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(R.string.manager_filter_from_account),
                        value = transaction.fromAccountNo.orEmpty(),
                    ),
                    ItemResult(
                        title = stringResource(R.string.feature_checker_bank),
                        value = transaction.branchName.orEmpty(),
                    ),
                ),
            )

            if (Tags.NSNN_CHECKER_TRANTYPE_IN != providerCode) {
                contentLst.add(ItemResult()) // divider
                contentLst.add(
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_customs_inland),
                        titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                    ),
                )
                contentLst.addAll(
                    listOf(
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_province),
                            value = "${transaction.provinceCode.orEmpty()} - ${transaction.provinceName.orEmpty()}",
                        ),
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_area),
                            value = "${transaction.areaCode.orEmpty()} - ${transaction.areaName.orEmpty()}",
                        ),
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_treasury),
                            value = "${transaction.treasuryCode.orEmpty()} - ${transaction.treasuryName.orEmpty()}",
                        ),
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_collection),
                            value = transaction.collectionAccountNo.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_collect),
                            value = "${transaction.collectAgencyCode.orEmpty()} - ${transaction.collectAgencyName.orEmpty()}",
                        ),
                    ),
                )

                if (!transaction.declareNumber.isNullOrEmpty()) {
                    contentLst.add(
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_custom),
                            value = transaction.declareNumber.orEmpty(),
                        ),
                    )
                }
            }

            contentLst.add(ItemResult()) // divider
            contentLst.add(
                ItemResult(
                    title = stringResource(R.string.checker_trace_init_infrastructure_detail),
                    titleColor = FoundationDesignSystem.Colors.characterHighlighted,
                ),
            )
            contentLst.addAll(
                listOf(
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure_provider),
                        value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == providerCode) {
                            transaction.invoiceId.orEmpty()
                        } else {
                            transaction.docId.orEmpty()
                        },
                    ),
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure_provider_doc),
                        value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == providerCode) {
                            transaction.voucherNumber.orEmpty()
                        } else {
                            transaction.docNum.orEmpty()
                        },
                    ),
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure_provider_syb),
                        value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == providerCode) {
                            transaction.voucherSymbol.orEmpty()
                        } else {
                            transaction.docSign.orEmpty()
                        },
                    ),
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure_provider_date),
                        value = if (Tags.NSNN_CHECKER_TRANTYPE_IN == providerCode) {
                            transaction.voucherDate.orEmpty()
                        } else {
                            transaction.docDate.orEmpty()
                        },
                    ),
                ),
            )

            if (Tags.NSNN_CHECKER_TRANTYPE_IN != providerCode) {
                contentLst.addAll(
                    listOf(
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_customs_inland_chap),
                            value = transaction.chapCode.orEmpty(),
                        ),
                        ItemResult(
                            title = stringResource(R.string.checker_trace_init_infrastructure_provider_subsect),
                            value = transaction.subsect.orEmpty(),
                        ),
                    ),
                )
            }

            // Amount + Fee
            contentLst.addAll(
                listOf(
                    ItemResult(
                        title = stringResource(R.string.checker_trace_init_infrastructure_provider_amount),
                        value = Utils.g().getDotMoneyHasCcy(transaction.amount.orEmpty(), transaction.currency.orEmpty()),
                    ),
                    ItemResult(
                        title = stringResource(R.string.transaction_label_fee),
                        value = Utils.g().getDotMoneyHasCcy(transaction.feeAmount.orEmpty(), transaction.currency.orEmpty()),
                    ),
                ),
            )
        }
    }
    override fun isGetBankList(): Boolean {
        return true
    }

    override fun isNewUI(): Boolean {
        return true
    }
}