package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_SSS
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.components.text.foundation.FoundationClickableText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import com.vietinbank.feature_transaction_manage.R
import com.vietinbank.feature_transaction_manage.fragment.render.GuaranteeIssueManagerImpl.Companion.FADE_DURATION
import java.text.SimpleDateFormat
import java.util.Locale
import javax.inject.Inject

/**
 * Render phat hành bảo lanh
 * */
class GuaranteeIssueManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {

    companion object {
        const val FADE_DURATION: Int = 300
        const val TYPE_GDN_GUARANTEE = "GDN"
    }

    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .background(Color.White)
                .padding(16.dp),
        ) {
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Padding.padding8))
            PendingApprovalCard(
                transaction = transaction,
            )
            HorizontalDivider(
                modifier = Modifier.padding(
                    vertical = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
            )
            SectionHeader(title = stringResource(id = R.string.feature_manage_guarantee_request))
            TransactionDetailsCard {
                transaction?.fileGNN?.filter {
                    !it.attachmentType.isNullOrEmpty() && it.attachmentType == TYPE_GDN_GUARANTEE
                }?.forEach { file ->
                    KeyValueRow(
                        label = when (file.attachmentType) {
                            TYPE_GDN_GUARANTEE -> stringResource(R.string.sdbl_online_paper)
                            else -> ""
                        },
                        value = file.fileName ?: "",
                        isHyperlink = true,
                        onClick = {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        },
                    )
                }
            }
            HorizontalDivider(
                modifier = Modifier.padding(
                    vertical = FoundationDesignSystem.Sizer.Padding.padding16,
                ),
            )
            SectionHeader(title = stringResource(id = R.string.feature_manage_general_information))
            Spacer(modifier = Modifier.height(16.dp))
            TransactionDetailsCard {
                KeyValueRow(
                    label = stringResource(id = R.string.feature_manage_transaction_no),
                    value = transaction?.mtId ?: "",
                )
                KeyValueRow(
                    label = stringResource(id = R.string.vtb_bl_type),
                    value = transaction?.documentTypeName ?: "",
                    isLast = true,
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_manage_info_customer),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.vtb_customer_name), value = transaction?.cifName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.vtb_business_registration_number), value = transaction?.guaranteeCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_date_range), value = transaction?.dateRange?.toFormattedDate() ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_issue_by), value = transaction?.issuesBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_head_address), value = transaction?.headQuarters ?: "")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_representative_name),
                        value = transaction?.representativeName ?: "",
                    )
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_authority), value = transaction?.authority ?: "")
                },
            )
            HorizontalDividerDetail()
            Spacer(modifier = Modifier.height(16.dp))
            SectionHeader(title = stringResource(id = R.string.feature_manage_info_contract))
            Spacer(modifier = Modifier.height(16.dp))
            TransactionDetailsCard {
                KeyValueRow(label = stringResource(id = R.string.vtb_mbnt_contract_number), value = transaction?.contractNo ?: "")
                KeyValueRow(label = stringResource(id = R.string.vtb_contract_date), value = transaction?.contractDate?.toFormattedDate() ?: "")
            }
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_manage_info_propose),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.feature_form_ckbl), value = transaction?.documentTypeName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_issuing_bank), value = "${transaction?.branch} - ${transaction?.branchName}")
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_info_propose),
                        value = transaction?.guaranteePurpose ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_type_change_only),
                        value = "${transaction?.effectiveStartDate ?: ""} - ${transaction?.effectiveEndDate ?: ""}",
                    )
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_manage_info_receiver),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_receiver_info), value = transaction?.beneficiaryName ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_head_transaction), value = transaction?.beneficiaryAddress ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_register_business), value = transaction?.beneficiaryCode ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_issue_by), value = transaction?.receiverIssuedBy ?: "")
                    KeyValueRow(label = stringResource(id = R.string.feature_manage_date_range), value = transaction?.receiverDateRange?.toFormattedDate() ?: "")
                },
            )
            HorizontalDividerDetail()
            CollapsibleSection(
                title = stringResource(id = R.string.feature_manage_info_guarantee),
                initiallyCollapsed = true,
                content = {
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_how_to_ph),
                        value = MessageType.fromTypeId(transaction?.messageType ?: "").getMessageTypeLabel(),
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_type_ph),
                        value = transaction?.issueTypeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_lang_used),
                        value = transaction?.language ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_condition),
                        value = transaction?.conditionPerform ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.sdbl_send_type),
                        value = transaction?.sendTypeDesc ?: "",
                    )
                    if (transaction?.messageType == "1" || transaction?.messageType == "01") {
                        KeyValueRow(
                            label = stringResource(id = R.string.nhct_commit_bl),
                            value = transaction.sendTypeCmnd ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.content_cccd_passport),
                            value = transaction.sendTypeNo ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_manage_date_range),
                            value = transaction.sendTypeDate?.toFormattedDate() ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_manage_issue_by),
                            value = transaction.sendTypeBy ?: "",
                        )
                    }
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_time_to_fee),
                        value = transaction?.feeTimeDesc ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.details),
                        value = transaction?.feeDesc ?: "",
                    )
//                KeyValueRow(
//                    label = stringResource(id = R.string.feature_manage_fee_bl),
//                    value = transaction?.feeGuarantee ?: "",
//                )
//                KeyValueRow(
//                    label = stringResource(id = R.string.details),
//                    value = transaction?.feeGuaranteeDesc ?: "",
//                )
                    KeyValueRow(
                        label = stringResource(id = R.string.feature_manage_way_to_ensure),
                        value = Measure.fromTypeId(transaction?.measure ?: "").firstOrNull()?.getMeasureLabel() ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.measure_account),
                        value = transaction?.measureAcct ?: "",
                    )
                    KeyValueRow(
                        label = stringResource(id = R.string.measure_amount),
                        value = transaction?.measureAmount ?: "",
                    )

                    val measure = Measure.fromTypeId(transaction?.measure ?: "").firstOrNull()
                    if (measure == Measure.TYPE_3) {
                        KeyValueRow(
                            label = stringResource(id = R.string.feature_manage_detail_way_to_eusure),
                            value = transaction?.measureDesc ?: "",
                        )
                    }
//                KeyValueRow(
//                    label = stringResource(id = R.string.content_customer_ensure),
//                    value = transaction?.commitContent ?: "",
//                )
                    if (transaction?.messageType == "3") {
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_bic_code),
                            value = transaction?.biCodeInfo?.bicCode ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_name),
                            value = transaction?.biCodeInfo?.bankName ?: "",
                        )
                        KeyValueRow(
                            label = stringResource(id = R.string.vtb_address),
                            value = transaction?.biCodeInfo?.bankAddr ?: "",
                        )
                    }
                },
            )
            HorizontalDividerDetail()
            Box(
                modifier = Modifier
                    .padding(
                        start = FoundationDesignSystem.Sizer.Padding.padding16,
                        end = FoundationDesignSystem.Sizer.Padding.padding16,
                        top = FoundationDesignSystem.Sizer.Padding.padding8,
                        bottom = FoundationDesignSystem.Sizer.Padding.padding16,
                    )
                    .safeClickable {
//                    actions?.onAttachmentsClick?.invoke()
                    },
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(
                            id = R.string.feature_manage_attatchment_details_info,
                        ),
                        style = FoundationDesignSystem.Typography.interactionButton,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                    Icon(
                        modifier = Modifier
                            .size(24.dp),
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_right),
                        contentDescription = "next",
                        tint = FoundationDesignSystem.Colors.characterHighlighted,
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        val amountDesc = StringBuilder()
        try {
            transaction?.amount?.getAmountServer()?.let {
                if (it.isNotEmpty()) {
                    amountDesc.append(
                        Utils.g()
                            .getDotMoneyHasCcy(
                                transaction?.amount ?: "",
                                transaction?.currency ?: "",
                            ),
                    )
                }
            }
            transaction?.amount2?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction?.amount2 ?: "",
                                transaction?.currency2 ?: "",
                            )
                        }",
                    )
                }
            }
            transaction?.amount3?.getAmountServer()?.let {
                if (it.isNotEmpty() && "0" != it) {
                    amountDesc.append(
                        "\n${
                            Utils.g().getDotMoneyHasCcy(
                                transaction?.amount3 ?: "",
                                transaction?.currency3 ?: "",
                            )
                        }",
                    )
                }
            }
        } catch (_: Exception) {
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(12.dp)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
        ) {
            Row(
                verticalAlignment = Alignment.Bottom,
            ) {
                FoundationText(
                    text = stringResource(id = com.vietinbank.core_ui.R.string.account_transaction_no),
                    color = FoundationDesignSystem.Colors.characterSecondary,
                    style = FoundationDesignSystem.Typography.captionL,
                )
                FoundationText(
                    text = transaction?.mtId ?: "",
                    modifier = Modifier.padding(start = 4.dp),
                    color = FoundationDesignSystem.Colors.characterPrimary,
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_switch_vertical_24),
                        contentDescription = "Switch icon vertically",
                        tint = AppColors.characterPrimary,
                        modifier = Modifier
                            .padding(end = 4.dp)
                            .safeClickable {
                                // TODO: switch vertical action
                            },
                    )

                    FoundationText(
                        text = transaction?.tranTypeName?.trim() ?: "",
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        style = FoundationDesignSystem.Typography.captionLSemibold,
                    )
                }

                val formattedAmount = remember(transaction?.amount, transaction?.currency) {
                    try {
                        val cleanAmount = transaction?.amount?.getAmountServer()
                        Utils.g().getDotMoneyHasCcy(cleanAmount ?: "", transaction?.currency ?: "")
                    } catch (_: Exception) {
                        "${transaction?.amount} ${transaction?.currency}"
                    }
                }
                FoundationText(
                    text = formattedAmount,
                    modifier = Modifier.align(Alignment.Top),
                    style = FoundationDesignSystem.Typography.captionLSemibold,
                )
            }

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.feature_transaction_manage.R.string.feature_manage_guaruatee_receiver),
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                value = transaction?.addressOfReceiver ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = 8.dp),
                title = stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_documentType),
                value = GuaranteeType.fromTypeId(transaction?.tranType ?: "").getGuaranteeTypeLabel(),
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.feature_transaction_manage.R.string.feature_manage_creator),
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                value = transaction?.creator ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_create_time),
                value = transaction?.createdDate?.getDateToFormat(
                    dd_MM_yyyy_HH_mm_ss_SSS,
                    dd_MM_yyyy_HH_mm_ss,
                ),
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap8),
                title = stringResource(com.vietinbank.core_ui.R.string.manager_detail_status),
                value = transaction?.statusName ?: "",
                titleStyle = FoundationDesignSystem.Typography.captionL,
                valueStyle = FoundationDesignSystem.Typography.captionLSemibold,
                valueColor = transaction?.statusCode?.getColorStatus(),
            )
        }
    }
}

enum class GuaranteeType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    TYPE_4(4),
    TYPE_5(5),
    TYPE_6(6),
    TYPE_7(7),
    TYPE_8(8),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getGuaranteeTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_1)
            TYPE_2 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_2)
            TYPE_3 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            TYPE_4 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            TYPE_5 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            TYPE_6 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            TYPE_7 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            TYPE_8 -> stringResource(id = com.vietinbank.feature_transaction_manage.R.string.feature_manage_document_type_3)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: String): GuaranteeType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: DEFAULT_TYPE
        }
    }
}

@Composable
fun PendingApprovalCard(
    transaction: TransDetailDomain?,
    modifier: Modifier = Modifier,
) {
    // Simple status chip using FoundationStatus
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = FoundationDesignSystem.Sizer.Padding.padding16),
    ) {
        FoundationStatus(
            statusMessage = transaction?.statusName
                ?: stringResource(com.vietinbank.core_ui.R.string.transaction_status_pending),
            statusCode = when (transaction?.status) {
                "approved" -> Status.Success
                "rejected" -> Status.Fail
                else -> Status.Pending
            },
        )
        Spacer(modifier = Modifier.weight(1f))
    }
}

@Composable
fun HorizontalDividerDetail(
    color: androidx.compose.ui.graphics.Color = FoundationDesignSystem.Colors.paletteNeutral100,
    thickness: Dp = 1.dp,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = FoundationDesignSystem.Sizer.Padding.padding16,
                end = FoundationDesignSystem.Sizer.Padding.padding16,
                top = FoundationDesignSystem.Sizer.Padding.padding4,
            )
            .height(thickness)
            .background(color),
    )
}

@Composable
fun SectionHeader(
    title: String,
    hasMoreInfo: Boolean = false,
    isCollapse: Boolean = false,
    onInfoClick: (() -> Unit)? = null,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = 16.dp,
            ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        FoundationText(
            text = title,
            style = FoundationDesignSystem.Typography.bodyB2,
            color = FoundationDesignSystem.Colors.characterHighlighted,
        )

        Spacer(modifier = Modifier.weight(1f))

        if (hasMoreInfo) {
            if (isCollapse) {
                IconButton(
                    onClick = {
                        onInfoClick?.invoke()
                    },
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_button_more_24),
                        contentDescription = "Expand icon",
                    )
                }
            } else {
                IconButton(
                    onClick = {
                        onInfoClick?.invoke()
                    },
                ) {
                    Icon(
                        painter = painterResource(id = com.vietinbank.core_ui.R.drawable.ic_button_less_24),
                        contentDescription = "Expand icon",
                    )
                }
            }
        }
    }
}

@Composable
fun TransactionDetailsCard(content: @Composable () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = FoundationDesignSystem.Colors.transparent),
    ) {
        Column(
            modifier = Modifier.padding(
                horizontal = 16.dp,
            ),
        ) {
            content()
        }
    }
}

@Composable
fun KeyValueRow(
    label: String,
    value: String,
    isHighlighted: Boolean = false,
    isHyperlink: Boolean = false,
    subValue: String? = null,
    iconResource: Int? = null, // Thêm tham số icon
    isWarning: Boolean = false, // Thêm tham số cảnh báo
    maxLine: Int = 6,
    isLast: Boolean = false,
    onClick: (() -> Unit)? = null,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                bottom = if (!isLast) {
                    12.dp
                } else {
                    0.dp
                },
            ),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top,
        ) {
            if (iconResource != null) {
                Icon(
                    painter = painterResource(id = iconResource),
                    contentDescription = null,
                    tint = if (isWarning) Color.Red else Color.Unspecified,
                    modifier = Modifier.size(24.dp),
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            FoundationText(
                modifier = Modifier.weight(1f),
                text = label,
                color = FoundationDesignSystem.Colors.characterSecondary,
                style = FoundationDesignSystem.Typography.bodyB2,
            )

            Spacer(modifier = Modifier.width(24.dp))

            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.End,
            ) {
                FoundationClickableText(
                    text = value,
                    style = if (isHyperlink) {
                        FoundationDesignSystem.Typography.bodyB2Emphasized
                    } else {
                        FoundationDesignSystem.Typography.bodyB2
                    },
                    underline = isHyperlink,
                    color = when {
                        isHyperlink -> FoundationDesignSystem.Colors.textSelected
                        isHighlighted -> FoundationDesignSystem.Colors.characterPrimary
                        isWarning -> Color.Red
                        else -> FoundationDesignSystem.Colors.characterPrimary
                    },
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.End,
                    onClick = {
                        onClick?.invoke()
                    },
                )

                if (!subValue.isNullOrEmpty()) {
                    FoundationText(
                        text = subValue,
                        style = FoundationDesignSystem.Typography.bodyB2,
                        color = FoundationDesignSystem.Colors.characterPrimary,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.End,
                    )
                }
            }
        }
    }
}

@Composable
fun CollapsibleSection(
    title: String,
    initiallyCollapsed: Boolean = true,
    content: @Composable () -> Unit,
) {
    var isCollapse by remember { mutableStateOf(initiallyCollapsed) }

    Column {
        SectionHeader(
            title = title,
            hasMoreInfo = true,
            isCollapse = isCollapse,
            onInfoClick = {
                isCollapse = !isCollapse
            },
        )

        AnimatedVisibility(
            visible = !isCollapse,
            enter = expandVertically(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ) + fadeIn(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ),
            exit = shrinkVertically(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ) + fadeOut(
                animationSpec = tween(durationMillis = FADE_DURATION),
            ),
        ) {
            TransactionDetailsCard {
                content()
            }
        }
    }
}

enum class FeeType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getFeeTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(id = R.string.fee_type_1)
            TYPE_2 -> stringResource(id = R.string.fee_type_2)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): FeeType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String): FeeType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: DEFAULT_TYPE
        }
    }
}

enum class MessageType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    TYPE_10(10),
    TYPE_11(typeId = 11),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getMessageTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.feature_manage_message_type_1)
            TYPE_2 -> stringResource(R.string.feature_manage_message_type_2)
            TYPE_3 -> stringResource(R.string.feature_manage_message_type_3)
            TYPE_10 -> stringResource(R.string.feature_manage_message_type_1)
            TYPE_11 -> stringResource(R.string.feature_manage_message_type_11)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): MessageType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String = "0"): MessageType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: DEFAULT_TYPE
        }
    }
}

enum class Measure(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getMeasureLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.measure_1)
            TYPE_2 -> stringResource(R.string.measure_2)
            TYPE_3 -> stringResource(R.string.measure_3)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: Int): Measure {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }

        fun fromTypeId(typeId: String): List<Measure> {
            return typeId.split("-").map { it.toIntOrNull() ?: 0 }.map { fromTypeId(it) }
        }
    }
}

enum class SendType(val typeId: Int) {
    TYPE_1(1),
    TYPE_2(2),
    TYPE_3(3),
    DEFAULT_TYPE(0),
    ;

    @Composable
    fun getSendTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(R.string.send_type_1)
            TYPE_2 -> stringResource(R.string.send_type_2)
            TYPE_3 -> stringResource(R.string.send_type_3)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: String): SendType {
            return entries.find { it.typeId == typeId.toIntOrNull() } ?: SendType.DEFAULT_TYPE
        }
    }
}

fun String.toFormattedDate(inputPattern: String = "yyyy-MM-dd HH:mm:ss"): String {
    return try {
        val inputFormat = SimpleDateFormat(inputPattern, Locale.getDefault())
        val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        val date = inputFormat.parse(this)
        date?.let { outputFormat.format(it) } ?: ""
    } catch (e: Exception) {
        this
    }
}

enum class DocumentType(val typeId: String) {
    TYPE_1("1"),
    TYPE_2("2"),
    TYPE_3("3"),
    TYPE_4("4"),
    TYPE_5("5"),
    TYPE_6("6"),
    TYPE_7("7"),
    TYPE_8("8"),
    DEFAULT_TYPE("0"),
    ;

    @Composable
    fun getDocumentTypeLabel(): String {
        return when (this) {
            TYPE_1 -> stringResource(id = R.string.feature_manage_document_type_1)
            TYPE_2 -> stringResource(id = R.string.feature_manage_document_type_2)
            TYPE_3 -> stringResource(id = R.string.feature_manage_document_type_3)
            TYPE_4 -> stringResource(id = R.string.feature_manage_document_type_4)
            TYPE_5 -> stringResource(id = R.string.feature_manage_document_type_5)
            TYPE_6 -> stringResource(id = R.string.feature_manage_document_type_6)
            TYPE_7 -> stringResource(id = R.string.feature_manage_document_type_7)
            TYPE_8 -> stringResource(id = R.string.feature_manage_document_type_8)
            DEFAULT_TYPE -> ""
        }
    }

    companion object {
        fun fromTypeId(typeId: String): DocumentType {
            return entries.find { it.typeId == typeId } ?: DEFAULT_TYPE
        }
    }
}