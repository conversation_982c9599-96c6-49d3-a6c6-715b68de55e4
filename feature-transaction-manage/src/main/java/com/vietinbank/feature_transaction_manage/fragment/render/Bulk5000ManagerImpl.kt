package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_1
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.components.text.foundation.appendSemiBold
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class Bulk5000ManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
    private val resourceProvider: IResourceProvider,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        transaction: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        if (transaction == null) return
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                .background(FDS.Colors.white)
                .padding(vertical = FDS.Sizer.Gap.gap24),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationInfoHorizontal(
                    title = stringResource(R.string.account_transaction_no),
                    value = transaction.mtId ?: "",
                    titleStyle = FDS.Typography.bodyB2,
                    valueStyle = FDS.Typography.bodyB2,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_total_transaction),
                    value = transaction.mtId ?: "",
                    titleStyle = FDS.Typography.bodyB2,
                    valueStyle = FDS.Typography.bodyB2,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_id),
                    value = transaction.mtId ?: "",
                    titleStyle = FDS.Typography.bodyB2,
                    valueStyle = FDS.Typography.bodyB2,
                )

                val (fileName, isFile) = if (transaction.fileName.isNullOrEmpty()) {
                    stringResource(R.string.salary_not_found_file) to false
                } else {
                    transaction.fileName to true
                }

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_name),
                    value = fileName,
                    isValueUnderline = isFile,
                    titleStyle = FDS.Typography.bodyB2,
                    valueStyle = FDS.Typography.bodyB2,
                    valueColor = if (isFile) {
                        FDS.Colors.characterHighlighted
                    } else {
                        FDS.Colors.characterPrimary
                    },
                    onClick = {
//                            if (isFile) {
//                                onFieldClick.invoke(TransactionFieldEvent.FileAttachmentClick(null))
//                            }
                    },
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_fee_method),
                    value = transaction.feePayMethodDesc ?: "",
                    titleStyle = FDS.Typography.captionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.approve_file_total_fee),
                    value = transaction.feePayMethodDesc ?: "",
                    titleStyle = FDS.Typography.captionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                )

                FoundationInfoHorizontal(
                    title = stringResource(R.string.transaction_label_transfer_type),
                    value = if (transaction.process_time.isNullOrEmpty()) {
                        stringResource(R.string.maker_transfer_dashboard_now)
                    } else {
                        stringResource(R.string.maker_transfer_dashboard_schedule)
                    },
                    titleStyle = FDS.Typography.captionL,
                    valueStyle = FDS.Typography.captionLSemibold,
                )
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            FoundationRichText(
                modifier = Modifier.fillMaxWidth(),
                style = FDS.Typography.captionL,
            ) {
                appendColored(
                    resourceProvider.getString(R.string.approve_file_id),
                    resourceProvider.getComposeColor(R.color.foundation_character_secondary),
                )
                append(" ")
                appendSemiBold(
                    transaction.bulkID ?: "",
                    resourceProvider.getComposeColor(R.color.foundation_character_primary),
                )
            }

            FoundationIconText(
                text = stringResource(R.string.approve_file_5000),
                iconTextGap = FDS.Sizer.Gap.gap4,
                icons = mapOf(
                    IconPosition.LEFT to IconConfig(
                        icon = R.drawable.ic_common_file_24,
                        size = FDS.Sizer.Icon.icon24,
                    ),
                ),
                style = FDS.Typography.captionLSemibold,
                color = FDS.Colors.characterPrimary,
            )

            val (fileName, onClickFile) = if (transaction.fileName.isNullOrEmpty()) {
                stringResource(R.string.salary_not_found_file) to null
            } else {
                transaction.fileName to {
//                    onAction.invoke(ManagerAction.OnFileClick(transaction.ibFile))
                }
            }

            val (colorFileName, styleFileName) = if (transaction.fileName.isNullOrEmpty()) {
                FDS.Colors.characterPrimary to FDS.Typography.captionL
            } else {
                FDS.Colors.characterHighlighted to FDS.Typography.captionL.copy(textDecoration = TextDecoration.Underline)
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.approve_file_result),
                titleStyle = FDS.Typography.captionL,
                value = fileName,
                valueStyle = styleFileName,
                valueColor = colorFileName,
                onClick = onClickFile,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.approve_file_total_transaction),
                titleStyle = FDS.Typography.captionL,
                value = transaction.total,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_transfer_type),
                value = if (transaction.process_time.isNullOrEmpty()) {
                    stringResource(R.string.maker_transfer_dashboard_now)
                } else {
                    stringResource(R.string.maker_transfer_dashboard_schedule)
                },
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            // thời gian tạo
            transaction.activityLogs?.createdBy?.let {
                if (!it.processDate.isNullOrEmpty()) {
                    FoundationInfoHorizontal(
                        title = stringResource(R.string.manager_detail_create_time),
                        value = it.processDate?.getDateToFormat(
                            dd_MM_yyyy_HH_mm_ss_1,
                            dd_MM_yyyy_HH_mm_ss,
                        ),
                        titleStyle = FDS.Typography.captionL,
                        valueStyle = FDS.Typography.captionLSemibold,
                    )
                }
            }

            // ngày đặt lịch
            if (!transaction.process_time.isNullOrEmpty()) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationText(
                        text = stringResource(R.string.manager_detail_appointment_date),
                        style = FDS.Typography.captionL,
                        color = FDS.Colors.characterSecondary,
                    )
                    if (transaction.isOverProcess) {
                        Image(
                            modifier = Modifier.safeClickable {},
                            painter = painterResource(R.drawable.ic_common_info_16),
                            contentDescription = "",
                        )
                    }
                    FoundationText(
                        modifier = Modifier.weight(1f),
                        text = transaction.process_time ?: "",
                        style = FDS.Typography.captionLSemibold,
                        color = if (transaction.isOverProcess) {
                            FDS.Colors.stateError
                        } else {
                            FDS.Colors.characterPrimary
                        },
                        textAlign = TextAlign.End,
                    )
                }
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }

    @Composable
    private fun ItemBulk() {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16),
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            FoundationText(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(color = FDS.Colors.characterSecondary)) {
                        append(stringResource(R.string.transaction_number_label))
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            color = FDS.Colors.characterPrimary,
                            fontWeight = FontWeight.SemiBold,
                        ),
                    ) {
                        append("file ID")
                    }
                },
                style = FDS.Typography.captionL,
                modifier = Modifier.weight(1f),
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.approve_file_amount),
                value = "Số tiền giao dịch",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_transfer_to),
                value = "Chuyuyển tới",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_content),
                value = "Chuyuyển tới",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_status),
                value = "Trạng thái",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = "05".getColorStatus(),
            )
        }
    }
}