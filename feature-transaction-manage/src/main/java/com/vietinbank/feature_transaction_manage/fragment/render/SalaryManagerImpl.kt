package com.vietinbank.feature_transaction_manage.fragment.render

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_common.utils.captures.MoneyHelper
import com.vietinbank.core_domain.models.checker.TransactionFieldEvent
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.compose.InfoHorizontalView
import com.vietinbank.core_ui.base.compose.getComposeFont
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.theme.AppColors
import com.vietinbank.core_ui.utils.getColorStatus
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel
import javax.inject.Inject
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

class SalaryManagerImpl @Inject constructor(
    private val moneyHelper: MoneyHelper,
) : IRenderManager {
    @Composable
    override fun RenderViewDetail(
        detailModel: TransDetailDomain?,
        onFieldClick: (TransactionFieldEvent) -> Unit,
    ) {
        detailModel?.let { transaction ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
//                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
            ) {
                Text(
                    text = "Thông tin chung",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 12.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(title = "Loại giao dich", value = transaction.tranTypeName)
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số giao dịch",
                        value = transaction.mtId ?: "",
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Trạng thái",
                        value = transaction.statusName,
                        valueColor = AppColors.blue02,
                    )
                    transaction.activityLogs?.createdBy?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người khởi tạo",
                            value = "${it.username} - ${it.processDate}",
                        )
                    }

                    var verifyUser = ""
                    transaction.activityLogs?.verifiedBy?.forEachIndexed { index, item ->
                        verifyUser += "${item.username ?: ""} - ${item.processDate}"
                        if (index < (transaction.activityLogs?.verifiedBy?.size ?: 0) - 1) {
                            verifyUser += "\n"
                        }
                    }
                    if (!verifyUser.isEmpty()) {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Người phê duyệt",
                            value = verifyUser,
                        )
                    }

                    transaction.rejectReason?.let {
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Lý do",
                            value = it,
                        )
                    }
                }

                Text(
                    text = "Thông tin tài khoản chuyển",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        title = "Từ tài khoản",
                        value = "${transaction.fromAccountNo} - VND - ${transaction.fromAccountName}",
                    )
                }

                Text(
                    text = "Thông tin chi tiết",
                    style = getComposeFont(5, 14.sp, AppColors.trenBgr),
                    modifier = Modifier.padding(vertical = 12.dp),
                )

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color.White)
                        .padding(vertical = 20.dp, horizontal = 16.dp),
                ) {
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Số tiền",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.amount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                        valueFont = 5,
                        valueColor = AppColors.blue02,
                        valueTwo = moneyHelper.convertAmountToWords(
                            transaction.amount ?: "",
                            transaction.debitCurrency,
                        ),
                    )
                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Phí giao dịch",
                        value = Utils.g().getDotMoneyHasCcy(
                            transaction.feeAmount ?: "",
                            transaction.debitCurrency ?: "",
                        ),
                    )

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Nội dung",
                        value = transaction.remark ?: "",
                    )

                    transaction.listFile?.firstOrNull()?.let { file ->
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "File đính kèm",
                            value = file.fileName,
                            valueColor = AppColors.blue02,
                        ) {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        }
                    }

                    transaction.listFile2?.firstOrNull()?.let { file ->
                        InfoHorizontalView(
                            modifier = Modifier.padding(top = 14.dp),
                            title = "Hồ sơ đính kèm",
                            value = file.fileName,
                            valueColor = AppColors.blue02,
                        ) {
                            onFieldClick(TransactionFieldEvent.FileAttachmentClick(file))
                        }
                    }

                    InfoHorizontalView(
                        modifier = Modifier.padding(top = 14.dp),
                        title = "Thời gian chuyển",
                        value = transaction.createdDate ?: "",
                    )
                }
            }
        }
    }

    @Composable
    override fun RenderViewItemManager(
        transaction: TransRsUIModel,
        onAction: (ManagerAction) -> Unit,
    ) {
        val typeSalary = when (transaction.tranType?.uppercase()) {
            Tags.TYPE_GROUP_SALARY_AUTO -> stringResource(R.string.salary_auto)
            Tags.TYPE_GROUP_SALARY_FOREIGN -> stringResource(R.string.feature_checker_transaction_type_foreign)
            Tags.TYPE_GROUP_SALARY -> stringResource(R.string.salary_bank_auto)
            else -> ""
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = FDS.Sizer.Gap.gap16)
                .safeClickable {
                    onAction.invoke(
                        ManagerAction.OnTransaction(
                            ManagerViewModel.CLICK_DETAIL,
                            transaction,
                        ),
                    )
                },
            verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap4),
            ) {
                FoundationText(
                    text = stringResource(R.string.transaction_number_label),
                    style = FDS.Typography.captionL,
                    color = FDS.Colors.characterSecondary,
                )

                FoundationIconText(
                    modifier = Modifier.safeClickable {
                        transaction.mtId?.let { id ->
                            onAction.invoke(ManagerAction.OnCopyClick(id))
                        }
                    },
                    text = transaction.mtId ?: "",
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    icons = mapOf(
                        IconPosition.RIGHT to IconConfig(
                            icon = R.drawable.ic_account_copy_24,
                            size = FDS.Sizer.Icon.icon24,
                        ),
                    ),
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
            ) {
                FoundationIconText(
                    text = typeSalary,
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    icons = mapOf(
                        IconPosition.LEFT to IconConfig(
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_transfer_24,
                            size = FDS.Sizer.Icon.icon16,
                        ),
                    ),
                )

                FoundationText(
                    modifier = Modifier.weight(1f),
                    text = Utils.g().getDotMoneyHasCcy(
                        transaction.amount ?: "",
                        transaction.currency ?: "",
                    ),
                    style = FDS.Typography.captionLSemibold,
                    color = FDS.Colors.characterPrimary,
                    textAlign = TextAlign.End,
                )
            }

            val (fileName, onClickFile) = if (transaction.fileName.isNullOrEmpty()) {
                stringResource(R.string.salary_not_found_file) to null
            } else {
                transaction.fileName to {
//                    onAction.invoke(ManagerAction.OnFileClick(transaction.ibFile))
                }
            }

            val (colorFileName, styleFileName) = if (transaction.fileName.isNullOrEmpty()) {
                FDS.Colors.characterPrimary to FDS.Typography.captionL
            } else {
                FDS.Colors.characterHighlighted to FDS.Typography.captionL.copy(textDecoration = TextDecoration.Underline)
            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.salary_file),
                value = fileName,
                titleStyle = FDS.Typography.captionL,
                valueColor = colorFileName,
                valueStyle = styleFileName,
                onClick = onClickFile,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.transaction_label_content),
                value = transaction.remark ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_create_time),
                value = transaction.createdDate ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
            )

//            if (!transaction.processTime.isNullOrEmpty()) {
//                val processTime = transaction.processTime.getDateToFormat(
//                    dd_MM_yyyy_HH_mm_ss_2, dd_MM_yyyy_HH_mm,
//                ) ?: ""
//                val processTimeMillis = processTime.toTimeInMillis(dd_MM_yyyy_HH_mm_ss)
//                val isOverdue =
//                    processTimeMillis > 0 && processTimeMillis - Calendar.getInstance().timeInMillis < 0
//
//                FoundationInfoHorizontal(
//                    title = stringResource(R.string.manager_detail_appointment_date),
//                    value = processTime,
//                    titleStyle = FDS.Typography.captionL,
//                    valueStyle = FDS.Typography.captionLSemibold,
//                    valueColor = if (isOverdue) FDS.Colors.stateError else null,
//                )
//            }

            FoundationInfoHorizontal(
                title = stringResource(R.string.manager_detail_status),
                value = transaction.statusName ?: "",
                titleStyle = FDS.Typography.captionL,
                valueStyle = FDS.Typography.captionLSemibold,
                valueColor = transaction.statusCode.getColorStatus(),
            )
        }
    }

    override fun isNewUI(): Boolean {
        return true
    }
}