package com.vietinbank.feature_transaction_manage.fragment

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.manage.ListReportParams
import com.vietinbank.core_domain.models.manage.TransactionReportActions
import com.vietinbank.core_domain.navigation.IConfirmCheckerLauncher
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_transaction_manage.DetailManagerViewModel
import com.vietinbank.feature_transaction_manage.di.IMTransactionNavigator
import com.vietinbank.feature_transaction_manage.screen.MListTransactionScreen
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MTransactionListFragment : BaseFragment<DetailManagerViewModel>() {
    override val viewModel: DetailManagerViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var transNavigator: IMTransactionNavigator

    @Inject
    lateinit var confirmLauncher: IConfirmCheckerLauncher

    @Composable
    override fun ComposeScreen() {
        val actions =
            TransactionReportActions(
                onHomeClick = { },
                onBackClick = { transNavigator.popBackStack() },
                onClick = { action, item ->
                    when (action) {
                        "COPY" -> {
                            requireActivity().viewModelStore.clear()
                            val bundle = Bundle()
                            viewModel.convertToTransfer(item)?.let { napasParam ->
                                bundle.putString(
                                    Tags.COPY_TRANSFER_OBJECT_BUNDLE,
                                    Utils.g().provideGson().toJson(napasParam),
                                )
                            }
                            when (item.tranType) {
                                Tags.TransferType.TYPE_PAYMENT_ORDER_TRANSFER -> {
                                }

                                else -> {
//                                    appNavigator.goToViewPagerTransferFragment(bundle)
                                }
                            }
                        }

                        "ROLE_APPROVE" -> {
                            val bundle = Bundle()
                            bundle.putString(Tags.TRANSACTION_BUNDLE, item.mtId ?: "")
                            bundle.putString(Tags.ACCOUNT_NUMBER_BUNDLE, item.fromAccountNo ?: "")
                            bundle.putString(Tags.SERVICE_ID_BUNDLE, item.tranType ?: "")
                            bundle.putString(Tags.CURRENCY_BUNDLE, item.currency ?: "")
                            appNavigator.goToinquiryApproverListFragment(bundle)
                        }

                        "UNC" -> {
                        }

                        "UNC_CKS" -> {
                        }

                        "TRACE" -> {
                            val bundle = Bundle()
                            bundle.putString(Tags.MTID_STRING_BUNDLE, item.mtId)
                            appNavigator.goToTracePaymentFragment(bundle)
                        }

                        "DELETE" -> {
                            // xoa dien
                            showConfirmDialog("")
                        }

                        // chi tiet
                        else -> {
                            transNavigator.goToDetailsTransaction(
                                Tags.TRANSFER_OBJECT_BUNDLE to Utils.g().provideGson().toJson(item),
                            )
                        }
                    }
                },
                onLoadMore = {
//                    viewModel.getListFilterTransaction()
                },
            )

        AppTheme { MListTransactionScreen(viewModel, actions) }
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        // Lấy dữ liệu transaction từ Bundle
        arguments?.getString(Tags.TRANSACTION_BUNDLE)?.let { jsonData ->
            try {
                printLog("ListReportParams: $jsonData")
                val reportParams =
                    Utils.g().provideGson().fromJson(jsonData, ListReportParams::class.java)
//                viewModel.renderUI(reportParams.trantype)
//                viewModel.setReportParams(reportParams)
            } catch (e: Exception) {
                printLog("Lỗi parse transaction JSON: ${e.message}")
                e.printStackTrace()
            }
        }
        initObserver()
    }

    private fun initObserver() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
            }
        }
    }
}