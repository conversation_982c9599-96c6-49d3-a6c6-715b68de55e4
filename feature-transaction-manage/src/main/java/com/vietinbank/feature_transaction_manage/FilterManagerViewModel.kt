package com.vietinbank.feature_transaction_manage

import android.text.TextUtils
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_1
import com.vietinbank.core_common.extensions.getAmountServer
import com.vietinbank.core_common.extensions.getDayAgo
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.extensions.todayAsString
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.ContactListParams
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.manage.FillDataDomain
import com.vietinbank.core_domain.models.manage.FilterManagerDomain
import com.vietinbank.core_domain.models.manage.FilterReportParams
import com.vietinbank.core_domain.models.manage.TAccountsDomain
import com.vietinbank.core_domain.models.manage.TGroupTypesDomain
import com.vietinbank.core_domain.models.manage.TStatusDomain
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class FilterManagerViewModel
@Inject constructor(
    private val manageUseCase: TransactionManageUseCase,
    private val transferUseCase: TransferUseCase,
    private val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {

    companion object {
        const val FILTER_RESULT_KEY = "FILTER_RESULT_KEY"
        const val FILTER_RESULT_BUNDLE = "FILTER_RESULT_BUNDLE"

        // ngay tim kiem
        const val DATE_INIT = "0"
        const val DATE_APPROVED = "1"

        // fill bottom sheet
        const val FILL_TRANSACTION = "FILL_TRANSACTION"
        const val FILL_STATUS = "FILL_STATUS"
        const val FILL_FROM_ACCOUNT = "FILL_FROM_ACCOUNT"
        const val FILL_BANK = "FILL_BANK"
        const val FILL_CONTACT = "FILL_CONTACT"
        const val FILL_SORT_OPTION = "FILL_SORT_OPTION"

        // input
        const val INPUT_START_AMOUNT = "INPUT_START_AMOUNT"
        const val INPUT_END_AMOUNT = "INPUT_END_AMOUNT"
        const val INPUT_ACCOUNT_NO = "INPUT_ACCOUNT_NO"
        const val INPUT_ACCOUNT_NAME = "INPUT_ACCOUNT_NAME"

        // seletor
        const val SELECTOR_DATE_TYPE = "SELECTOR_DATE_TYPE"
        const val SELECTOR_DATE_RANGE = "SELECTOR_DATE_RANGE"

        // type group
        const val ALL_GROUP = "ALL"
        const val ALL_STATUS = "000"
    }

    // danh sach filter
    private var lstGroupsStatus: MutableList<TStatusDomain> = mutableListOf()
    private var lstGroupTypes: MutableList<TGroupTypesDomain> = mutableListOf()
    private var lstContactsCache: List<ContactDomains>? = null
    private var lstFromAccount: MutableList<TAccountsDomain> = mutableListOf()
    private var lstToAccount: MutableList<TAccountsDomain> = mutableListOf()

    // danh sach ngan hang
    private var lstDataBanks: List<DataBankDomain>? = null
    private var filterOrigin: FilterManagerDomain? = null

    private val _filterState = MutableStateFlow<FilterManagerDomain?>(null)
    val filterState = _filterState.asStateFlow()

    fun getPreFilterTransaction() {
        if ((false == _filterState.value?.isFilterAdvance && true == _filterState.value?.isFilterFinal) || (true == _filterState.value?.isFilterAdvance && true == _filterState.value?.isFilterAdvanceFinal)) return
        launchJob {
            val res = manageUseCase.getPreFilterTransaction(
                FilterReportParams(
                    userName = userProf.getUserName() ?: "",
                    searchAdvance = if (true == _filterState.value?.isFilterAdvance) "1" else "0",
                ),
            )
            handleResource(res) { data ->
                lstGroupTypes.clear()
                lstFromAccount.clear()
                lstToAccount.clear()
                lstGroupTypes.addAll(data.groupTypes ?: listOf())
                lstFromAccount.addAll(data.fromAccounts ?: listOf())
                lstToAccount.addAll(data.toAccounts ?: listOf())
                data.groupTypes?.firstOrNull()?.let { item ->
                    lstGroupsStatus.clear()
                    lstGroupsStatus.addAll(item.statusGroups ?: listOf())
                    if (filterOrigin == null) {
                        filterOrigin = FilterManagerDomain(
                            dateType = if (userProf.isChecker()) {
                                DATE_APPROVED
                            } else {
                                DATE_INIT
                            },
                            dateRange = Tags.DAY_7.toString(),
                            startDate = getDayAgo(dayAgo = Tags.DAY_7 - 1),
                            endDate = todayAsString(),
                            group = item,
                            status = item.statusGroups?.firstOrNull(),
                            isFilterFinal = true,
                            isFilterAdvanceFinal = true == _filterState.value?.isFilterAdvanceFinal || true == _filterState.value?.isFilterAdvance,
                        )
                    }
                    _filterState.value = _filterState.value?.copy(
                        isFilterFinal = true,
                        isFilterAdvanceFinal = true == _filterState.value?.isFilterAdvanceFinal || true == _filterState.value?.isFilterAdvance,
                    ) ?: filterOrigin?.copy()
                }
            }
        }
    }

    fun onResetFilter() {
        _filterState.value = filterOrigin?.copy(
            isFilterFinal = true,
            isFilterAdvanceFinal = true == _filterState.value?.isFilterAdvanceFinal,
        )
    }

    fun onValidateApplyFilter(): String? {
        return _filterState.value?.let {
            var fromDateTime = it.startDate.toTimeInMillis(dd_MM_yyyy_1)
            var toDateTime = it.endDate.toTimeInMillis(dd_MM_yyyy_1)
            val dateRange = toDateTime.minus(fromDateTime)
            val amountValidate = (
                it.endAmount?.getAmountServer()?.toLongOrNull()
                    ?: 0
                ).minus(it.startAmount?.getAmountServer()?.toLongOrNull() ?: 0) >= 0
            if (it.group == null) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_group_validate)
            } else if (it.status == null) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_status_validate)
            } else if (toDateTime == 0L || fromDateTime == 0L) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_empty_validate)
            } else if (it.group?.groupType == ALL_GROUP && dateRange > Tags.SS_30_DAY) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_over_30_validate)
            } else if (it.group?.groupType != ALL_GROUP && dateRange > Tags.SS_90_DAY) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_date_over_90_validate)
            } else if (!amountValidate) {
                resourceProvider.getString(com.vietinbank.core_ui.R.string.manager_filter_amount_validate)
            } else {
                null
            }
        }
    }

    fun onChangeFillModel(filterUpdate: Any?) {
        when (filterUpdate) {
            is TGroupTypesDomain -> {
                lstGroupsStatus.clear()
                lstGroupsStatus.addAll(filterUpdate.statusGroups ?: emptyList())
                _filterState.value = _filterState.value?.copy(
                    group = filterUpdate,
                    status = null,
                    fillSheet = null,
                    dateOverError = calculateTimeFilter(
                        filterUpdate, _filterState.value?.startDate, _filterState.value?.endDate,
                    ),
                )
            }

            is TStatusDomain -> {
                _filterState.value =
                    _filterState.value?.copy(status = filterUpdate, fillSheet = null)
            }

            is TAccountsDomain -> {
                _filterState.value =
                    _filterState.value?.copy(fromAccount = filterUpdate, fillSheet = null)
            }

            is ContactDomains -> {
                if (null == lstDataBanks) {
                    _filterState.value =
                        _filterState.value?.copy(toAccount = filterUpdate, fillSheet = null)
                    getNapasBankList()
                    return
                }
                _filterState.value = _filterState.value?.copy(
                    toAccount = filterUpdate,
                    toBank = lstDataBanks?.firstOrNull { item -> item.ebankCode == filterUpdate.bank },
                    fillSheet = null,
                )
            }

            is DataBankDomain -> {
                _filterState.value =
                    _filterState.value?.copy(toBank = filterUpdate, fillSheet = null)
            }

            else -> {
                _filterState.value = _filterState.value?.copy(fillSheet = null)
            }
        }
    }

    fun onChangeInput(inputType: String, inputValue: String) {
        _filterState.value = _filterState.value?.copy()?.apply {
            when (inputType) {
                INPUT_START_AMOUNT -> startAmount = inputValue
                INPUT_END_AMOUNT -> endAmount = inputValue

                INPUT_ACCOUNT_NO -> {
                    toAccount = toAccount?.copy(id = null, account = inputValue) ?: ContactDomains(
                        id = null, account = inputValue,
                    )
                }

                INPUT_ACCOUNT_NAME -> {
                    toAccount = toAccount?.copy(id = null, payeename = inputValue)
                        ?: ContactDomains(id = null, payeename = inputValue)
                }

                else -> {}
            }
        }
    }

    fun onChangeDate(type: String, value: String) {
        _filterState.value = _filterState.value?.copy()?.apply {
            when {
                type == SELECTOR_DATE_TYPE -> {
                    dateType = value
                }
                // SELECTOR_DATE_RANGE
                else -> {
                    dateRange = value
                    startDate = getDayAgo(dayAgo = value.toInt() - 1)
                    endDate = todayAsString()
                }
            }
        }
    }

    fun onChangeRangeDate(start: String?, end: String?) {
        _filterState.value = _filterState.value?.copy()?.apply {
            startDate = start
            endDate = end
            dateRange = null
            dateOverError = calculateTimeFilter(group, start, end)
        }
    }

    private fun calculateTimeFilter(
        group: TGroupTypesDomain?,
        startDate: String?,
        endDate: String?,
    ): Int {
        val dateOver =
            endDate.toTimeInMillis(dd_MM_yyyy_1).minus(startDate.toTimeInMillis(dd_MM_yyyy_1))
        return if (ALL_GROUP == group?.groupType && dateOver > Tags.SS_30_DAY) {
            30
        } else if (dateOver > Tags.SS_90_DAY) {
            90
        } else {
            0
        }
    }

    // show optional
    fun onChangeSheet(fillType: String? = null) {
        _filterState.value = when (fillType) {
            FILL_TRANSACTION -> {
                _filterState.value?.copy(
                    fillSheet = FillDataDomain(
                        FILL_TRANSACTION,
                        lstGroupTypes,
                        _filterState.value?.group,
                        false, // lstGroupTypes.size > Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_STATUS -> {
                _filterState.value?.copy(
                    fillSheet = FillDataDomain(
                        FILL_STATUS,
                        lstGroupsStatus,
                        _filterState.value?.status,
                        false, // lstGroupsStatus.size > Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_FROM_ACCOUNT -> {
                _filterState.value?.copy(
                    fillSheet = FillDataDomain(
                        FILL_FROM_ACCOUNT,
                        lstFromAccount,
                        _filterState.value?.fromAccount,
                        lstFromAccount.size >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_BANK -> {
                if (null == lstDataBanks) {
                    getNapasBankList()
                    return
                }
                _filterState.value?.copy(
                    fillSheet = FillDataDomain(
                        FILL_BANK,
                        lstDataBanks,
                        _filterState.value?.toBank,
                        (lstDataBanks?.size ?: 0) >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            FILL_CONTACT -> {
                if (null == lstContactsCache) {
                    getContactList()
                    return
                }
                _filterState.value?.copy(
                    fillSheet = FillDataDomain(
                        FILL_CONTACT,
                        lstContactsCache,
                        _filterState.value?.toAccount,
                        (lstContactsCache?.size ?: 0) >= Tags.SIZE_OPTION_SEARCH,
                    ),
                )
            }

            else -> {
                _filterState.value?.copy(fillSheet = null)
            }
        }
    }

    // show date picker
    fun onShowDatePicker(isShow: Boolean) {
        _filterState.value = _filterState.value?.copy(isShowPicker = isShow)
    }

    fun onChangeFilterAdvance() {
        _filterState.value = _filterState.value?.copy(
            isFilterAdvance = false == _filterState.value?.isFilterAdvance,
        )
        getPreFilterTransaction()
    }

    private fun getContactList() {
        // lay cache danh ba thu huong
        if (true == transferCacheManager.getContactList()?.isNotEmpty()) {
            mapContactAccount(transferCacheManager.getContactList())
            return
        }
        launchJob(showLoading = true) {
            val params = ContactListParams(
                username = userProf.getUserName().toString(),
                serviceId = "",
            )
            val res = transferUseCase.contactList(params)
            handleResource(res) { data ->
                transferCacheManager.saveContactist(data.contacts)
                mapContactAccount(data.contacts)
            }
        }
    }

    private fun getNapasBankList() {
        if (true == transferCacheManager.getBankList()?.isNotEmpty()) {
            mapBankSelector(transferCacheManager.getBankList() ?: listOf())
            return
        }
        launchJob {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                transferCacheManager.saveBankList(data.dataBanks)
                mapBankSelector(data.dataBanks)
            }
        }
    }

    private fun mapContactAccount(lstOrigin: List<ContactDomains>?) {
        lstContactsCache = lstOrigin?.filter {
            when (it.trantype) {
                Tags.TransferType.TYPE_IN -> true
                Tags.TransferType.TYPE_NAPAS_ACCOUNT, Tags.TransferType.TYPE_OUT -> !it.bank.isNullOrEmpty()
                else -> false
            }
        } ?: emptyList()
        onChangeSheet(FILL_CONTACT)
    }

    private fun mapBankSelector(lstOrigin: List<DataBankDomain>) {
        lstDataBanks = lstOrigin
        // truong hop chuyen tien noi bo
        if (Tags.TransferType.TYPE_IN == _filterState.value?.toAccount?.trantype) {
            lstDataBanks?.firstOrNull { itemBank ->
                Tags.TransferType.TYPE_IN.equals(itemBank.type, true)
            }?.let { bank ->
                onChangeFillModel(bank)
            }
        } else if (!TextUtils.isEmpty(_filterState.value?.toAccount?.bank)) {
            lstDataBanks?.firstOrNull { itemBank ->
                itemBank.ebankCode.equals(_filterState.value?.toAccount?.bank, true) ||
                    itemBank.binCode.equals(_filterState.value?.toAccount?.bank, true)
            }?.let { bank ->
                onChangeFillModel(bank)
            }
        } else {
            onChangeSheet(FILL_BANK)
        }
    }
}
//
// sealed class FilterAction {
//    data object TapBack : FilterAction()
//    data object TapReset : FilterAction()
//    data object TapApply : FilterAction()
//    data object TapChangeFilter : FilterAction()
//    data class TapDatePicker(val isShow: Boolean) : FilterAction()
//    data class TapFillSheet(val sheetType: String?) : FilterAction()
//    data class TapOnFillData(val data: IManageData?) : FilterAction()
//    data class TapChip(val chipType: String, val chipValue: String) : FilterAction()
//    data class TapInput(val inputType: String, val inputValue: String) : FilterAction()
//    data class TapRangeDate(val startDate: String?, val endDate: String?) : FilterAction()
// }