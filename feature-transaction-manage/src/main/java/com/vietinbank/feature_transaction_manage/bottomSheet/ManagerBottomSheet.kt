package com.vietinbank.feature_transaction_manage.bottomSheet

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.containsNotVietNam
import com.vietinbank.core_domain.models.maker.ContactDomains
import com.vietinbank.core_domain.models.maker.DataBankDomain
import com.vietinbank.core_domain.models.manage.FillDataDomain
import com.vietinbank.core_domain.models.manage.FilterDomain
import com.vietinbank.core_domain.models.manage.IManageData
import com.vietinbank.core_domain.models.manage.TAccountsDomain
import com.vietinbank.core_domain.models.manage.TGroupTypesDomain
import com.vietinbank.core_domain.models.manage.TSortDomain
import com.vietinbank.core_domain.models.manage.TStatusDomain
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.core_ui.base.sheet.BaseBottomSheet
import com.vietinbank.core_ui.components.FoundationAreaBottomSheet
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationCategorySection
import com.vietinbank.core_ui.components.FoundationChips
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.foundation.textfield.FoundationEditText
import com.vietinbank.core_ui.components.foundation.textfield.FoundationFieldType
import com.vietinbank.core_ui.components.foundation.textfield.InputType
import com.vietinbank.core_ui.components.text.foundation.FoundationIconText
import com.vietinbank.core_ui.components.text.foundation.FoundationRichText
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.components.text.foundation.appendColored
import com.vietinbank.core_ui.components.text.foundation.appendSemiBold
import com.vietinbank.core_ui.models.IconConfig
import com.vietinbank.core_ui.models.IconPosition
import com.vietinbank.core_ui.utils.dismissKeyboardOnClickOutside
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.feature_transaction_manage.FilterAction
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_BANK
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_CONTACT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_FROM_ACCOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_SORT_OPTION
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_STATUS
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.FILL_TRANSACTION
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NAME
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_ACCOUNT_NO
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_END_AMOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.INPUT_START_AMOUNT
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.SELECTOR_DATE_RANGE
import com.vietinbank.feature_transaction_manage.FilterManagerViewModel.Companion.SELECTOR_DATE_TYPE
import com.vietinbank.feature_transaction_manage.IManage
import com.vietinbank.feature_transaction_manage.ManagerAction
import com.vietinbank.feature_transaction_manage.ManagerViewModel.Companion.DATE_APPROVED
import com.vietinbank.feature_transaction_manage.ManagerViewModel.Companion.DATE_INIT
import java.math.BigDecimal
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun FilterManagerSheet(
    visible: Boolean,
    filterModel: FilterDomain?,
    onAction: ((IManage) -> Unit),
) {
    val dateItems = remember {
        listOf(
            R.string.manager_transaction_date_initialization to DATE_INIT,
            R.string.manager_transaction_date_approve to DATE_APPROVED,
        )
    }

    val dateRangeItems = remember {
        listOf(
            R.string.account_detail_7_days to Tags.DAY_7,
            R.string.account_detail_15_days to Tags.DAY_15,
            R.string.account_detail_30_days to Tags.DAY_30,
        )
    }

    BaseBottomSheet<ManagerAction>(
        visible = visible,
        onDismissRequest = {
            onAction(FilterAction.TapShowFilter(false))
        },
        onResult = {},
        allowTouchDismiss = true,
        secureFlag = true,
    ) { action ->

        val focusManager = LocalFocusManager.current

        Column(
            modifier = Modifier.dismissKeyboardOnClickOutside(),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(
                        RoundedCornerShape(
                            topStart = FDS.Sizer.Radius.radius32,
                            topEnd = FDS.Sizer.Radius.radius32,
                        ),
                    )
                    .background(FDS.Colors.backgroundBgContainer),
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap24, bottom = FDS.Sizer.Gap.gap16)
                        .padding(horizontal = FDS.Sizer.Gap.gap24),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    FoundationText(
                        text = stringResource(R.string.manager_filter_option),
                        style = FDS.Typography.headingH3,
                        color = FDS.Colors.characterHighlighted,
                    )

                    Image(
                        modifier = Modifier.safeClickable {
                            onAction(FilterAction.TapReset)
                        },
                        painter = painterResource(R.drawable.ic_common_filter_reset_24),
                        contentDescription = "",
                    )
                }

                FoundationDivider()
            }

            Column(
                modifier = Modifier
                    .weight(1f, false)
                    .imePadding(),
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f, false)
                        .background(FDS.Colors.backgroundBgContainer)
                        .padding(vertical = FDS.Sizer.Gap.gap16, horizontal = FDS.Sizer.Gap.gap24),
                ) {
                    item {
                        FoundationCategorySection(
                            title = stringResource(R.string.manager_sort),
                            value = filterModel?.sortField?.name,
                            isShowLine = true,
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
                        ) { onAction(FilterAction.TapFillSheet(FILL_SORT_OPTION)) }

                        FoundationCategorySection(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                            title = stringResource(R.string.account_detail_transaction_type),
                            value = filterModel?.group?.groupName,
                            isShowLine = true,
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
                        ) { onAction(FilterAction.TapFillSheet(FILL_TRANSACTION)) }

                        FoundationCategorySection(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                            title = stringResource(R.string.manager_transaction_status),
                            value = filterModel?.status?.statusName,
                            isShowLine = true,
                            icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
                        ) { onAction(FilterAction.TapFillSheet(FILL_STATUS)) }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                            text = stringResource(R.string.manager_filter_date_search),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        ) {
                            items(dateItems) { (dateId, dateType) ->
                                FoundationChips(
                                    text = stringResource(dateId),
                                    isSelector = filterModel?.dateType == dateType,
                                ) {
                                    onAction(FilterAction.TapChip(SELECTOR_DATE_TYPE, dateType))
                                }
                            }
                        }

                        FoundationText(
                            modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                            text = stringResource(R.string.common_select_date),
                            style = FDS.Typography.bodyB2Emphasized,
                            color = FDS.Colors.characterSecondary,
                        )

                        LazyRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap8),
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                        ) {
                            items(dateRangeItems) { (rangeId, rangeType) ->
                                FoundationChips(
                                    text = stringResource(rangeId),
                                    isSelector = filterModel?.dateRange == rangeType.toString(),
                                ) {
                                    onAction(
                                        FilterAction.TapChip(
                                            SELECTOR_DATE_RANGE,
                                            rangeType.toString(),
                                        ),
                                    )
                                }
                            }
                        }

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = FDS.Sizer.Gap.gap16),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                        ) {
                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_start_date),
                                value = filterModel?.startDate,
                                isShowLine = true,
                            ) { onAction(FilterAction.TapDatePicker(true)) }

                            FoundationCategorySection(
                                modifier = Modifier.weight(1f),
                                title = stringResource(R.string.account_detail_end_date),
                                value = filterModel?.endDate,
                                isShowLine = true,
                            ) { onAction(FilterAction.TapDatePicker(true)) }
                        }

                        AnimatedVisibility(
                            visible = (true == filterModel?.isFilterAdvance),
                            enter = fadeIn() + expandVertically(),
                            exit = fadeOut() + shrinkVertically(),
                        ) {
                            Column {
                                FoundationCategorySection(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    title = stringResource(R.string.manager_filter_from_account),
                                    value = filterModel?.fromAccount?.accountNo,
                                    isShowLine = true,
                                    icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
                                ) { onAction(FilterAction.TapFillSheet(FILL_FROM_ACCOUNT)) }

                                FoundationCategorySection(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    title = stringResource(R.string.manager_filter_beneficiary_bank),
                                    value = filterModel?.toBank?.bankName,
                                    isShowLine = true,
                                    icon = com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_dropdown_24,
                                ) { onAction(FilterAction.TapFillSheet(FILL_BANK)) }

                                FoundationEditText(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    value = filterModel?.toAccount?.account ?: "",
                                    onValueChange = {
                                        onAction(FilterAction.TapInput(INPUT_ACCOUNT_NO, it))
                                    },
                                    placeholder = stringResource(R.string.manager_filter_to_account),
                                    showBottomBorder = true,
                                    showCharacterCounter = false,
                                    inputType = InputType.NUMBER,
                                    imeAction = ImeAction.Done,
                                    keyboardActions = KeyboardActions(
                                        onDone = { focusManager.clearFocus() },
                                    ),
                                    trailingIcon = {
                                        Image(
                                            modifier = Modifier
                                                .padding(start = FDS.Sizer.Gap.gap4)
                                                .safeClickable {
                                                    onAction(
                                                        FilterAction.TapFillSheet(FILL_CONTACT),
                                                    )
                                                },
                                            painter = painterResource(com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_contact_24),
                                            contentDescription = "contact",
                                        )
                                    },
                                )

                                FoundationEditText(
                                    modifier = Modifier.padding(top = FDS.Sizer.Gap.gap16),
                                    value = filterModel?.toAccount?.payeename ?: "",
                                    onValueChange = {
                                        onAction(FilterAction.TapInput(INPUT_ACCOUNT_NAME, it))
                                    },
                                    placeholder = stringResource(R.string.manager_filter_beneficiary_name),
                                    showBottomBorder = true,
                                    showCharacterCounter = false,
                                    imeAction = ImeAction.Done,
                                    keyboardActions = KeyboardActions(
                                        onDone = { focusManager.clearFocus() },
                                    ),
                                )

                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = FDS.Sizer.Gap.gap16),
                                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap16),
                                ) {
                                    FoundationEditText(
                                        modifier = Modifier.weight(1f),
                                        value = filterModel?.startAmount ?: "",
                                        onValueChange = {
                                            onAction(
                                                FilterAction.TapInput(
                                                    INPUT_START_AMOUNT,
                                                    it,
                                                ),
                                            )
                                        },
                                        inputType = InputType.AMOUNT,
                                        placeholder = stringResource(R.string.account_filter_min_amount),
                                        hintText = stringResource(R.string.maker_transfer_dashboard_amount),
                                        showBottomBorder = true,
                                        showCharacterCounter = false,
                                        maxLength = 19,
                                        imeAction = ImeAction.Done,
                                        keyboardActions = KeyboardActions(
                                            onDone = { focusManager.clearFocus() },
                                        ),
                                    )

                                    FoundationEditText(
                                        modifier = Modifier.weight(1f),
                                        value = filterModel?.endAmount ?: "",
                                        onValueChange = {
                                            onAction(
                                                FilterAction.TapInput(
                                                    INPUT_END_AMOUNT,
                                                    it,
                                                ),
                                            )
                                        },
                                        inputType = InputType.AMOUNT,
                                        placeholder = stringResource(R.string.account_filter_max_amount),
                                        hintText = stringResource(R.string.maker_transfer_dashboard_amount),
                                        showBottomBorder = true,
                                        showCharacterCounter = false,
                                        maxLength = 19,
                                        imeAction = ImeAction.Done,
                                        keyboardActions = KeyboardActions(
                                            onDone = { focusManager.clearFocus() },
                                        ),
                                    )
                                }

                                if (!filterModel?.startAmount.isNullOrEmpty() &&
                                    !filterModel.endAmount.isNullOrEmpty() &&
                                    (
                                        filterModel.startAmount?.toBigDecimalOrNull()
                                            ?: BigDecimal(0)
                                        ) > (
                                        filterModel.endAmount?.toBigDecimalOrNull()
                                            ?: BigDecimal(0)
                                        )
                                ) {
                                    FoundationText(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(top = FDS.Sizer.Gap.gap16),
                                        text = stringResource(R.string.account_filter_validate_amount),
                                        style = FDS.Typography.captionCaptionL,
                                        color = FDS.Colors.stateError,
                                    )
                                }
                            }
                        }
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(
                            RoundedCornerShape(
                                bottomStart = FDS.Sizer.Radius.radius32,
                                bottomEnd = FDS.Sizer.Radius.radius32,
                            ),
                        )
                        .background(FDS.Colors.backgroundBgContainer)
                        .padding(top = FDS.Sizer.Gap.gap8, bottom = FDS.Sizer.Gap.gap24)
                        .safeClickable { onAction(FilterAction.TapChangeFilter) },
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    FoundationIconText(
                        text = if (true == filterModel?.isFilterAdvance) {
                            stringResource(R.string.manager_filter_collapse)
                        } else {
                            stringResource(R.string.manager_filter_expand)
                        },
                        style = FDS.Typography.interactionSmallButton,
                        color = FDS.Colors.characterHighlighted,
                        icons = mapOf(
                            IconPosition.LEFT to IconConfig(
                                icon = if (true == filterModel?.isFilterAdvance) {
                                    com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_collapse_24
                                } else {
                                    com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_expand_24
                                },
                                size = FDS.Sizer.Icon.icon24,
                            ),
                        ),
                    )
                }
            }

            FoundationAreaBottomSheet {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap8),
                ) {
                    FoundationButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_back),
                        isLightButton = false,
                        onClick = { onAction.invoke(FilterAction.TapShowFilter(false)) },
                    )

                    FoundationButton(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.common_apply),
                        onClick = { onAction.invoke(FilterAction.TapApply) },
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FillListDataBottomSheet(
    visible: Boolean = false,
    sheet: FillDataDomain? = null,
    imageLoader: CoilImageLoader,
    onDismissRequest: () -> Unit,
    onResult: (IManageData) -> Unit,
) {
    var searchText by remember { mutableStateOf("") }
    val searchLst by remember(sheet?.fillOrigin, searchText) {
        derivedStateOf {
            if (searchText.isEmpty()) {
                sheet?.fillOrigin
            } else {
                sheet?.fillOrigin?.filter { item ->
                    when (item) {
                        is TAccountsDomain -> {
                            true == item.accountNo?.containsNotVietNam(searchText) || true == item.accountName?.containsNotVietNam(
                                searchText,
                            ) || true == item.currency?.containsNotVietNam(searchText)
                        }

                        is ContactDomains -> {
                            true == item.account?.containsNotVietNam(searchText) || true == item.bankName?.containsNotVietNam(
                                searchText,
                            ) || true == item.payeename?.containsNotVietNam(searchText)
                        }

                        is DataBankDomain -> {
                            true == item.shortName?.containsNotVietNam(searchText) || true == item.bankName?.containsNotVietNam(
                                searchText,
                            )
                        }

                        else -> true
                    }
                }
            }
        }
    }

    BaseBottomSheet<IManageData>(
        visible = visible,
        onDismissRequest = {
            searchText = ""
            onDismissRequest()
        },
        onResult = { onResult(it) },
        allowTouchDismiss = true,
        secureFlag = true,
    ) { onSelect ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                .background(FDS.Colors.backgroundBgContainer)
                .padding(vertical = FDS.Sizer.Gap.gap20),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            FoundationText(
                modifier = Modifier.fillMaxWidth(),
                text = when (sheet?.fillType) {
                    FILL_TRANSACTION -> stringResource(R.string.account_detail_transaction_type)
                    FILL_STATUS -> stringResource(R.string.manager_transaction_status)
                    FILL_FROM_ACCOUNT -> stringResource(R.string.manager_filter_from_account)
                    FILL_BANK -> stringResource(R.string.manager_filter_beneficiary_bank)
                    FILL_CONTACT -> stringResource(R.string.manager_filter_beneficiary_contact)
                    FILL_SORT_OPTION -> stringResource(R.string.manager_sort)
                    else -> ""
                },
                style = FDS.Typography.headingH3,
                color = FDS.Colors.characterHighlighted,
                textAlign = TextAlign.Center,
            )

            FoundationDivider(modifier = Modifier.padding(top = FDS.Sizer.Gap.gap24))

            if (true == sheet?.isShowSearch) {
                FoundationFieldType(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = FDS.Sizer.Gap.gap24, bottom = FDS.Sizer.Gap.gap8)
                        .padding(horizontal = FDS.Sizer.Gap.gap24)
                        .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                        .background(FDS.Colors.gray50),
                    value = searchText,
                    onValueChange = { searchText = it },
                    placeholder = when (sheet.fillType) {
                        FILL_TRANSACTION -> stringResource(R.string.account_detail_transaction_type)
                        FILL_STATUS -> stringResource(R.string.manager_transaction_status)
                        FILL_FROM_ACCOUNT -> stringResource(R.string.manager_filter_from_account_hint)
                        FILL_BANK -> stringResource(R.string.manager_filter_beneficiary_bank_hint)
                        FILL_CONTACT -> stringResource(R.string.manager_filter_beneficiary_contact_hint)
                        else -> ""
                    },
                    leadingIcon = {
                        Image(
                            painter = painterResource(R.drawable.ic_search),
                            contentDescription = "",
                        )
                    },
                    trailingIcon = {
                        if (searchText.isNotEmpty()) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_close),
                                contentDescription = "Clear",
                                tint = FDS.Colors.characterSecondary,
                                modifier = Modifier
                                    .size(FDS.Sizer.Icon.icon24)
                                    .safeClickable {
                                        searchText = ""
                                    },
                            )
                        }
                    },
                )
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false)
                    .heightIn(max = FDS.Sizer.Dialog.maxListHeight),
                contentPadding = PaddingValues(horizontal = FDS.Sizer.Padding.padding24),
                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap0),
            ) {
                when {
                    true == searchLst?.isEmpty() -> {
                        // khong co ket qua theo search
                        item {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        top = FDS.Sizer.Gap.gap40,
                                        bottom = FDS.Sizer.Gap.gap56,
                                    )
                                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.ic_common_empty_search_96),
                                    contentDescription = "",
                                )

                                FoundationText(
                                    text = stringResource(R.string.manager_filter_search_empty),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.headingH3,
                                )
                            }
                        }
                    }

                    true == searchLst?.isNotEmpty() -> {
                        itemsIndexed(searchLst ?: emptyList()) { index, data ->
                            ManagerItem(
                                item = data,
                                selectedItem = sheet?.fillSelected,
                                coilLoader = imageLoader,
                                onClick = onSelect,
                            )
                        }
                    }

                    else -> {
                        // danh sach trong
                        item {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(
                                        top = FDS.Sizer.Gap.gap40,
                                        bottom = FDS.Sizer.Gap.gap56,
                                    )
                                    .padding(horizontal = FDS.Sizer.Gap.gap24),
                                verticalArrangement = Arrangement.spacedBy(FDS.Sizer.Gap.gap24),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Image(
                                    painter = painterResource(com.vietinbank.feature_transaction_manage.R.drawable.ic_manager_empty_report_96),
                                    contentDescription = "",
                                )

                                FoundationText(
                                    text = stringResource(R.string.account_detail_empty),
                                    color = FDS.Colors.characterHighlighted,
                                    style = FDS.Typography.headingH3,
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ManagerItem(
    modifier: Modifier = Modifier,
    item: IManageData,
    selectedItem: IManageData?,
    coilLoader: CoilImageLoader,
    onClick: (IManageData) -> Unit,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = FDS.Sizer.Gap.gap8)
            .safeClickable(onSafeClick = { onClick.invoke(item) }),
        horizontalArrangement = Arrangement.spacedBy(FDS.Sizer.Spacing.spacingSmall),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        when (item) {
            is ContactDomains -> item.iconUrl ?: ""
            is DataBankDomain -> item.icon ?: ""
            else -> null
        }?.let { urlIcon ->
            Box(
                modifier = Modifier
                    .padding(end = FDS.Sizer.Gap.gap8)
                    .size(FDS.Sizer.Icon.icon40)
                    .clip(RoundedCornerShape(FDS.Sizer.Radius.radius32))
                    .background(FDS.Colors.gray50),
                contentAlignment = Alignment.Center,
            ) {
                coilLoader.LoadUrl(
                    modifier = Modifier.size(FDS.Sizer.Icon.icon24),
                    url = urlIcon,
                    isCache = true,
                    placeholderRes = null,
                    errorRes = null,
                )
            }
        }

        Column(modifier = Modifier.weight(1f)) {
            when (item) {
                is ContactDomains -> {
                    val characterPrimary = FDS.Colors.characterPrimary
                    val characterSecondary = FDS.Colors.characterSecondary

                    FoundationRichText(
                        style = FDS.Typography.captionL,
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                    ) {
                        appendSemiBold(text = item.payeename ?: "", color = characterPrimary)
                        if (!item.payeenickname.isNullOrEmpty()) {
                            appendColored(
                                text = " (${item.payeenickname})",
                                color = characterSecondary,
                            )
                        }
                    }

                    FoundationText(
                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
                        text = "${item.revBankName ?: ""} - ${item.account ?: ""}",
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.captionL,
                    )
                }

                is DataBankDomain -> {
                    FoundationText(
                        text = item.shortName ?: "",
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.bodyB2,
                    )

                    FoundationText(
                        modifier = Modifier.padding(top = FDS.Sizer.Gap.gap4),
                        text = item.bankName ?: "",
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 2,
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionL,
                    )
                }

                is TAccountsDomain -> {
                    val characterPrimary = FDS.Colors.characterPrimary
                    val characterSecondary = FDS.Colors.characterSecondary

                    FoundationRichText(style = FDS.Typography.captionCaptionM) {
                        appendColored(text = item.accountNo ?: "", color = characterPrimary)
                        if (!item.currency.isNullOrEmpty()) {
                            appendColored(text = " (${item.currency})", color = characterSecondary)
                        }
                    }

                    FoundationText(
                        text = item.accountName ?: "",
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.bodyB1,
                    )
                }

                else -> {
                    FoundationText(
                        text = when (item) {
                            is TGroupTypesDomain -> item.groupName ?: ""
                            is TStatusDomain -> item.statusName ?: ""
                            is TSortDomain -> item.name ?: ""
                            else -> ""
                        },
                        color = FDS.Colors.characterPrimary,
                        style = FDS.Typography.bodyB2,
                    )
                }
            }
        }

        Image(
            painter = painterResource(
                when {
                    selectedItem is TGroupTypesDomain && item is TGroupTypesDomain && selectedItem.groupType == item.groupType -> R.drawable.ic_common_radio_check_24
                    selectedItem is TStatusDomain && item is TStatusDomain && selectedItem.statusCode == item.statusCode -> R.drawable.ic_common_radio_check_24
                    selectedItem is TAccountsDomain && item is TAccountsDomain && selectedItem.accountNo == item.accountNo -> R.drawable.ic_common_radio_check_24
                    selectedItem is ContactDomains && item is ContactDomains && selectedItem.id == item.id -> R.drawable.ic_common_radio_check_24
                    selectedItem is DataBankDomain && item is DataBankDomain && selectedItem.binCode == item.binCode -> R.drawable.ic_common_radio_check_24
                    selectedItem is TSortDomain && item is TSortDomain && selectedItem.name == item.name -> R.drawable.ic_common_radio_check_24
                    else -> R.drawable.ic_common_radio_uncheck_24
                },
            ),
            contentDescription = null,
        )
    }
}