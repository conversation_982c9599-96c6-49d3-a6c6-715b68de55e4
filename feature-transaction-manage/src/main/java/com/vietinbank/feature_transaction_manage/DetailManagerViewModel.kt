package com.vietinbank.feature_transaction_manage

import android.content.Context
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.StatusType
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss_1
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.extensions.saveBase64File
import com.vietinbank.core_common.extensions.toTimeInMillis
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.checker.FileTransactionDomain
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileDomain
import com.vietinbank.core_domain.models.checker.GetDownloadBase64FileParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.SubTransactionDetailDomain
import com.vietinbank.core_domain.models.checker.SubTransactionParams
import com.vietinbank.core_domain.models.maker.NapasBankListParams
import com.vietinbank.core_domain.models.maker.TempTransactionDomains
import com.vietinbank.core_domain.models.manage.ActionFileDomain
import com.vietinbank.core_domain.models.manage.DetailManagerState
import com.vietinbank.core_domain.models.manage.DetailReportParams
import com.vietinbank.core_domain.models.manage.HistoryApprovalParams
import com.vietinbank.core_domain.models.manage.SubTranItemListDomain
import com.vietinbank.core_domain.models.manage.TransDetailDomain
import com.vietinbank.core_domain.models.manage.TransRsUIModel
import com.vietinbank.core_domain.models.trace_payment.ActivityLogsDomains
import com.vietinbank.core_domain.models.trace_payment.VerifiedByEntity
import com.vietinbank.core_domain.repository.cache.ITransferCacheManager
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.transaction_manage.TransactionManageUseCase
import com.vietinbank.core_domain.usecase.transfer.TransferUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.core_ui.base.imageloader.CoilImageLoader
import com.vietinbank.feature_transaction_manage.fragment.render.IRenderManager
import com.vietinbank.feature_transaction_manage.fragment.render.RenderManagerFactory
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.String

@HiltViewModel
class DetailManagerViewModel
@Inject constructor(
    private val useCase: CheckerUserCase,
    private val manageUseCase: TransactionManageUseCase,
    private val transferUseCase: TransferUseCase,
    val dataSourceProperties: DataSourceProperties,
    val renderManager: RenderManagerFactory,
    val imageLoader: CoilImageLoader,
    private val transferCacheManager: ITransferCacheManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    companion object {
        const val LOOKUP = "LOOKUP"
        const val COPY = "COPY"
        const val DELETE = "DELETE"
        const val UNC = "UNC"
        const val UNCCKS = "UNCCKS"
        const val BM01 = "BM01"
        const val C102 = "C102"
        const val C102COCKS = "C102COCKS"

        const val SUCCESS = "SUCCESS"
        const val PENDING = "PENDING"
        const val ERROR = "ERROR"
    }

    var transRsUICopyModel: TransRsUIModel? = null

    // UI State để hiển thị - sử dụng StateFlow

    private val _detailState = MutableStateFlow<DetailManagerState>(DetailManagerState())
    val detailState = _detailState.asStateFlow()

    private var detailTrans: TransDetailDomain? = null
    private val _detailTransState = MutableStateFlow<TransDetailDomain?>(null)
    val detailTransState: StateFlow<TransDetailDomain?> = _detailTransState.asStateFlow()

    fun getDetailTransaction() {
        val req = DetailReportParams(
            transRsUICopyModel?.mtId ?: "",
            transRsUICopyModel?.serviceType ?: "",
            transRsUICopyModel?.tranType ?: "",
            userProf.getUserName() ?: "",
            transRsUICopyModel?.mtIdList,
        )

        if (transRsUICopyModel?.tranType == "sn") {
            transRsUICopyModel?.mtId?.let {
                getSubTransactionDetail(it)
            }
        }

        launchJob {
            val res = manageUseCase.getReportDetail(req)
            handleResource(res) { data ->
                _renderer.value = renderUI(data?.tranType) // ?: data.btxTranItem?.tranType)
                val fileLst = mutableListOf<ActionFileDomain>()
                if (isHasUNCCKS(data?.tranType, data?.status, data?.providerCode)) {
                    fileLst.add(
                        ActionFileDomain(
                            resourceProvider.getString(com.vietinbank.core_ui.R.string.file_unc_cks),
                            UNC,
                        ),
                    )
                }
                if (isHasUNC(data?.tranType, data?.status, data?.providerCode)) {
                    fileLst.add(
                        ActionFileDomain(
                            resourceProvider.getString(com.vietinbank.core_ui.R.string.file_unc),
                            UNC,
                        ),
                    )
                }
                if (isHasBM01C102(data?.tranType, data?.status, data?.providerCode)) {
                    fileLst.add(
                        ActionFileDomain(
                            resourceProvider.getString(com.vietinbank.core_ui.R.string.file_bm_01),
                            BM01,
                        ),
                    )
                    fileLst.add(
                        ActionFileDomain(
                            resourceProvider.getString(com.vietinbank.core_ui.R.string.file_c102),
                            C102,
                        ),
                    )
                }

                if (isHasC102COCKS(data?.tranType, data?.status, data?.providerCode)) {
                    fileLst.add(
                        ActionFileDomain(
                            resourceProvider.getString(com.vietinbank.core_ui.R.string.file_c102_cks),
                            C102COCKS,
                        ),
                    )
                }

                _detailState.value = _detailState.value.copy(
                    isCanCopy = isHasCopy(data?.tranType, data?.status),
                    isCanTrace = isHasTrace(data?.tranType, data?.status),
                    isCheckLimit = isCheckLimit(data?.activityLogs?.nextApproverBy, data?.status),
                    fileLst = fileLst,
                    approveLst = initHistoryApprove(
                        data?.rejectReason, data?.status, data?.activityLogs,
                    ),
                )

                detailTrans = data // .transaction ?: data.btxTranItem
                updateDetailTransState()
                if (true == _renderer.value?.isGetBankList()) {
                    getBankList(data)
                } else {
                    _detailState.value = _detailState.value.copy(detailModel = data)
                }
            }
        }
    }

    private val _renderer = MutableStateFlow<IRenderManager?>(null)
    val renderer = _renderer.asStateFlow()
    fun renderUI(tranType: String? = null): IRenderManager? {
        return renderManager.render(tranType)
    }

    // SharedFlow để thông báo khi URL sẵn sàng
    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private val _openFileIntent = MutableSharedFlow<Intent>()
    val openFileIntent = _openFileIntent.asSharedFlow()

    private val _errorMessage = MutableSharedFlow<String>()
    val errorMessage = _errorMessage.asSharedFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()
    fun getDownloadFileID(file: FileTransactionDomain? = null) {
        launchJob {
            val res = useCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = getFileIDFollowTranType(
                        transRsUICopyModel?.tranType,
                        file,
                    ), // tam thoi dung mtid
                    mtID = getMtIdFollowTranType(transRsUICopyModel, file),
                    tranType = transRsUICopyModel?.tranType ?: "",
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        printLog("download url: $downloadUrl")
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    sealed class FileDownloadEvent {
        data class ShowSelectTransactionSheet(
            val mtIdList: List<String>,
            val type: String,
            val signType: String,
            val title: String,
        ) : FileDownloadEvent()

        data class DownloadDirect(val mtId: String, val type: String, val signType: String) :
            FileDownloadEvent()
    }

    private val _fileDownloadEvent = MutableSharedFlow<FileDownloadEvent>()
    val fileDownloadEvent: SharedFlow<FileDownloadEvent> = _fileDownloadEvent

    fun getAllMtIds(subTranItemList: List<SubTranItemListDomain>): List<String> {
        return subTranItemList.mapNotNull { it.mtId?.toString() }
    }

    fun handleDownloadAction(
        mtId: String,
        tranType: String,
        type: String,
        signType: String,
    ) {
        val title = when {
            type == Tags.DOWNLOAD_FILE_TYPE_BM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_BM -> Tags.DOWNLOAD_FILE_BM01
            type == Tags.DOWNLOAD_FILE_TYPE_CM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_BM -> Tags.DOWNLOAD_FILE_C102
            type == Tags.DOWNLOAD_FILE_TYPE_CM && signType == Tags.DOWNLOAD_FILE_SIGN_TYPE_CM -> Tags.DOWNLOAD_FILE_C102CKS
            else -> return //  Không hỗ trợ -> thoát luôn
        }

        viewModelScope.launch {
            val itemList = detailTrans?.subTranItemList?.let { getAllMtIds(it) }.orEmpty()
            if (tranType == "btx" && itemList.isNotEmpty()) {
                _fileDownloadEvent.emit(
                    FileDownloadEvent.ShowSelectTransactionSheet(
                        itemList,
                        type,
                        signType,
                        title,
                    ),
                )
            } else {
                _fileDownloadEvent.emit(
                    FileDownloadEvent.DownloadDirect(
                        mtId,
                        type,
                        signType,
                    ),
                )
            }
        }
    }

    fun getDownloadBase64File(context: Context, mtId: String?, type: String?, signType: String?) {
        launchJob {
            val res = useCase.getDownloadBase64(
                GetDownloadBase64FileParams(
                    username = userProf.getUserName(),
                    type = type,
                    mtId = mtId,
                    signType = signType,
                ),
            )
            handleResource(res) { data ->
                handleDownloadFile(context, data)
            }
        }
    }

    // giao dich chuyen tien tach lenh
    private var subTransList: List<SubTransactionDetailDomain>? = null
    fun getSubTransactionDetail(hostMtId: String? = null) = launchJob(showLoading = true) {
        val res =
            useCase.getSubTransactionDetail(SubTransactionParams(hostMtId, userProf.getUserName()))
        handleResource(res) { data ->
            subTransList = data.data ?: emptyList()
            updateDetailTransState()
        }
    }

    // truong hop la giao dich tach lenh -> doi 2 api detail + sub thanh cong -> update trang thai
    private fun updateDetailTransState() {
        detailTrans?.let {
            if (it.tranType == "sn" && subTransList != null) {
                it.splitTransaction = subTransList
                _detailTransState.value = it
            } else if (it.tranType != "sn") {
                _detailTransState.value = it
            }
        }
    }

    // show action for transaction
    fun isHasUNC(
        tranType: String? = null,
        statusCode: String? = null,
        providerCode: String? = null,
    ): Boolean {
        // trang thai xoa
        val statusNotSupport = listOf(StatusType.DELETED_13.value, StatusType.DELETED_93.value)
        val typeSupport = listOf(
            Tags.TYPE_GROUP_TRANSFER_NAPAS,
            Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_IN,
            Tags.TYPE_GROUP_TRANSFER_NAPAS_ACCOUNT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS_CARD,
            Tags.TYPE_GROUP_TRANSFER_CTTF300,
            Tags.TYPE_GROUP_TRANSFER_CTTF5000,
        )
        return statusCode !in statusNotSupport && when (tranType?.uppercase()) {
            // chuyen tien don
            in typeSupport -> true
            // nop phi ha tang cang bien HCM
            Tags.TYPE_GROUP_INFRASTRUCTURE -> "975" == providerCode
            else -> false
        }
    }

    fun isHasUNCCKS(
        tranType: String? = null,
        statusCode: String? = null,
        providerCode: String? = null,
    ): Boolean {
        val typeSupport = listOf(
            Tags.TYPE_GROUP_TRANSFER_NAPAS,
            Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_IN,
            Tags.TYPE_GROUP_TRANSFER_NAPAS_ACCOUNT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS_CARD,
        )
        return StatusType.SUCCESS.value == statusCode && when (tranType?.uppercase()) {
            in typeSupport -> true
            // nop phi ha tang cang bien HCM
            Tags.TYPE_GROUP_INFRASTRUCTURE -> "975" == providerCode
            else -> false
        }
    }

    fun isHasBM01C102(
        tranType: String? = null,
        statusCode: String? = null,
        providerCode: String? = null,
    ): Boolean {
        // trang thai xoa
        val statusNotSupport = listOf(StatusType.DELETED_13.value, StatusType.DELETED_93.value)
        // Kiểm tra nếu tranType thuộc danh sách cho phép và statusCode hợp lệ
        return !userProf.isChecker() && statusCode !in statusNotSupport && when (tranType?.uppercase()) {
            Tags.TYPE_GROUP_FILENSNN, Tags.TYPE_GROUP_CUSTOMSINLAND -> true
            // providecode, 31: HP ha tang cang bien
            Tags.TYPE_GROUP_INFRASTRUCTURE -> "31" == providerCode
            else -> false
        }
    }

    fun isHasC102COCKS(
        tranType: String? = null,
        statusCode: String? = null,
        providerCode: String? = null,
    ): Boolean =
        !userProf.isChecker() && StatusType.SUCCESS.value == statusCode && when (tranType?.uppercase()) {
            // nộp NSNN, nộp thuế theo file
            Tags.TYPE_GROUP_FILENSNN, Tags.TYPE_GROUP_CUSTOMSINLAND -> true
            // providecode, 31: HP ha tang cang bien
            Tags.TYPE_GROUP_INFRASTRUCTURE -> "31" == providerCode
            else -> false
        }

    fun isHasCopy(tranType: String? = null, statusCode: String? = null): Boolean {
        val statusNotSupport = listOf(StatusType.DELETED_13.value, StatusType.DELETED_93.value)
        return !userProf.isChecker() && statusCode !in statusNotSupport && when (tranType?.uppercase()) {
            // chuyen tien don + lenh chi + giai ngan online + dich vu bao lanh
            // -> all giao dich
            Tags.TYPE_GROUP_TRANSFER_IN,
            Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS,
            Tags.TYPE_GROUP_PAYMENT,
            Tags.TYPE_DISBURSEMENT_ONLINE,
            Tags.TYPE_GUARANTEE,
            -> true

            else -> false
        }
    }

    fun isHasTrace(tranType: String? = null, statusCode: String? = null): Boolean =
        !userProf.isChecker() && when (tranType?.uppercase()) {
            // chuyen tien don
            // chi luong tu dong
            // -> thanh cong
            Tags.TYPE_GROUP_TRANSFER_IN, Tags.TYPE_GROUP_TRANSFER_OUT,
            Tags.TYPE_GROUP_TRANSFER_NAPAS,
            Tags.TYPE_GROUP_SALARY_AUTO,
            -> StatusType.SUCCESS.value == statusCode
            // lenh chi
            // cl qua ngan hang + cl ngoai te + NSNN thế nội địa, hải quan + Thuế hạ tầng
            // -> Thành công + Chờ ngân hàng xử lý + Ngân hàng đang xử lý
            Tags.TYPE_GROUP_PAYMENT,
            Tags.TYPE_GROUP_SALARY_FOREIGN,
            Tags.TYPE_GROUP_SALARY,
            Tags.TYPE_GROUP_CUSTOMSINLAND,
            Tags.TYPE_GROUP_INFRASTRUCTURE,
            Tags.TYPE_GROUP_TRANSFER_FOREIGN,
            -> when (statusCode) {
                StatusType.SUCCESS.value,
                StatusType.WAITING_BANK_PROCESSING.value,
                StatusType.BANK_PROCESSING_07.value,
                StatusType.BANK_PROCESSING_08.value,
                StatusType.BANK_PROCESSING_09.value,
                StatusType.BANK_PROCESSING_98.value,
                -> true

                else -> false
            }

            else -> false
        }

    fun isHasDelete(tranType: String? = null, statusCode: String? = null): Boolean =
        userProf.isChecker() && when (tranType?.uppercase() ?: "") {
            // giai ngan online + dich vu bao lanh
            // -> ktt/ctk từ chối
            Tags.TYPE_DISBURSEMENT_ONLINE, Tags.TYPE_GUARANTEE -> Tags.STATUS_USER_REJECT == statusCode
            else -> false
        }

    fun isCheckLimit(
        approveList: MutableList<VerifiedByEntity>?,
        statusCode: String? = null,
    ): Boolean =
        true == statusCode?.contains(StatusType.REGEX_AWAITING_APPROVAL.value) && (!userProf.isChecker() || approveList.isNullOrEmpty())

    private fun getFileIDFollowTranType(tranType: String?, file: FileTransactionDomain?): String? {
        return when (tranType) {
            "sl", "sx" -> {
                "SL_${file?.mtId}_${file?.id}"
            }

            "slo" -> {
                file?.mtId
            }

            "do", "go", "goc", "gor" -> {
                file?.objectId
            }

            else -> file?.mtId // gor
        }
    }

    private fun getMtIdFollowTranType(
        transaction: TransRsUIModel?,
        file: FileTransactionDomain?,
    ): String? {
        return when (transaction?.tranType ?: "") {
            "do", "go", "goc", "gor" -> {
                transaction?.mtId
            }

            else -> {
                file?.mtId ?: transaction?.mtId
            }
        }
    }

    fun convertToTransfer(domain: Any): TempTransactionDomains? {
        return when (domain) {
            is TransDetailDomain -> {
                TempTransactionDomains(
                    amount = domain.amount,
                    content = domain.remark,
                    currency = domain.currency,
                    fromAccountNo = domain.fromAccountNo,
                    toAccountName = domain.toAccountNo,
                    toAccountNo = domain.toAccountNo,
                    toBankCode = domain.receiveBank,
                    toBankName = domain.receiveBankName,
                    tranType = domain.tranType,
                    isCopy = true,
                )
            }

            is TransRsUIModel -> {
                TempTransactionDomains(
                    amount = domain.amount,
                    content = domain.remark,
                    currency = domain.currency,
                    fromAccountNo = domain.fromAccountNo,
                    toAccountName = domain.toAccountNo,
                    toAccountNo = domain.toAccountNo,
                    toBankCode = domain.receiveBank,
                    toBankName = domain.receiveBankName,
                    tranType = domain.tranType,
                    isCopy = true,
                )
            }

            else -> null
        }
    }

    // kiem tra cap phe duyet
    private fun getApproveHistory() {
        if (_detailState.value.approveUserLst != null) {
            return
        }
        launchJob(showLoading = true) {
            val params = HistoryApprovalParams(
                mtId = _detailState.value.detailModel?.mtId ?: "",
                username = userProf.getUserName() ?: "",
            )
            val res = manageUseCase.getHistoryApproval(params)
            handleResource(res) { data ->
                _detailState.value = _detailState.value.copy(
                    approveUserLst = data.usersList,
                )
            }
        }
    }

    // tab change
    fun onChangeTab(tabIndex: Int) {
        _detailState.value = _detailState.value.copy(tabSelected = tabIndex)
//        if (tabIndex == 1) {
//            getApproveHistory()
//        }
    }

    fun onHandleFile(isShow: Boolean) {
        _detailState.value = _detailState.value.copy(isShowDownload = isShow)
    }

    // download unc
    private suspend fun handleDownloadFile(context: Context, data: GetDownloadBase64FileDomain) {
        if (data.file.isNullOrEmpty()) {
            _errorMessage.emit(resourceProvider.getString(com.vietinbank.core_ui.R.string.file_not_found_error))
            return
        } else {
            try {
                saveBase64File(
                    context,
                    data.file.orEmpty(),
                    data.fileName.orEmpty(),
                ).onSuccess { file ->
                    val uri = Utils.getFileUri(context, file)
                    val mimeType = Utils.getMimeType(file)

                    val intent = Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(uri, mimeType)
                        flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                    }

                    if (intent.resolveActivity(context.packageManager) != null) {
                        _openFileIntent.emit(intent)
                    } else {
                        _errorMessage.emit(resourceProvider.getString(com.vietinbank.core_ui.R.string.file_app_support_error))
                    }
                }.onFailure { e ->
                    _errorMessage.emit(
                        e.message
                            ?: resourceProvider.getString(com.vietinbank.core_ui.R.string.file_unknown_error),
                    )
                }
            } catch (e: Exception) {
                _errorMessage.emit(
                    resourceProvider.getString(
                        com.vietinbank.core_ui.R.string.file_save_error,
                        e.message ?: "",
                    ),
                )
            }
        }
    }

    fun getDownloadUNCFile(context: Context, mtId: String?, signType: String?) {
        launchJob {
            val res = useCase.getDownloadUNCFile(
                GetDownloadBase64FileParams(
                    username = userProf.getUserName(),
                    type = null,
                    mtId = mtId,
                    signType = signType,
                ),
            )
            handleResource(res) { data ->
                handleDownloadFile(context, data)
            }
        }
    }

    private fun getBankList(detailTrans: TransDetailDomain? = null) {
        if (transferCacheManager.getBankList()?.isNotEmpty() == true) {
            mappingBankReceive(detailTrans)
            return
        }
        launchJob {
            val res = transferUseCase.getNapasBankList(
                NapasBankListParams(
                    username = userProf.getUserName() ?: "",
                    cifno = userProf.getCifNo() ?: "",
                ),
            )
            handleResource(res) { data ->
                transferCacheManager.saveBankList(data.dataBanks)
                mappingBankReceive(detailTrans)
            }
        }
    }

    private fun mappingBankReceive(detailTrans: TransDetailDomain? = null) {
        val detailModelWithBank = detailTrans?.apply {
            fromBankUrl = transferCacheManager.getBankList()?.firstOrNull { bank ->
                (
                    bank.type.equals(
                        Tags.TransferType.TYPE_IN, true,
                    )
                    ) || bank.ebankCode == branchId || bank.binCode == receiveBank
            }?.icon ?: ""
            transferCacheManager.getBankList()?.firstOrNull { bank ->
                (
                    Tags.TransferType.TYPE_IN == tranType && bank.type.equals(
                        tranType, true,
                    )
                    ) || bank.ebankCode == receiveBank || bank.binCode == receiveBank
            }?.let { bankSelect ->
                receiveBankName = bankSelect.bankName ?: ""
                toBankUrl = bankSelect.icon ?: ""
            }
        }
        _detailState.value = _detailState.value.copy(
            detailModel = detailModelWithBank,
        )
    }

    private fun initHistoryApprove(
        rejectReason: String? = null,
        status: String? = null,
        activityLogs: ActivityLogsDomains? = null,
    ): MutableList<VerifiedByEntity> {
        val approveUserLst: MutableList<VerifiedByEntity> = mutableListOf()
        val isError = !rejectReason.isNullOrEmpty() || status in listOf(
            StatusType.ERROR.value, StatusType.ERROR_11A.value,
        )
        val isWaiting = status?.contains(StatusType.REGEX_AWAITING_APPROVAL.value) == true
        activityLogs?.let {
            it.createdBy?.let { createBy ->
                approveUserLst.add(
                    createBy.copy(
                        status = SUCCESS,
                        description = resourceProvider.getString(
                            com.vietinbank.core_ui.R.string.maker_transfer_account_result_create_at,
                            createBy.processDate?.getDateToFormat(
                                dd_MM_yyyy_HH_mm_ss_1, dd_MM_yyyy_HH_mm_ss,
                            ) ?: "",
                        ),
                    ),
                )
            }

            // sap xep theo thoi gian tang dan
            it.verifiedBy?.sortBy { item ->
                item.processDate?.toTimeInMillis(
                    dd_MM_yyyy_HH_mm_ss_1,
                ) ?: 0L
            }

            if (!isWaiting) {
                it.verifiedBy?.forEach { item ->
                    val processDate = item.processDate?.getDateToFormat(
                        dd_MM_yyyy_HH_mm_ss_1, dd_MM_yyyy_HH_mm_ss,
                    ) ?: ""
                    when {
                        // neu tu choi => lay thang cuoi cung verifiedBy hien thi loi
                        isError && item == it.verifiedBy?.lastOrNull() -> {
                            approveUserLst.add(
                                item.copy(
                                    status = ERROR,
                                    description = resourceProvider.getString(
                                        com.vietinbank.core_ui.R.string.feature_checker_reject_time,
                                        processDate,
                                    ),
                                    messageError = rejectReason,
                                ),
                            )
                        }

                        else -> {
                            approveUserLst.add(
                                item.copy(
                                    status = SUCCESS,
                                    description = resourceProvider.getString(
                                        com.vietinbank.core_ui.R.string.feature_checker_approve_time,
                                        processDate,
                                    ),
                                ),
                            )
                        }
                    }
                }
            } else {
                // dang cho phe duyet
                it.nextApproverBy?.forEach { item ->
                    approveUserLst.add(
                        item.copy(
                            status = PENDING,
                            description = resourceProvider.getString(
                                com.vietinbank.core_ui.R.string.maker_transfer_result_pending,
                            ),
                        ),
                    )
                }
            }
        }
        return approveUserLst
    }
}

sealed class ManagerDetailAction {
    data object TapHome : ManagerDetailAction()
    data object TapBack : ManagerDetailAction()
    data object TapCheckApproved : ManagerDetailAction()
    data class TapDownloadFile(val isShow: Boolean) : ManagerDetailAction()
    data class TapOnTab(val tabIndex: Int) : ManagerDetailAction()
    data class TapSecondAction(val actionType: String?, val detail: TransDetailDomain?) :
        ManagerDetailAction()

    data object TapCheckLimit : ManagerDetailAction()
}
