<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/m_transaction_nav"
    app:startDestination="@id/mTransactionFilterFragment">

    <fragment
        android:id="@+id/mTransactionManagerFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.TransactionManagerFragment"
        android:label="TransactionManagerFragment">
    </fragment>

    <fragment
        android:id="@+id/mTransactionFilterFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.MTransactionFilterFragment"
        android:label="MTransactionFilterFragment">

        <deepLink app:uri="transaction://filter" />

    </fragment>

    <fragment
        android:id="@+id/mTransactionListFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.MTransactionListFragment"
        android:label="MTransactionListFragment" />

    <fragment
        android:id="@+id/mTransactionDetailFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.MTransactionDetailFragment"
        android:label="MTransactionDetailFragment" />

    <fragment
        android:id="@+id/transactionDetailFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.TransactionDetailFragment"
        android:label="TransactionDetailFragment" />

</navigation>