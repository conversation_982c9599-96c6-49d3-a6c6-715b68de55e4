<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/app_nav_graph"
    app:startDestination="@id/startFragment">

    <!--LoginFragment -->
    <fragment
        android:id="@+id/loginFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.LoginFragment"
        android:label="Login" />

    <!--HomePreLoginFragment -->
    <fragment
        android:id="@+id/homePreLoginFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.HomePreLoginFragment"
        android:label="HomePreLogin" />

    <!--HomeFragment -->
    <fragment
        android:id="@+id/homeFragment"
        android:name="com.vietinbank.feature_home.ui.screen.home.HomeFragment"
        android:label="Home" />

    <!--editShortcutFragment -->
    <fragment
        android:id="@+id/editShortcutFragment"
        android:name="com.vietinbank.feature_home.ui.screen.home_new_ui.short_cut.EditShortcutFragment"
        android:label="editShortcutFragment" />

    <!--homePageFragment -->
    <fragment
        android:id="@+id/homePageFragment"
        android:name="com.vietinbank.feature_home.ui.screen.home_new_ui.home_page.HomePageFragment"
        android:label="homePageFragment" />

    <!--accountFragment -->
    <fragment
        android:id="@+id/accountFragment"
        android:name="com.vietinbank.feature_home.ui.screen.home_new_ui.account_home.AccountFragment"
        android:label="accountFragment" />

    <!--StartFragment -->
    <fragment
        android:id="@+id/startFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.StartFragment"
        android:label="Start" />

    <!--ChangePasswordFragment -->
    <fragment
        android:id="@+id/changePasswordFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.ChangePasswordFragment"
        android:label="ChangePassword" />

    <fragment
        android:id="@+id/changePasswordFragmentNewUI"
        android:name="com.vietinbank.feature_login.ui.fragment.NewUIChangePasswordFragment"
        android:label="ChangePasswordNewUI" />

    <!--Active2FAStep1Fragment -->
    <fragment
        android:id="@+id/active2FAStep1Fragment"
        android:name="com.vietinbank.feature_login.ui.fragment.Active2FAStep1Fragment"
        android:label="Active2FAStep1" />

    <!--Active2FAStep2Fragment -->
    <fragment
        android:id="@+id/active2FAStep2Fragment"
        android:name="com.vietinbank.feature_login.ui.fragment.Active2FAStep2Fragment"
        android:label="Active2FAStep2" />

    <!-- NewUI Fragments -->
    <!--NewUIPreLoginFragment -->
    <fragment
        android:id="@+id/newUIPreLoginFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.NewUIPreLoginFragment"
        android:label="NewUIPreLogin" />
        
    <!--NewUILoginFragment -->
    <fragment
        android:id="@+id/newUILoginFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.NewUILoginFragment"
        android:label="NewUILogin" />


    <!--ApprovalListFragment -->
    <fragment
        android:id="@+id/approvalListFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.ApprovalListFragment"
        android:label="AprovalList" />
    
    <!--NewUIApprovalListFragment -->
    <fragment
        android:id="@+id/newUIApprovalListFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.NewUIApprovalListFragment"
        android:label="NewUIApprovalList" />


    <!--    multiple transaction-->
    <fragment
        android:id="@+id/newUIMultipleApprovalFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.multiple.NewUIMultipleApprovalFragment"
        android:label="NewUIMultipleApprovalFragment" />

    <fragment
        android:id="@+id/newUIMultipleConfirmFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.multiple.NewUIMultipleConfirmFragment"
        android:label="NewUIMultipleConfirmFragment" />

    <fragment
        android:id="@+id/newUIMultipleResultFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.multiple.NewUIMultipleResultFragment"
        android:label="NewUIMultipleResultFragment" />

    <!--InitTransactionFragment -->
    <fragment
        android:id="@+id/initTransactionFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.InitTransactionFragment"
        android:label="InitTransaction" />

    <!--NewUIInitTransactionFragment -->
    <fragment
        android:id="@+id/newUIInitTransactionFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.NewUIInitTransactionFragment"
        android:label="NewUIInitTransaction" />

    <!--ConfirmCheckerFragment -->
    <fragment
        android:id="@+id/confirmCheckerFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.ConfirmCheckerFragment"
        android:label="ConfirmChecker" />

    <!--ConfirmOTPFragment -->
    <fragment
        android:id="@+id/confirmOTPFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.ConfirmOTPFragment"
        android:label="ConfirmOTP" />

    <!--ConfirmKeypassFragment -->
    <fragment
        android:id="@+id/confirmKeypassFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.ConfirmKeypassFragment"
        android:label="ConfirmKeypass" />

    <!--SuccessFragment -->
    <fragment
        android:id="@+id/successCheckerFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.SuccessFragment"
        android:label="SuccessChecker" />
    
    <!--NewUISuccessFragment -->
    <fragment
        android:id="@+id/newUISuccessFragment"
        android:name="com.vietinbank.feature_checker.ui.fragment.newui.NewUISuccessFragment"
        android:label="NewUISuccess" />

    <fragment
        android:id="@+id/fragmentPaymentOrder"
        android:name="com.vietinbank.feture_maker.maker_ui.create.PaymentOrderFragment"
        android:label="PaymentOrder" />

    <fragment
        android:id="@+id/makerTranserQRFragment"
        android:name="com.vietinbank.feture_maker.maker_ui.scan.MakerTransferQRFragment"
        android:label="MakerTransferQRFragment" />

    <fragment
        android:id="@+id/makerResultTranserFragment"
        android:name="com.vietinbank.feture_maker.maker_ui.result.MakerResultTransferFragment"
        android:label="MakerResultTranser" />

    <fragment
        android:id="@+id/inquiryApproverListFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.inquiry_approver_list.InquiryApproverListFragment"
        android:label="InquiryApproverList" />
    <fragment
        android:id="@+id/tracePaymentFragment"
        android:name="com.vietinbank.feature_trace_payment.TracePaymentFragment"
        android:label="TracePayment" />
    <fragment
        android:id="@+id/tracePaymentDetailFragment"
        android:name="com.vietinbank.feature_trace_payment.TracePaymentDetailFragment"
        android:label="TracePaymentDetail" />
    <fragment
        android:id="@+id/tracePaymentComfirmFragment"
        android:name="com.vietinbank.feature_trace_payment.comfirm.TracePaymentComfirmFragment"
        android:label="TracePaymentComfirm" />
    <fragment
        android:id="@+id/dashBoardReportFragment"
        android:name="com.vietinbank.feature_report.fragment.DashBoardReportFragment"
        android:label="DashBoardReport" />
    <fragment
        android:id="@+id/listRejectTransferDetailFragment"
        android:name="com.vietinbank.feature_report.fragment.ListRejectTransferDetailFragment"
        android:label="listRejectTransferDetail" />

    <!--ConfirmUpdateEkycFragment -->
    <fragment
        android:id="@+id/confirmUpdateEkycFragment"
        android:name="com.vietinbank.core_ekyc.update_ekyc.confirm_update_ekyc.ConfirmUpdateEkycFragment"
        android:label="confirmUpdateEkycFragment" />

    <!--transactionStatusFragment -->
    <fragment
        android:id="@+id/transactionStatusFragment"
        android:name="com.vietinbank.feature_home.ui.screen.transaction_status.TransactionStatusFragment"
        android:label="transactionStatusFragment" />

    <!--SettingFragment -->
    <fragment
        android:id="@+id/settingFragment"
        android:name="com.vietinbank.feature_home.ui.screen.setting.newui.fragment.NewUISettingFragment"
        android:label="settingFragment" />

    <!--    list ott graph  -->
    <include app:graph="@navigation/list_ott_nav" />

    <!--    soft graph   -->
    <include app:graph="@navigation/v_soft_otp_nav" />

    <include app:graph="@navigation/ott_service_nav_graph" />

    <!--    manage transaction graph   -->
<!--    <include app:graph="@navigation/m_transaction_nav" />-->

    <!--    account graph   -->
    <include app:graph="@navigation/account_nav" />

    <!--    smart ca graph   -->
    <include app:graph="@navigation/smartca_nav" />

    <!--    manage ott graph   -->
    <include app:graph="@navigation/m_ott_nav" />

    <!--    update Ekyc graph   -->
    <include app:graph="@navigation/update_ekyc_nav_graph" />

    <!--    register Ekyc graph   -->
    <include app:graph="@navigation/m_register_ekyc_nav" />

    <!--    account balance change graph   -->
    <include app:graph="@navigation/account_balance_change_nav_graph" />

    <!--    maker transfer graph   -->
    <include app:graph="@navigation/maker_transfer_nav_graph" />
    <!--    maker payment order graph   -->
    <include app:graph="@navigation/maker_payment_order_nav_graph" />

    <action android:id="@+id/action_global_maker_transfer_dashboard"
        app:destination="@id/maker_transfer_nav_graph"
        app:launchSingleTop="true"
        app:popUpToInclusive="false"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"/>

    <action android:id="@+id/action_global_maker_payment_order_dashboard"
        app:destination="@id/maker_payment_order_nav_graph"
        app:launchSingleTop="true"
        app:popUpToInclusive="false"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"/>

    <action android:id="@+id/action_global_smart_ca_list_dashboard"
        app:destination="@id/smartca_nav"
        app:launchSingleTop="true"
        app:popUpToInclusive="false"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"/>

    <fragment
        android:id="@+id/accountLockFragmentNewUI"
        android:name="com.vietinbank.feature_login.ui.fragment.newui_account_lock.NewUIAccountLockInitFragment"
        android:label="AccountLockFragmentNewUI" />

    <fragment
        android:id="@+id/accountLockFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.account_lock.AccountLockInitFragment"
        android:label="AccountLockFragment" />

    <fragment
        android:id="@+id/accountLockConfirmFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.account_lock.AccountLockConfirmFragment"
        android:label="AccountLockConfirmFragment" />

    <fragment
        android:id="@+id/accountLockResultFragment"
        android:name="com.vietinbank.feature_login.ui.fragment.account_lock.AccountLockResultFragment"
        android:label="AccountLockResultFragment" />

    <!--    Manager transaction -->
    <fragment
        android:id="@+id/transactionManagerFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.TransactionManagerFragment"
        android:label="TransactionManagerFragment">
    </fragment>

    <fragment
        android:id="@+id/transactionFilterFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.MTransactionFilterFragment"
        android:label="MTransactionFilterFragment">
    </fragment>

    <fragment
        android:id="@+id/mTransactionDetailFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.MTransactionDetailFragment"
        android:label="MTransactionDetailFragment" />

    <fragment
        android:id="@+id/transactionDetailFragment"
        android:name="com.vietinbank.feature_transaction_manage.fragment.TransactionDetailFragment"
        android:label="MTransactionDetailFragment" />

    <!-- Global action for OTT Dashboard to prevent multiple instances -->
    <action android:id="@+id/action_global_ott_dashboard"
        app:destination="@id/list_ott_nav"
        app:launchSingleTop="true"
        app:popUpTo="@id/homeFragment"
        app:popUpToInclusive="false"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right">
        <argument android:name="messageId" app:argType="string" app:nullable="true"/>
        <argument android:name="openFromNotification" app:argType="boolean" android:defaultValue="false"/>
    </action>

    <!--Soft otp-->
    <include app:graph="@navigation/soft_otp_nav_graph" />

</navigation>