package com.vietinbank.newcore.nav

import android.os.Bundle
import androidx.core.net.toUri
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.dialog.KeypassAuthDialog
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import com.vietinbank.feature_soft.views.newui.fragment.active.ActiveSoftOTPFlow
import com.vietinbank.feature_soft.views.newui.fragment.confirm_otp.ConfirmOTPFlow
import com.vietinbank.newcore.R

/**
 * Created by vandz on 27/2/25.
 */
class AppNavigatorImpl(
    private val navController: NavController,
    private val fragmentManager: FragmentManager,
) : IAppNavigator {
    override fun goToLogin() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.newUILoginFragment, null, navOptions)
    }

    override fun goToLoginForNewAccount() {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putBoolean("clear_form", true)
        }
        navController.navigate(R.id.newUILoginFragment, bundle, navOptions)
    }

    override fun goToLoginWithSelectedUser(username: String, fullName: String?, corpName: String?) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString("selected_username", username)
            putString("selected_fullname", fullName)
            putString("selected_corpname", corpName)
        }
        navController.navigate(R.id.newUILoginFragment, bundle, navOptions)
    }

    override fun goToLoginAndPopAll(sessionExpiredMessage: String?) {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.newUIPreLoginFragment, false) // Pop back TO PreLogin (keep it in stack)
            .setLaunchSingleTop(true) // Prevent multiple instances
            .build()

        // Always set session expired flag when this method is called
        // The Fragment will get the message from its own string resource
        val bundle = Bundle().apply {
            putBoolean("session_expired_flag", true)
        }

        navController.navigate(R.id.newUILoginFragment, bundle, navOptions)
    }

    override fun goToLoginFromSplash() {
        val navOptions = createSlideNavOptions(popUpStartFragment = true)
        navController.navigate(R.id.newUIPreLoginFragment, null, navOptions)
    }

    override fun goToHomePreLogin() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.newUIPreLoginFragment, null, navOptions)
    }

    override fun goToHomePreLoginFromSplash() {
        val navOptions = createSlideNavOptions(popUpStartFragment = true)
        navController.navigate(R.id.newUIPreLoginFragment, null, navOptions)
    }

    override fun goToHomeFrom2FA() {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.active2FAStep1Fragment, inclusive = true)
            .build()
        navController.navigate(R.id.homeFragment, null, builder)
    }

    override fun goToHomePreLoginAndPopAll() {
        val navOptions = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)
            .setPopUpTo(R.id.newUIPreLoginFragment, true) // xóa toàn bộ back stack
            .build()

        navController.navigate(R.id.newUIPreLoginFragment, null, navOptions)
    }

    override fun goToHome() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.homePageFragment, null, navOptions)
    }

    override fun popToHomeFromAccount() {
        val popped = navController.popBackStack(R.id.homePageFragment, false)
        if (!popped) {
            navController.navigate(R.id.homePageFragment, null, NavOptions.Builder().build())
        }
    }

    override fun goToHomeTest() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.homeFragment, null, navOptions)
    }

    override fun goToEditShortcut() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.editShortcutFragment, null, navOptions)
    }

    override fun goToAccountFromLogin() {
        navController.navigate(R.id.homePageFragment, null, NavOptions.Builder().build())
        navController.navigate(R.id.accountFragment, null, NavOptions.Builder().build())
    }

    override fun goToAccountNoAnm() {
        navController.navigate(R.id.accountFragment, null, NavOptions.Builder().build())
    }

    override fun goToActive() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.changePasswordFragmentNewUI, null, navOptions)
    }

    override fun goToApprovalList(
        transaction: String,
        tranType: String,
        groupType: String,
        serviceType: String,
        isBatchFileMode: Boolean,
        countTransaction: String?,
    ) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.TRANSACTION_BUNDLE, transaction)
            putString(Tags.TRAN_TYPE_BUNDLE, tranType)
            putString(Tags.GROUP_TYPE_BUNDLE, groupType)
            putString(Tags.SERVICE_TYPE_BUNDLE, serviceType)
            putBoolean(Tags.IS_BATCH_FILE_MODE, isBatchFileMode)
            putString(Tags.COUNT_TRANSACTION_BUNDLE, countTransaction)
        }

        // Navigate to NewUI version of ApprovalListFragment
        // Following the same pattern as login which uses NewUI by default
        navController.navigate(R.id.newUIApprovalListFragment, bundle, navOptions)
    }

    override fun popToApprovalList(clearBackStack: Boolean) {
        if (clearBackStack) {
            // Xóa toàn bộ luồng giao dịch khỏi back stack
            navController.popBackStack(R.id.newUIApprovalListFragment, false)
        } else {
            // Chỉ quay lại NewUIApprovalListFragment nhưng giữ lại stack hiện tại
            navController.navigate(R.id.newUIApprovalListFragment)
        }
    }

    override fun goToInitTransaction(transaction: String, multipleType: String?) {
        val navOptions = createSlideNavOptions()

        // Tạo Bundle:
        val bundle = Bundle().apply {
            putString(Tags.TRANSACTION_BUNDLE, transaction)
            multipleType?.let { putString(Tags.DATA_TYPE, it) }
        }

        // Gọi navController.navigate() với Bundle:
        navController.navigate(R.id.newUIInitTransactionFragment, bundle, navOptions)
    }

    override fun goToConfirmChecker(
        transaction: String,
        paymentMethod: String,
        confirmType: String,
        dataType: String,
    ) {
        val navOptions = createSlideNavOptions()

        // Tạo Bundle:
        val bundle = Bundle().apply {
            putString(Tags.TRANSACTION_BUNDLE, transaction)
            putString(Tags.PAYMENT_METHOD_BUNDLE, paymentMethod)
            putString(Tags.CONFIRM_TYPE_BUNDLE, confirmType)
            putString(Tags.DATA_TYPE, dataType)
        }

        // Gọi navController.navigate() với Bundle:
        navController.navigate(R.id.confirmCheckerFragment, bundle, navOptions)
    }

    override fun goToConfirmOTP(
        transaction: String,
        transactionID: String,
        originalTransaction: String,
        token: String,
        step: Int,
        nextApprover: String,
        confirmType: String,
        mtId: String,
        ekycId: String,
    ) {
        val navOptions = createSlideNavOptions()

        // Tạo Bundle:
        val bundle = Bundle().apply {
            putString(Tags.TRANSACTION_BUNDLE, transaction)
            putString(Tags.TRANSACTION_ID_BUNDLE, transactionID)
            putString(Tags.ORIGINAL_TRANSACTION_BUNDLE, originalTransaction)
            putString(Tags.TOKEN_BUNDLE, token)
            putInt(Tags.STEP_BUNDLE, step)
            putString(Tags.CONFIRM_TYPE_BUNDLE, confirmType)
            putString(Tags.NEXT_APPROVER_BUNDLE, nextApprover)
            putString(Tags.MT_ID, mtId)
            putString(Tags.REGISTER_EKYC_ID, ekycId)
        }

        // Gọi navController.navigate() với Bundle:
        navController.navigate(R.id.confirmOTPFragment, bundle, navOptions)
    }

    override fun goToConfirmKeypass(
        challengeCode: String,
        originalTransaction: String,
        confirmType: String,
        transaction: String,
        nextApprover: String,
        mtId: String,
        ekycId: String,
    ) {
        val navOptions = createSlideNavOptions()
        val bundle = Bundle().apply {
            putString(Tags.CHALLENGE_CODE_BUNDLE, challengeCode)
            putString(Tags.CONFIRM_TYPE_BUNDLE, confirmType)
            putString(Tags.TRANSACTION_BUNDLE, transaction)
            putString(Tags.NEXT_APPROVER_BUNDLE, nextApprover)
            putString(Tags.ORIGINAL_TRANSACTION_BUNDLE, originalTransaction)
            putString(Tags.MT_ID, mtId)
            putString(Tags.REGISTER_EKYC_ID, ekycId)
        }

        // Gọi navController.navigate() với Bundle:
        navController.navigate(R.id.confirmKeypassFragment, bundle, navOptions)
    }

    override fun goToConfirmKeypassDialog(
        challengeCode: String,
    ) {
        KeypassAuthDialog.newInstance(
            challengeCode = challengeCode,
        ).show(fragmentManager, KeypassAuthDialog::class.java.name)
    }

    override fun gotoSuccessChecker(successList: String, confirmRes: String, confirmType: String, message: String) {
        val navOptions = createSlideNavOptions()

        // Tạo Bundle:
        val bundle = Bundle().apply {
            putString(Tags.SUCCESS_LIST_BUNDLE, successList)
            putString(Tags.TRANSACTION_BUNDLE, confirmRes)
            putString(Tags.CONFIRM_TYPE_BUNDLE, confirmType)
            putString(Tags.MESSAGE_STATUS_BUNDLE, message)
        }

        // Gọi navController.navigate() với Bundle:
        // Navigate to NewUISuccessFragment instead of old SuccessFragment
        navController.navigate(R.id.newUISuccessFragment, bundle, navOptions)
    }

    override fun navigateUp() {
        navController.navigateUp()
    }

    override fun popBackStack() {
        navController.popBackStack()
    }

    override fun goToMakerTransfer(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.action_global_maker_transfer_dashboard, bundle, navOptions)
    }

    override fun goToMakerPaymentOrder(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.action_global_maker_payment_order_dashboard, bundle, navOptions)
    }
    override fun goToMakerTransferBankChoosed(typeTransfer: String, itemStringObj: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.MAKER_TRANSFER_BANK_CHOOSED_FRAGMENT}$typeTransfer/$itemStringObj".toUri(),
            navOptions,
        )
    }

    override fun goToMakerTransferCard(typeTransfer: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.MAKER_TRANSFER_BANK_CHOOSED_FRAGMENT}$typeTransfer".toUri(),
            navOptions,
        )
    }
    override fun goToMakerTransferConfirm(typeTransfer: String, validateItem: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.MAKER_TRANSFER_BANK_CONFIRM_FRAGMENT}$typeTransfer/$validateItem".toUri(),
            navOptions,
        )
    }

    override fun goToMakerTransferResult(
        typeTransfer: String,
        confirmItem: String,
        resultItem: String,
    ) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.MAKER_TRANSFER_BANK_RESULT_FRAGMENT}$typeTransfer/$confirmItem/$resultItem".toUri(),
            navOptions,
        )
    }

    override fun goToPaymentOrderFragment() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.fragmentPaymentOrder, null, navOptions)
    }

    override fun goToMakerResultTransferFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.makerResultTranserFragment, bundle, navOptions)
    }

    override fun goToActive2FAStep1Fragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.active2FAStep1Fragment, bundle, navOptions)
    }

    override fun goToActive2FAStep2Fragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.active2FAStep2Fragment, bundle, navOptions)
    }

    override fun setFragmentResult(requestKey: String, result: Bundle) {
        // Lấy NavHostFragment
        fragmentManager.setFragmentResult(requestKey, result)
    }

    // keypass
    override fun gotoKeypassMain() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.KEYPASS_HOME.toUri(), navOptions)
    }

    override fun gotoActivSoft() {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            com.vietinbank.feature_soft.R.id.soft_otp_nav_graph,
            null,
            navOptions,
        )
    }

    override fun goToForgotKeypass() {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.NEW_ACTIVE_SOFT}${ActiveSoftOTPFlow.FORGOT_KEYPASS.name}".toUri(),
            navOptions,
        )
    }

    override fun gotToForgotSoftOTP() {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.TERM_OF_USE_SOFT}${ActiveSoftOTPFlow.FORGOT.name}".toUri(),
            navOptions,
        )
    }

    override fun goToEnterPIN(flowPin: String) {
        val navOptions = createSlideNavOptions()
        when (flowPin) {
            VSoftConstants.VtpOTPFlowType.ACTIVE.name -> {
                gotoActivSoft()
            }
            VSoftConstants.VtpOTPFlowType.SYNC.name -> {
                navController.navigate(
                    "${Tags.NEW_SOFT_PIN}${ConfirmOTPFlow.SYNC.name}".toUri(),
                    navOptions,
                )
            }
            "test" -> {
                navController.navigate(
                    "soft://test_otp".toUri(),
                    navOptions,
                )
            }

            else -> {
                navController.navigate(
                    "${Tags.SOFT_PIN}$flowPin".toUri(),
                    navOptions,
                )
            }
        }
    }

    override fun goToLockSoftOTPDialog() {
        navController.navigate(Tags.NEW_SOFT_DIALOG.toUri())
    }

    override fun gotoHomeSoft() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.SOFT_HOME.toUri(), navOptions)
    }

    // transaction manage
    override fun gotoTransactionManager(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.transactionManagerFragment, bundle, navOptions)
    }

    override fun gotoFilterTransaction() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.transactionFilterFragment, null, navOptions)
    }

    override fun goToinquiryApproverListFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.inquiryApproverListFragment, bundle, navOptions)
    }

    override fun goToInquiryAccount(inquiryAccount: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.ACCOUNT_INQUIRY}$inquiryAccount".toUri(), navOptions)
    }

    override fun goToTracePaymentFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.tracePaymentFragment, bundle, navOptions)
    }

    override fun goToTracePaymentDetailFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.tracePaymentDetailFragment, bundle, navOptions)
    }

    override fun goToTracePaymentComfirmFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.tracePaymentComfirmFragment, bundle, navOptions)
    }

    override fun goToDashBoardReportFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.dashBoardReportFragment, bundle, navOptions)
    }

    override fun gotoListRejectTransferDetailFragment(bundle: Bundle) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.listRejectTransferDetailFragment, bundle, navOptions)
    }

    override fun goToListSmartCA() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.action_global_smart_ca_list_dashboard, bundleOf(), navOptions)
    }
    override fun goToDetailSmartCA(mtID: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.SMARTCA_DETAIL}$mtID".toUri(),
            navOptions,
        )
    }

    override fun goToRegisterSmartCA() {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            Tags.SMARTCA_REGISTER.toUri(),
            navOptions,
        )
    }

    override fun goToConfirmSmartCA(transactionBundle: String, dataType: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.SMARTCA_CONFIRM}$transactionBundle/$dataType".toUri(),
            navOptions,
        )
    }

    override fun goToResultSmartCA(transactionBundle: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate(
            "${Tags.SMARTCA_RESULT}$transactionBundle".toUri(),
            navOptions,
        )
    }

    override fun goToDisbursement() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.DISBURSEMENT_HOME.toUri(), navOptions)
    }

    override fun goToOttDashboard() {
        // Use the overloaded method with null messageId to ensure consistent behavior
        goToOttDashboard(null)
    }

    override fun goToDashboardFeatureOTT(flowSetting: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.DASHBOARD_FEATURE_OTT}$flowSetting".toUri(), navOptions)
    }
    override fun goToOttDashboard(messageId: String?) {
        // Check if already at OTT screen to avoid flashing
        val currentDestination = navController.currentDestination
        // Check by label since we can't access R.id.m_ott_nav from app module
        if (currentDestination?.label == "ListOttFragment" ||
            currentDestination?.parent?.label == "m_ott_nav"
        ) {
            navController.currentBackStackEntry?.savedStateHandle?.set("messageId", messageId)
            navController.currentBackStackEntry?.savedStateHandle?.set("openFromNotification", messageId != null)
            return
        }

        val bundle = Bundle().apply {
            putString("messageId", messageId)
            putBoolean("openFromNotification", messageId != null)
        }

        try {
            // Use global action for single instance guarantee
            navController.navigate(R.id.action_global_ott_dashboard, bundle)
        } catch (e: Exception) {
            // Fallback to deep link if global action fails
            val navOptions = NavOptions.Builder()
                .setEnterAnim(R.anim.slide_in_right)
                .setExitAnim(R.anim.slide_out_left)
                .setPopEnterAnim(R.anim.slide_in_left)
                .setPopExitAnim(R.anim.slide_out_right)
                .setLaunchSingleTop(true)
                .setPopUpTo(R.id.homeFragment, false)
                .build()

            val uri = if (messageId != null) {
                "${Tags.OTT_DASHBOARD}?messageId=$messageId&openFromNotification=true".toUri()
            } else {
                Tags.OTT_DASHBOARD.toUri()
            }
            navController.navigate(uri, navOptions)
        }
    }

    override fun goToRegisterOTT(tag: String, flowSetting: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.REGISTER_OTT}$flowSetting/$tag".toUri(), navOptions)
    }

    override fun goToValidateOTT(tag: String, alertType: String?) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.SMS_OTP_CREATE}$tag/$alertType".toUri(), navOptions)
    }

    override fun goToConfirmOTT() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.CONFIRM_OTT.toUri(), navOptions)
    }
    override fun goToConfirmOTTLoan() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.CONFIRM_OTT_LOAN.toUri(), navOptions)
    }

    override fun goToConfirmSmsTransform(alertType: String, tagRoute: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.CONFIRM_SMS}/$alertType/$tagRoute".toUri(), navOptions)
    }

    override fun goToTransactionStatus() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.transactionStatusFragment, null, navOptions)
    }

    override fun gotoUpdateEkyc() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.UPDATE_BIOMETRIC.toUri(), navOptions)
    }
    override fun gotoUpdateEkycUserInfo() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.UPDATE_EKYC_USER_INFO.toUri(), navOptions)
    }

    override fun goToConfirmUpdateEkycScreen() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.confirmUpdateEkycFragment, null, navOptions)
    }

    override fun goToRegisterEkycScreen() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.EKYC_STEP_REGISTER.toUri(), navOptions)
    }

    override fun goToSetting() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.settingFragment, null, navOptions)
    }

    override fun goToAccountBalanceSetting(accountType: String) {
        val navOptions = createSlideNavOptions()
        navController.navigate("${Tags.ACCOUNT_BALANCE_TAG_NAV}$accountType".toUri(), navOptions)
    }

    override fun goToOttServiceManage() {
        val navOptions = createSlideNavOptions()
        navController.navigate(Tags.OTT_SERVICE.toUri(), navOptions)
    }

    override fun goToMakerQRTransferFragment() {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.makerTranserQRFragment, null, navOptions)
    }

    // mơ khoa/ cap lai mat khau nguoi dung
    override fun goToLockAccount(bundle: Bundle?) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.accountLockFragmentNewUI, bundle, navOptions)
    }

    override fun goToLockConfirmAccount(lockTransaction: String) {
        val bundle = bundleOf().apply { putString(Tags.TRANSACTION_BUNDLE, lockTransaction) }
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.accountLockConfirmFragment, bundle, navOptions)
    }

    override fun goToLockResultAccount(lockResult: String?) {
        val bundle = bundleOf().apply { putString(Tags.TRANSACTION_BUNDLE, lockResult) }
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.accountLockResultFragment, bundle, navOptions)
    }

    override fun popToLogin(clearBackStack: Boolean) {
        if (clearBackStack) {
            // Xóa toàn bộ các fragment khỏi back stack
            navController.popBackStack(R.id.newUILoginFragment, false)
        } else {
            // Chỉ quay lại Login nhưng giữ lại stack hiện tại
            navController.navigate(R.id.newUILoginFragment)
        }
    }

    /**
     * Tạo NavOptions cho màn forward:
     * - enterAnim, exitAnim, popEnterAnim, popExitAnim
     */
    private fun createSlideNavOptions(popUpStartFragment: Boolean = false): NavOptions {
        val builder = NavOptions.Builder()
            .setEnterAnim(R.anim.slide_in_right)
            .setExitAnim(R.anim.slide_out_left)
            .setPopEnterAnim(R.anim.slide_in_left)
            .setPopExitAnim(R.anim.slide_out_right)

        if (popUpStartFragment) {
            // Xoá startFragment khỏi stack
            builder.setPopUpTo(R.id.startFragment, inclusive = true)
        }

        return builder.build()
    }

    override fun goToMultipleApprovalList(bundle: Bundle?) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.newUIMultipleApprovalFragment, bundle, navOptions)
    }

    override fun goToMultipleConfirmApproval(bundle: Bundle?) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.newUIMultipleConfirmFragment, bundle, navOptions)
    }

    override fun goToMultipleResultApproval(bundle: Bundle?) {
        val navOptions = createSlideNavOptions()
        navController.navigate(R.id.newUIMultipleResultFragment, bundle, navOptions)
    }
}
