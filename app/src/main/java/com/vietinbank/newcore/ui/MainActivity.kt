package com.vietinbank.newcore.ui

import android.Manifest
import android.app.AlertDialog
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.findNavController
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.extensions.printLog
import com.vietinbank.core_common.nav.NavigationEventManager
import com.vietinbank.core_common.network.NetworkMonitor
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_domain.ott.IOttManager
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.core_ui.base.BaseActivity
import com.vietinbank.newcore.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject

/**
 * Main entry point for the application.
 * This activity serves as the host for all fragments and manages navigation between them.
 * Uses Hilt for dependency injection.
 */
@AndroidEntryPoint
class MainActivity : BaseActivity() {
    private val viewModel: MainViewModel by viewModels()
    private val navController by lazy { findNavController(R.id.nav_host_fragment) }

    // Store app active state to pass to StartFragment
    private var isAppActive: Boolean = false

    @Inject
    lateinit var ottManager: IOttManager

    @Inject
    lateinit var smartCAManager: ISmartCAManager

    @Inject
    lateinit var sessionManager: ISessionManager

    @Inject
    lateinit var navigationEventManager: NavigationEventManager

    @Inject
    lateinit var userProf: IUserProf

    @Inject
    lateinit var networkMonitor: NetworkMonitor

    @Inject
    lateinit var appConfig: IAppConfigManager

    // Queue for pending OTT notifications
    private val pendingOttMessages = ConcurrentLinkedQueue<OttNotificationData>()
    private var isProcessingNotifications = false

    // Data class for notification queue
    data class OttNotificationData(
        val messageId: String,
        val ottData: String,
        val timestamp: Long = System.currentTimeMillis(),
    )

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        if (isGranted) {
            // Có thể log hoặc thực hiện các hành động sau khi quyền được cấp
            printLog("Notification permission granted")
        } else {
            // Có thể hiển thị dialog giải thích hoặc hướng dẫn user vào settings
            showPermissionExplanationIfNeeded()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        val splashScreen = installSplashScreen()

        // Keep splash screen while initializing
        var isReady = false
        splashScreen.setKeepOnScreenCondition { !isReady }

        super.onCreate(savedInstanceState)

        // IMPORTANT: Set content view BEFORE accessing navController
        setContentView(R.layout.activity_main)

        // Pre-calculate navigation bar height for performance optimization
        // This avoids repeated calculations when dialogs open
        window.decorView.post {
            val bottomPx = ViewCompat.getRootWindowInsets(window.decorView)
                ?.getInsets(WindowInsetsCompat.Type.navigationBars())
                ?.bottom ?: 0
            if (bottomPx > 0) {
                viewModel.saveNavigationBarHeight(bottomPx)
                printLog("MainActivity: Navigation bar height saved: ${bottomPx}px")
            }
        }

        // Always initialize app (robust against process death and restores)
        initializeApp {
            isReady = true
            handleInitialNavigation()
        }

        setupObservers()

        // Start network monitoring
        networkMonitor.startMonitoring()

        // Handle deep link navigation
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent) // Update activity's intent for future getIntent() calls
        handleIntent(intent)
    }

    override fun onDestroy() {
        // Stop network monitoring to prevent memory leaks
        networkMonitor.stopMonitoring()
        super.onDestroy()
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        handleSessionExpiredPendingIfNeeded()
    }

    override fun onResume() {
        super.onResume()
        // Fallback for cases where Activity wasn't destroyed (no savedInstanceState)
        handleSessionExpiredPendingIfNeeded()
    }

    private fun handleSessionExpiredPendingIfNeeded() {
        if (appConfig.isSessionExpiredPending() && !isOnLoginDest()) {
            // Clear flag BEFORE navigating to avoid loops
            appConfig.setSessionExpiredPending(false)

            try {
                // Mirror AppNavigatorImpl.goToLoginAndPopAll()
                val navOptions = NavOptions.Builder()
                    .setEnterAnim(R.anim.slide_in_right)
                    .setExitAnim(R.anim.slide_out_left)
                    .setPopEnterAnim(R.anim.slide_in_left)
                    .setPopExitAnim(R.anim.slide_out_right)
                    .setPopUpTo(R.id.newUIPreLoginFragment, false)
                    .setLaunchSingleTop(true)
                    .build()

                val bundle = Bundle().apply {
                    putBoolean("session_expired_flag", true)
                }
                navController.navigate(R.id.newUILoginFragment, bundle, navOptions)
            } catch (e: Exception) {
                printLog("MainActivity: Navigation to login failed: ${e.message}")
            }
        }
    }

    private fun isOnLoginDest(): Boolean {
        val destId = navController.currentDestination?.id
        return destId == R.id.newUILoginFragment || destId == R.id.newUIPreLoginFragment
    }

    private fun handleIntent(intent: Intent?) {
        // Deep link will be handled automatically by Navigation Component
        printLog("MainActivity handleIntent - data: ${intent?.data}")

        // Check if this is from OTT notification (foreground case)
        if (intent?.action == "com.vietinbank.action.OTT_NOTIFICATION") {
            val messageId = intent.getStringExtra("ott_message_id")
            val ottData = intent.getStringExtra("ottData")

            if (!messageId.isNullOrEmpty() && !sessionManager.getSessionId().isNullOrEmpty()) {
                printLog("MainActivity: Queueing OTT notification for message: $messageId")

                // Queue the notification data
                if (!ottData.isNullOrEmpty()) {
                    pendingOttMessages.offer(OttNotificationData(messageId, ottData))
                }

                // Process the queue
                processPendingNotifications()
                return
            }
        }

        // Check for FCM data from background notification (merged from SplashActivity)
        val ottData = intent?.extras?.getString("ottData")
        if (!ottData.isNullOrEmpty()) {
            handleFcmNotification(ottData)
        }
    }

    private fun navigateToOttDashboard(messageId: String) {
        try {
            // Check if already at OTT screen to avoid flashing
            val currentDestination = navController.currentDestination
            // Check by label since we can't access R.id.m_ott_nav from app module
            if (currentDestination?.label == "ListOttFragment" ||
                currentDestination?.parent?.label == "m_ott_nav"
            ) {
                // Already at OTT, just update the messageId
                navController.currentBackStackEntry?.savedStateHandle?.set("messageId", messageId)
                navController.currentBackStackEntry?.savedStateHandle?.set("openFromNotification", true)
                printLog("Already at OTT dashboard, updating messageId: $messageId")
                return
            }

            val bundle = Bundle().apply {
                putString("messageId", messageId)
                putBoolean("openFromNotification", true)
            }
            navController.navigate(R.id.action_global_ott_dashboard, bundle)
            printLog("Navigated to OTT dashboard with messageId: $messageId")
        } catch (e: Exception) {
            printLog("Navigation error: ${e.message}")
            // Fallback: save as pending
            navigationEventManager.setPendingOttNavigation(messageId)
        }
    }

    private fun processPendingNotifications() {
        if (isProcessingNotifications) {
            printLog("MainActivity: Already processing notifications, skipping...")
            return
        }

        lifecycleScope.launch {
            isProcessingNotifications = true

            // Set flag in OttManager to prevent race conditions
            ottManager.setProcessingNotification(true)

            try {
                val messagesToProcess = mutableListOf<OttNotificationData>()

                // Drain the queue
                while (pendingOttMessages.isNotEmpty()) {
                    pendingOttMessages.poll()?.let { messagesToProcess.add(it) }
                }

                if (messagesToProcess.isEmpty()) {
                    printLog("MainActivity: No pending notifications to process")
                    return@launch
                }

                printLog("MainActivity: Processing ${messagesToProcess.size} pending notifications")

                // With data-only messages, the message is already saved in onMessageReceived
                // No need to save again - this prevents duplicate saves and potential DB triggers
                printLog("MainActivity: Skip saving messages - already saved via onMessageReceived")

                // With data-only messages, all messages are already saved in DB
                // No need to sync as onMessageReceived handles all messages
                printLog("MainActivity: Messages already saved via onMessageReceived, no sync needed")

                // Navigate to OTT with the last message
                val lastMessageId = messagesToProcess.lastOrNull()?.messageId
                if (!lastMessageId.isNullOrEmpty()) {
                    withContext(Dispatchers.Main) {
                        navigateToOttDashboard(lastMessageId)
                    }
                }
            } catch (e: Exception) {
                printLog("MainActivity: Error processing notifications: ${e.message}")
            } finally {
                isProcessingNotifications = false
                // Clear flag in OttManager
                ottManager.setProcessingNotification(false)
            }
        }
    }

    /**
     * Handles the Up button press in the action bar.
     * Delegates navigation handling to the NavController.
     *
     * @return Boolean true if Up navigation was handled successfully, false otherwise
     */
    override fun onSupportNavigateUp(): Boolean {
        return navController.navigateUp() || super.onSupportNavigateUp()
    }

    private fun setupObservers() {
        lifecycleScope.launch {
            ottManager.ottState.collect { state ->
                when (state) {
                    is IOttManager.OttState.NeedsNotificationPermission -> {
                        requestNotificationPermissionIfNeeded()
                    }
                    // Xử lý các trạng thái khác
                    else -> {}
                }
            }
        }
    }

    private fun requestNotificationPermissionIfNeeded() {
        if (!ottManager.hasNotificationPermission()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (shouldShowRequestPermissionRationale(
                        Manifest.permission.POST_NOTIFICATIONS,
                    )
                ) {
                    // Hiển thị lý do tại sao cần quyền trước khi yêu cầu
                    showPermissionRationale {
                        requestPermissionLauncher.launch(
                            Manifest.permission.POST_NOTIFICATIONS,
                        )
                    }
                } else {
                    // Yêu cầu quyền trực tiếp
                    requestPermissionLauncher.launch(
                        Manifest.permission.POST_NOTIFICATIONS,
                    )
                }
            }
        }
    }

    private fun showPermissionRationale(onPositive: () -> Unit) {
        AlertDialog.Builder(this)
            .setTitle("Quyền thông báo")
            .setMessage("Ứng dụng cần quyền hiển thị thông báo để bạn không bỏ lỡ các thông báo quan trọng.")
            .setPositiveButton("Cho phép") { _, _ -> onPositive() }
            .setNegativeButton("Để sau", null)
            .show()
    }

    private fun showPermissionExplanationIfNeeded() {
        // Hiển thị hướng dẫn vào settings nếu user từ chối
        if (!ottManager.hasNotificationPermission()) {
            AlertDialog.Builder(this)
                .setTitle("Quyền thông báo")
                .setMessage("Bạn đã từ chối quyền thông báo. Vui lòng vào Cài đặt để bật quyền này.")
                .setPositiveButton("Cài đặt") { _, _ -> openAppSettings() }
                .setNegativeButton("Để sau", null)
                .show()
        }
    }

    private fun openAppSettings() {
        Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
            startActivity(this)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        smartCAManager.handleSignResult(requestCode, resultCode, data)
    }

    /**
     * Handle FCM notification data
     * This logic is merged from SplashActivity
     */
    private fun handleFcmNotification(ottData: String) {
        try {
            val json = JSONObject(ottData)
            val messageId = json.optString("messageId")

            if (messageId.isNotEmpty()) {
                if (!sessionManager.getSessionId().isNullOrEmpty()) {
                    // User is logged in, queue and process
                    printLog("MainActivity: User logged in, processing FCM notification: $messageId")
                    pendingOttMessages.offer(OttNotificationData(messageId, ottData))
                    processPendingNotifications()
                } else {
                    // Not logged in, save as pending
                    printLog("MainActivity: User not logged in, saving pending FCM navigation")
                    navigationEventManager.setPendingOttNavigation(messageId)
                }
            }
        } catch (e: Exception) {
            printLog("MainActivity: Error handling FCM data: ${e.message}")
        }
    }

    /**
     * Initialize app configurations
     * This replaces SplashActivity's initialization logic
     */
    private fun initializeApp(onComplete: () -> Unit) {
        viewModel.initializeApp()

        lifecycleScope.launch {
            viewModel.initState.collect { state ->
                when (state) {
                    is MainViewModel.InitState.Ready -> {
                        // Store app active state to set later when NavController is ready
                        isAppActive = state.isAppActive
                        onComplete()
                    }
                    is MainViewModel.InitState.Error -> {
                        // Handle initialization error
                        showErrorAndFinish(state.exception)
                    }
                    else -> {} // Loading
                }
            }
        }
    }

    /**
     * Handle initial navigation after app initialization
     * StartFragment is already the start destination, so no explicit navigation needed
     */
    private fun handleInitialNavigation() {
        // Use post to ensure NavController is ready
        window.decorView.post {
            try {
                // Pass app active state to StartFragment via SavedStateHandle
                navController.currentBackStackEntry?.savedStateHandle?.set(
                    "isAppActive",
                    isAppActive,
                )
                printLog("MainActivity: App initialized with isAppActive=$isAppActive")
            } catch (e: Exception) {
                printLog("MainActivity: Error setting app state: ${e.message}")
                // StartFragment will fallback to checking userProf.isActive() directly
            }
        }
    }

    /**
     * Show error dialog and finish the app
     */
    private fun showErrorAndFinish(exception: Exception) {
        AlertDialog.Builder(this)
            .setTitle("Initialization Error")
            .setMessage("Failed to initialize app: ${exception.message}")
            .setPositiveButton(getString(com.vietinbank.core_ui.R.string.common_close)) { _, _ -> finishAffinity() }
            .setCancelable(false)
            .show()
    }
}
