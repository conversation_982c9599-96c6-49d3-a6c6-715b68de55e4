package com.vietinbank.feature_new_smart_ca.smart_ca_result

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.components.dialog.DialogType
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SmartCAResultFragment : BaseFragment<SmartCAResultViewModel>() {
    override val viewModel: SmartCAResultViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
        }
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    SmartCAResultViewModel.SmartCAResultEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }

                    SmartCAResultViewModel.SmartCAResultEvent.GoToSmartCAList -> {
                        appNavigator.goToListSmartCA()
                    }

                    is SmartCAResultViewModel.SmartCAResultEvent.ShowToastMess -> {
                        Toast.makeText(requireContext(), event.toastMess, Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
        viewModel.apply {
            FlowUtils.collectFlow(this@SmartCAResultFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    if (resource.data.downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog(
                            message = resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_error_find_file),
                            type = DialogType.ERROR,
                        )
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(
                        message = resource.message,
                        type = DialogType.ERROR,
                    )
                }
            }

            FlowUtils.collectFlow(this@SmartCAResultFragment, fileUrlReady) { url ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog(
                        message = resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_cant_open_file, e.message),
                        type = DialogType.ERROR,
                    )
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            SmartCAResultScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}