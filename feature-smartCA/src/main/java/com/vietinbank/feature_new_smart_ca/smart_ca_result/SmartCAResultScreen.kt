package com.vietinbank.feature_new_smart_ca.smart_ca_result

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationStatus
import com.vietinbank.core_ui.components.FoundationTransfer
import com.vietinbank.core_ui.components.Status
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.ScreenshotUtil
import com.vietinbank.core_ui.utils.ScreenshotUtil.renderComposableToBitmap
import com.vietinbank.core_ui.utils.ScreenshotUtil.trackBounds
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen.SmartCAItemDetailView
import com.vietinbank.feature_smartca.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun SmartCAResultScreen(
    state: SmartCAResultViewModel.SmartCAResultState,
    onAction: ((SmartCAResultViewModel.SmartCAResultAction) -> Unit),
) {
    var isCapturingScreenshot by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalActivity.current
    var contentBounds by remember { mutableStateOf<Rect?>(null) }

    val shareAction: () -> Unit = {
        coroutineScope.launch {
            isCapturingScreenshot = true
            delay(100)
            context?.let {
                try {
                    val bitmap = renderComposableToBitmap(context) {
                        ContentColumn(state, onAction)
                    }
                    ScreenshotUtil.shareBitmap(context, bitmap)
                } catch (e: Exception) {
                    onAction(
                        SmartCAResultViewModel.SmartCAResultAction.ShowToastMessage(
                            com.vietinbank.core_ui.R.string.maker_transfer_account_result_error_share_picture,
                        ),
                    )
                }
            }

            isCapturingScreenshot = false
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        Column(modifier = Modifier.padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8)) {
            // App Bar
            FoundationAppBar(
                modifier = Modifier.padding(vertical = FoundationDesignSystem.Sizer.Gap.gap16),
                isLightIcon = false,
                isSingleLineAppBar = false,
                title = stringResource(com.vietinbank.core_ui.R.string.success_screen_title),
                onNavigationClick = { onAction.invoke(SmartCAResultViewModel.SmartCAResultAction.OnBackPressed) },
                navigationIcon = painterResource(com.vietinbank.core_ui.R.drawable.ic_core_ui_home),
                actions = listOf(
                    AppBarAction(
                        icon = com.vietinbank.core_ui.R.drawable.ic_common_share_24,
                        contentDescription = "ic_core_ui_add",
                        onClick = { shareAction() },
                    ),
                ),
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .imePadding()
                    .verticalScroll(rememberScrollState())
                    .clip(
                        RoundedCornerShape(
                            AppSizer.Radius.radius32,
                        ),
                    )
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap80)
                    .trackBounds { coordinates ->
                        contentBounds = coordinates.boundsInRoot()
                    },
            ) {
                ContentColumn(state, onAction)
            }
        }
        if (!isCapturingScreenshot) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0f),
                                FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0.5f),
                                FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 1f),
                            ),
                            startY = 0f,
                            endY = Float.POSITIVE_INFINITY,
                        ),
                    )
                    .padding(
                        horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                        vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                    ),
            ) {
                FoundationButton(
                    isLightButton = true,
                    text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_list),
                    onClick = { onAction.invoke(SmartCAResultViewModel.SmartCAResultAction.GoToSmartCAList) },
                    enabled = true,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
private fun ContentColumn(
    state: SmartCAResultViewModel.SmartCAResultState,
    onAction: (SmartCAResultViewModel.SmartCAResultAction) -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(
                RoundedCornerShape(
                    AppSizer.Radius.radius32,
                ),
            )
            .background(FoundationDesignSystem.Colors.backgroundBgContainer)
            .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
    ) {
        FoundationTransfer(
            contentTop = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    FoundationStatus(
                        modifier = Modifier,
                        statusCode = Status.RegisterSmartCASuccess,
                    )
                }
            },
            contentBottom = {
                state.listDisplayInfoCKS.forEachIndexed { index, item ->
                    SmartCAItemDetailView(
                        modifier = Modifier,
                        model = item,
                        isFirst = index == 0,
                        isLast = index == state.listDisplayInfoCKS.size - 1,
                        actions = { onAction(SmartCAResultViewModel.SmartCAResultAction.OnDownloadClick) },
                    )
                }
            },
        )
    }
}
