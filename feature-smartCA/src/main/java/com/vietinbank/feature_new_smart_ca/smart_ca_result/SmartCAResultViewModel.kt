package com.vietinbank.feature_new_smart_ca.smart_ca_result

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.Bundle.UTF8
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class SmartCAResultViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val smartCAUseCase: SmartCAUseCase,
    private val checkerUserCase: CheckerUserCase,
    private val dataSourceProperties: DataSourceProperties,

) : BaseViewModel() {
    private val _state = MutableStateFlow(SmartCAResultState())
    val state: StateFlow<SmartCAResultState> = _state.asStateFlow()

    private val _events = Channel<SmartCAResultEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private fun getRegisterConfirm(model: SmartCARegisterDomain?): List<DisplayModelUI>? {
        if (model == null) return null
        return listOf(
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_creator),
                value = model.esignRegister?.creator ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_dashboard_item_esignType),
                value = model.esignRegister?.esignTypeName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serviceTypeName),
                value = model.esignRegister?.serviceTypeName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_dashboard_item_roleDesc),
                value = model.esignRegister?.roleName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_name),
                value = model.esignRegister?.name ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_no),
                value = model.esignRegister?.no ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serial),
                value = model.esignRegister?.serial ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_endDate),
                value = model.esignRegister?.endDate ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_esignFrom),
                value = model.esignRegister?.esignFrom ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_nameEsign),
                value = model.esignRegister?.nameEsign ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_register_customer_bottom_sheet_idEsign),
                value = model.esignRegister?.idEsign ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.maker_payment_order_dashboard_processing_branch),
                value = model.esignRegister?.branchName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_fileName),
                value = model.toaDo?.fileName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.text_blue_02),
                clickDirect = Tags.TYPE_FILE,
            ),
        )
    }

    private fun updateListCKS(value: List<DisplayModelUI>) {
        _state.update { currentState ->
            currentState.copy(
                listDisplayInfoCKS = value,
            )
        }
    }

    private fun updateSmartCARegisterDomain(value: SmartCARegisterDomain?) {
        _state.update { currentState ->
            currentState.copy(
                smartCARegisterDomain = value,
            )
        }
    }
    fun showToast(message: String) {
        viewModelScope.launch {
            _events.send(SmartCAResultEvent.ShowToastMess(message))
        }
    }
    private fun getDownloadFileID(fileId: String?) {
        launchJob(showLoading = true) {
            val res = checkerUserCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = fileId,
                    mtID = fileId,
                    tranType = null,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }
    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: SmartCAResultAction) {
        when (action) {
            is SmartCAResultAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(SmartCAResultEvent.NavigateBack)
                }
            }

            is SmartCAResultAction.OnDownloadClick -> {
                val fileID =
                    "${state.value.smartCARegisterDomain?.esignRegister?.mtId}_${state.value.smartCARegisterDomain?.toaDo?.fileId.orEmpty()}"
                getDownloadFileID(fileID)
            }

            is SmartCAResultAction.GoToSmartCAList -> {
                viewModelScope.launch {
                    _events.send(SmartCAResultEvent.GoToSmartCAList)
                }
            }
            is SmartCAResultAction.ShowToastMessage -> {
                showToast(resourceProvider.getString(action.toastMessageRes))
            }
        }
    }

    sealed class SmartCAResultEvent {
        data object NavigateBack : SmartCAResultEvent()
        data object GoToSmartCAList : SmartCAResultEvent()
        data class ShowToastMess(val toastMess: String) : SmartCAResultEvent()
    }

    sealed class SmartCAResultAction {
        data object OnBackPressed : SmartCAResultAction()
        data object OnDownloadClick : SmartCAResultAction()
        data object GoToSmartCAList : SmartCAResultAction()
        data class ShowToastMessage(val toastMessageRes: Int) :
            SmartCAResultAction()
    }

    data class SmartCAResultState(
        val smartCARegisterDomain: SmartCARegisterDomain? = null,
        val listDisplayInfoCKS: List<DisplayModelUI> = emptyList(),

    )

    private inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        val raw = arguments.getString(key, "") ?: return null
        return try {
            val decoded = URLDecoder.decode(raw, UTF8)
            Utils.g().provideGson().fromJson(decoded, T::class.java)
        } catch (_: Exception) {
            null
        }
    }

    fun handleArguments(arguments: Bundle) {
        decodeArgumentJson<SmartCARegisterDomain>(
            arguments,
            SmartCAConstants.Bundle.TRANSACTION_BUNDLE,
        )?.let {
            updateSmartCARegisterDomain(it)
            getRegisterConfirm(it)?.let { list -> updateListCKS(list) }
        }
    }
}