package com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen

import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.checker.ApproveDomain
import com.vietinbank.core_domain.models.checker.ApproveParams
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.checker.TransactionDomain
import com.vietinbank.core_domain.models.maker.ApproverDomains
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterParams
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.Bundle.UTF8
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLDecoder
import javax.inject.Inject

@HiltViewModel
class SmartCAConfirmViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val smartCAUseCase: SmartCAUseCase,
    private val checkerUserCase: CheckerUserCase,
    private val dataSourceProperties: DataSourceProperties,
) : BaseViewModel() {
    private val _state = MutableStateFlow(SmartCAConfirmState())
    val state: StateFlow<SmartCAConfirmState> = _state.asStateFlow()

    private val _events = Channel<SmartCAConfirmEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private var isChecking = false
    fun isChecking() = isChecking
    private var mtIdSign = ""

    private val _signFlowState = MutableSharedFlow<SignCallState>()
    var signFlowState = _signFlowState.asSharedFlow()
    fun changeSignState(state: SignCallState) {
        viewModelScope.launch {
            _signFlowState.emit(state)
        }
    }

    // xac nhan phe duyet giao dich -> goi trong truong hop phe duyet gd bang ky so
    var expiredCount: Int = 300
    private val _doApproveResponse = MutableSharedFlow<ApproveDomain>()
    val doApproveResponse: SharedFlow<ApproveDomain> = _doApproveResponse.asSharedFlow()
    fun doSignApprove(
        listApprover: List<ApproverDomains>,
        currentTransaction: TransactionDomain?,
        listTransaction: List<String>,
    ) = launchJob {
        val nextApproversList = ArrayList<ApproverDomains>()
        listApprover.forEach { approver ->
            val updatedApprove = approver.copy()
            updatedApprove.isSelected = true
            nextApproversList.add(updatedApprove)
        }
        val res = checkerUserCase.doApprove(
            ApproveParams(
                username = userProf.getUserName().toString(),
                token = "",
                softOtpTransId = "",
                authenType = "VNPTSMARTCA",
                requestType = "",
                serviceType = currentTransaction?.serviceType.orEmpty(),
                tranType = currentTransaction?.tranType.orEmpty(),
                transactions = ArrayList(listTransaction),
                transactionsTp = arrayListOf(),
                nextApprovers = nextApproversList,
            ),
        )
        handleResource(res) { data ->
            data.verifySignInfo?.expiredIn?.let {
                expiredCount = it.toIntOrNull() ?: 300
            }
            _doApproveResponse.emit(data)
        }
    }
    fun registerCKSSilent(typeRegister: String? = null, onError: ((AppException) -> Unit)?) =
        launchJobSilent {
            val stateValue = state.value
            val certItem = stateValue.smartCARegisterDomain?.esignRegister
            isChecking = typeRegister == "CHECK"
            val params = SmartCARegisterParams(
                mtId = mtIdSign,
                username = userProf.getUserName(),
                userRegister = certItem?.creator,

                serviceType = certItem?.serviceType,

                roleList = stateValue.currentRole,
                fullName = certItem?.name,
                no = certItem?.no,
                esignType = "1",
                serialNo = certItem?.serial,
                startDate = certItem?.startDate,
                endDate = certItem?.endDate,
                esignFrom = certItem?.esignFrom,
                esignName = certItem?.nameEsign,
                idEsign = certItem?.idEsign,
                branch = certItem?.branch,
                role = userProf.getRoleId(),
                ou = "",
                type = typeRegister,
            )
            val res = smartCAUseCase.registerCKS(params)
            handleResourceSilent(
                resource = res,
                onSuccess = { data ->
                    data.esignRegister?.mtId?.let {
                        mtIdSign = it
                    }
                    viewModelScope.launch {
                        _events.send(SmartCAConfirmEvent.RegisterCKLEvent(data))
                    }
                },
                onError = onError,
            )
        }

    fun registerCKS(typeRegister: String? = null) = launchJob {
        isChecking = typeRegister == "CHECK"

        val stateValue = state.value
        val certItem = stateValue.smartCARegisterDomain?.esignRegister

        val params = SmartCARegisterParams(
            mtId = certItem?.mtId,
            username = userProf.getUserName(),
            userRegister = certItem?.creator,
            serviceType = certItem?.serviceType,
            roleList = stateValue.currentRole,
            fullName = certItem?.name,
            no = certItem?.no,
            esignType = "1",
            serialNo = certItem?.serial,
            startDate = certItem?.startDate,
            endDate = certItem?.endDate,
            esignFrom = certItem?.esignFrom,
            esignName = certItem?.nameEsign,
            idEsign = certItem?.idEsign,
            branch = certItem?.branch,
            role = userProf.getRoleId(),
            ou = "",
            type = typeRegister,
        )

        val res = smartCAUseCase.registerCKS(params)
        handleResource(res) { data ->
            data.esignRegister?.mtId?.let { mtIdSign = it }
            viewModelScope.launch {
                _events.send(SmartCAConfirmEvent.RegisterCKLEvent(data))
            }
        }
    }

    private fun getRegisterConfirm(model: SmartCARegisterDomain?): List<DisplayModelUI>? {
        if (model == null) return null
        return listOf(
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_creator),
                value = model.esignRegister?.creator ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_dashboard_item_esignType),
                value = model.esignRegister?.esignTypeName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serviceTypeName),
                value = model.esignRegister?.serviceTypeName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_dashboard_item_roleDesc),
                value = model.esignRegister?.roleName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_name),
                value = model.esignRegister?.name ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_no),
                value = model.esignRegister?.no ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serial),
                value = model.esignRegister?.serial ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_endDate),
                value = model.esignRegister?.endDate ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_esignFrom),
                value = model.esignRegister?.esignFrom ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_nameEsign),
                value = model.esignRegister?.nameEsign ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_register_customer_bottom_sheet_idEsign),
                value = model.esignRegister?.idEsign ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.maker_payment_order_dashboard_processing_branch),
                value = model.esignRegister?.branchName ?: "",
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_fileName_non_sign),
                value = model.toaDo?.fileName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.text_blue_02),
                clickDirect = Tags.TYPE_FILE,
            ),
        )
    }

    private fun updateListCKS(value: List<DisplayModelUI>) {
        _state.update { currentState ->
            currentState.copy(
                listDisplayInfoCKS = value,
            )
        }
    }

    private fun updateIsCheckedPolicy() {
        _state.update { currentState ->
            currentState.copy(
                isCheckedPolicy = !state.value.isCheckedPolicy,
            )
        }
    }

    private fun updateSmartCARegisterDomain(value: SmartCARegisterDomain?) {
        _state.update { currentState ->
            currentState.copy(
                smartCARegisterDomain = value,
            )
        }
    }

    private fun updateCurrentRole(value: String) {
        _state.update { currentState ->
            currentState.copy(
                currentRole = value,
            )
        }
    }

    private fun getDownloadFileID(fileId: String?) {
        launchJob(showLoading = true) {
            val res = checkerUserCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = fileId,
                    mtID = fileId,
                    tranType = null,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException && exception.requestPath == Constants.MB_SMART_CA_GET_CERT) {
            viewModelScope.launch {
                _events.send(SmartCAConfirmEvent.OnDisMissAllDialog)
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: SmartCAConfirmAction) {
        when (action) {
            is SmartCAConfirmAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(SmartCAConfirmEvent.NavigateBack)
                }
            }

            is SmartCAConfirmAction.OnDownloadClick -> {
                val fileID =
                    "${state.value.smartCARegisterDomain?.esignRegister?.mtId}_${state.value.smartCARegisterDomain?.toaDo?.fileId.orEmpty()}"
                getDownloadFileID(fileID)
            }

            is SmartCAConfirmAction.OnClickPolicy -> {
                updateIsCheckedPolicy()
            }

            is SmartCAConfirmAction.OnClickButtonContinue -> {
            }
        }
    }

    sealed class SmartCAConfirmEvent {
        data object NavigateBack : SmartCAConfirmEvent()
        data object OnClickButtonContinue : SmartCAConfirmEvent()
        data object OnDisMissAllDialog : SmartCAConfirmEvent()
        data class RegisterCKLEvent(val smartCARegisterDomain: SmartCARegisterDomain) :
            SmartCAConfirmEvent()
    }

    sealed class SmartCAConfirmAction {
        data object OnBackPressed : SmartCAConfirmAction()
        data object OnDownloadClick : SmartCAConfirmAction()
        data object OnClickPolicy : SmartCAConfirmAction()
        data object OnClickButtonContinue : SmartCAConfirmAction()
    }

    data class SmartCAConfirmState(
        val smartCARegisterDomain: SmartCARegisterDomain? = null,
        val listDisplayInfoCKS: List<DisplayModelUI> = emptyList(),
        val isCheckedPolicy: Boolean = false,
        val currentRole: String = "",
    )

    private inline fun <reified T> decodeArgumentJson(arguments: Bundle, key: String): T? {
        val raw = arguments.getString(key, "") ?: return null
        return try {
            val decoded = URLDecoder.decode(raw, UTF8)
            Utils.g().provideGson().fromJson(decoded, T::class.java)
        } catch (_: Exception) {
            null
        }
    }

    fun handleArguments(arguments: Bundle) {
        decodeArgumentJson<SmartCARegisterDomain>(
            arguments,
            SmartCAConstants.Bundle.TRANSACTION_BUNDLE,
        )?.let {
            updateSmartCARegisterDomain(it)
            getRegisterConfirm(it)?.let { list -> updateListCKS(list) }
        }
        updateCurrentRole(arguments.getString(SmartCAConstants.Bundle.DATA_TYPE, ""))
    }
}