package com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.extensions.isInstalledApp
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.FlowUtils
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.smartCA.SignCallState
import com.vietinbank.core_domain.smartca.ISignResult
import com.vietinbank.core_domain.smartca.ISmartCAManager
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.Bundle.UTF8
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.ConfirmSmartCA.PACKAGE_VNPT
import com.vietinbank.feature_smartca.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject

@AndroidEntryPoint
class SmartCAConfirmFragment : BaseFragment<SmartCAConfirmViewModel>(), ISignResult {
    override val viewModel: SmartCAConfirmViewModel by viewModels()
    override val useCompose: Boolean = true
    private var countDownTimer: CountDownTimer? = null

    @Inject
    override lateinit var appNavigator: IAppNavigator

    @Inject
    lateinit var smartCAManager: ISmartCAManager

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        arguments?.let { bundle ->
            viewModel.handleArguments(bundle)
            parentFragment?.arguments = null
            arguments = null
        }
        smartCAManager.setSignListener(this)
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    SmartCAConfirmViewModel.SmartCAConfirmEvent.NavigateBack -> {
                        appNavigator.popBackStack()
                    }

                    SmartCAConfirmViewModel.SmartCAConfirmEvent.OnClickButtonContinue -> {
                        showConfirmDialog(
                            message = resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_go_to_SDK),
                            negativeButtonText = resources.getString(com.vietinbank.core_ui.R.string.common_close),
                            positiveButtonText = resources.getString(com.vietinbank.core_ui.R.string.common_agree),
                            positiveAction = {
                                viewModel.registerCKS("SIGN")
                            },
                        )
                    }

                    is SmartCAConfirmViewModel.SmartCAConfirmEvent.RegisterCKLEvent -> {
                        showNoticeDialog(
                            title = resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_process),
                            message = resources.getString(
                                com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_process_content,
                            ),
                            positiveAction = {
                            },
                        )
                        viewModel.apply {
                            if (isChecking()) {
                                // dieu huong sang man ket qua
                                countDownTimer?.cancel()
                                countDownTimer = null
                                val itemString =
                                    Utils.g().provideGson().toJson(event.smartCARegisterDomain)
                                val encodedItem = URLEncoder.encode(itemString, UTF8)
                                appNavigator.goToResultSmartCA(
                                    transactionBundle = encodedItem,
                                )
                            } else if (!activity.isInstalledApp(PACKAGE_VNPT)) {
                                // kiem tra device da co app vnpt smart ca hay chua
                                // chua co app
                                showConfirmDialog(
                                    resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_install_SDK_VNPT_VALIDATE),
                                    resources.getString(com.vietinbank.core_ui.R.string.common_agree),
                                    resources.getString(com.vietinbank.core_ui.R.string.common_close),
                                    positiveAction = {
//                                        changeSignState(SignCallState.Loading)
                                        dismissNoticeDialog()
                                    },
                                )
                            } else {
                                // điều hướng sang vnpt để ký số
                                activity?.let {
                                    changeSignState(SignCallState.Loading)
                                    smartCAManager.signTransaction(
                                        it,
                                        event.smartCARegisterDomain.androidKey ?: "",
                                        event.smartCARegisterDomain.data?.addInfoDetail?.firstOrNull()?.tran_code
                                            ?: "",
                                    )
                                }
                            }
                        }
                    }

                    SmartCAConfirmViewModel.SmartCAConfirmEvent.OnDisMissAllDialog -> {
                        dismissNoticeDialog()
                    }
                }
            }
        }

        viewModel.apply {
            FlowUtils.collectFlow(this@SmartCAConfirmFragment, getDownloadFileID) { resource ->
                if (resource != null && resource is Resource.Success) {
                    // Lấy downloadFileId từ kết quả
                    if (resource.data.downloadFileId.isNullOrEmpty()) {
                        showNoticeDialog(resources.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_error_find_file))
                    }
                } else if (resource is Resource.Error) {
                    showNoticeDialog(resource.message)
                }
            }
            FlowUtils.collectFlow(this@SmartCAConfirmFragment, fileUrlReady) { url ->
                try {
                    // Mở URL trong trình duyệt
                    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
                    startActivity(intent)
                } catch (e: Exception) {
                    showNoticeDialog(
                        resources.getString(
                            com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_cant_open_file,
                            e.message,
                        ),
                    )
                }
            }
            FlowUtils.collectFlow(this@SmartCAConfirmFragment, signFlowState) { state ->
                when (state) {
                    is SignCallState.Error -> {
                        forceAllHideLoading()
                        countDownTimer?.cancel()
                        countDownTimer = null
                        state.exception?.let {
                            onError(it)
                        } ?: run {
                            showNoticeDialog(state.message ?: "") { appNavigator.popBackStack() }
                        }
                    }

                    // trang thai ky thanh cong + case ky tren device khac
                    is SignCallState.Success, is SignCallState.Loading -> {
                        showLoading()
                        if (countDownTimer == null) {
                            countDownTimer = object : CountDownTimer(300 * 1000L, 1000) {
                                override fun onTick(timeLeft: Long) {
                                    if ((timeLeft / 1000) % 5 == 0L) {
                                        // 5s call lai 1 lan nha
                                        viewModel.registerCKSSilent("CHECK") { appError ->
                                            /**
                                             * 1 thành công, ký xong -> case thanh cong -> khong can xu ly
                                             * 0 lỗi
                                             * 3 pending sign chờ ký số, check tiếp
                                             * */
                                            if (appError is AppException.ApiException && appError.code == "3") {
                                                // tiep tuc goi ky so theo count time -> khong can xu ly gi
                                            } else {
                                                // show thong bao loi -> dung luong recall api check
                                                changeSignState(SignCallState.Error(exception = appError))
                                            }
                                        }
                                    }
                                }

                                override fun onFinish() {
                                    changeSignState(
                                        SignCallState.Error(
                                            message = resources.getString(
                                                com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_call_SDK_TIME_OUT,
                                            ),
                                        ),
                                    )
                                }
                            }.start()
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            SmartCAConfirmScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        countDownTimer = null
    }

    override fun onResult(statusCode: Int, statusMessage: String) {
        if (statusCode == 0) {
            viewModel.changeSignState(SignCallState.Success)
        } else if ((statusCode != 3 && statusCode != -1)) {
            viewModel.changeSignState(SignCallState.Error(message = statusMessage))
        } else {
            viewModel.changeSignState(SignCallState.Loading)
        }
    }
}