package com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationSelector
import com.vietinbank.core_ui.components.SelectorType
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen.SmartCAConfirmViewModel.SmartCAConfirmAction
import com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen.SmartCAItemDetailView
import com.vietinbank.feature_smartca.R

@Composable
fun SmartCAConfirmScreen(
    state: SmartCAConfirmViewModel.SmartCAConfirmState,
    onAction: ((SmartCAConfirmViewModel.SmartCAConfirmAction) -> Unit),
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        Column(modifier = Modifier.padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8)) {
            // App Bar
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_title),
                onNavigationClick = { onAction.invoke(SmartCAConfirmViewModel.SmartCAConfirmAction.OnBackPressed) },
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .imePadding()
                    .verticalScroll(rememberScrollState())
                    .clip(
                        RoundedCornerShape(
                            AppSizer.Radius.radius32,
                        ),
                    )
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap16)
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap80), // Chiều cao của button + padding
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(
                            RoundedCornerShape(
                                AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FoundationDesignSystem.Colors.backgroundBgContainer),
                ) {
                    FoundationText(
                        text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_info_item_cks),
                        style = FoundationDesignSystem.Typography.headingH3,
                        color = FoundationDesignSystem.Colors.characterHighlighted,
                        modifier = Modifier
                            .padding(vertical = FoundationDesignSystem.Sizer.Gap.gap24)
                            .align(Alignment.CenterHorizontally),
                    )
                    HorizontalDivider(
                        color = FoundationDesignSystem.Colors.divider,
                        thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        modifier = Modifier.padding(bottom = FoundationDesignSystem.Sizer.Gap.gap8),
                    )
                    state.listDisplayInfoCKS.forEachIndexed { index, item ->
                        SmartCAItemDetailView(
                            modifier = Modifier.padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                            model = item,
                            isFirst = index == 0,
                            isLast = index == state.listDisplayInfoCKS.size - 1,
                            actions = { onAction.invoke(SmartCAConfirmViewModel.SmartCAConfirmAction.OnDownloadClick) },
                        )
                    }
                    HorizontalDivider(
                        color = FoundationDesignSystem.Colors.divider,
                        thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        modifier = Modifier.padding(bottom = FoundationDesignSystem.Sizer.Gap.gap8),
                    )
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
                    Row(
                        modifier = Modifier
                            .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24)
                            .safeClickable {
                                onAction.invoke(SmartCAConfirmAction.OnClickPolicy)
                            },
                    ) {
                        FoundationSelector(
                            boxType = SelectorType.Radio,
                            isSelected = state.isCheckedPolicy,
                            modifier = Modifier.size(FoundationDesignSystem.Sizer.Icon.icon24),
                            isClickable = false,
                        )
                        Spacer(modifier = Modifier.width(FoundationDesignSystem.Sizer.Gap.gap8))
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_confirm_policy),
                            style = FoundationDesignSystem.Typography.bodyB2,
                            color = FoundationDesignSystem.Colors.characterPrimary,
                        )
                    }
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                }
            }
        }
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0.5f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 1f),
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY,
                    ),
                )
                .padding(
                    horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                    vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                ),
        ) {
            FoundationButton(
                isLightButton = true,
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = { onAction.invoke(SmartCAConfirmAction.OnClickButtonContinue) },
                enabled = state.isCheckedPolicy,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}
