package com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen

import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.constants.Constants
import com.vietinbank.core_common.constants.DataSourceProperties
import com.vietinbank.core_common.constants.Tags
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Resource
import com.vietinbank.core_data.models.response.DisplayModelUI
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDDomain
import com.vietinbank.core_domain.models.checker.GetDownloadFileIDParams
import com.vietinbank.core_domain.models.smartCA.SmartCADataDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailDomain
import com.vietinbank.core_domain.models.smartCA.SmartCADetailParams
import com.vietinbank.core_domain.usecase.checker.CheckerUserCase
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.R
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.CERT_TYPE_KEY
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_PENDING
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_SUCCESS
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.TYPE_CLOSE_CKS
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.TYPE_DELETE_CKS
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SmartCADetailViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val smartCAUseCase: SmartCAUseCase,
    private val checkerUserCase: CheckerUserCase,
    private val dataSourceProperties: DataSourceProperties,

) : BaseViewModel() {
    private val _state = MutableStateFlow(SmartCADetailState())
    val state: StateFlow<SmartCADetailState> = _state.asStateFlow()

    private val _events = Channel<SmartCADetailEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private val _getDownloadFileID = MutableSharedFlow<Resource<GetDownloadFileIDDomain>?>()
    val getDownloadFileID = _getDownloadFileID.asSharedFlow()

    private val _fileUrlReady = MutableSharedFlow<String>()
    val fileUrlReady = _fileUrlReady.asSharedFlow()

    private var argumentsHandled = false

    private fun getDetailCKS() = launchJob {
        val params = SmartCADetailParams(
            "SCA",
            state.value.mtID,
            userProf.getUserName(),
            "",
            userProf.getRoleId(),
        )
        val res = smartCAUseCase.getDetailCKS(params)
        handleResource(res) { data ->
            updateSmartCADataDomain(data.data)
            getDisplayInfoCKS(data.data)?.let { updateListCKS(it) }
            updateStateButton(data)
        }
    }

    fun updateCKS() = launchJob {
        val action = when (state.value.smartCADataDomain?.statusCode) {
            STATUS_CODE_SUCCESS, STATUS_CODE_PENDING -> {
                TYPE_CLOSE_CKS
            }

            else -> {
                TYPE_DELETE_CKS
            }
        }
        val params = SmartCADetailParams(
            CERT_TYPE_KEY,
            state.value.mtID,
            userProf.getUserName(),
            action,
            userProf.getRoleId(),
        )
        val res = smartCAUseCase.updateCKS(params)
        handleResource(res) { data ->
            val resId = when (state.value.smartCADataDomain?.statusCode) {
                STATUS_CODE_SUCCESS, STATUS_CODE_PENDING ->
                    R.string.feature_smart_ca_dashboard_info_close_CKS_SUCCESS
                else ->
                    R.string.feature_smart_ca_dashboard_info_delete_CKS_SUCCESS
            }

            val string = resourceProvider.getString(resId, state.value.smartCADataDomain?.serial.orEmpty())
            _events.send(SmartCADetailEvent.OnUpdateCKSSuccess(string))
        }
    }

    private fun updateStateButton(data: SmartCADetailDomain) {
        val (textButton, iconResButton) = when (data.data?.statusCode) {
            STATUS_CODE_SUCCESS ->
                resourceProvider.getString(R.string.feature_smart_ca_dashboard_info_button_close_CKS) to R.drawable.ic_trash
            STATUS_CODE_PENDING -> {
                resourceProvider.getString(R.string.common_back) to 0
            }
            else ->
                resourceProvider.getString(R.string.feature_smart_ca_dashboard_info_button_delete_CKS) to R.drawable.ic_trash
        }

        updateTextButton(textButton)
        updateIconResButton(iconResButton)
    }

    private fun getDownloadFileID(fileId: String?) {
        launchJob(showLoading = true) {
            val res = checkerUserCase.getDownloadFileID(
                GetDownloadFileIDParams(
                    username = userProf.getUserName(),
                    fileId = fileId,
                    mtID = fileId,
                    tranType = null,
                ),
            )
            handleResource(res) { data ->
                _getDownloadFileID.emit(Resource.Success(data))

                data.downloadFileId?.let { fileId ->
                    if (fileId.isNotEmpty()) {
                        val baseUrl = dataSourceProperties.getUrl()
                        val downloadUrl = baseUrl + Constants.MB_DOWNLOAD_FILE.replace(
                            "{encryptStr}",
                            fileId,
                        )
                        _fileUrlReady.emit(downloadUrl)
                    }
                }
            }
        }
    }

    private fun updateListCKS(value: List<DisplayModelUI>) {
        _state.update { currentState ->
            currentState.copy(
                listDisplayInfoCKS = value,
            )
        }
    }

    private fun getDisplayInfoCKS(model: SmartCADataDomain? = null): List<DisplayModelUI>? {
        if (model == null) return null
        val lstDisplay = mutableListOf(
            DisplayModelUI(title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_mtid), value = model.mtId ?: ""),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_statusName),
                value = model.statusName ?: "",
                valueColor = when (model.statusCode) {
                    STATUS_CODE_SUCCESS -> {
                        resourceProvider.getComposeColor(R.color.foundation_state_success)
                    }

                    STATUS_CODE_PENDING -> {
                        resourceProvider.getComposeColor(R.color.foundation_state_warning)
                    }

                    else -> {
                        resourceProvider.getComposeColor(R.color.foundation_state_error)
                    }
                },
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_creator),
                value = model.creator ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_esignTypeName),
                value = model.esignTypeName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serviceTypeName),
                value = model.serviceTypeName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_roleName),
                value = model.roleName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_name),
                value = model.name ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_no),
                value = model.no ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_serial),
                value = model.serial ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_endDate),
                value = model.endDate ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_esignFrom),
                value = model.esignFrom ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_nameEsign),
                value = model.nameEsign ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_idEsign),
                value = model.idEsign ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
            DisplayModelUI(
                title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_branchName),
                value = model.branchName ?: "",
                valueColor = resourceProvider.getComposeColor(R.color.foundation_character_primary),
            ),
        )
        if (!TextUtils.isEmpty(model.files?.firstOrNull()?.fileName)) {
            lstDisplay.add(
                DisplayModelUI(
                    title = resourceProvider.getString(R.string.feature_smart_ca_detail_item_fileName),
                    value = model.files?.firstOrNull()?.fileName,
                    valueColor = resourceProvider.getComposeColor(
                        R.color.text_blue_02,
                    ),
                    clickDirect = Tags.TYPE_FILE,
                ),
            )
        }
        return lstDisplay
    }

    private fun updateMTid(value: String) {
        _state.update { currentState ->
            currentState.copy(
                mtID = value,
            )
        }
    }

    private fun updateSmartCADataDomain(value: SmartCADataDomain?) {
        _state.update { currentState ->
            currentState.copy(
                smartCADataDomain = value,
            )
        }
    }

    private fun updateTextButton(value: String) {
        _state.update { currentState ->
            currentState.copy(
                textButton = value,
            )
        }
    }

    private fun updateIconResButton(value: Int) {
        _state.update { currentState ->
            currentState.copy(
                iconResButton = value,
            )
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: SmartCADetailAction) {
        when (action) {
            is SmartCADetailAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(SmartCADetailEvent.NavigateBack)
                }
            }

            is SmartCADetailAction.OnDownloadClick -> {
                val fileID =
                    "${state.value.smartCADataDomain?.mtId}_${state.value.smartCADataDomain?.files?.firstOrNull()?.id}"
                getDownloadFileID(fileID)
            }

            is SmartCADetailAction.OnClickConfirmButton -> {
                viewModelScope.launch {
                    when (state.value.smartCADataDomain?.statusCode) {
                        STATUS_CODE_PENDING -> {
                            _events.send(SmartCADetailEvent.NavigateBack)
                        }
                        STATUS_CODE_SUCCESS -> {
                            val resId = R.string.feature_smart_ca_dashboard_info_confirm_close_CKS
                            val string = resourceProvider.getString(
                                resId,
                                state.value.smartCADataDomain?.serial.orEmpty(),
                            )
                            _events.send(SmartCADetailEvent.OnClickConfirmButton(string))
                        }
                        else -> {
                            val resId = R.string.feature_smart_ca_dashboard_info_confirm_delete_CKS
                            val string = resourceProvider.getString(
                                resId,
                                state.value.smartCADataDomain?.serial.orEmpty(),
                            )
                            _events.send(SmartCADetailEvent.OnClickConfirmButton(string))
                        }
                    }
                }
            }
        }
    }

    sealed class SmartCADetailEvent {
        data object NavigateBack : SmartCADetailEvent()
        data class OnClickConfirmButton(val text: String) : SmartCADetailEvent()
        data class OnUpdateCKSSuccess(val text: String) : SmartCADetailEvent()
    }

    sealed class SmartCADetailAction {
        data object OnBackPressed : SmartCADetailAction()
        data object OnDownloadClick : SmartCADetailAction()
        data object OnClickConfirmButton : SmartCADetailAction()
    }

    data class SmartCADetailState(
        val smartCADataDomain: SmartCADataDomain? = null,
        val listDisplayInfoCKS: List<DisplayModelUI> = emptyList(),
        val mtID: String = "",
        val textButton: String = "",
        val iconResButton: Int = 0,
    )

    fun handleArguments(arguments: Bundle) {
        if (argumentsHandled) return
        argumentsHandled = true
        val mtID = arguments.getString(SmartCAConstants.Bundle.KEY_MT_ID, "")
        if (mtID.isNotEmpty()) {
            updateMTid(mtID)
            getDetailCKS()
        }
    }
}