package com.vietinbank.feature_new_smart_ca.smart_ca_register

import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.exception.AppException
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_domain.models.smartCA.BranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCABranchDomain
import com.vietinbank.core_domain.models.smartCA.SmartCACertVNPTParams
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignRegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAGetParams
import com.vietinbank.core_domain.models.smartCA.SmartCAParams
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterDomain
import com.vietinbank.core_domain.models.smartCA.SmartCARegisterParams
import com.vietinbank.core_domain.models.smartCA.SmartCARoleDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAServiceDomain
import com.vietinbank.core_domain.models.smartCA.SmartCAUserDomain
import com.vietinbank.core_domain.usecase.smartca.SmartCAUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_new_smart_ca.bottomSheet.BranchBottomSheetUiState
import com.vietinbank.feature_new_smart_ca.bottomSheet.CertBottomSheetUiState
import com.vietinbank.feature_new_smart_ca.bottomSheet.RadioBottomSheetUiState
import com.vietinbank.feature_new_smart_ca.bottomSheet.ServiceTypeBottomSheetBottomSheetUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SmartCARegisterViewModel @Inject constructor(
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
    private val smartCAUseCase: SmartCAUseCase,

) : BaseViewModel() {
    private val _state = MutableStateFlow(SmartCARegisterState())
    val state: StateFlow<SmartCARegisterState> = _state.asStateFlow()

    private val _events = Channel<SmartCARegisterEvent>(Channel.BUFFERED)
    val events = _events.receiveAsFlow()

    private var isChecking = false
    fun isChecking() = isChecking
    private var mtIdSign = ""

    init {
        if (!isKeepCallInit()) {
            getParamsCKS()
            getBranch()
            setKeepCallInit()
        }
    }

    private fun registerCKS(typeRegister: String? = null) = launchJob {
        isChecking = typeRegister == "CHECK"

        val stateValue = state.value
        val certItem = stateValue.currentCertItem
        val userSelected = stateValue.userSelected

        val params = SmartCARegisterParams(
            mtId = mtIdSign,
            username = userProf.getUserName(),
            userRegister = userSelected?.username,
            serviceType = stateValue.currentServiceDomain?.key,
            roleList = stateValue.currentRole?.key.orEmpty(),
            fullName = userSelected?.fullName,
            no = certItem?.no ?: userSelected?.idNumber,
            esignType = "1",
            serialNo = certItem?.serial,
            startDate = certItem?.startDate,
            endDate = certItem?.endDate,
            esignFrom = certItem?.esignFrom,
            esignName = certItem?.nameEsign,
            idEsign = certItem?.idEsign,
            branch = stateValue.currentBranch?.branchId,
            role = userProf.getRoleId(),
            ou = "",
            type = typeRegister,
        )

        val res = smartCAUseCase.registerCKS(params)
        handleResource(res) { data ->
            data.esignRegister?.mtId?.let { mtIdSign = it }

            viewModelScope.launch {
                _events.send(SmartCARegisterEvent.ValidatePaymentOrderState(data, stateValue.currentRole?.key.orEmpty()))
            }
        }
    }

    private fun getCertVNPT(isShowBottomSheet: Boolean = false) = launchJob {
        if (state.value.esignRegisterList?.isNotEmpty() == true) {
            canShowCertBottomSheet(true)
            return@launchJob
        }
        val params = SmartCACertVNPTParams(
            state.value.userSelected?.idNumber ?: "",
            userProf.getRoleId(),
            userProf.getUserName(),
        )
        val res = smartCAUseCase.getCertVNPT(params)
        handleResource(res) { data ->
            updateCertBottomSheet(data.esignRegisterList?.toMutableList() ?: mutableListOf())
            canShowCertBottomSheet(isShowBottomSheet)
        }
    }

    private fun getParamsCKS() = launchJob {
        val params = SmartCAGetParams(userProf.getRoleId(), "PARAMS", userProf.getUserName())
        val res = smartCAUseCase.getParamCKS(params)
        handleResource(res) { data ->
            updateUserList(data.userList?.toMutableList() ?: mutableListOf())
            updateServiceTypeList(data.serviceTypeListFull?.toMutableList() ?: mutableListOf())
            val providerList = data.providerList?.toMutableList() ?: mutableListOf()
            updateProviderList(providerList)
            // default provider
            val vnptProvider = providerList.find { it.key.equals("vnpt", ignoreCase = true) }
            vnptProvider?.let {
                updateCurrentProvider(vnptProvider)
            }
            // default user
            data.userList?.firstOrNull { user ->
                !TextUtils.isEmpty(userProf.getUserName()) && user.username == userProf.getUserName()
            }?.let {
                updateUserSelected(
                    it,
                )
                getCertVNPT()
            }
        }
    }

    private fun getBranch() = launchJob {
        val params = SmartCAParams(userProf.getUserName(), userProf.getRoleId())
        val res = smartCAUseCase.getBranchCKS(params)
        handleResource(res) { data ->
            updateSmartCABranchDomain(data)
        }
    }

    private fun updateUserSelected(value: SmartCAUserDomain) {
        _state.update { currentState ->
            currentState.copy(
                userSelected = value,
            )
        }
    }

    private fun updateUserList(value: MutableList<SmartCAUserDomain>) {
        _state.update { currentState ->
            currentState.copy(
                userList = value,
            )
        }
    }

    private fun updateServiceTypeList(value: MutableList<SmartCAServiceDomain>) {
        _state.update { currentState ->
            val item =
                currentState.serviceTypeBottomSheetUiState.copy(items = value)
            currentState.copy(
                serviceTypeBottomSheetUiState = item,
            )
        }
    }

    private fun updateRoleList(value: MutableList<SmartCARoleDomain>) {
        _state.update { currentState ->
            val item =
                currentState.roleBottomSheetUiState.copy(items = value)
            currentState.copy(
                roleBottomSheetUiState = item,
            )
        }
    }

    private fun updateProviderList(value: MutableList<SmartCARoleDomain>) {
        _state.update { currentState ->
            val item =
                currentState.providerBottomSheetUiState.copy(items = value)
            currentState.copy(
                providerBottomSheetUiState = item,
            )
        }
    }

    private fun updateBranchBottomSheet(value: MutableList<BranchDomain>) {
        _state.update { currentState ->
            val item =
                currentState.branchBottomSheetUiState.copy(items = value)
            currentState.copy(
                branchBottomSheetUiState = item,
            )
        }
    }

    private fun updateCertBottomSheet(value: MutableList<SmartCAEsignRegisterDomain>) {
        _state.update { currentState ->
            currentState.copy(
                esignRegisterList = value,
            )
        }
        _state.update { currentState ->
            val item =
                currentState.certBottomSheetUiState.copy(items = value)
            currentState.copy(
                certBottomSheetUiState = item,
            )
        }
    }

    private fun updateSmartCABranchDomain(value: SmartCABranchDomain) {
        _state.update { currentState ->
            currentState.copy(
                smartCABranchDomain = value,
            )
        }
    }

    private fun updateCurrentServiceDomain(value: SmartCAServiceDomain) {
        _state.update { currentState ->
            currentState.copy(
                currentServiceDomain = value,
            )
        }
    }

    private fun updateCurrentRole(value: SmartCARoleDomain?) {
        _state.update { currentState ->
            currentState.copy(
                currentRole = value,
            )
        }
    }

    private fun updateCurrentProvider(value: SmartCARoleDomain) {
        _state.update { currentState ->
            currentState.copy(
                currentProvider = value,
            )
        }
    }

    private fun updateCurrentBranch(value: BranchDomain) {
        _state.update { currentState ->
            currentState.copy(
                currentBranch = value,
            )
        }
    }

    private fun updateCurrentCertItem(value: SmartCAEsignRegisterDomain?) {
        _state.update { currentState ->
            currentState.copy(
                currentCertItem = value,
            )
        }
    }

    private fun canShowServiceTypeBottomSheet(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowServiceTypeBottomSheet = show,
            )
        }
    }

    private fun canShowRoleBottomSheetUiState(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowRoleBottomSheetUiState = show,
            )
        }
    }

    private fun canShowProviderBottomSheet(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowProviderBottomSheet = show,
            )
        }
    }

    private fun canShowBranchBottomSheet(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowBranchBottomSheet = show,
            )
        }
    }

    private fun canShowCertBottomSheet(show: Boolean) {
        _state.update { currentState ->
            currentState.copy(
                canShowCertBottomSheet = show,
            )
        }
    }

    private fun validateField(): String? {
        val s = state.value
        return when {
            s.currentServiceDomain == null -> resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_validate_currentServiceDomain)
            s.currentRole == null -> resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_validate_currentRole)
            s.currentProvider == null -> resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_validate_currentProvider)
            s.currentBranch == null -> resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_validate_currentBranch)
            state.value.currentCertItem == null -> resourceProvider.getString(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_validate_choose_CKS)
            else -> null
        }
    }

    private fun handleErrorValidate(error: String) {
        viewModelScope.launch {
            _events.send(SmartCARegisterEvent.ShowErrorValidateField(error))
        }
    }

    override fun onDisplayErrorMessage(exception: AppException) {
        if (exception is AppException.ApiException) {
            when (exception.requestPath) {
            }
        }
        super.onDisplayErrorMessage(exception)
    }

    fun onAction(action: SmartCARegisterAction) {
        when (action) {
            is SmartCARegisterAction.OnBackPressed -> {
                viewModelScope.launch {
                    _events.send(SmartCARegisterEvent.NavigateBack)
                }
            }

            is SmartCARegisterAction.CanShowRoleBottomSheetUiState -> {
                canShowRoleBottomSheetUiState(action.isShow)
            }

            is SmartCARegisterAction.CanShowProviderBottomSheet -> {
                canShowProviderBottomSheet(action.isShow)
            }

            is SmartCARegisterAction.CanShowServiceTypeBottomSheet -> {
                canShowServiceTypeBottomSheet(action.isShow)
            }

            is SmartCARegisterAction.OnChooseServiceType -> {
                updateCurrentServiceDomain(action.serviceItem)
                updateCurrentRole(SmartCARoleDomain("", ""))
                updateRoleList(action.serviceItem.roleList?.toMutableList() ?: mutableListOf())
                updateBranchBottomSheet(
                    when (state.value.currentServiceDomain?.key) {
                        "2" -> state.value.smartCABranchDomain?.listLoans
                        else -> state.value.smartCABranchDomain?.listSavings
                    }.orEmpty().toMutableList(),
                )
                canShowServiceTypeBottomSheet(false)
            }

            is SmartCARegisterAction.OnChooseRole -> {
                updateCurrentRole(action.role)
                canShowRoleBottomSheetUiState(false)
            }

            is SmartCARegisterAction.OnChooseSProvider -> {
                updateCurrentProvider(action.provider)
                canShowProviderBottomSheet(false)
            }

            is SmartCARegisterAction.CanShowBranchBottomSheet -> {
                canShowBranchBottomSheet(action.isShow)
            }

            is SmartCARegisterAction.OnChooseBranch -> {
                updateCurrentBranch(action.branchItem)
                canShowBranchBottomSheet(false)
            }

            is SmartCARegisterAction.OnClickButtonContinue -> {
                val errorObject = validateField()
                if (errorObject != null) {
                    handleErrorValidate(errorObject)
                    return
                }
                registerCKS("VALIDATE")
            }

            is SmartCARegisterAction.CanShowCertBottomSheet -> {
                if (state.value.currentCertItem == null) {
                    getCertVNPT(true)
                } else {
                    canShowCertBottomSheet(action.isShow)
                }
            }

            is SmartCARegisterAction.OnChooseCert -> {
                val updatedList = state.value.esignRegisterList?.map { item ->
                    if (item.serial == action.certItem.serial) {
                        item.copy(isSelected = true)
                    } else {
                        item.copy(isSelected = false)
                    }
                }?.toMutableList() ?: mutableListOf()
                updateCertBottomSheet(updatedList)
            }

            is SmartCARegisterAction.OnConfirmCert -> {
                val selectedItem = state.value.esignRegisterList?.find { it.isSelected }
                updateCurrentCertItem(selectedItem)
                canShowCertBottomSheet(false)
            }
        }
    }

    sealed class SmartCARegisterEvent {
        data object NavigateBack : SmartCARegisterEvent()
        data class ShowErrorValidateField(val errorMess: String) : SmartCARegisterEvent()
        data class ValidatePaymentOrderState(val smartCARegisterDomain: SmartCARegisterDomain, val dataType: String) :
            SmartCARegisterEvent()
    }

    sealed class SmartCARegisterAction {
        data object OnBackPressed : SmartCARegisterAction()
        data class CanShowServiceTypeBottomSheet(val isShow: Boolean) :
            SmartCARegisterAction()

        data class OnChooseServiceType(val serviceItem: SmartCAServiceDomain) :
            SmartCARegisterAction()

        data class CanShowRoleBottomSheetUiState(val isShow: Boolean) :
            SmartCARegisterAction()

        data class OnChooseRole(val role: SmartCARoleDomain) :
            SmartCARegisterAction()

        data class CanShowProviderBottomSheet(val isShow: Boolean) :
            SmartCARegisterAction()

        data class OnChooseSProvider(val provider: SmartCARoleDomain) :
            SmartCARegisterAction()

        data class CanShowBranchBottomSheet(val isShow: Boolean) :
            SmartCARegisterAction()

        data class OnChooseBranch(val branchItem: BranchDomain) :
            SmartCARegisterAction()

        data class CanShowCertBottomSheet(val isShow: Boolean) :
            SmartCARegisterAction()

        data class OnChooseCert(val certItem: SmartCAEsignRegisterDomain) :
            SmartCARegisterAction()

        data object OnConfirmCert : SmartCARegisterAction()

        data object OnClickButtonContinue : SmartCARegisterAction()
    }

    data class SmartCARegisterState(
        val userSelected: SmartCAUserDomain? = null,
        val userList: MutableList<SmartCAUserDomain>? = mutableListOf(),
        val currentServiceDomain: SmartCAServiceDomain? = null,
        val currentRole: SmartCARoleDomain? = null,
        val currentProvider: SmartCARoleDomain? = null,
        val currentBranch: BranchDomain? = null,
        val smartCABranchDomain: SmartCABranchDomain? = null,
        val esignRegisterList: MutableList<SmartCAEsignRegisterDomain>? = mutableListOf(),
        val currentCertItem: SmartCAEsignRegisterDomain? = null,

        var canShowServiceTypeBottomSheet: Boolean = false,
        var canShowRoleBottomSheetUiState: Boolean = false,
        var canShowProviderBottomSheet: Boolean = false,
        var canShowBranchBottomSheet: Boolean = false,
        var canShowCertBottomSheet: Boolean = false,

        val serviceTypeBottomSheetUiState: ServiceTypeBottomSheetBottomSheetUiState = ServiceTypeBottomSheetBottomSheetUiState(),
        val roleBottomSheetUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        val providerBottomSheetUiState: RadioBottomSheetUiState = RadioBottomSheetUiState(),
        val branchBottomSheetUiState: BranchBottomSheetUiState = BranchBottomSheetUiState(),
        val certBottomSheetUiState: CertBottomSheetUiState = CertBottomSheetUiState(),
    )
}