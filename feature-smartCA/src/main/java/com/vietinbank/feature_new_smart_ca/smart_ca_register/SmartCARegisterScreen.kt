package com.vietinbank.feature_new_smart_ca.smart_ca_register

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationButton
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.text.foundation.FoundationText
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_new_smart_ca.bottomSheet.BranchBottomSheet
import com.vietinbank.feature_new_smart_ca.bottomSheet.CertBottomSheet
import com.vietinbank.feature_new_smart_ca.bottomSheet.RegisterSmartCABottomSheet
import com.vietinbank.feature_new_smart_ca.bottomSheet.ServiceTypeBottomSheet
import com.vietinbank.feature_smartca.R
import com.vietinbank.core_ui.theme.FoundationDesignSystem as FDS

@Composable
fun SmartCARegisterScreen(
    state: SmartCARegisterViewModel.SmartCARegisterState,
    onAction: ((SmartCARegisterViewModel.SmartCARegisterAction) -> Unit),
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding(),
    ) {
        Column {
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_title),
                onNavigationClick = { onAction.invoke(SmartCARegisterViewModel.SmartCARegisterAction.OnBackPressed) },
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .imePadding()
                    .verticalScroll(rememberScrollState())
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap16)
                    .padding(bottom = FoundationDesignSystem.Sizer.Gap.gap80), // Chiều cao của button + padding
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(
                            RoundedCornerShape(
                                AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap8),
                ) {
                    state.userSelected?.let { userSelected ->
                        FoundationText(
                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info),
                            style = FoundationDesignSystem.Typography.headingH3,
                            color = FoundationDesignSystem.Colors.characterHighlighted,
                            modifier = Modifier
                                .padding(
                                    vertical = FoundationDesignSystem.Sizer.Gap.gap24,
                                )
                                .align(Alignment.CenterHorizontally),
                        )

                        HorizontalDivider(
                            color = FoundationDesignSystem.Colors.divider,
                            thickness = FoundationDesignSystem.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
                        FoundationInfoHorizontal(
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_username),
                            value = userSelected.username,
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                        FoundationInfoHorizontal(
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_idNumber),
                            value = userSelected.idNumber,
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),

                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                        FoundationInfoHorizontal(
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_fullName),
                            value = userSelected.fullName,
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),

                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                        FoundationInfoHorizontal(
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_titleName),
                            value = userSelected.titleName,
                            titleStyle = FoundationDesignSystem.Typography.bodyB2,
                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                    }
                }
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap4))
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = FDS.Sizer.Gap.gap8)
                        .clip(RoundedCornerShape(AppSizer.Radius.radius32))
                        .background(FDS.Colors.backgroundBgContainer),
                ) {
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
                    FoundationText(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24),

                        text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_service),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .safeClickable { },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            modifier = Modifier.weight(1f),
                            text = state.currentServiceDomain?.value ?: "",
                            color = FDS.Colors.characterHighlighted,
                            style = FDS.Typography.bodyB1,
                        )
                        IconButton(
                            onClick = {
                                onAction.invoke(
                                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowServiceTypeBottomSheet(
                                        true,
                                    ),
                                )
                            },
                        ) {
                            Icon(
                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                contentDescription = "ic_drop_down",
                                tint = Color.Unspecified,
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                    state.currentRole?.let {
                        FoundationText(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24),

                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_role),
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.captionCaptionL,
                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .safeClickable { },
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                modifier = Modifier.weight(1f),
                                text = state.currentRole.value ?: "",
                                color = FDS.Colors.characterHighlighted,
                                style = FDS.Typography.bodyB1,
                            )
                            IconButton(
                                onClick = {
                                    onAction.invoke(
                                        SmartCARegisterViewModel.SmartCARegisterAction.CanShowRoleBottomSheetUiState(
                                            true,
                                        ),
                                    )
                                },
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                    }

                    FoundationText(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24),

                        text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_provider),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .safeClickable { },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            modifier = Modifier.weight(1f),
                            text = state.currentProvider?.value ?: "",
                            color = FDS.Colors.characterHighlighted,
                            style = FDS.Typography.bodyB1,
                        )
                        IconButton(
                            onClick = {
                                onAction.invoke(
                                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowProviderBottomSheet(
                                        true,
                                    ),
                                )
                            },
                        ) {
                            Icon(
                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                contentDescription = "ic_drop_down",
                                tint = Color.Unspecified,
                            )
                        }
                    }

                    if (state.smartCABranchDomain != null && state.currentServiceDomain != null) {
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                        HorizontalDivider(
                            color = FDS.Colors.divider,
                            thickness = FDS.Sizer.Stroke.stroke1,
                        )
                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
                        FoundationText(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24),

                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_branch),
                            color = FDS.Colors.characterSecondary,
                            style = FDS.Typography.captionCaptionL,
                        )
                        Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = FDS.Sizer.Gap.gap24)
                                .safeClickable { },
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            FoundationText(
                                modifier = Modifier.weight(1f),
                                text = listOfNotNull(
                                    state.currentBranch?.branchId?.takeIf { it.isNotBlank() },
                                    state.currentBranch?.branchName?.takeIf { it.isNotBlank() },
                                ).joinToString(" - "),
                                color = FDS.Colors.characterHighlighted,
                                style = FDS.Typography.bodyB1,
                            )
                            IconButton(
                                onClick = {
                                    onAction.invoke(
                                        SmartCARegisterViewModel.SmartCARegisterAction.CanShowBranchBottomSheet(
                                            true,
                                        ),
                                    )
                                },
                            ) {
                                Icon(
                                    painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                    contentDescription = "ic_drop_down",
                                    tint = Color.Unspecified,
                                )
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    HorizontalDivider(
                        color = FDS.Colors.divider,
                        thickness = FDS.Sizer.Stroke.stroke1,
                    )
                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))

                    FoundationText(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24),

                        text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_choose_CKS),
                        color = FDS.Colors.characterSecondary,
                        style = FDS.Typography.captionCaptionL,
                    )
                    Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = FDS.Sizer.Gap.gap24)
                            .safeClickable { },
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        FoundationText(
                            modifier = Modifier.weight(1f),
                            text = state.currentCertItem?.serial ?: "",
                            color = FDS.Colors.characterHighlighted,
                            style = FDS.Typography.bodyB1,
                        )
                        IconButton(
                            onClick = {
                                onAction.invoke(
                                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowCertBottomSheet(
                                        true,
                                    ),
                                )
                            },
                        ) {
                            Icon(
                                painter = painterResource(com.vietinbank.core_ui.R.drawable.ic_drop_down),
                                contentDescription = "ic_drop_down",
                                tint = Color.Unspecified,
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
                }
//                state.currentCertItem?.let { item ->
//                    Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap4))
//                    Column(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(horizontal = FDS.Sizer.Gap.gap8)
//                            .clip(RoundedCornerShape(AppSizer.Radius.radius32))
//                            .background(FDS.Colors.backgroundBgContainer),
//                    ) {
//                        FoundationText(
//                            text = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_info_cert),
//                            style = FDS.Typography.headingH3,
//                            color = FDS.Colors.characterHighlighted,
//                            modifier = Modifier
//                                .padding(vertical = FDS.Sizer.Gap.gap24)
//                                .align(Alignment.CenterHorizontally),
//                        )
//                        HorizontalDivider(
//                            color = FDS.Colors.divider,
//                            thickness = FDS.Sizer.Stroke.stroke1,
//                            modifier = Modifier.padding(bottom = FDS.Sizer.Gap.gap8),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_cert_name),
//                            value = item.name.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_cert_serial),
//                            value = item.serial.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_endDate),
//                            value = item.endDate.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_serviceType),
//                            value = item.serviceTypeName.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_serviceType),
//                            value = item.esignFrom.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap8))
//                        FoundationInfoHorizontal(
//                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_idEsign),
//                            value = item.idEsign.orEmpty(),
//                            titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
//                            titleColor = FoundationDesignSystem.Colors.characterSecondary,
//                            valueStyle = FoundationDesignSystem.Typography.bodyB2,
//                            valueColor = FoundationDesignSystem.Colors.characterPrimary,
//                            modifier = Modifier.padding(horizontal = FDS.Sizer.Gap.gap24),
//                        )
//                        Spacer(modifier = Modifier.height(FDS.Sizer.Gap.gap16))
//                    }
//                }
            }
        }
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 0.5f),
                            FoundationDesignSystem.Colors.dialogBackground.copy(alpha = 1f),
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY,
                    ),
                )
                .padding(
                    horizontal = FoundationDesignSystem.Sizer.Gap.gap24,
                    vertical = FoundationDesignSystem.Sizer.Gap.gap8,
                ),
        ) {
            FoundationButton(
                isLightButton = true,
                text = stringResource(com.vietinbank.core_ui.R.string.btn_continue),
                onClick = { onAction.invoke(SmartCARegisterViewModel.SmartCARegisterAction.OnClickButtonContinue) },
                enabled = true,
                modifier = Modifier.fillMaxWidth(),
            )
        }
        ServiceTypeBottomSheet(
            isVisible = state.canShowServiceTypeBottomSheet,
            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_ServiceType),
            onItemSelected = { selectedItem ->
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnChooseServiceType(
                        selectedItem,
                    ),
                )
            },
            onDismiss = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowServiceTypeBottomSheet(
                        false,
                    ),
                )
            },
            state = state.serviceTypeBottomSheetUiState,
        )

        RegisterSmartCABottomSheet(
            isVisible = state.canShowRoleBottomSheetUiState,
            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_role),
            onItemSelected = { selectedItem ->
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnChooseRole(
                        selectedItem,
                    ),
                )
            },
            onDismiss = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowRoleBottomSheetUiState(
                        false,
                    ),
                )
            },
            state = state.roleBottomSheetUiState,
        )

        RegisterSmartCABottomSheet(
            isVisible = state.canShowProviderBottomSheet,
            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_provider),
            onItemSelected = { selectedItem ->
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnChooseSProvider(
                        selectedItem,
                    ),
                )
            },
            onDismiss = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowProviderBottomSheet(
                        false,
                    ),
                )
            },
            state = state.providerBottomSheetUiState,
        )

        BranchBottomSheet(
            isVisible = state.canShowBranchBottomSheet,
            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_register_customer_bottom_sheet_branch),
            onItemSelected = { selectedItem ->
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnChooseBranch(
                        selectedItem,
                    ),
                )
            },
            onDismiss = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowBranchBottomSheet(
                        false,
                    ),
                )
            },
            state = state.branchBottomSheetUiState,
        )

        CertBottomSheet(
            isVisible = state.canShowCertBottomSheet,
            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_list),
            onItemSelected = { selectedItem ->
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnChooseCert(
                        selectedItem,
                    ),
                )
            },
            onDismiss = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.CanShowCertBottomSheet(
                        false,
                    ),
                )
            },
            onConfirmItemSelected = {
                onAction.invoke(
                    SmartCARegisterViewModel.SmartCARegisterAction.OnConfirmCert,
                )
            },
            state = state.certBottomSheetUiState,
        )
    }
}
