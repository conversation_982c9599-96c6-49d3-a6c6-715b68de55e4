package com.vietinbank.feature_new_smart_ca.smart_ca_register

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.Bundle.UTF8
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.net.URLEncoder
import javax.inject.Inject

@AndroidEntryPoint
class SmartCARegisterFragment : BaseFragment<SmartCARegisterViewModel>() {
    override val viewModel: SmartCARegisterViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    SmartCARegisterViewModel.SmartCARegisterEvent.NavigateBack -> {
                        appNavigator.popBackStack()
                    }

                    is SmartCARegisterViewModel.SmartCARegisterEvent.ShowErrorValidateField -> {
                        showNoticeDialog(message = event.errorMess)
                    }

                    is SmartCARegisterViewModel.SmartCARegisterEvent.ValidatePaymentOrderState -> {
                        val itemString =
                            Utils.g().provideGson().toJson(event.smartCARegisterDomain)
                        val encodedItem = URLEncoder.encode(itemString, UTF8)
                        appNavigator.goToConfirmSmartCA(
                            transactionBundle = encodedItem,
                            dataType = event.dataType,
                        )
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            SmartCARegisterScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }
}