package com.vietinbank.feature_new_smart_ca.smart_ca_list

import android.os.Bundle
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.vietinbank.core_common.nav.IAppNavigator
import com.vietinbank.core_ui.base.BaseFragment
import com.vietinbank.core_ui.theme.AppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SmartCAListFragment : BaseFragment<SmartCAListViewModel>() {
    override val viewModel: SmartCAListViewModel by viewModels()
    override val useCompose: Boolean = true

    @Inject
    override lateinit var appNavigator: IAppNavigator
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeEvents()
    }

    private fun observeEvents() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.events.collect { event ->
                when (event) {
                    SmartCAListViewModel.SmartCAListEvent.NavigateBack -> {
                        appNavigator.goToHome()
                    }

                    is SmartCAListViewModel.SmartCAListEvent.OnDetailClick -> {
                        appNavigator.goToDetailSmartCA(event.model.mtId ?: "")
                    }

                    is SmartCAListViewModel.SmartCAListEvent.OnGoToRegisterSmartCA -> {
                        appNavigator.goToRegisterSmartCA()
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeScreen() {
        val uiState by viewModel.state.collectAsState()
        AppTheme {
            SmartCAListScreen(
                state = uiState,
                onAction = viewModel::onAction,
            )
        }
    }

    override fun onBackPressed(): Boolean {
        appNavigator.goToHome()
        return true
    }
}