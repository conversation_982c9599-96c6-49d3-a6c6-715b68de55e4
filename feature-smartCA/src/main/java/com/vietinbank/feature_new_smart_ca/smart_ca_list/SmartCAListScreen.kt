package com.vietinbank.feature_new_smart_ca.smart_ca_list

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.vietinbank.core_common.extensions.dd_MM_yyyy_HH_mm_ss
import com.vietinbank.core_common.extensions.getDateToFormat
import com.vietinbank.core_common.extensions.yyyy_MM_DD_T_HH_mm_ss_SSSZ
import com.vietinbank.core_common.models.AppBarAction
import com.vietinbank.core_domain.models.smartCA.SmartCAEsignDomain
import com.vietinbank.core_ui.components.FoundationAppBar
import com.vietinbank.core_ui.components.FoundationDivider
import com.vietinbank.core_ui.components.FoundationInfoHorizontal
import com.vietinbank.core_ui.components.FoundationTabs
import com.vietinbank.core_ui.components.TabType
import com.vietinbank.core_ui.theme.AppSizer
import com.vietinbank.core_ui.theme.FoundationDesignSystem
import com.vietinbank.core_ui.utils.eFastBackground
import com.vietinbank.core_ui.utils.safeClickable
import com.vietinbank.core_ui.utils.systemBarsPadding
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.ESIGN_TYPE_BUSINESS
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.ESIGN_TYPE_PERSONAL
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_PENDING
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.STATUS_CODE_SUCCESS
import com.vietinbank.feature_new_smart_ca.constance.SmartCAConstants.DashBoardSmartCA.TAB_ACTIVE
import com.vietinbank.feature_new_smart_ca.empty_list.EmptyListScreen
import com.vietinbank.feature_smartca.R

@Composable
fun SmartCAListScreen(
    state: SmartCAListViewModel.SmartCAListState,
    onAction: ((SmartCAListViewModel.SmartCAListAction) -> Unit),
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .eFastBackground()
            .systemBarsPadding()
            .padding(top = FoundationDesignSystem.Sizer.Gap.gap16),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize(),
        ) {
            // App Bar
            FoundationAppBar(
                isLightIcon = false,
                isSingleLineAppBar = true,
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_list),
                onNavigationClick = { onAction.invoke(SmartCAListViewModel.SmartCAListAction.OnBackPressed) },
                navigationIcon = painterResource(com.vietinbank.core_ui.R.drawable.ic_core_ui_home),
                actions = listOf(
                    AppBarAction(
                        icon = com.vietinbank.core_ui.R.drawable.ic_core_ui_add,
                        contentDescription = "ic_core_ui_add",
                        onClick = { onAction.invoke(SmartCAListViewModel.SmartCAListAction.OnGoToRegisterSmartCA) },
                    ),
                ),
            )

            FoundationTabs(
                tabs = listOf(
                    stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_tab_active),
                    stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_tab_inactive),
                ),
                selectedIndex = state.selectedIndex,
                onTabSelected = {
                    onAction.invoke(
                        SmartCAListViewModel.SmartCAListAction.OnSelectedIndex(
                            it,
                        ),
                    )
                },
                type = TabType.Pill,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = FoundationDesignSystem.Sizer.Gap.gap16,
                        vertical = FoundationDesignSystem.Sizer.Gap.gap16,
                    ),
                horizontalItemPadding = FoundationDesignSystem.Sizer.Padding.padding0,
            )
            if (TAB_ACTIVE == state.selectedIndex) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                ) {
                    if (state.isLoadingListCKS) return
                    if (state.signInfoListActive.isNotEmpty()) {
                        LazyColumn() {
                            itemsIndexed(state.signInfoListActive) { index, item ->
                                SmartCAItemView(
                                    model = item,
                                    actions = { onAction.invoke(SmartCAListViewModel.SmartCAListAction.OnDetailClick(item)) },
                                    isFirst = index == 0,
                                    isLast = index == state.signInfoListActive.size - 1,
                                )
                            }
                        }
                    } else {
                        EmptyListScreen(
                            iconResourceID = R.mipmap.ic_active_tab_null_list,
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_tab_active_null_list),
                        )
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(
                            RoundedCornerShape(
                                topStart = AppSizer.Radius.radius32,
                                topEnd = AppSizer.Radius.radius32,
                            ),
                        )
                        .background(FoundationDesignSystem.Colors.backgroundBgContainer)
                        .padding(horizontal = FoundationDesignSystem.Sizer.Gap.gap24),
                ) {
                    if (state.isLoadingListCKS) return
                    if (state.signInfoListInActive.isNotEmpty()) {
                        LazyColumn() {
                            itemsIndexed(state.signInfoListInActive) { index, item ->
                                SmartCAItemView(
                                    model = item,
                                    actions = { onAction.invoke(SmartCAListViewModel.SmartCAListAction.OnDetailClick(item)) },
                                    isFirst = index == 0,
                                    isLast = index == state.signInfoListInActive.size - 1,
                                )
                            }
                        }
                    } else {
                        EmptyListScreen(
                            iconResourceID = R.mipmap.ic_active_tab_null_list,
                            title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_tab_active_null_list),
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SmartCAItemView(
    modifier: Modifier = Modifier,
    model: SmartCAEsignDomain? = null,
    actions: () -> Unit,
    isFirst: Boolean,
    isLast: Boolean,
) {
    Box(
        modifier = modifier
            .safeClickable { actions.invoke() },
    ) {
        Column {
            if (isFirst) {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap24))
            } else {
                Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap16))
            }
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_userName),
                value = model?.userName,
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_serial),
                value = model?.serial,
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_esignType),
                value = when (model?.esignType) {
                    ESIGN_TYPE_PERSONAL -> stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_esignType_personal)
                    ESIGN_TYPE_BUSINESS -> stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_esignType_business)
                    else -> ""
                },
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_roleDesc),
                value = model?.roleDesc?.firstOrNull(),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_createDate),
                value = model?.createDate.getDateToFormat(formatFrom = yyyy_MM_DD_T_HH_mm_ss_SSSZ, formatTo = dd_MM_yyyy_HH_mm_ss),
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_endtDate),
                value = model?.endtDate,
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_esignFrom),
                value = model?.esignFrom,
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = FoundationDesignSystem.Colors.characterPrimary,
            )
            Spacer(modifier = Modifier.height(FoundationDesignSystem.Sizer.Gap.gap8))
            FoundationInfoHorizontal(
                title = stringResource(com.vietinbank.core_ui.R.string.feature_smart_ca_dashboard_item_statusName),
                value = model?.statusName,
                titleStyle = FoundationDesignSystem.Typography.captionCaptionL,
                titleColor = FoundationDesignSystem.Colors.characterSecondary,
                valueStyle = FoundationDesignSystem.Typography.captionCaptionL,
                valueColor = when (model?.statusCode) {
                    STATUS_CODE_SUCCESS -> {
                        FoundationDesignSystem.Colors.stateSuccess
                    }
                    STATUS_CODE_PENDING -> {
                        FoundationDesignSystem.Colors.stateWarning
                    }
                    else -> {
                        FoundationDesignSystem.Colors.stateError
                    }
                },
            )
            FoundationDivider(modifier = Modifier.padding(top = FoundationDesignSystem.Sizer.Gap.gap16))
        }
    }
}
