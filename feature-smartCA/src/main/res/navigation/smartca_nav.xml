<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:startDestination="@id/smartCAListFragment"
    android:id="@+id/smartca_nav">

    <fragment
        android:id="@+id/smartCAListFragment"
        android:name="com.vietinbank.feature_new_smart_ca.smart_ca_list.SmartCAListFragment"
        android:label="SmartCAListFragment">
        <deepLink app:uri="smartca://list" />

    </fragment>
    <fragment
        android:id="@+id/smartCADetailFragment"
        android:name="com.vietinbank.feature_new_smart_ca.smart_ca_detail_screen.SmartCADetailFragment"
        android:label="smartCADetailFragment">
        <deepLink app:uri="smartca://smartCADetailFragment/{KEY_MT_ID}" />
    </fragment>

    <fragment
        android:id="@+id/smartCARegisterFragment"
        android:name="com.vietinbank.feature_new_smart_ca.smart_ca_register.SmartCARegisterFragment"
        android:label="SmartCARegisterFragment">
        <deepLink app:uri="smartca://register" />

    </fragment>
    <fragment
        android:id="@+id/smartCAConfirmFragment"
        android:name="com.vietinbank.feature_new_smart_ca.smart_ca_confirm_screen.SmartCAConfirmFragment"
        android:label="smartCAConfirmFragment">
        <deepLink app:uri="smartca://confirm/{TRANSACTION_BUNDLE}/{DATA_TYPE}" />
    </fragment>
    <fragment
        android:id="@+id/smartCAResultFragment"
        android:name="com.vietinbank.feature_new_smart_ca.smart_ca_result.SmartCAResultFragment"
        android:label="smartCAResultFragment">
        <deepLink app:uri="smartca://result/{TRANSACTION_BUNDLE}" />
    </fragment>



<!--    <fragment-->
<!--        android:id="@+id/smartCAResultFragment"-->
<!--        android:name="com.vietinbank.feature_smartca.fragment.SmartCAResultFragment"-->
<!--        android:label="SmartCAResultFragment" />-->

</navigation>