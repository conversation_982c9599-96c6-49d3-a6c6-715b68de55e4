<resources>
    <string name="vtb_ra_notice">Thông báo</string>
    <string name="vtb_ra_close"><PERSON><PERSON><PERSON></string>
    <string name="vtb_ra_retry">Th<PERSON> lại</string>
    <string name="vtb_ra_cancel">Hủ<PERSON></string>
    <string name="vtb_ra_continue">Tiế<PERSON> tục</string>
    <string name="vtb_ra_no_internet">Không có kết nối mạng. Quý khách vui lòng thử lại sau.</string>
    <string name="vtb_ra_error_at_server">Đ<PERSON> xảy ra lỗi. Quý khách vui lòng thử lại</string>
    <string name="vtb_ra_error_cerpinning">Chứng chỉ kết nối không đúng</string>
    <string name="vtb_ra_error_system">Đã xảy ra lỗi. Quý khách vui lòng thử lại</string>
    <string name="vtb_ra_authen_soft_title" translatable="false">Mã Soft OTP</string>
    <string name="vtb_ra_authen_title" translatable="false">Nhập mã PIN</string>
    <string name="vtb_ra_authen_pin_code" translatable="false">Mã PIN</string>
    <string name="vtb_ra_authen_pin_description" translatable="false">Quý khách vui lòng nhập mã PIN Soft OTP để tiếp tục giao dịch</string>
    <string name="vtb_ra_authen_keypass_description" translatable="false">Quý khách vui lòng nhập mã giao dịch %1$s vào thẻ Keypass để nhận chuỗi mã trả ra và nhập vào ô dưới đây</string>
    <string name="vtb_ra_authen_soft_description" translatable="false">Mã xác thực OTP bằng phương thức xác thực Soft OTP của QUý khách đang được hiển thị dưới đây. Quý khách vui lòng nhấn "Xác nhận" để tiếp tục giao dịch</string>
    <string name="vtb_ra_authen_soft_note" translatable="false">Lưu ý: Soft OTP sẽ bị khóa trong 1 giờ nếu Quý khách nhập sai PIN 5 lần liên tiếp</string>
    <string name="vtb_ra_authen_soft_time" translatable="false">Thời gian hiệu lực Soft OTP: %1$ss</string>

    <string name="v_soft_copied">Đã sao chép mã</string>

    <!--    Term of use screen-->
    <string name="soft_opt_term_confirm">Tôi đã đọc và đồng ý với điều khoản cung cấp và sử dụng Soft OTP</string>
    <string name="soft_opt_term_title">Điều khoản sử dụng Soft OTP</string>

    <!--    Active info screen-->
    <string name="active_opt_info_header">Kích hoạt Soft OTP</string>
    <string name="active_opt_info_title">Thông tin người dùng</string>
    <string name="active_opt_info_full_name">Họ và tên</string>
    <string name="active_opt_info_user_name">Tên đăng nhập</string>
    <string name="active_opt_info_email">E-mail</string>
    <string name="active_opt_info_phone_number">Số điện thoại</string>
    <string name="receive_otp_picker_title">Phương thức nhận mã</string>
    <string name="active_opt_fail_message">Gửi mã kích hoạt không thành công.</string>

    <!--    Keypass-->
    <string name="active_forgot_keypass_header">Quên PIN Keypass</string>
    <string name="keypass_info_title">Mã kích hoạt Keypass</string>
    <string name="keypass_info_token_serial">Token serial</string>
    <string name="keypass_info_token_code">Mã kích hoạt</string>
    <string name="keypass_forgot_request_pin_success">Quý khách đã gửi yêu cầu cấp lại mã PIN Keypass thành công.</string>
    <string name="keypass_dialog_instruction_title">Hướng dẫn lấy mã PUK</string>
    <string name="keypass_auth_title">Xác thực bằng Keypass</string>
    <string name="keypass_auth_description">Nhập mã giao dịch sau vào thiết bị Keypass</string>
    <string name="keypass_auth_sub_description">Nhập mã OTP từ thiết bị vào ô bên dưới</string>
    <string name="keypass_auth_instruction_button">Hướng dẫn lấy mã OTP bằng Keypass</string>
    <string name="keypass_auth_forgot_button">Quên PIN</string>
    <string name="keypass_auth_fail_message">Mã OTP không đúng</string>
    <string name="keypass_auth_blocked_message">Tài khoản đã bị khoá phương thức xác thực Keypass do nhập sai mã xác thực OTP quá số lần quy định. Quý khách vui lòng thực hiện đồng bộ Keypass.</string>

    <!--    Confirm install OTP screen-->
    <string name="confirm_otp_screen_title">Xác thực cài đặt</string>
    <string name="confirm_otp_screen_des">Mã kích hoạt đã được gửi về số điện thoại/email của Quý khách.</string>
    <string name="confirm_otp_screen_resend_ask">Chưa nhận được mã kích hoạt?\u0020</string>
    <string name="confirm_otp_screen_resend_cta">Gửi lại</string>
    <string name="confirm_otp_screen_otp_fail_alert">Mã kích hoạt không đúng. Quý khách đã nhập sai %s lần. Lưu ý: Sai 5 lần liên tiếp sẽ bị khóa.</string>
    <string name="confirm_otp_dialog_locked_message">Tài khoản đã bị khóa phương thức xác thực Soft OTP trong vòng 1 giờ do nhập mã PIN sai quá số lần quy định. Quý khách vui lòng thực hiện giao dịch lại sau.</string>
    <string name="confirm_otp_dialog_locked_button">Về Trang chủ</string>


    <!--    Install Soft OTP screen-->
    <string name="install_otp_screen_title">Cài đặt mã PIN Soft OTP</string>
    <string name="install_otp_screen_input_pin_title">Thiếp lập mã PIN</string>
    <string name="install_otp_screen_reinput_pin_title">Nhập lại mã PIN.</string>
    <string name="install_otp_screen_input_alert">Mã PIN không khớp.</string>
    <string name="install_otp_dialog_success">Quý khách đã kích hoạt xác thực giao dịch bằng Soft OTP thành công.</string>
    <string name="keypass_input_field_label">Mã PUK</string>
    <string name="keypass_input_error_message">Mã PUK không hợp lệ.</string>

    <!--    Confirm verify install second timen-->
    <string name="install_otp_second_title">Nhập mã PIN</string>
    <string name="install_otp_second_des">Quý khách vui lòng nhập mã PIN Soft OTP để tiếp tục. Mã PIN này được cài đặt từ lần kích hoạt đầu tiên.</string>

    <!--    Toggle OTP screen-->
    <string name="toggle_otp_screen_title">Nhập mã PIN Soft OTP</string>
    <string name="toggle_otp_cta">Quên PIN</string>
    <string name="toggle_otp_dialog_alert_message">Quý khách có chắc chắn muốn huỷ xác thực giao dịch bằng Soft OTP?</string>
    <string name="toggle_otp_dialog_success_message">Quý khách đã huỷ xác thực giao dịch bằng Soft OTP thành công.</string>

    <!--    Sync OTP screen-->
    <string name="sync_otp_title">Nhập mã PIN</string>
    <string name="sync_otp_cta">Quên PIN</string>
    <string name="sync_otp_dialog_success_message">Quý khách đã huỷ xác thực giao dịch bằng Soft OTP thành công.</string>

    <!--    Forgot OTP-->
    <string name="forgot_otp_dialog_message">Quý khách sẽ yêu cầu kích hoạt lại tất cả các user phê duyệt. Quý khách có muốn tiếp tục?</string>

    <!--    Lock Soft OTP-->
    <string name="lock_soft_otp_dialog_message">Quý khách đang tạm thời bị khóa kích hoạt Soft OTP do nhập mã kích hoạt sai quá số lần quy định. Vui lòng thực hiện lại sau %s phút %s giây</string>

    <!--    SDK Error message-->
    <string name="sdk_fail">Không thành công</string>
    <string name="sdk_wrong_activation_5_times">Kích hoạt sai 5 lần liên tiếp</string>
    <string name="sdk_wrong_activation_code">Sai mã kích hoạt</string>
    <string name="sdk_activation_code_expired">Mã kích hoạt hết hạn</string>
    <string name="sdk_wrong_pin">Sai mã PIN</string>
    <string name="sdk_connection_timeout">Lỗi kết nối: Kết nối đến server bị timeout</string>
    <string name="sdk_connection_failed_1">Lỗi kết nối: Không thể kết nối đến server</string>
    <string name="sdk_connection_failed_2">Lỗi kết nối: Không thể kết nối đến server</string>
    <string name="sdk_sync_limit_exceeded">Đồng bộ quá số lần cho phép trong 1 timestep</string>
    <string name="sdk_rooted_device">Thiết bị Root/Jailbreak</string>
    <string name="sdk_invalid_url">URL không hợp lệ</string>
    <string name="sdk_not_activated">Ứng dụng chưa được kích hoạt</string>
    <string name="sdk_invalid_data">Dữ liệu không hợp lệ</string>
    <string name="sdk_db_error">Lỗi cơ sở dữ liệu</string>
    <string name="sdk_transaction_timeout">Lỗi giao dịch hết hạn xử lý</string>
    <string name="sdk_transaction_update_fail">Cập nhật trạng thái giao dịch không thành công</string>
    <string name="sdk_challenge_code_used">Challenge code cho giao dịch này đã được sử dụng</string>
    <string name="sdk_transaction_not_found">Giao dịch không tồn tại</string>
    <string name="sdk_system_error">Lỗi hệ thống</string>
    <string name="sdk_callback_failed">Gọi lại không thành công. Kiểm tra kết nối với API</string>
</resources>