package com.vietinbank.feature_soft.views.fragment

import androidx.lifecycle.viewModelScope
import com.google.gson.reflect.TypeToken
import com.vietinbank.core_common.config.IAppConfigManager
import com.vietinbank.core_common.livedata.SingleLiveEvent
import com.vietinbank.core_common.ott.IOttSetupService
import com.vietinbank.core_common.session.ISessionManager
import com.vietinbank.core_common.session.IUserProf
import com.vietinbank.core_common.utils.IResourceProvider
import com.vietinbank.core_common.utils.Utils
import com.vietinbank.core_domain.models.soft.SoftEntity
import com.vietinbank.core_domain.models.soft.SoftOtpActivationType
import com.vietinbank.core_domain.models.soft.SoftUserEntity
import com.vietinbank.core_domain.models.soft.VBlockSoftOtpParams
import com.vietinbank.core_domain.models.soft.VGetSoftOtpActivationCodeParams
import com.vietinbank.core_domain.models.soft.VSoftResponseParams
import com.vietinbank.core_domain.softotp.ISoftManager
import com.vietinbank.core_domain.usecase.soft.SoftUseCase
import com.vietinbank.core_ui.base.BaseViewModel
import com.vietinbank.feature_soft.common.constant.VSoftConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import vn.mk.token.sdk.SdkNative
import javax.inject.Inject

@HiltViewModel
class VSoftViewModel @Inject constructor(
    val softUseCase: SoftUseCase,
    val softManager: ISoftManager,
    override val resourceProvider: IResourceProvider,
    override val appConfig: IAppConfigManager,
    override val userProf: IUserProf,
    override val ottSetupService: IOttSetupService,
    override val sessionManager: ISessionManager,
) : BaseViewModel() {
    fun getFullName() = userProf.getFullName() ?: ""
    fun getUserName() = userProf.getUserName() ?: ""
    fun getPhone() = userProf.getPhoneNo() ?: ""
    fun getEmail() = userProf.getEmail() ?: ""
    fun getKeypassProfile() = userProf.getKeypassProfile()
    fun updateStatusSoft(isActive: Boolean) = appConfig.updateStatusSoft(isActive)
    fun updateLockSoft(timeOpen: Long) = appConfig.updateLockSoft(timeOpen)

    // lay ma kich hoat smart otp
    val activeCodeLiveData = SingleLiveEvent<VSoftResponseParams>()
    fun getActivationCode(
        sendType: String,
    ) = launchJob {
        val result = softUseCase.getActivationCode(
            VGetSoftOtpActivationCodeParams(
                SoftOtpActivationType.EMAIL,
                userProf.getCifNo() ?: "",
                userProf.getUserName() ?: "",
            ),
        )
        handleResource<VSoftResponseParams>(result) { data ->
            activeCodeLiveData.postValue(data)
        }
    }

    // block token
    val blockTokenLiveData = SingleLiveEvent<VSoftResponseParams>()
    fun blockToken(
        req: VBlockSoftOtpParams,
    ) = launchJob {
        val result = softUseCase.blockToken(req)
        handleResource<VSoftResponseParams>(result) { data ->
            //        - Response code = 0 hoặc 14 thì xóa thông tin SoftTokenSN ở DB của MB app.
//        - Response code # khả năng là có lỗi kết nối hoặc lỗi liên quan đến service của Keypass OTP.

            blockTokenLiveData.postValue(data)
        }
    }

    /****
     * handle with SDK MK
     */

    val sdkCheckStatusCodeLD = SingleLiveEvent<Int>()
    val sdkCheckStatusLD = SingleLiveEvent<Boolean>()

    // xoa user
    val deleteAllTokensLD = SingleLiveEvent<Boolean>()
    fun deleteAllTokensSmartOTP() {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                // khi login user moi # voi user dang kich hoạt tren app -> xoa toàn bo token da luu
                SdkNative.getInstance().setSelectedUserId("")
                val result = SdkNative.getInstance().deleteAllExistingTokens()
                deleteAllTokensLD.postValue(result)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // kich hoat smart otp
    fun sdkDoActive(otp: String) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().doActive(
                    VSoftConstants.VTB_OTP_PREFIX + otp,
                    "tokenpush",
                    VSoftConstants.VTB_OTP_APP_ID,
                    "https://api-uat.vietinbank.vn/keypass.wsmobile/",
                )
                val resultCode = String(result).toInt()
                sdkCheckStatusCodeLD.postValue(resultCode)
            } catch (_: Exception) {
                sdkCheckStatusCodeLD.postValue(VSoftConstants.OTPResultCode.FAIL.code)
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // kiem tra pin
    val sdkAuthenLD = SingleLiveEvent<Int>()
    fun sdkLoginPin(otp: String) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().loginPin(otp)
                val resultCode = String(result).toInt()
                sdkAuthenLD.postValue(resultCode)
            } catch (_: Exception) {
                sdkAuthenLD.postValue(VSoftConstants.OTPResultCode.FAIL.code)
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // dong bo time voi BE
    fun sdkDoSyncTime(isVerity: Boolean = true) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().doSyncTime()
                val resultCode = String(result).toInt()
                // thanh cong thi tiep tuc call
                if (VSoftConstants.OTPResultCode.SUCCESS.code == resultCode && isVerity) {
                    // khi tiếp tục thì kiểm tra user đã kích hoạt soft trên app hay chua
                    sdkGetTransactionInfo("", "")
                } else {
                    // hien thị loi
                    sdkCheckStatusCodeLD.postValue(resultCode)
                }
            } catch (_: Exception) {
                sdkCheckStatusCodeLD.postValue(VSoftConstants.OTPResultCode.FAIL.code)
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // kiem tra userId đa active hay chua
    fun sdkCheckUserIdExistence(userID: String? = null) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance()
                    .checkUserIdExistence(userID ?: userProf.getKeypassProfile() ?: "")
                sdkCheckStatusLD.postValue(result)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    //
    val sdkTransactionInfoLD = SingleLiveEvent<SoftEntity>()
    var sdkTransInfoModel: SoftEntity? = null
    fun sdkGetTransactionInfo(transactionId: String, messageId: String) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().getTransactionInfo(transactionId, messageId)
                val resultConvert =
                    Utils.provideGson().fromJson(String(result), SoftEntity::class.java)
                sdkTransInfoModel = resultConvert
                sdkTransactionInfoLD.postValue(resultConvert)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    //
    val sdkCrotpWithTransactionInfoLD = SingleLiveEvent<String>()
    fun sdkGetCRotpWithTransactionInfo(transactionData: String?, challengeCode: String?) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result =
                    SdkNative.getInstance()
                        .getCRotpWithTransactionInfo(transactionData, challengeCode)
                val resultCode = String(result)
                sdkCrotpWithTransactionInfoLD.postValue(resultCode)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // xac nhan giao dich
    val sdkConfirmTransactionLD = SingleLiveEvent<Int>()
    fun sdkConfirmTransaction(
        userId: String?,
        transactionId: String?,
        otp: String?,
        isConfirm: Boolean = false,
    ) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().confirmTransaction(
                    userId,
                    transactionId,
                    otp,
                    isConfirm,
                )
                sdkConfirmTransactionLD.postValue(result)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // xac nhan giao dich
    val sdkDecryptQRCodeDataLD = SingleLiveEvent<String>()
    fun sdkDecryptQRCodeDataWithUserId(
        userId: String,
        dataQr: String,
    ) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().decryptQRCodeDataWithUserId(userId, dataQr)
                val resultCode = String(result)
                sdkDecryptQRCodeDataLD.postValue(resultCode)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // lay token hien tai dang kich hoat smart otp
    val sdkGetTokenSnLD = SingleLiveEvent<String>()
    fun sdkGetTokenSn() {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val result = SdkNative.getInstance().tokenSn
                val resultCode = String(result)
                sdkGetTokenSnLD.postValue(resultCode)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    // lay token hien tai dang kich hoat smart otp
    val sdkUserListInJson = SingleLiveEvent<List<SoftUserEntity>>()
    fun sdkUserListInJson(isLoading: Boolean = true) {
        viewModelScope.launch {
            if (isLoading) {
                CoroutineScope(Dispatchers.Main).launch { showLoading() }
                delay(1000)
            }
            try {
                val result = SdkNative.getInstance().userListInJson
                val type = object : TypeToken<List<SoftUserEntity>>() {}.type
                val transactions: List<SoftUserEntity> =
                    Utils.g().provideGson().fromJson(String(result), type)
                sdkUserListInJson.postValue(transactions)
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch {
                    hideLoading()
                }
            }
        }
    }

    fun checkTranEfast(tranId: String? = null, messageId: String? = null) {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                if (softManager.isSkipVerify) {
                    // giao dich thu 2 neu trong sessoin
                    // -> khong can goi doSyncTime, checkActiveToken, checkUserIdExistence
                    SdkNative.getInstance().setSelectedUserId(userProf.getKeypassProfile() ?: "")
                    val result =
                        SdkNative.getInstance().getTransactionInfo(tranId ?: "", messageId ?: "")
                    sdkTransInfoModel =
                        Utils.provideGson().fromJson(String(result), SoftEntity::class.java)
                    if (sdkTransInfoModel?.responseCode == VSoftConstants.OTPResultCode.SUCCESS.code) {
                        println("OTP: ${sdkTransInfoModel?.userID} - ${sdkTransInfoModel?.transactionData} - ${sdkTransInfoModel?.challenge}")
                        sdkGetCRotpWithTransactionInfo(
                            sdkTransInfoModel?.transactionData ?: "",
                            sdkTransInfoModel?.challenge ?: "",
                        )
                    } else {
                        sdkCheckStatusCodeLD.postValue(sdkTransInfoModel?.responseCode)
                    }
                } else {
                    val syncTimeCode = String(SdkNative.getInstance().doSyncTime()).toInt()
                    if (syncTimeCode == VSoftConstants.OTPResultCode.SUCCESS.code) {
                        if (SdkNative.getInstance()
                                .checkUserIdExistence(userProf.getKeypassProfile() ?: "")
                        ) {
                            softManager.isSkipVerify = true
                            SdkNative.getInstance()
                                .setSelectedUserId(userProf.getKeypassProfile() ?: "")
                            val result = SdkNative.getInstance()
                                .getTransactionInfo(tranId ?: "", messageId ?: "")
                            sdkTransInfoModel =
                                Utils.provideGson().fromJson(String(result), SoftEntity::class.java)
                            if (sdkTransInfoModel?.responseCode == VSoftConstants.OTPResultCode.SUCCESS.code) {
                                println("OTP: ${sdkTransInfoModel?.userID} - ${sdkTransInfoModel?.transactionData} - ${sdkTransInfoModel?.challenge}")
                                sdkGetCRotpWithTransactionInfo(
                                    sdkTransInfoModel?.transactionData ?: "",
                                    sdkTransInfoModel?.challenge ?: "",
                                )
                            } else {
                                sdkCheckStatusCodeLD.postValue(sdkTransInfoModel?.responseCode)
                            }
                        } else {
                            sdkCheckStatusLD.postValue(false)
                        }
                    } else {
                        sdkCheckStatusCodeLD.postValue(syncTimeCode)
                    }
                }
            } catch (e: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }

    fun turnOffInApp(userId: String? = userProf.getKeypassProfile(), pushToken: String? = "") {
        viewModelScope.launch {
            CoroutineScope(Dispatchers.Main).launch { showLoading() }
            try {
                delay(1000)
                val tokenSn = String(SdkNative.getInstance().getTokenSnWithUserId(userId))
                val resultRemoveUser = String(
                    SdkNative.getInstance().removeUserOnKeypassServer(userId, tokenSn, pushToken),
                ).toInt()
                if (resultRemoveUser == 0) {
                    sdkCheckStatusLD.postValue(SdkNative.getInstance().deleteUserByUserId(userId))
                } else {
                    sdkCheckStatusCodeLD.postValue(resultRemoveUser)
                }
            } catch (_: Exception) {
            } finally {
                CoroutineScope(Dispatchers.Main).launch { hideLoading() }
            }
        }
    }
}
