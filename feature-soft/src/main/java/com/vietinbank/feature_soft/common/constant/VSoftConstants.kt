package com.vietinbank.feature_soft.common.constant

object VSoftConstants {

    object Bundle {
        const val KEY_DATA_1 = "DATA_1" // transactionData
        const val KEY_DATA_2 = "DATA_2" // challenge
        const val KEY_FLOW_OTP = "KEY_FLOW_OTP"
    }
    const val KEY_OTP_FAIL_COUNT = "KEY_OTP_FAIL_COUNT"

    const val URL_SYNC = "https://ebanking.vietinbank.vn/synchronizeOTP/keypass/sync"

    const val VTB_OTP_APP_ID = "4"
    const val VTB_OTP_PREFIX = "5200"
    const val LOCK_1_MIN = 60 * 1000
    const val LOCK_5_MIN = 5 * 60 * 1000
    const val COUNT_OTP_TIME = 60
    const val COUNT_SMS_TIME = 300
    const val WRONG_PIN_CODE_MAX = 5

    // RESULT CODE
    const val RESULT_REND_OTP = "RESULT_REND_OTP"
    const val RESULT_VERIFY_PIN = "RESULT_VERIFY_PIN"
    const val RESULT_CONFIRM_OTP = "RESULT_CONFIRM_OTP"

    const val RESULT_UPDATE_KEYPASS = "RESULT_UPDATE_KEYPASS"

    // enum constructor - cannot be public or protected
    // rule message (C: mã code)(RQ: requestId)
    enum class OTPResultCode(val code: Int, val description: String) {
        SUCCESS(0, "Thành công"), // thanh cong
        FAIL(93, "Không thành công (C: M-93)"), // Không thành công
        WRONG_5TIMES_ACTIVE_CODE(
            89,
            "Kích hoạt sai 5 lân liên tiếp (C: M-89)",
        ), // Kích hoạt sai 5 lần liên tiếp
        WRONG_ACTIVE_CODE(91, "Sai mã kích hoạt (C: M-91)"), // Sai mã kích hoạt
        WRONG_ACTIVE_CODE_EXPIRED(90, "Mã kích hoạt hết hạn (C: M-90)"), // MÃ KÍCH HOẠT HẾT HẠN
        WRONG_PIN_CODE(21, "Sai mã PIN (C: M-21)"), // Sai mã PIN
        SERVER_TIMEOUT(
            28,
            "Lỗi kết nối: Kết nối đến server bị timeout (C: M-28)",
        ), // Lỗi kết nối: Kết nối đến server bị timeout
        SERVER_NOT_CONNECTED(
            7,
            "Lỗi kết nối: Không thể kết nối đến server (C: M-7)",
        ), // Lỗi kết nối: Không thể kết nối đến server
        SERVER_NOT_CONNECTED_2(
            56,
            "Lỗi kết nối: Không thể kết nối đến server (C: M-56)",
        ), // Lỗi kết nối: Không thể kết nối đến server
        WRONG_SYNC_TOO_MANY(
            205,
            "Đồng bộ quá số lần cho phép trong 1 timestep (C: M-205)",
        ), // Đồng bộ quá số lần cho phép trong 1 timestep
        WRONG_ROOT_DEVICE(94, "Thiết bị Root/Jailbreak (C: M-94)"), // Thiết bị Root/Jailbreak
        WRONG_URL(92, "URL không hợp lệ (C: M-92)"), // URL không hợp lệ
        WRONG_NOT_ACTICE(
            206,
            "Ứng dụng chưa được kích hoạt (C: M-206)",
        ), // Ứng dụng chưa được kích hoạt
        WRONG_INVALID_DATA(3, "Dữ liệu không hợp lệ (C: M-3)"), // Dữ liệu không hợp lệ
        WRONG_DATABASE(11, "Lỗi cơ sở dữ liệu (C: M-11)"), // Lỗi cơ sở dữ liệu
        WRONG_TRANSACTION_EXPIRED(
            31,
            "Lỗi giao dịch hết hạn xử lý (C: M-31)",
        ), // giao dịch hết hạn xử lý
        WRONG_UPDATE_TRANSACTION_FAIL(
            72,
            "Cập nhật trạng thái giao dịch không thành công (C: M-72)",
        ), // Cập nhật trạng thái giao dịch không thành công
        WRONG_CHALLENG_USED(
            97,
            "challenge code cho giao dịch này đã được sử dụng (C: M-97)",
        ), // challenge code cho giao dịch này đã được sử dụng
        WRONG_TRANSACTION_NOT_EXIST(
            99,
            "Giao dịch không tồn tại (C: M-99)",
        ), //  Giao dịch không tồn tại
        WRONG_SYSTEM(100, "Lỗi hệ thống (C: M-100)"), // Lỗi hệ thống
        WRONG_CALLBACK_FAILED(
            102,
            "Gọi lại không thành công. Kiểm tra kết nối với API. (C: M-102)",
        ), // Gọi lại không thành công. Kiểm tra kết nối với API.
    }

    private val mapping: HashMap<Int, String> = HashMap()
    fun getErrorMessage(code: Int = -1): String {
        if (mapping.isEmpty()) {
            OTPResultCode.values().forEach {
                mapping[it.code] = it.description
            }
        }
        try {
            return mapping[code] ?: ""
        } catch (_: Exception) {
        }
        return "Lỗi không xác định (C: M-$code)"
    }

    // cc
    enum class VtpOTPFlowType {
        CONFIRM_IN_APP, // test luồng in app
        DEFAULT,

        FORGOT_PIN,
        ACTIVE,
        SYNC,
        BLOCK,
        CONFIRM_NOTIFY, // Luồng xác thực qua push notification
        CONFIRM_IN_EFAST, // Luồng xác thực in app
        GET_OTP, // lấy mã OTP chủ động + Luồng xác thực offline (Sử dụng QR Code)
    }
}
